<template>
<!-- 此组件样式为普通商品流和拼团商品流混排，使用见品类商品流组件 -->
  <div>
    <ul class="templist test">
      <template   v-for="(item, index) in datalist"   :key="`${forceRenderKey}-${item.id}`">
        <template v-if="!item.groupPurchaseInfo">
          <li
          @click="shopClick({name:item.productInfo.showName, index, id:item.productInfo.id,qt_sku_data:item.productInfo['qtSkuData']})"
          v-in-viewport="{fn:exposureView,data:{index,id:item.productInfo.id,name:item.productInfo.showName,qt_sku_data:item.productInfo['qtSkuData']}}"
            v-if="item.cardType==1"
            :class="[true && 'templist-item']"
            @mouseenter.self="mouseOver(item.productInfo?.id)"
            @mouseleave.self="mouseLeave(item.productInfo?.id)"
            :data-exposure="`{
            eventName: 'pc_page_ListPage_Exposure',
            commonName: '${item.productInfo?.showName}',
            spid: '${trackInfo.spid}',
            sid: '${trackInfo.sid}',
            sptype: '${trackInfo.sptype}',
            merchantId: '${merchantId}',
            productId: '${item.productInfo?.id}',
            position: '${index}',
            search_sort_strategy_id:'${searchSortStrategyId}'
            }`"
            :data-analysysagent-exposure="`{
              eventName: 'page_list_product_exposure',
              jgspid: '${jgspid}',
              sid: '${trackInfo.sid}',
              sptype: '${trackInfo.sptype}',
              result_cnt: ${total},
              page_no: ${pageNum},
              page_size: ${20},
              total_page: ${pages},
              rank: ${(pageNum - 1) * 20 + index + 1},
              key_word: '${keyword}',
              product_type:'${item.productInfo?.productType}',
              search_sort_strategy_id: '${searchSortStrategyId && searchSortStrategyId != 'null' ? `${searchSortStrategyId}` : null}',
              operation_id: ${item.productInfo?.operationId && item.productInfo?.operationId != 'null' ? `${item.productInfo?.operationId}` : null},
              operation_rank: ${item.productInfo?.operationId ? 1 : null},
              list_position_type: '${item.productInfo?.positionType}',
              list_position_typename: '${item.productInfo?.positionTypeName}',
              product_id: ${item.productInfo?.id},
              product_name: '${item.productInfo?.productName}',
              product_first: '${item.productInfo?.categoryFirstId}',
              product_present_price: ${getPrice(item.productInfo)},
              product_shop_code: '${item.productInfo?.shopCode}',
              product_shop_name: '${item.productInfo?.shopName}',
            }`"
          >
          <div class="images">
            <a @click="toDetail(item.productInfo?.id, item.productInfo, index, 1)">
              <div v-if="item.productInfo?.status === 2" class="sold-out">已售罄</div>
              <div>
                <img
                  class="pic"
                  v-lazy="imgUrl + '/ybm/product/min/' + item.productInfo?.imageUrl"
                  :key="item.productInfo?.imageUrl"
                  :alt="item.productInfo?.showName"
                />
                <!-- 不带文案的标签 -->
                <img
                  v-if="item.productInfo?.markerUrl && !item.productInfo?.reducePrice"
                  :src="imgUrl + item.productInfo?.markerUrl"
                  class="activity-token"
                  alt
                />
                <!-- 带文案的标签 -->
                <div
                  class="mark-text"
                  v-if="
                    item.productInfo?.markerUrl &&
                    item.productInfo?.reducePrice &&
                    (item.productInfo?.isControl != 1 ||
                      (item.productInfo?.isPurchase == true && item.productInfo?.isControl == 1))
                  "
                >
                  <img :src="imgUrl + item.productInfo.markerUrl" alt="药采节价" />
                  <h4>药采节价：{{ fixedtwo(item.productInfo.reducePrice) }}</h4>
                </div>
                <!-- 促销图标 -->
                <div
                  class="promotionSkuPrice"
                  v-if="
                    item.productInfo.promotionSkuImageUrl &&
                    (item.productInfo.isControl != 1 ||
                      (item.productInfo.isPurchase == true && item.productInfo.isControl == 1))
                  "
                >
                  <img :src="imgUrl + item.productInfo.promotionSkuImageUrl" alt />
                  <span v-if="item.productInfo?.limitFullDiscountActInfo?.limitFullDiscount > 0">
                    ￥{{ fixedtwo(item.productInfo.limitFullDiscountActInfo.limitFullDiscount) }}
                  </span>
                  <span v-else>￥{{ fixedtwo(item.productInfo.promotionSkuPrice) }}</span>
                </div>
                <div
                  class="active-tags"
                  v-if="(item.productInfo.tags || {}).activityTag && (item.productInfo.tags || {}).activityTag.tagNoteBackGroupUrl"
                >
                  <img :src="imgUrl + item.productInfo.tags.activityTag.tagNoteBackGroupUrl" alt />
                  <div class="timeStr" v-if="item.productInfo.tags.activityTag.timeStr">
                    <span>{{ item.productInfo.tags.activityTag.timeStr }}</span>
                  </div>
                  <div class="temtejia806">
                    <span
                      class="price806"
                      v-for="(item3, index) in item.productInfo.tags.activityTag.skuTagNotes"
                      :style="{ color: '#' + item3.textColor }"
                      :key="index"
                      >{{ item3.text }}</span
                    >
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div
            v-if="item.productInfo.nearEffect || (item.productInfo.tags.purchaseTags || []).filter(i=>i.type==4).length"
            class="nearEffectBox"
            :class="{leftText: (item.productInfo.tags.purchaseTags || []).filter(i=>i.type==4).length}"
          >
            <span v-if="item.productInfo.nearEffect">有效期 {{ item.productInfo.nearEffect }}</span>
            <span class="countTag" v-if="(item.productInfo.tags.purchaseTags || []).filter(i=>i.type==4).length">{{ ((item.productInfo.tags.purchaseTags || []).filter(i=>i.type==4)[0] || {}).text }}</span>
          </div>
          <div v-if="item.productInfo.actPt">
            <groupProduct :group-data="item.productInfo" :track-info="trackInfo" :licenseStatus="licenseStatus" />
          </div>
          <div v-else-if="item.productInfo.actPgby">
            <groupPgbyProduct :group-data="item.productInfo" :track-info="trackInfo" :licenseStatus="licenseStatus" />
          </div>
          <div class="info" v-else>
            <!-- 价格  -->
            <div class="price-add-wrap">
              <div class="price-wrap">
                <div class="price-container">
                  <div class="controlTitleText" v-if="item.productInfo.controlTitle && item.productInfo.controlType != 5">
                    <span v-if="item.productInfo?.limitFullDiscountActInfo?.limitFullDiscount > 0">
                      ￥{{ fixedtwo(item.productInfo.limitFullDiscountActInfo.limitFullDiscount) }}
                    </span>
                    <span v-else>{{ item.productInfo.controlTitle }}</span>
                  </div>
                  <!-- <div class="qualifications" v-if="licenseStatus === 1 || licenseStatus === 5">
                    认证资质可见
                  </div>
                  <div class="nobuy" v-else-if="item.productInfo.isPurchase != true && item.productInfo.isControl == 1">
                    暂无购买权限
                  </div> -->
                  <div v-else class="price-numer">
                    <!-- <span class="price-permission" v-if="!merchantId">
                      价格登录可见
                    </span>
                    <span class="price-permission" v-else-if="(item.productInfo.isOEM == 'true' && item.productInfo.signStatus == 0) || item.productInfo.showAgree == 0">
                      签署协议可见
                    </span> -->
                    <i class="price-numer-i" v-if="item.productInfo.levelPriceDTO">
                      {{item.productInfo.levelPriceDTO.rangePriceShowText}}
                    </i>
                    <i v-else-if="item.productInfo.priceType == 2 && item.productInfo.skuPriceRangeList">
                      ￥{{ fixedtwo(item.productInfo.skuPriceRangeList[0].price) }}起
                      <!-- ~{{
                        fixedtwo(item.productInfo.skuPriceRangeList[item.productInfo.skuPriceRangeList.length - 1].price)
                      }} -->
                    </i>
                    <div v-else class="pricewapper">
                      <div class="price-box clearfixed">
                        <div class="price-two">
                          <p>
                            <span class="priceDec">￥</span>
                            <span v-if="item.productInfo?.limitFullDiscountActInfo?.limitFullDiscount > 0">
                              <span class="priceInt">{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[0]}}</span>
                              <span class="priceFloat">.{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[1] || '00'}}</span>
                            </span>
                            <span v-else>
                              <span class="priceInt">{{String(item.productInfo.fob.toFixed(2)).split('.')[0]}}</span>
                              <span class="priceFloat">.{{String(item.productInfo.fob.toFixed(2)).split('.')[1] || '00'}}</span>
                            </span>
                            <!-- <span>￥{{ fixedtwo(item.productInfo.fob) }}</span> -->
                            <span class="zheHouPrice" v-if="item.productInfo.zheHouPrice">
                              {{ item.productInfo.zheHouPrice }}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- <div style="minheight: 43px;"> -->
                  <!-- <div class="label-box" v-if="item.productInfo.tagList && item.productInfo.tagList.length > 0">
                    <div class="labels">
                      <div class="labels-span">
                        <span
                          v-for="(item2, index2) in item.productInfo.tagList.slice(0, 3)"
                          :key="index2"
                          :class="`span${item2.uiType}`"
                          :style="{
                            background: ['临期', '近效期'].indexOf(item2.name) != -1
                              ? '#FF8E00'
                              : '#fe0800',
                          }"
                        >
                          {{ item2.name }}
                        </span>
                      </div>
                    </div>
                  </div> -->
                  <!-- 零售价毛利 -->
                  <!-- <div class="control-hid" v-if="merchantId">
                    <div class="control-box" v-if="!(licenseStatus === 1 || licenseStatus === 5)">
                      <priceBox
                        v-if="
                          (item.productInfo.isOEM != 'true' &&
                            (item.productInfo.isControl != 1 ||
                              (item.productInfo.isPurchase == true &&
                                item.productInfo.isControl == 1))) ||
                          (item.productInfo.isOEM == 'true' && item.productInfo.signStatus == 1)
                        "
                        :uniformPrice="item.productInfo.uniformPrice"
                        :suggestPrice="item.productInfo.suggestPrice"
                        :grossMargin="item.productInfo.grossMargin"
                      />
                    </div>
                  </div> -->
                <!-- </div> -->
              </div>
              <div v-if="item.productInfo.controlTitle && item.productInfo.controlType != 5 && item.productInfo.zheHouPrice" class="zheHouPrice">折后约￥{{ (item.productInfo.zheHouPrice).split('￥')[1] || '' }}</div>
            </div>
            <a @click="toDetail(item.productInfo.id, item.productInfo, index, 1)">
              <div class="commonName">
                <!-- 药名和规格 -->
                <span class="name">{{ item.productInfo.showName.trim() }}{{ item.productInfo.spec }}</span>
              </div>
              <div class="tagBox">
                <span v-for="(tag,index) in item.productInfo.tags.productTags" :key="tag.text">
                  <span
                    class="tagItem"
                    v-if="tag.text && tag.uiStyle == 1 && index < 3"
                    :style="{ color: parseRGBA(tag.textColor), background: parseRGBA(tag.bgColor), border: `1px solid ${parseRGBA(tag.borderColor)}` }"
                  >
                    <img v-if="tag.pcIcon" style="height: 14px; width: auto; object-fit: cover;display: inline-block;" :src="imgUrl + tag.pcIcon">
                    {{ tag.text }}
                  </span>
                </span>
                <span v-for="(tag,index) in item.productInfo.tags.dataTags" :key="tag.text">
                  <span
                    class="tagItem"
                    v-if="tag.text && tag.uiStyle == 1 && index < (3- ((item.productInfo.tags.productTags || []).length > 3 ? 3 : item.productInfo.tags.productTags || []).length)"
                    :style="{ color: parseRGBA(tag.textColor), background: parseRGBA(tag.bgColor), border: `1px solid ${parseRGBA(tag.borderColor)}` }"
                  >{{ tag.text }}</span>
                </span>
              </div>
              <div class="manufacturer textellipsis">{{ item.productInfo.manufacturer }}</div>
              <div class="collageBox">
                <div class="skText" v-if="item.productInfo.actSk">
                  距结束&nbsp;
                  <groupLimitTime :endTime="(item.productInfo.actSk || {}).endTime" timeColor="#F43000" />
                </div>
                <div v-else class="actBox">
                  <div v-if="((item.productInfo.tags || {}).couponTags || []).length" :class="((item.productInfo.tags || {}).couponTags[0] || {}).subType == 9 ? 'couponBg-zp' : 'couponBg'">
                    <span v-if="((item.productInfo.tags || {}).couponTags[0] || {}).subType != 9">{{ ((item.productInfo.tags || {}).couponTags[0] || {}).text }}</span>
                    <span v-else
                      class="tagItem tagItemBig"
                      :style="{ color: parseRGBA(((item.productInfo.tags || {}).couponTags[0] || {}).textColor), background: parseRGBA(((item.productInfo.tags || {}).couponTags[0] || {}).bgColor), border: `1px solid ${parseRGBA(((item.productInfo.tags || {}).couponTags[0] || {}).borderColor)}` }"
                    >
                      {{ ((item.productInfo.tags || {}).couponTags[0] || {}).text }}
                    </span>
                  </div>
                  <div class="tagBox" v-if="((item.productInfo.tags || {}).activityTags || []).length">
                    <span v-for="tag in item.productInfo.tags.activityTags" :key="tag.text">
                      <span
                        class="tagItem tagItemBig"
                        :class="tag.promoType == 8 ? 'tejia' : tag.promoType == 20 ? 'zhijiang' : ''"
                        v-if="tag.text && (tag.promoType == 8 || tag.promoType == 20)"
                      >{{ tag.text }}</span>
                      <span
                        v-else-if="tag.text"
                        class="tagItem tagItemBig"
                        :style="{ color: parseRGBA(tag.textColor), background: parseRGBA(tag.bgColor), border: `1px solid ${parseRGBA(tag.borderColor)}` }"
                      >{{ tag.text }}</span>
                    </span>
                  </div>
                </div>
              </div>
            </a>

            <div class="self-support">
              <span class="shrink0" v-if="(item.productInfo.tags || {}).titleTags && (item.productInfo.tags || {}).titleTags.length">
                {{ ((item.productInfo.tags || {}).titleTags[0] || {}).text }}
              </span>
              <!-- <span
                v-else-if="item.productInfo.titleTagList && item.productInfo.titleTagList.length"
              >
                {{ (item.productInfo.titleTagList[0] || {}).text }}
              </span> -->
              <span class="shopName" :class="{maxWidthName: (item.productInfo.tags.purchaseTags || []).filter(i=>i.type==9).length && (item.productInfo.tags || {}).titleTags && (item.productInfo.tags || {}).titleTags.length}">{{ item.productInfo.shopName }}</span>
              <span class="purchaseTags" v-if="(item.productInfo.tags.purchaseTags || []).filter(i=>i.type==9).length">{{ ((item.productInfo.tags.purchaseTags || []).filter(i=>i.type==9)[0] || {}).text }}</span>
            </div>
          </div>
          <a
            @click="toDetail(item.productInfo.id, item.productInfo, index, 1)"
            class="float-carButton"
            v-show="showCarBtnIndex == item.productInfo.id"
          >
            <!-- <div class="collection" @click.stop.prevent="collection(item, index)">
              <div class="zone-1">
                <div v-if="item.productInfo.favoriteStatus == 2" class="top top-1">
                  <span class="w-icon-normal icon-normal-collectEpt"></span>
                </div>
                <div v-if="item.productInfo.favoriteStatus == 1" class="top top-2">
                  <span class="w-icon-normal icon-normal-collectFull"></span>
                </div>
              </div>
              <div class="zone-2">
                <div v-if="item.productInfo.favoriteStatus == 2" class="bottom bottom-1">
                  <p class="textOne">收藏</p>
                </div>
                <div v-if="item.productInfo.favoriteStatus == 1" class="bottom bottom-2">
                  <p class="textOne">已收藏</p>
                </div>
              </div>
            </div> -->
            <div v-if="item.productInfo.actPt">
              <div
                :style="{top: (item.productInfo?.limitFullDiscountActInfo ? '25px' : '5px')}" 
                class="toStore" 
                @click.stop.prevent="toStore(item.productInfo.shopUrl);shopClick({name:item.productInfo.showName, index, id:item.productInfo.id,qt_sku_data:item.productInfo['qtSkuData'],isShop:true,shopCode:item.productInfo.shopCode})">
                <div class="storeIcon">
                  <img src="../assets/images/icon-store.png" />
                </div>
                <div class="toStoreText">进店</div>
              </div>
              <!-- stepPriceStatus=2为单阶梯 -->
              <div v-if="item.productInfo.actPt.stepPriceStatus == 2">
                <div class="hoverBox">
                  <div>
                    <div class="price-wrap">
                      <div class="price-container">
                        <span class="priceDec">￥</span>
                        <span v-if="getActivityStatus(item.productInfo.actPt) === 'inProgress'">
                          <!-- 多阶梯价格 -->
                          <span v-if="(item.productInfo.actPt || {}).stepPriceStatus == 1">
                            <span class="priceInt">{{String((item.productInfo.actPt || {}).minSkuPrice.toFixed(2)).split('.')[0]}}</span>
                            <span class="priceFloat">.{{String((item.productInfo.actPt || {}).minSkuPrice.toFixed(2)).split('.')[1] || '00'}}</span>
                            起
                          </span>
                          <!-- 单阶梯价格 -->
                          <span v-if="(item.productInfo.actPt || {}).stepPriceStatus == 2">
                            <span v-if="item.productInfo?.limitFullDiscountActInfo?.limitFullDiscount > 0">
                              <span class="priceInt">{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[0]}}</span>
                              <span class="priceFloat">.{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[1] || '00'}}</span>
                            </span>
                            <span v-else>
                              <span class="priceInt" v-if="getActivityStatus(item.productInfo.actPt) === 'inProgress'">{{String((item.productInfo.actPt || {}).assemblePrice.toFixed(2)).split('.')[0]}}</span>
                              <span class="priceFloat" v-if="getActivityStatus(item.productInfo.actPt) === 'inProgress'">.{{String((item.productInfo.actPt || {}).assemblePrice.toFixed(2)).split('.')[1] || '00'}}</span>
                            </span>
                          </span>
                        </span>
                        <span class="priceInt" v-if="getActivityStatus(item.productInfo.actPt) === 'notStart'">??</span>
                        <span class="priceFloat" v-if="getActivityStatus(item.productInfo.actPt) === 'notStart'">.??</span>
                      </div>
                      <div v-if="getActivityStatus(item.productInfo.actPt) === 'inProgress' && item.productInfo.zheHouPrice && Number(item.productInfo.zheHouPrice.split('￥')[1]) < (item.productInfo.actPt || {}).assemblePrice" class="zheHouPrice">折后约￥{{ (item.productInfo.zheHouPrice).split('￥')[1] || '' }}</div>
                      <!-- <div class="retailPrice" v-if="(item.productInfo.actPt || {}).stepPriceStatus == 2">￥{{item.productInfo.retailPrice.toFixed(2)}}</div> -->
                    </div>
                    <div class="ptShowName" style="font-size: 16px; color: black;">{{item.productInfo.showName}}/{{item.productInfo.spec}}</div>
                    <div v-if="item.productInfo.actPurchaseTip" style="padding: 5px;border-radius: 5px;background-color: #FFF7E9;margin: 3px 0;color: #333333;">{{ item.productInfo.actPurchaseTip }}</div>
                    <div class="pCount">
                      <span class="qpCount">已拼{{(item.productInfo.actPt || {}).orderNum}}{{item.productInfo.productUnit}}</span>
                      <span class="qpCount">/{{(item.productInfo.actPt || {}).skuStartNum}}{{item.productInfo.productUnit}}起拼</span>
                    </div>
                  </div>
                  <!-- <div class="progressBox">
                    <div class="progressContnet" :style="{width: `${(item.productInfo.actPt || {}).percentage*1 > 100 ? 100 : (item.productInfo.actPt || {}).percentage*1}%`}"></div>
                    <div class="progressSpot" :style="{left: `${(item.productInfo.actPt || {}).percentage*1 > 100 ? 100 : (item.productInfo.actPt || {}).percentage*1}%`}"></div>
                  </div> -->
                  <!-- <span class="ptPercentage">{{((item.productInfo.actPt || {}).percentage*1).toFixed(0)}}%</span> -->
                  <div>
                    <cartBtnPt  :scmE="scmE" :qtData="{...qtData,index:getRank(item.productInfo['qtSkuData']),result_cnt:datalist.length}" :product-data="item.productInfo" :searchSortStrategyId="searchSortStrategyId" :index="(pageNum - 1) * 20 + index" @change-price="changePrice" :jgAgentInfo="jgAgentInfo" :keyWord="keyword"/>
                  </div>
                </div>
              </div>
              <!-- stepPriceStatus=1为多阶梯 -->
              <div v-else-if="item.productInfo.actPt.stepPriceStatus == 1" class="ptHoverBox">
                <!-- <div class="ptPrice">{{ item.productInfo.activePrice || `￥${item.productInfo.actPt.maxSkuPrice.toFixed(2)}` }}</div> -->
                <div>
                  <div class="price-wrap">
                    <div class="price-container">
                      <span class="priceDec">￥</span>
                      <span v-if="getActivityStatus(item.productInfo.actPt) === 'inProgress'">
                        <!-- 多阶梯价格 -->
                        <span v-if="(item.productInfo.actPt || {}).stepPriceStatus == 1">
                          <span class="priceInt">{{String((item.productInfo.actPt || {}).minSkuPrice.toFixed(2)).split('.')[0]}}</span>
                          <span class="priceFloat">.{{String((item.productInfo.actPt || {}).minSkuPrice.toFixed(2)).split('.')[1] || '00'}}</span>
                          起
                          <!-- -
                          <span class="priceInt">{{String((item.productInfo.actPt || {}).maxSkuPrice.toFixed(2)).split('.')[0]}}</span>
                          <span class="priceFloat">.{{String((item.productInfo.actPt || {}).maxSkuPrice.toFixed(2)).split('.')[1] || '00'}}</span> -->
                        </span>
                        <!-- 单阶梯价格 -->
                        <span v-if="(item.productInfo.actPt || {}).stepPriceStatus == 2">
                          <span v-if="item.productInfo?.limitFullDiscountActInfo?.limitFullDiscount > 0">
                            <span class="priceInt">{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[0]}}</span>
                            <span class="priceFloat">.{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[1] || '00'}}</span>
                          </span>
                          <span v-else>
                            <span class="priceInt" v-if="getActivityStatus(item.productInfo.actPt) === 'inProgress'">{{String((item.productInfo.actPt || {}).assemblePrice.toFixed(2)).split('.')[0]}}</span>
                            <span class="priceFloat" v-if="getActivityStatus(item.productInfo.actPt) === 'inProgress'">.{{String((item.productInfo.actPt || {}).assemblePrice.toFixed(2)).split('.')[1] || '00'}}</span>
                          </span>
                        </span>
                      </span>
                      <span class="priceInt" v-if="getActivityStatus(item.productInfo.actPt) === 'notStart'">??</span>
                      <span class="priceFloat" v-if="getActivityStatus(item.productInfo.actPt) === 'notStart'">.??</span>
                    </div>
                    <div v-if="getActivityStatus(item.productInfo.actPt) === 'inProgress' && item.productInfo.zheHouPrice && Number(item.productInfo.zheHouPrice.split('￥')[1]) < (item.productInfo.actPt || {}).minSkuPrice" class="zheHouPrice">{{ item.productInfo.zheHouPrice }}</div>
                    <!-- <div class="retailPrice" v-if="(item.productInfo.actPt || {}).stepPriceStatus == 2">￥{{item.productInfo.retailPrice.toFixed(2)}}</div> -->
                  </div>
                  <div class="ptShowName" style="font-size: 16px;color: black;">{{item.productInfo.showName}}/{{item.productInfo.spec}}</div>
                  <div v-if="item.productInfo.actPurchaseTip" style="padding: 5px;border-radius: 5px;background-color: #FFF7E9;margin: 3px 0;color: #333333;">{{ item.productInfo.actPurchaseTip }}</div>
                  <div class="priceDesc"  v-if="!item.productInfo.controlTitle || item.productInfo.controlType == 5">
                    <div v-for="text in (item.productInfo.actPt || {}).stepPriceShowTexts" :key="text">{{ text }}</div>
                  </div>
                  <div class="pCount">
                    <span class="qpCount">已拼{{(item.productInfo.actPt || {}).orderNum}}{{item.productInfo.productUnit}}</span>
                    <span class="qpCount">/{{(item.productInfo.actPt || {}).skuStartNum}}{{item.productInfo.productUnit}}起拼</span>
                  </div>
                </div>
                <!-- <div class="progressBox">
                  <div class="progressContnet" :style="{width: `${(item.productInfo.actPt || {}).percentage*1 > 100 ? 100 : (item.productInfo.actPt || {}).percentage*1}%`}"></div>
                  <div class="progressSpot" :style="{left: `${(item.productInfo.actPt || {}).percentage*1 > 100 ? 100 : (item.productInfo.actPt || {}).percentage*1}%`}"></div>
                </div> -->
                <!-- <span class="ptPercentage">{{((item.productInfo.actPt || {}).percentage*1).toFixed(0)}}%</span> -->
                <div>
                  <cartBtnPt  :scmE="scmE" :qtData="{...qtData,index:getRank(item.productInfo['qtSkuData']),result_cnt:datalist.length}" :product-data="item.productInfo" :searchSortStrategyId="searchSortStrategyId" :index="(pageNum - 1) * 20 + index" @change-price="changePrice" :jgAgentInfo="jgAgentInfo" :keyWord="keyword"/>
                </div>
              </div>
            </div>
            <div v-else-if="item.productInfo.actSk">
              <div
                :style="{top: (item.productInfo?.limitFullDiscountActInfo ? '25px' : '5px')}" 
                class="toStore" 
                @click.stop.prevent="toStore(item.productInfo.shopUrl);shopClick({name:item.productInfo.showName, index, id:item.productInfo.id,qt_sku_data:item.productInfo['qtSkuData'],isShop:true,shopCode:item.productInfo.shopCode})">
                <div class="storeIcon">
                  <img src="../assets/images/icon-store.png" />
                </div>
                <div class="toStoreText">进店</div>
              </div>
              <div class="ptHoverBox">
                <div>
                  <div class="price-wrap">
                    <div class="price-container">
                      <div class="controlTitleText" v-if="item.productInfo.controlTitle && item.productInfo.controlType != 5">
                        <span v-if="item.productInfo?.limitFullDiscountActInfo?.limitFullDiscount > 0" style="color: red;">
                          ￥{{ fixedtwo(item.productInfo.limitFullDiscountActInfo.limitFullDiscount) }}
                        </span>
                        <span v-else style="font-weight: 500;color: red;">{{ item.productInfo.controlTitle }}</span>
                      </div>
                      <!-- <div class="qualifications" v-if="licenseStatus === 1 || licenseStatus === 5">
                        认证资质可见
                      </div>
                      <div class="nobuy" v-else-if="item.productInfo.isPurchase != true && item.productInfo.isControl == 1">
                        暂无购买权限
                      </div> -->
                      <div v-else class="price-numer">
                        <!-- <span class="price-permission" v-if="!merchantId">
                          价格登录可见
                        </span>
                        <span class="price-permission" v-else-if="(item.productInfo.isOEM == 'true' && item.productInfo.signStatus == 0) || item.productInfo.showAgree == 0">
                          签署协议可见
                        </span> -->
                        <i class="price-numer-i" v-if="item.productInfo.levelPriceDTO" style="color: red;">
                          {{item.productInfo.levelPriceDTO.rangePriceShowText}}
                        </i>
                        <i v-else-if="item.productInfo.priceType == 2 && item.productInfo.skuPriceRangeList" style="color: red;">
                          ￥{{ fixedtwo(item.productInfo.skuPriceRangeList[0].price) }}起
                          <!-- ~{{
                            fixedtwo(item.productInfo.skuPriceRangeList[item.productInfo.skuPriceRangeList.length - 1].price)
                          }} -->
                        </i>
                        <div v-else class="pricewapper">
                          <div class="price-box clearfixed">
                            <div class="price-two">
                              <p style="color: red;">
                                <span class="priceDec">￥</span>
                                <span v-if="item.productInfo?.limitFullDiscountActInfo?.limitFullDiscount > 0">
                                  <span class="priceInt">{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[0]}}</span>
                                  <span class="priceFloat">.{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[1] || '00'}}</span>
                                </span>
                                <span v-else>
                                  <span class="priceInt">{{String(item.productInfo.fob.toFixed(2)).split('.')[0]}}</span>
                                  <span class="priceFloat">.{{String(item.productInfo.fob.toFixed(2)).split('.')[1] || '00'}}</span>
                                </span>
                                <!-- <span>￥{{ fixedtwo(item.productInfo.fob) }}</span> -->
                                <span class="zheHouPrice" v-if="item.productInfo.zheHouPrice">
                                  {{ item.productInfo.zheHouPrice }}
                                </span>
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-if="item.productInfo.controlTitle && item.productInfo.controlType != 5 && item.productInfo.zheHouPrice" class="zheHouPrice">折后约￥{{ (item.productInfo.zheHouPrice).split('￥')[1] || '' }}</div>
                  </div>
                  <div class="ptShowName" style="color: black;font-size: 16px;">{{item.productInfo.showName}}/{{item.productInfo.spec}}</div>
                  <div v-if="item.productInfo.actPurchaseTip" style="padding: 5px;border-radius: 5px;background-color: #FFF7E9;margin: 3px 0;color: #333333;">{{ item.productInfo.actPurchaseTip }}</div>
                  <div class="progressBox">
                    <div class="progressContnet" :style="{width: `${(item.productInfo.actSk || {}).percentage*1 > 100 ? 100 : (item.productInfo.actSk || {}).percentage*1}%`}"></div>
                    <div class="progressSpot" :style="{left: `${(item.productInfo.actSk || {}).percentage*1 > 100 ? 100 : (item.productInfo.actSk || {}).percentage*1}%`}"></div>
                  </div>
                  <span class="ptPercentage">{{((item.productInfo.actSk || {}).percentage*1).toFixed(0)}}%</span>
                </div>
                <div>
                  <cartBtn  :scmE="scmE" :qtData="{...qtData,index:getRank(item.productInfo['qtSkuData']),result_cnt:datalist.length}" goodsType="actSk" :product-data="item.productInfo" :searchSortStrategyId="searchSortStrategyId" :index="(pageNum - 1) * 20 + index" :jgAgentInfo="jgAgentInfo" :keyWord="keyword"/>
                </div>
              </div>
            </div>
            <div v-else-if="item.productInfo.actPgby">
              <!-- <div class="toStore" @click.stop.prevent="toStore(item.productInfo.shopUrl)">
                <div class="storeIcon">
                  <img src="../assets/images/icon-store.png" />
                </div>
                <div class="toStoreText">进店</div>
              </div> -->
              <div class="ptHoverBox">
                <div>
                  <div class="price-wrap">
                    <div class="price-container">
                      <span class="priceDec">￥</span>
                      <span>
                        <span v-if="item.productInfo?.limitFullDiscountActInfo?.limitFullDiscount > 0">
                          <span class="priceInt">{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[0]}}</span>
                          <span class="priceFloat">.{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[1] || '00'}}</span>
                        </span>
                        <span v-else>
                          <span class="priceInt">{{String((item.productInfo.actPgby || {}).assemblePrice.toFixed(2)).split('.')[0]}}</span>
                          <span class="priceFloat">.{{String((item.productInfo.actPgby || {}).assemblePrice.toFixed(2)).split('.')[1] || '00'}}</span>
                        </span>
                      </span>
                    </div>
                    <div v-if="item.productInfo.zheHouPrice" class="zheHouPrice">折后约￥{{ (item.productInfo.zheHouPrice).split('￥')[1] || '' }}</div>
                    <!-- <div class="retailPrice" v-if="(item.productInfo.actPt || {}).stepPriceStatus == 2">￥{{item.productInfo.retailPrice.toFixed(2)}}</div> -->
                  </div>
                  <div class="ptShowName" style="font-size: 16px;color: black;">{{item.productInfo.showName}}</div>
                  </div>
                  <div v-if="item.productInfo.actPurchaseTip" style="padding: 5px;border-radius: 5px;background-color: #FFF7E9;margin: 3px 0;color: #333333;">{{ item.productInfo.actPurchaseTip }}</div>
                <!-- <div class="progressBox">
                  <div class="progressContnet" :style="{width: `${(item.productInfo.actSk || {}).percentage*1 > 100 ? 100 : (item.productInfo.actSk || {}).percentage*1}%`}"></div>
                  <div class="progressSpot" :style="{left: `${(item.productInfo.actSk || {}).percentage*1 > 100 ? 100 : (item.productInfo.actSk || {}).percentage*1}%`}"></div>
                </div> -->
                <!-- <span class="ptPercentage">{{((item.productInfo.actSk || {}).percentage*1).toFixed(0)}}%</span> -->
                <div>
                  <cartBtnPgby  :scmE="scmE" :qtData="{...qtData,index:getRank(item.productInfo['qtSkuData']),result_cnt:datalist.length}" :product-data="item.productInfo" :searchSortStrategyId="searchSortStrategyId" @change-price="changePrice" :index="(pageNum - 1) * 20 + index" :jgAgentInfo="jgAgentInfo" :keyWord="keyword"/>
                </div>
              </div>
            </div>
            <div v-else>
              <div class="hoverBox levelHoverBox">
                <div>
                  <div class="price-wrap">
                    <div class="price-container">
                      <div class="controlTitleText" v-if="item.productInfo.controlTitle && item.productInfo.controlType != 5">
                        <span v-if="item.productInfo?.limitFullDiscountActInfo?.limitFullDiscount > 0">
                          ￥{{ fixedtwo(item.productInfo.limitFullDiscountActInfo.limitFullDiscount) }}
                        </span>
                        <span v-else style="font-weight: 500;">{{ item.productInfo.controlTitle }}</span>
                      </div>
                      <!-- <div class="qualifications" v-if="licenseStatus === 1 || licenseStatus === 5">
                        认证资质可见
                      </div>
                      <div class="nobuy" v-else-if="item.productInfo.isPurchase != true && item.productInfo.isControl == 1">
                        暂无购买权限
                      </div> -->
                      <div v-else class="price-numer">
                        <!-- <span class="price-permission" v-if="!merchantId">
                          价格登录可见
                        </span>
                        <span class="price-permission" v-else-if="(item.productInfo.isOEM == 'true' && item.productInfo.signStatus == 0) || item.productInfo.showAgree == 0">
                          签署协议可见
                        </span> -->
                        <i class="price-numer-i" v-if="item.productInfo.levelPriceDTO">
                          {{item.productInfo.levelPriceDTO.rangePriceShowText}}
                        </i>
                        <i v-else-if="item.productInfo.priceType == 2 && item.productInfo.skuPriceRangeList">
                          ￥{{ fixedtwo(item.productInfo.skuPriceRangeList[0].price) }}起
                          <!-- ~{{
                            fixedtwo(item.productInfo.skuPriceRangeList[item.productInfo.skuPriceRangeList.length - 1].price)
                          }} -->
                        </i>
                        <div v-else class="pricewapper">
                          <div class="price-box clearfixed">
                            <div class="price-two">
                              <p style="color: red;">
                                <span class="priceDec">￥</span>
                                <span v-if="item.productInfo?.limitFullDiscountActInfo?.limitFullDiscount > 0">
                                  <span class="priceInt">{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[0]}}</span>
                                  <span class="priceFloat">.{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[1] || '00'}}</span>
                                </span>
                                <span v-else>
                                  <span class="priceInt">{{String(item.productInfo.fob.toFixed(2)).split('.')[0]}}</span>
                                  <span class="priceFloat">.{{String(item.productInfo.fob.toFixed(2)).split('.')[1] || '00'}}</span>
                                </span>
                                <!-- <span>￥{{ fixedtwo(item.productInfo.fob) }}</span> -->
                                <span class="zheHouPrice" v-if="item.productInfo.zheHouPrice">
                                  {{ item.productInfo.zheHouPrice }}
                                </span>
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-if="item.productInfo.controlTitle && item.productInfo.controlType != 5 && item.productInfo.zheHouPrice" class="zheHouPrice">折后约￥{{ (item.productInfo.zheHouPrice).split('￥')[1] || '' }}</div>
                  </div>
                  <div class="ptShowName" style="font-size: 16px;color: black;">{{item.productInfo.showName}}/{{item.productInfo.spec}}</div>
                  <div v-if="item.productInfo.actPurchaseTip" style="padding: 5px;border-radius: 5px;background-color: #FFF7E9;margin: 3px 0;color: #333333;">{{ item.productInfo.actPurchaseTip }}</div>
                  <div class="priceDesc"  v-if="(!item.productInfo.controlTitle || item.productInfo.controlType == 5) && item.productInfo.levelPriceDTO">
                    <div v-for="text in (item.productInfo.levelPriceDTO || {}).stepPriceShowTexts" :key="text">{{ text }}</div>
                  </div>
                </div>
                <div>
                  <cartBtn  :scmE="scmE" :qtData="{...qtData,index:getRank(item.productInfo['qtSkuData']),result_cnt:datalist.length}" :product-data="item.productInfo" :track-info="trackInfo" :searchSortStrategyId="searchSortStrategyId" :index="(pageNum - 1) * 20 + index" :jgAgentInfo="jgAgentInfo" :keyWord="keyword" />
                </div>
              </div>
              <!-- <div class="inputNumber" v-if="merchantId">
                <div
                  v-if="
                    (item.productInfo.isOEM == 'true' && item.productInfo.signStatus == 0) ||
                    item.productInfo.showAgree == 0
                  "
                ></div>
                <div v-else style="height: 100%;">
                  <cartBtn
                    v-if="
                      (item.productInfo.isControl != 1 ||
                        (item.productInfo.isPurchase == true && item.productInfo.isControl == 1)) &&
                      !(licenseStatus === 1 || licenseStatus === 5)
                    "
                    :is-pack="false"
                    :data-id="item.productInfo.id"
                    :is-split="item.productInfo.isSplit"
                    :product-num="item.productInfo.cartProductNum"
                    :med-num="item.productInfo.mediumPackageNum"
                  ></cartBtn>
                </div>
              </div> -->
            </div>
          </a>
          </li>
          <li
              v-else
              @click="shopClick({product:item.operationInfo.products[0],isOperation:true,name:item.operationInfo.products[0].showName, index, id:item.operationInfo.products[0].id,qt_sku_data:item.operationInfo.products[0]['qtSkuData']})"
              v-in-viewport="{fn:exposureViewOperation,data:{product:item.operationInfo.products[0],index,id:item.operationInfo.products[0].id,name:item.operationInfo.products[0].showName,qt_sku_data:item.operationInfo.products[0]['qtSkuData']}}"
              :class="[true && 'templist-item',]"
              @mouseenter.self="mouseOver(item.operationInfo.products[0].id)"
              @mouseleave.self="mouseLeave(item.operationInfo.products[0].id)"
              :data-exposure="`{
                eventName: 'pc_search_Active_Exposure',
                commonName: '${item.operationInfo.products[0].showName}',
                spid: '${trackInfo.spid}',
                sid: '${trackInfo.sid}',
                sptype: '${trackInfo.sptype}',
                merchantId: '${merchantId}',
                productId: '${item.operationInfo.products[0].id}',
                active_index: '${index}',
                search_sort_strategy_id:'${searchSortStrategyId}',
                active_type:'0',
                goods_groupid:'${item.operationInfo.products[0].operationExhibitionId}',
                active_id:'${item.operationInfo.products[0].operationId}'
              }`"
              :data-analysysagent-exposure="`{
                eventName: 'page_list_product_exposure',
                jgspid: '${jgspid}',
                sid: '${trackInfo.sid}',
                sptype: '${trackInfo.sptype}',
                result_cnt: ${total},
                page_no: ${pageNum},
                page_size: ${20},
                total_page: ${pages},
                rank: ${(pageNum - 1) * 20 + index + 1},
                key_word: '${keyword}',
                product_type:'${item.operationInfo.products[0].productType}',
                search_sort_strategy_id: '${searchSortStrategyId && searchSortStrategyId != 'null' ? `${searchSortStrategyId}` : null}',
                operation_id: ${item.operationInfo.products[0].operationId && item.operationInfo.products[0].operationId != 'null' ? `${item.operationInfo.products[0].operationId}` : null},
                operation_rank: ${item.operationInfo.products[0].operationId ? 1 : null},
                list_position_type: '${item.operationInfo.products[0].positionType}',
                list_position_typename: '${item.operationInfo.products[0].positionTypeName}',
                product_id: ${item.operationInfo.products[0].id},
                product_name: '${item.operationInfo.products[0].productName}',
                product_first: '${item.operationInfo.products[0].categoryFirstId}',
                product_present_price: ${getPrice(item.operationInfo.products[0])},
                product_shop_code: '${item.operationInfo.products[0].shopCode}',
                product_shop_name: '${item.operationInfo.products[0].shopName}',
              }`"

          >
            <div class="images">
              <a @click="toDetail(item.operationInfo.products[0].id, item.operationInfo, index, 2)">
                <div v-if="item.operationInfo.products[0].status === 2" class="sold-out">已售罄</div>
                <div>
                  <img
                    class="pic"
                    v-lazy="imgUrl + '/ybm/product/min/' + item.operationInfo.products[0].imageUrl"
                    :key="item.operationInfo.products[0].imageUrl"
                    :alt="item.operationInfo.products[0].showName"
                  />
                  <!-- 不带文案的标签 -->
                  <img
                    v-if="item.operationInfo.products[0].markerUrl && !item.operationInfo.products[0].reducePrice"
                    :src="imgUrl + item.operationInfo.products[0].markerUrl"
                    class="activity-token"
                    alt
                  />
                  <!-- 带文案的标签 -->
                  <div
                    class="mark-text"
                    v-if="
                      item.operationInfo.products[0].markerUrl &&
                      item.operationInfo.products[0].reducePrice &&
                      (item.operationInfo.products[0].isControl != 1 ||
                        (item.operationInfo.products[0].isPurchase == true && item.operationInfo.products[0].isControl == 1))
                    "
                  >
                    <img :src="imgUrl + item.operationInfo.products[0].markerUrl" alt="药采节价" />
                    <h4>药采节价：{{ fixedtwo(item.operationInfo.products[0].reducePrice) }}</h4>
                  </div>
                  <!-- 促销图标 -->
                  <div
                    class="promotionSkuPrice"
                    v-if="
                      item.operationInfo.products[0].promotionSkuImageUrl &&
                      (item.operationInfo.products[0].isControl != 1 ||
                        (item.operationInfo.products[0].isPurchase == true && item.operationInfo.products[0].isControl == 1))
                    "
                  >
                    <img :src="imgUrl + item.operationInfo.products[0].promotionSkuImageUrl" alt />
                    <span>￥{{ fixedtwo(item.operationInfo.products[0].promotionSkuPrice) }}</span>
                  </div>
                  <div
                    class="active-tags"
                    v-if="(item.operationInfo.products[0].tags || {}).activityTag && (item.operationInfo.products[0].tags || {}).activityTag.tagNoteBackGroupUrl"
                  >
                    <img :src="imgUrl + item.operationInfo.products[0].tags.activityTag.tagNoteBackGroupUrl" alt />
                    <div class="timeStr" v-if="item.operationInfo.products[0].tags.activityTag.timeStr">
                      <span>{{ item.operationInfo.products[0].tags.activityTag.timeStr }}</span>
                    </div>
                    <div class="temtejia806">
                      <span
                        class="price806"
                        v-for="(item3, index) in item.operationInfo.products[0].tags.activityTag.skuTagNotes"
                        :style="{ color: '#' + item3.textColor }"
                        :key="index"
                        >{{ item3.text }}</span
                      >
                    </div>
                  </div>
                </div>
              </a>
            </div>
            <div
              v-if="item.operationInfo.products[0].nearEffect || (item.operationInfo.products[0].tags.purchaseTags || []).filter(i=>i.type==4).length"
              class="nearEffectBox"
              :class="{leftText: (item.operationInfo.products[0].tags.purchaseTags || []).filter(i=>i.type==4).length}"
            >
              <span v-if="item.operationInfo.products[0].nearEffect">有效期 {{ item.operationInfo.products[0].nearEffect }}</span>
              <span class="countTag" v-if="(item.operationInfo.products[0].tags.purchaseTags || []).filter(i=>i.type==4).length">{{ ((item.operationInfo.products[0].tags.purchaseTags || []).filter(i=>i.type==4)[0] || {}).text }}</span>
            </div>
            <div v-if="item.operationInfo.products[0].actPt">
              <groupProductRun :group-data="item.operationInfo.products[0]" :track-info="trackInfo"  :licenseStatus="licenseStatus" />
            </div>
            <div v-else-if="item.operationInfo.products[0].actPgby">
              <groupPgbyProductRun :group-data="item.operationInfo.products[0]" :track-info="trackInfo"   :licenseStatus="licenseStatus" />
            </div>
            <div class="info" v-else>
              <!-- 价格  -->
              <div class="price-add-wrap">
                <div class="price-wrap">
                  <div class="price-container">
                    <div class="controlTitleText" v-if="item.operationInfo.products[0].controlTitle && item.operationInfo.products[0].controlType != 5">
                      {{ item.operationInfo.products[0].controlTitle }}
                    </div>
                    <!-- <div class="qualifications" v-if="licenseStatus === 1 || licenseStatus === 5">
                      认证资质可见
                    </div>
                    <div class="nobuy" v-else-if="item.productInfo.isPurchase != true && item.productInfo.isControl == 1">
                      暂无购买权限
                    </div> -->
                    <div v-else class="price-numer">
                      <!-- <span class="price-permission" v-if="!merchantId">
                        价格登录可见
                      </span>
                      <span class="price-permission" v-else-if="(item.productInfo.isOEM == 'true' && item.productInfo.signStatus == 0) || item.productInfo.showAgree == 0">
                        签署协议可见
                      </span> -->
                      <i class="price-numer-i" v-if="item.operationInfo.products[0].levelPriceDTO">
                        {{item.operationInfo.products[0].levelPriceDTO.rangePriceShowText}}
                      </i>
                      <i v-else-if="item.operationInfo.products[0].priceType == 2 && item.operationInfo.products[0].skuPriceRangeList">
                        ￥{{ fixedtwo(item.operationInfo.products[0].skuPriceRangeList[0].price) }}~{{
                          fixedtwo(item.operationInfo.products[0].skuPriceRangeList[item.operationInfo.products[0].skuPriceRangeList.length - 1].price)
                        }}
                      </i>
                      <div v-else class="pricewapper">
                        <div class="price-box clearfixed">
                          <div class="price-two">
                            <p>
                              <span></span>
                              <span class="priceDec">￥</span>
                              <span class="priceInt">{{String(item.operationInfo.products[0].fob.toFixed(2)).split('.')[0]}}</span>
                              <span class="priceFloat">.{{String(item.operationInfo.products[0].fob.toFixed(2)).split('.')[1] || '00'}}</span>
                              <!-- <span>￥{{ fixedtwo(item.productInfo.fob) }}</span> -->
                              <span class="zheHouPrice" v-if="item.operationInfo.products[0].zheHouPrice && Number(item.operationInfo.products[0].zheHouPrice.split('￥')[1]) < item.operationInfo.products[0].fob">
                                折后约￥{{ (item.operationInfo.products[0].zheHouPrice).split('￥')[1] || '' }}
                              </span>
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- <div style="minheight: 43px;"> -->
                    <!-- <div class="label-box" v-if="item.productInfo.tagList && item.productInfo.tagList.length > 0">
                      <div class="labels">
                        <div class="labels-span">
                          <span
                            v-for="(item2, index2) in item.productInfo.tagList.slice(0, 3)"
                            :key="index2"
                            :class="`span${item2.uiType}`"
                            :style="{
                              background: ['临期', '近效期'].indexOf(item2.name) != -1
                                ? '#FF8E00'
                                : '#fe0800',
                            }"
                          >
                            {{ item2.name }}
                          </span>
                        </div>
                      </div>
                    </div> -->
                    <!-- 零售价毛利 -->
                    <!-- <div class="control-hid" v-if="merchantId">
                      <div class="control-box" v-if="!(licenseStatus === 1 || licenseStatus === 5)">
                        <priceBox
                          v-if="
                            (item.productInfo.isOEM != 'true' &&
                              (item.productInfo.isControl != 1 ||
                                (item.productInfo.isPurchase == true &&
                                  item.productInfo.isControl == 1))) ||
                            (item.productInfo.isOEM == 'true' && item.productInfo.signStatus == 1)
                          "
                          :uniformPrice="item.productInfo.uniformPrice"
                          :suggestPrice="item.productInfo.suggestPrice"
                          :grossMargin="item.productInfo.grossMargin"
                        />
                      </div>
                    </div> -->
                  <!-- </div> -->
                </div>
              </div>
              <a @click="toDetail(item.operationInfo.products[0].id, item.operationInfo,index, 2)">
                <div class="commonName">
                  <!-- 药名和规格 -->
                  <span class="name">{{ item.operationInfo.products[0].showName.trim() }}{{ item.operationInfo.products[0].spec }}</span>
                </div>
                <div class="tagBox">
                  <span v-for="(tag,index) in item.operationInfo.products[0].tags.productTags" :key="tag.text">
                    <span
                      class="tagItem"
                      v-if="tag.text && tag.uiStyle == 1 && index < 3"
                      :style="{ color: parseRGBA(tag.textColor), background: parseRGBA(tag.bgColor), border: `1px solid ${parseRGBA(tag.borderColor)}` }"
                    >
                      <img v-if="tag.pcIcon" style="height: 14px; width: auto; object-fit: cover;display: inline-block;" :src="imgUrl + tag.pcIcon">
                      {{tag.text }}
                    </span>
                  </span>
                  <span v-for="(tag,index) in item.operationInfo.products[0].tags.dataTags" :key="tag.text">
                    <span
                      class="tagItem"
                      v-if="tag.text && tag.uiStyle == 1 && index < (3- ((item.operationInfo.products[0].tags.productTags || []).length > 3 ? 3 : item.operationInfo.products[0].tags.productTags || []).length)"
                      :style="{ color: parseRGBA(tag.textColor), background: parseRGBA(tag.bgColor), border: `1px solid ${parseRGBA(tag.borderColor)}` }"
                    >{{ tag.text }}</span>
                  </span>
                </div>
                <div class="manufacturer textellipsis">{{ item.operationInfo.products[0].manufacturer }}</div>
                <div class="collageBox">
                  <div class="skText" v-if="item.operationInfo.products[0].actSk">
                    距结束&nbsp;
                    <groupLimitTime :endTime="(item.operationInfo.products[0].actSk || {}).endTime" timeColor="#F43000" />
                  </div>
                  <div v-else class="actBox">
                    <div v-if="((item.operationInfo.products[0].tags || {}).couponTags || []).length" :class="((item.operationInfo.products[0].tags || {}).couponTags[0] || {}).subType == 9 ? 'couponBg-zp' : 'couponBg'">
                      <span v-if="((item.operationInfo.products[0].tags || {}).couponTags[0] || {}).subType != 9">{{ ((item.operationInfo.products[0].tags || {}).couponTags[0] || {}).text }}</span>
                      <span v-else
                        class="tagItem tagItemBig"
                        :style="{ color: parseRGBA(((item.operationInfo.products[0].tags || {}).couponTags[0] || {}).textColor), background: parseRGBA(((item.operationInfo.products[0].tags || {}).couponTags[0] || {}).bgColor), border: `1px solid ${parseRGBA(((item.operationInfo.products[0].tags || {}).couponTags[0] || {}).borderColor)}` }"
                      >
                        {{ ((item.operationInfo.products[0].tags || {}).couponTags[0] || {}).text }}
                      </span>
                    </div>
                    <div class="tagBox" v-if="((item.operationInfo.products[0].tags || {}).activityTags || []).length">
                      <span v-for="tag in item.operationInfo.products[0].tags.activityTags" :key="tag.text">
                        <span
                          class="tagItem tagItemBig"
                          :class="tag.promoType == 8 ? 'tejia' : tag.promoType == 20 ? 'zhijiang' : ''"
                          v-if="tag.text && (tag.promoType == 8 || tag.promoType == 20)"
                        >{{ tag.text }}</span>
                        <span
                          v-else-if="tag.text"
                          class="tagItem tagItemBig"
                          :style="{ color: parseRGBA(tag.textColor), background: parseRGBA(tag.bgColor), border: `1px solid ${parseRGBA(tag.borderColor)}` }"
                        >{{ tag.text }}</span>
                      </span>
                    </div>
                  </div>
                </div>
              </a>

              <!-- <div class="self-support">
                <span class="shrink0" v-if="(item.operationInfo.products[0].tags || {}).titleTags && (item.operationInfo.products[0].tags || {}).titleTags.length">
                  {{ ((item.operationInfo.products[0].tags || {}).titleTags[0] || {}).text }}
                </span> -->
                <!-- <span
                  v-else-if="item.productInfo.titleTagList && item.productInfo.titleTagList.length"
                >
                  {{ (item.productInfo.titleTagList[0] || {}).text }}
                </span> -->
                <!-- <span class="shopName" :class="{maxWidthName: (item.operationInfo.products[0].tags.purchaseTags || []).filter(i=>i.type==9).length && (item.operationInfo.products[0].tags || {}).titleTags && (item.operationInfo.products[0].tags || {}).titleTags.length}">{{ item.operationInfo.products[0].shopName }}</span>
                <span class="purchaseTags" v-if="(item.operationInfo.products[0].tags.purchaseTags || []).filter(i=>i.type==9).length">{{ ((item.operationInfo.products[0].tags.purchaseTags || []).filter(i=>i.type==9)[0] || {}).text }}</span>
              </div> -->
            </div>
            <div class="" style="margin-top: 5px">
                <span class="shrink0" v-if="item.operationInfo.title" style="display: flex;align-items: center;">
                  <img src="../assets/images/active/hd1.png" alt="" style="height: 15px;width:auto">&nbsp;{{ item.operationInfo.title}}<el-icon><ArrowRightBold /></el-icon>
                </span>
              
            </div>
            <a
              @click="toDetail(item.operationInfo.products[0].id, item.operationInfo, index, 2)"
              class="float-carButton"
              v-show="showCarBtnIndex == item.operationInfo.products[0].id"
            >
              <!-- <div class="collection" @click.stop.prevent="collection(item, index)">
                <div class="zone-1">
                  <div v-if="item.productInfo.favoriteStatus == 2" class="top top-1">
                    <span class="w-icon-normal icon-normal-collectEpt"></span>
                  </div>
                  <div v-if="item.productInfo.favoriteStatus == 1" class="top top-2">
                    <span class="w-icon-normal icon-normal-collectFull"></span>
                  </div>
                </div>
                <div class="zone-2">
                  <div v-if="item.productInfo.favoriteStatus == 2" class="bottom bottom-1">
                    <p class="textOne">收藏</p>
                  </div>
                  <div v-if="item.productInfo.favoriteStatus == 1" class="bottom bottom-2">
                    <p class="textOne">已收藏</p>
                  </div>
                </div>
              </div> -->
              <div v-if="item.operationInfo.products[0].actPt">
                <div
                  :style="{top: (item.productInfo?.limitFullDiscountActInfo ? '25px' : '5px')}" 
                  class="toStore" 
                  @click.stop.prevent="toStore(item.operationInfo.products[0].shopUrl, item.operationInfo);shopClick({product:item.operationInfo.products[0],name:item.operationInfo.products[0].showName, index, id:item.operationInfo.products[0].id,qt_sku_data:item.operationInfo.products[0]['qtSkuData'],isShop:true,isOperation:true,shopCode:item.operationInfo.products[0].shopCode})">
                  <div class="storeIcon">
                    <img src="../assets/images/icon-store.png" />
                  </div>
                  <div class="toStoreText">进店</div>
                </div>
                <!-- stepPriceStatus=2为单阶梯 -->
                <div v-if="item.operationInfo.products[0].actPt.stepPriceStatus == 2">
                  <div class="hoverBox">
                    <div>
                      <div class="price-wrap">
                        <div class="price-container">
                          <span class="priceDec">￥</span>
                          <span v-if="getActivityStatus(item.operationInfo.products[0].actPt) === 'inProgress'">
                            <!-- 多阶梯价格 -->
                            <span v-if="(item.operationInfo.products[0].actPt || {}).stepPriceStatus == 1">
                              <span class="priceInt">{{String((item.operationInfo.products[0].actPt || {}).minSkuPrice.toFixed(2)).split('.')[0]}}</span>
                              <span class="priceFloat">.{{String((item.operationInfo.products[0].actPt || {}).minSkuPrice.toFixed(2)).split('.')[1] || '00'}}</span>
                              -
                              <span class="priceInt">{{String((item.operationInfo.products[0].actPt || {}).maxSkuPrice.toFixed(2)).split('.')[0]}}</span>
                              <span class="priceFloat">.{{String((item.operationInfo.products[0].actPt || {}).maxSkuPrice.toFixed(2)).split('.')[1] || '00'}}</span>
                            </span>
                            <!-- 单阶梯价格 -->
                            <span v-if="(item.operationInfo.products[0].actPt || {}).stepPriceStatus == 2">
                              <span v-if="item.productInfo?.limitFullDiscountActInfo?.limitFullDiscount > 0">
                                <span class="priceInt">{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[0]}}</span>
                                <span class="priceFloat">.{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[1] || '00'}}</span>
                              </span>
                              <span v-else>
                                <span class="priceInt" v-if="getActivityStatus(item.operationInfo.products[0].actPt) === 'inProgress'">{{String((item.operationInfo.products[0].actPt || {}).assemblePrice.toFixed(2)).split('.')[0]}}</span>
                                <span class="priceFloat" v-if="getActivityStatus(item.operationInfo.products[0].actPt) === 'inProgress'">.{{String((item.operationInfo.products[0].actPt || {}).assemblePrice.toFixed(2)).split('.')[1] || '00'}}</span>
                              </span>
                            </span>
                          </span>
                          <span class="priceInt" v-if="getActivityStatus(item.operationInfo.products[0].actPt) === 'notStart'">??</span>
                          <span class="priceFloat" v-if="getActivityStatus(item.operationInfo.products[0].actPt) === 'notStart'">.??</span>
                        </div>
                        <div v-if="getActivityStatus(item.operationInfo.products[0].actPt) === 'inProgress' && item.operationInfo.products[0].zheHouPrice && Number(item.operationInfo.products[0].zheHouPrice.split('￥')[1]) < (item.operationInfo.products[0].actPt || {}).assemblePrice" class="zheHouPrice">折后约￥{{ (item.operationInfo.products[0].zheHouPrice).split('￥')[1] || '' }}</div>
                        <!-- <div class="retailPrice" v-if="(item.productInfo.actPt || {}).stepPriceStatus == 2">￥{{item.productInfo.retailPrice.toFixed(2)}}</div> -->
                      </div>
                      <div class="ptShowName" style="font-size: 16px;color: black;">{{item.operationInfo.products[0].showName}}/{{item.operationInfo.products[0].spec}}</div>
                      <div v-if="item.operationInfo.products[0].actPurchaseTip" style="padding: 5px;border-radius: 5px;background-color: #FFF7E9;margin: 3px 0;color: #333333;">{{ item.operationInfo.products[0].actPurchaseTip }}</div>
                      <div class="pCount">
                        <span class="qpCount">已拼{{(item.operationInfo.products[0].actPt || {}).orderNum}}{{item.operationInfo.products[0].productUnit}}</span>
                        <span class="qpCount">/{{(item.operationInfo.products[0].actPt || {}).skuStartNum}}{{item.operationInfo.products[0].productUnit}}起拼</span>
                      </div>
                    </div>
                    <!-- <div class="progressBox">
                      <div class="progressContnet" :style="{width: `${(item.operationInfo.products[0].actPt || {}).percentage*1 > 100 ? 100 : (item.operationInfo.products[0].actPt || {}).percentage*1}%`}"></div>
                      <div class="progressSpot" :style="{left: `${(item.operationInfo.products[0].actPt || {}).percentage*1 > 100 ? 100 : (item.operationInfo.products[0].actPt || {}).percentage*1}%`}"></div>
                    </div> -->
                    <!-- <span class="ptPercentage">{{((item.operationInfo.products[0].actPt || {}).percentage*1).toFixed(0)}}%</span> -->
                    <div>
                      <cartBtnPt :scmE="scmE" :qtData="{...qtData,isOperation:true,index:index+(20*(qtData.pageNum-1)),result_cnt:datalist.length}" :product-data="item.operationInfo.products[0]" :searchSortStrategyId="searchSortStrategyId" :index="(pageNum - 1) * 20 + index" @change-price="changePrice" :jgAgentInfo="jgAgentInfo" :keyWord="keyword"/>
                      <div v-if="item.operationInfo.title" style="display: flex;align-items:center;padding:4px 0" @click="go($event, item.operationInfo);shopClick({product:item.operationInfo.products[0],operationInfo:item.operationInfo,isGo:true,isOperation:true,name:item.operationInfo.products[0].showName, index, id:item.operationInfo.products[0].id,qt_sku_data:item.operationInfo.products[0]['qtSkuData']})"><img src="../assets/images/active/hd1.png" alt="" style="height: 15px;width:auto;color: white">&nbsp;{{ item.operationInfo.title}}<el-icon><ArrowRightBold /></el-icon></div>
                    </div>
                  </div>
                </div>
                <!-- stepPriceStatus=1为多阶梯 -->
                <div v-else-if="item.operationInfo.products[0].actPt.stepPriceStatus == 1" class="ptHoverBox">
                  <!-- <div class="ptPrice">{{ item.productInfo.activePrice || `￥${item.productInfo.actPt.maxSkuPrice.toFixed(2)}` }}</div> -->
                  <div>
                    <div class="price-wrap">
                      <div class="price-container">
                        <span class="priceDec">￥</span>
                        <span v-if="getActivityStatus(item.operationInfo.products[0].actPt) === 'inProgress'">
                          <!-- 多阶梯价格 -->
                          <span v-if="(item.operationInfo.products[0].actPt || {}).stepPriceStatus == 1">
                            <span class="priceInt">{{String((item.operationInfo.products[0].actPt || {}).minSkuPrice.toFixed(2)).split('.')[0]}}</span>
                            <span class="priceFloat">.{{String((item.operationInfo.products[0].actPt || {}).minSkuPrice.toFixed(2)).split('.')[1] || '00'}}</span>
                            -
                            <span class="priceInt">{{String((item.operationInfo.products[0].actPt || {}).maxSkuPrice.toFixed(2)).split('.')[0]}}</span>
                            <span class="priceFloat">.{{String((item.operationInfo.products[0].actPt || {}).maxSkuPrice.toFixed(2)).split('.')[1] || '00'}}</span>
                          </span>
                          <!-- 单阶梯价格 -->
                          <span v-if="(item.operationInfo.products[0].actPt || {}).stepPriceStatus == 2">
                            <span v-if="item.productInfo?.limitFullDiscountActInfo?.limitFullDiscount > 0">
                              <span class="priceInt">{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[0]}}</span>
                              <span class="priceFloat">.{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[1] || '00'}}</span>
                            </span>
                            <span v-else>
                              <span class="priceInt" v-if="getActivityStatus(item.operationInfo.products[0].actPt) === 'inProgress'">{{String((item.operationInfo.products[0].actPt || {}).assemblePrice.toFixed(2)).split('.')[0]}}</span>
                              <span class="priceFloat" v-if="getActivityStatus(item.operationInfo.products[0].actPt) === 'inProgress'">.{{String((item.operationInfo.products[0].actPt || {}).assemblePrice.toFixed(2)).split('.')[1] || '00'}}</span>
                            </span>
                          
                          </span>
                        </span>
                        <span class="priceInt" v-if="getActivityStatus(item.operationInfo.products[0].actPt) === 'notStart'">??</span>
                        <span class="priceFloat" v-if="getActivityStatus(item.operationInfo.products[0].actPt) === 'notStart'">.??</span>
                      </div>
                      <div v-if="getActivityStatus(item.operationInfo.products[0].actPt) === 'inProgress' && item.operationInfo.products[0].zheHouPrice && Number(item.operationInfo.products[0].zheHouPrice.split('￥')[1]) < (item.operationInfo.products[0].actPt || {}).minSkuPrice" class="zheHouPrice">折后约￥{{ (item.operationInfo.products[0].zheHouPrice).split('￥')[1] || '' }}</div>
                      <!-- <div class="retailPrice" v-if="(item.productInfo.actPt || {}).stepPriceStatus == 2">￥{{item.productInfo.retailPrice.toFixed(2)}}</div> -->
                    </div>
                    <div class="ptShowName" style="font-size: 16px;color: black;">{{item.operationInfo.products[0].showName}}/{{item.operationInfo.products[0].spec}}</div>
                    <div v-if="item.operationInfo.products[0].actPurchaseTip" style="padding: 5px;border-radius: 5px;background-color: #FFF7E9;margin: 3px 0;color: #333333;">{{ item.operationInfo.products[0].actPurchaseTip }}</div>
                    <div class="priceDesc"  v-if="!item.operationInfo.products[0].controlTitle || item.operationInfo.products[0].controlType == 5">
                      <div v-for="text in (item.operationInfo.products[0].actPt || {}).stepPriceShowTexts" :key="text">{{ text }}</div>
                    </div>
                    <div class="pCount">
                      <span class="qpCount">已拼{{(item.operationInfo.products[0].actPt || {}).orderNum}}{{item.operationInfo.products[0].productUnit}}</span>
                      <span class="qpCount">/{{(item.operationInfo.products[0].actPt || {}).skuStartNum}}{{item.operationInfo.products[0].productUnit}}起拼</span>
                    </div>
                  </div>
                  <!-- <div class="progressBox">
                    <div class="progressContnet" :style="{width: `${(item.operationInfo.products[0].actPt || {}).percentage*1 > 100 ? 100 : (item.operationInfo.products[0].actPt || {}).percentage*1}%`}"></div>
                    <div class="progressSpot" :style="{left: `${(item.operationInfo.products[0].actPt || {}).percentage*1 > 100 ? 100 : (item.operationInfo.products[0].actPt || {}).percentage*1}%`}"></div>
                  </div> -->
                  <!-- <span class="ptPercentage">{{((item.operationInfo.products[0].actPt || {}).percentage*1).toFixed(0)}}%</span> -->
                  <div>
                    <cartBtnPt :scmE="scmE" :qtData="{...qtData,isOperation:true,index:index+(20*(qtData.pageNum-1)),result_cnt:datalist.length}" :product-data="item.operationInfo.products[0]" :searchSortStrategyId="searchSortStrategyId" :index="(pageNum - 1) * 20 + index" @change-price="changePrice" :jgAgentInfo="jgAgentInfo" :keyWord="keyword"/>
                    <div v-if="item.operationInfo.title" style="display: flex;align-items:center;padding:4px 0" @click="go($event, item.operationInfo);shopClick({product:item.operationInfo.products[0],operationInfo:item.operationInfo,isGo:true,isOperation:true,name:item.operationInfo.products[0].showName, index, id:item.operationInfo.products[0].id,qt_sku_data:item.operationInfo.products[0]['qtSkuData']})"><img src="../assets/images/active/hd1.png" alt="" style="height: 15px;width:auto;color: white">&nbsp;{{ item.operationInfo.title}}<el-icon><ArrowRightBold /></el-icon></div>
                  </div>
                </div>
                
              </div>
              <div v-else-if="item.operationInfo.products[0].actSk">
                <div
                  :style="{top: (item.productInfo?.limitFullDiscountActInfo ? '25px' : '5px')}" 
                  class="toStore" 
                  @click.stop.prevent="toStore(item.operationInfo.products[0].shopUrl, item.operationInfo);shopClick({product:item.operationInfo.products[0],name:item.operationInfo.products[0].showName, index, id:item.operationInfo.products[0].id,qt_sku_data:item.operationInfo.products[0]['qtSkuData'],isShop:true,isOperation:true,shopCode:item.operationInfo.products[0].shopCode})">
                  <div class="storeIcon">
                    <img src="../assets/images/icon-store.png" />
                  </div>
                  <div class="toStoreText">进店</div>
                </div>
                <div class="ptHoverBox">
                  <div>
                    <div class="price-wrap">
                      <div class="price-container">
                        <div class="controlTitleText" v-if="item.operationInfo.products[0].controlTitle && item.operationInfo.products[0].controlType != 5">
                          {{ item.operationInfo.products[0].controlTitle }}
                        </div>
                        <div v-else class="price-numer">
                          <i class="price-numer-i" v-if="item.operationInfo.products[0].levelPriceDTO">
                            {{item.operationInfo.products[0].levelPriceDTO.rangePriceShowText}}
                          </i>
                          <i v-else-if="item.operationInfo.products[0].priceType == 2 && item.operationInfo.products[0].skuPriceRangeList">
                            ￥{{ fixedtwo(item.operationInfo.products[0].skuPriceRangeList[0].price) }}~{{
                              fixedtwo(item.operationInfo.products[0].skuPriceRangeList[item.operationInfo.products[0].skuPriceRangeList.length - 1].price)
                            }}
                          </i>
                          <div v-else class="pricewapper">
                            <div class="price-box clearfixed">
                              <div class="price-two">
                                <p style="color: red;">
                                  <span></span>
                                  <span class="priceDec">￥</span>
                                  <span class="priceInt">{{String(item.operationInfo.products[0].fob.toFixed(2)).split('.')[0]}}</span>
                                  <span class="priceFloat">.{{String(item.operationInfo.products[0].fob.toFixed(2)).split('.')[1] || '00'}}</span>
                                  <!-- <span>￥{{ fixedtwo(item.productInfo.fob) }}</span> -->
                                  <span class="zheHouPrice" v-if="item.operationInfo.products[0].zheHouPrice && Number(item.operationInfo.products[0].zheHouPrice.split('￥')[1]) < item.operationInfo.products[0].fob">
                                    折后约￥{{ (item.operationInfo.products[0].zheHouPrice).split('￥')[1] || '' }}
                                  </span>
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="ptShowName" style="font-size: 16px;color: black;">{{item.operationInfo.products[0].showName}}/{{item.operationInfo.products[0].spec}}</div>
                    <div v-if="item.operationInfo.products[0].actPurchaseTip" style="padding: 5px;border-radius: 5px;background-color: #FFF7E9;margin: 3px 0;color: #333333;">{{ item.operationInfo.products[0].actPurchaseTip }}</div>
                    <div class="progressBox">
                      <div class="progressContnet" :style="{width: `${(item.operationInfo.products[0].actSk || {}).percentage*1 > 100 ? 100 : (item.operationInfo.products[0].actSk || {}).percentage*1}%`}"></div>
                      <div class="progressSpot" :style="{left: `${(item.operationInfo.products[0].actSk || {}).percentage*1 > 100 ? 100 : (item.operationInfo.products[0].actSk || {}).percentage*1}%`}"></div>
                    </div>
                    <span class="ptPercentage">{{((item.operationInfo.products[0].actSk || {}).percentage*1).toFixed(0)}}%</span>
                  </div>
                  <div>
                    <cartBtn  :scmE="scmE" :qtData="{...qtData,isOperation:true,index:index+(20*(qtData.pageNum-1)),result_cnt:datalist.length}" goodsType="actSk" :product-data="item.operationInfo.products[0]" :searchSortStrategyId="searchSortStrategyId" :index="(pageNum - 1) * 20 + index" :jgAgentInfo="jgAgentInfo" :keyWord="keyword" />
                    <div v-if="item.operationInfo.title" style="display: flex;align-items:center;padding:4px 0" @click="go($event, item.operationInfo);shopClick({product:item.operationInfo.products[0],operationInfo:item.operationInfo,isGo:true,isOperation:true,name:item.operationInfo.products[0].showName, index, id:item.operationInfo.products[0].id,qt_sku_data:item.operationInfo.products[0]['qtSkuData']})"><img src="../assets/images/active/hd1.png" alt="" style="height: 15px;width:auto;color: white">&nbsp;{{ item.operationInfo.title}}<el-icon><ArrowRightBold /></el-icon></div>
                  </div>
                </div>
              </div>
              <div v-else-if="item.operationInfo.products[0].actPgby">
                <!-- <div class="toStore" @click.stop.prevent="toStore(item.productInfo.shopUrl)">
                  <div class="storeIcon">
                    <img src="../assets/images/icon-store.png" />
                  </div>
                  <div class="toStoreText">进店</div>
                </div> -->
                <div class="ptHoverBox">
                  <div class="price-wrap">
                    <div class="price-container">
                      <span class="priceDec">￥</span>
                      <span>
                        <span v-if="item.productInfo?.limitFullDiscountActInfo?.limitFullDiscount > 0">
                          <span class="priceInt">{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[0]}}</span>
                          <span class="priceFloat">.{{String(item.productInfo.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[1] || '00'}}</span>
                        </span>
                        <span v-else>
                          <span class="priceInt">{{String((item.operationInfo.products[0].actPgby || {}).assemblePrice.toFixed(2)).split('.')[0]}}</span>
                          <span class="priceFloat">.{{String((item.operationInfo.products[0].actPgby || {}).assemblePrice.toFixed(2)).split('.')[1] || '00'}}</span>
                        </span>
                      </span>
                    </div>
                    <div v-if="item.operationInfo.products[0].zheHouPrice && Number(item.operationInfo.products[0].zheHouPrice.split('￥')[1] < (item.operationInfo.products[0].actPgby || {}).assemblePrice)" class="zheHouPrice">折后约￥{{ (item.operationInfo.products[0].zheHouPrice).split('￥')[1] || '' }}</div>
                    <!-- <div class="retailPrice" v-if="(item.productInfo.actPt || {}).stepPriceStatus == 2">￥{{item.productInfo.retailPrice.toFixed(2)}}</div> -->
                  </div>
                  <div class="ptShowName" style="font-size: 16px;color: black;">{{item.operationInfo.products[0].showName}}/{{item.operationInfo.products[0].spec}}</div>
                  <div v-if="item.operationInfo.products[0].actPurchaseTip" style="padding: 5px;border-radius: 5px;background-color: #FFF7E9;margin: 3px 0;color: #333333;">{{ item.operationInfo.products[0].actPurchaseTip }}</div>
                  <!-- <div class="progressBox">
                    <div class="progressContnet" :style="{width: `${(item.productInfo.actSk || {}).percentage*1 > 100 ? 100 : (item.productInfo.actSk || {}).percentage*1}%`}"></div>
                    <div class="progressSpot" :style="{left: `${(item.productInfo.actSk || {}).percentage*1 > 100 ? 100 : (item.productInfo.actSk || {}).percentage*1}%`}"></div>
                  </div> -->
                  <!-- <span class="ptPercentage">{{((item.productInfo.actSk || {}).percentage*1).toFixed(0)}}%</span> -->
                  <div>
                    <cartBtnPgby :scmE="scmE" :qtData="{...qtData,isOperation:true,index:index+(20*(qtData.pageNum-1)),result_cnt:datalist.length}" :product-data="item.operationInfo.products[0]" :searchSortStrategyId="searchSortStrategyId" :index="(pageNum - 1) * 20 + index" @change-price="changePrice" :jgAgentInfo="jgAgentInfo" :keyWord="keyword"/>
                    <div v-if="item.operationInfo.title" style="display: flex;align-items:center;padding:4px 0" @click="go($event, item.operationInfo);shopClick({product:item.operationInfo.products[0],operationInfo:item.operationInfo,isGo:true,isOperation:true,name:item.operationInfo.products[0].showName, index, id:item.operationInfo.products[0].id,qt_sku_data:item.operationInfo.products[0]['qtSkuData']})"><img src="../assets/images/active/hd1.png" alt="" style="height: 15px;width:auto;color: white">&nbsp;{{ item.operationInfo.title}}<el-icon><ArrowRightBold /></el-icon></div>
                  </div>
                </div>
              </div>
              <div v-else>
                <div class="hoverBox levelHoverBox">
                  <div>
                    <div class="price-wrap">
                      <div class="price-container">
                        <div class="controlTitleText" v-if="item.operationInfo.products[0].controlTitle && item.operationInfo.products[0].controlType != 5">
                          {{ item.operationInfo.products[0].controlTitle }}
                        </div>
                        <div v-else class="price-numer">
                          <i class="price-numer-i" v-if="item.operationInfo.products[0].levelPriceDTO">
                            {{item.operationInfo.products[0].levelPriceDTO.rangePriceShowText}}
                          </i>
                          <i v-else-if="item.operationInfo.products[0].priceType == 2 && item.operationInfo.products[0].skuPriceRangeList">
                            ￥{{ fixedtwo(item.operationInfo.products[0].skuPriceRangeList[0].price) }}~{{
                              fixedtwo(item.operationInfo.products[0].skuPriceRangeList[item.operationInfo.products[0].skuPriceRangeList.length - 1].price)
                            }}
                          </i>
                          <div v-else class="pricewapper">
                            <div class="price-box clearfixed">
                              <div class="price-two">
                                <p style="color: red;">
                                  <span></span>
                                  <span class="priceDec">￥</span>
                                  <span class="priceInt">{{String(item.operationInfo.products[0].fob.toFixed(2)).split('.')[0]}}</span>
                                  <span class="priceFloat">.{{String(item.operationInfo.products[0].fob.toFixed(2)).split('.')[1] || '00'}}</span>
                                  <!-- <span>￥{{ fixedtwo(item.productInfo.fob) }}</span> -->
                                  <span class="zheHouPrice" v-if="item.operationInfo.products[0].zheHouPrice && Number(item.operationInfo.products[0].zheHouPrice.split('￥')[1]) < item.operationInfo.products[0].fob">
                                    折后约￥{{ (item.operationInfo.products[0].zheHouPrice).split('￥')[1] || '' }}
                                  </span>
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="ptShowName" style="font-size: 16px;color: black;">{{item.operationInfo.products[0].showName}}/{{item.operationInfo.products[0].spec}}</div>
                    <div v-if="item.operationInfo.products[0].actPurchaseTip" style="padding: 5px;border-radius: 5px;background-color: #FFF7E9;margin: 3px 0;color: #333333;">{{ item.operationInfo.products[0].actPurchaseTip }}</div>
                    <div class="priceDesc"  v-if="(!item.operationInfo.products[0].controlTitle || item.operationInfo.products[0].controlType == 5) && item.operationInfo.products[0].levelPriceDTO">
                      <div v-for="text in (item.operationInfo.products[0].levelPriceDTO || {}).stepPriceShowTexts" :key="text">{{ text }}</div>
                    </div>
                  </div>
                  <div>
                    <cartBtn :scmE="scmE" :qtData="{...qtData,isOperation:true,index:index+(20*(qtData.pageNum-1)),result_cnt:datalist.length}" :product-data="item.operationInfo.products[0]" :searchSortStrategyId="searchSortStrategyId" :index="(pageNum - 1) * 20 + index" :jgAgentInfo="jgAgentInfo" :keyWord="keyword" :track-info="trackInfo" />
                    <div v-if="item.operationInfo.title" style="display: flex;align-items:center;padding:4px 0" @click="go($event, item.operationInfo);shopClick({product:item.operationInfo.products[0],operationInfo:item.operationInfo,isGo:true,isOperation:true,name:item.operationInfo.products[0].showName, index, id:item.operationInfo.products[0].id,qt_sku_data:item.operationInfo.products[0]['qtSkuData']})"><img src="../assets/images/active/hd1.png" alt="" style="height: 15px;width:auto;color: white">&nbsp;{{ item.operationInfo.title}}<el-icon><ArrowRightBold /></el-icon></div>
                  </div>
                </div>
                <!-- <div class="inputNumber" v-if="merchantId">
                  <div
                    v-if="
                      (item.productInfo.isOEM == 'true' && item.productInfo.signStatus == 0) ||
                      item.productInfo.showAgree == 0
                    "
                  ></div>
                  <div v-else style="height: 100%;">
                    <cartBtn
                      v-if="
                        (item.productInfo.isControl != 1 ||
                          (item.productInfo.isPurchase == true && item.productInfo.isControl == 1)) &&
                        !(licenseStatus === 1 || licenseStatus === 5)
                      "
                      :is-pack="false"
                      :data-id="item.productInfo.id"
                      :is-split="item.productInfo.isSplit"
                      :product-num="item.productInfo.cartProductNum"
                      :med-num="item.productInfo.mediumPackageNum"
                    ></cartBtn>
                  </div>
                </div> -->
              </div>
            </a>
          </li>
        </template>
        <template v-else>
          <li class="templist-combination"   v-loading="loading" element-loading-text="" element-loading-spinner="none">
            <div v-if="loading" class="loading-overlay">
                加载中...
            </div>
            <div>
              <div class="combination">
                <div class="templist-item-combination"  v-in-viewport="{fn:exposureViewzhg,data:{product:item.groupPurchaseInfo.mainProduct,isMain:true} }" >
                  <div class="images">
                    <a @click="toDetail(item.groupPurchaseInfo.mainProduct?.id, item.groupPurchaseInfo.mainProduct, index, 1);zhgClick({product:item.groupPurchaseInfo.mainProduct,isMain:true})">
                      <div>
                        <img
                          class="pic"
                          v-lazy="imgUrl + '/ybm/product/min/' + item.groupPurchaseInfo?.mainProduct?.imageUrl"
                          :key="item.groupPurchaseInfo?.mainProduct?.imageUrl"
                          :alt="item?.groupPurchaseInfo?.mainProduct?.productInfo?.showName"
                        />
                        <!-- 不带文案的标签 -->
                        <img
                          v-if="item.groupPurchaseInfo.mainProduct?.markerUrl && !item.groupPurchaseInfo.mainProduct?.reducePrice"
                          :src="imgUrl + item.groupPurchaseInfo.mainProduct?.markerUrl"
                          class="activity-token"
                          alt
                        />
                        <!-- 带文案的标签 -->
                        <div
                          class="mark-text"
                          v-if="
                            item.groupPurchaseInfo.mainProduct?.markerUrl &&
                            item.groupPurchaseInfo.mainProduct?.reducePrice &&
                            (item.groupPurchaseInfo.mainProduct?.isControl != 1 ||
                              (item.groupPurchaseInfo.mainProduct?.isPurchase == true && item.groupPurchaseInfo.mainProduct?.isControl == 1))
                          "
                        >
                          <img :src="imgUrl + item.groupPurchaseInfo.mainProduct.markerUrl" alt="药采节价" />
                          <h4>药采节价：{{ fixedtwo(item.groupPurchaseInfo.mainProduct.reducePrice) }}</h4>
                        </div>
                        <!-- 促销图标 -->
                        <div
                          class="promotionSkuPrice"
                          v-if="
                            item.groupPurchaseInfo.mainProduct.promotionSkuImageUrl &&
                            (item.groupPurchaseInfo.mainProduct.isControl != 1 ||
                              (item.groupPurchaseInfo.mainProduct.isPurchase == true && item.groupPurchaseInfo.mainProduct.isControl == 1))
                          "
                        >
                          <img :src="imgUrl + item.groupPurchaseInfo.mainProduct.promotionSkuImageUrl" alt />
                          <span v-if="item.groupPurchaseInfo.mainProduct?.limitFullDiscountActInfo?.limitFullDiscount > 0">
                            ￥{{ fixedtwo(item.groupPurchaseInfo.mainProduct.limitFullDiscountActInfo.limitFullDiscount) }}
                          </span>
                          <span v-else>￥{{ fixedtwo(item.groupPurchaseInfo.mainProduct.promotionSkuPrice) }}</span>
                        </div>
                        <div
                          class="active-tags"
                          v-if="(item.groupPurchaseInfo.mainProduct.tags || {}).activityTag && (item.groupPurchaseInfo.mainProduct.tags || {}).activityTag.tagNoteBackGroupUrl"
                        >
                          <img :src="imgUrl + item.groupPurchaseInfo.mainProduct.tags.activityTag.tagNoteBackGroupUrl" alt />
                          <div class="timeStr" v-if="item.groupPurchaseInfo.mainProduct.tags.activityTag.timeStr">
                            <span>{{ item.groupPurchaseInfo.mainProduct.tags.activityTag.timeStr }}</span>
                          </div>
                          <div class="temtejia806">
                            <span
                              class="price806"
                              v-for="(item3, index) in item.groupPurchaseInfo.mainProduct.tags.activityTag.skuTagNotes"
                              :style="{ color: '#' + item3.textColor }"
                              :key="index"
                              >{{ item3.text }}</span
                            >
                          </div>
                        </div>
                      </div>
                    </a>
                  </div>
                  <div
                    v-if="item.groupPurchaseInfo.mainProduct.nearEffect || (item.groupPurchaseInfo.mainProduct.tags.purchaseTags || []).filter(i=>i.type==4).length"
                    class="nearEffectBox"
                    :class="{leftText: (item.groupPurchaseInfo.mainProduct.tags.purchaseTags || []).filter(i=>i.type==4).length}"
                    >
                    <span v-if="item.groupPurchaseInfo.mainProduct.nearEffect">有效期 {{ item.groupPurchaseInfo.mainProduct.nearEffect }}</span>
                    <span class="countTag" v-if="(item.groupPurchaseInfo.mainProduct.tags.purchaseTags || []).filter(i=>i.type==4).length">{{ ((item.groupPurchaseInfo.mainProduct.tags.purchaseTags || []).filter(i=>i.type==4)[0] || {}).text }}</span>
                  </div>
                  
                  <div class="info" >
                    <!-- 价格  -->
                    <div class="price-wrap">
                      <div class="price-container">
                        <p>
                          <span class="priceDec">￥</span>
                          <span v-if="item.groupPurchaseInfo.mainProduct.actPt.amount">
                            <span class="priceInt">{{String(item.groupPurchaseInfo.mainProduct?.actPt.amount.toFixed(2)).split('.')[0]}}</span>
                            <span class="priceFloat">.{{String(item.groupPurchaseInfo.mainProduct?.actPt.amount.toFixed(2)).split('.')[1] || '00'}}</span>
                          </span>
                        </p>
                      </div>
                    </div>
                    <a @click="toDetail(item.groupPurchaseInfo.mainProduct.id, item.groupPurchaseInfo.mainProduct, index, 1)">
                      <div class="commonName">
                        <!-- 药名和规格 -->
                        <span class="name">{{ item.groupPurchaseInfo.mainProduct.showName.trim() }}{{ item.groupPurchaseInfo.mainProduct.spec }}</span>
                      </div>
                    </a>
                  </div>
                  <div class="content">
                    <div class="spread-add">
                      <div class="reduce" @click.stop.prevent="addProductCart('min',1,item);zhgbtnClick({product:item.groupPurchaseInfo.mainProduct,index:1,text:'减'})">-</div>
                      <div class="cont">
                        <input
                          @click.stop.prevent="zhgbtnClick({product:item.groupPurchaseInfo.mainProduct,index:2,text:item.groupPurchaseInfo.mainProduct.actPt.count})"
                          class="input-val"
                          type="tel"
                          v-model="item.groupPurchaseInfo.mainProduct.actPt.count"
                          @change="inputCart($event,1,item)"
                        />
                      </div>
                      <div class="plus" @click.stop.prevent="addProductCart('add',1,item);zhgbtnClick({product:item.groupPurchaseInfo.mainProduct,index:3,text:'加'})">+</div>
                    </div>
                  </div>
                </div>
                <div class="middle">
                  <img style="width:24px;height:24px;" src="../assets/images/search/combination-plus.png" alt="plus">
                </div>
                <div class="templist-item-combination" :key="item.groupPurchaseInfo.subProducts[0].id" v-in-viewport="{fn:exposureViewzhg,data:{product:item.groupPurchaseInfo.subProducts[0],isMain:false} }">
                  <div class="images">
                    <a @click="toDetail(item.groupPurchaseInfo.subProducts[0]?.id, item.groupPurchaseInfo.subProducts[0], index, 1);zhgClick({product:item.groupPurchaseInfo.subProducts[0],isMain:false})">
                      <div>
                        <img
                          class="pic"
                          v-lazy="imgUrl + '/ybm/product/min/' + item.groupPurchaseInfo.subProducts[0]?.imageUrl"
                          :key="item.groupPurchaseInfo.subProducts[0]?.imageUrl"
                          :alt="item.groupPurchaseInfo.subProducts[0]?.showName"
                        />
                        <div class="activity-token-combination" v-if="discount">
                          <div>组合购省{{discount}}元</div>
                        </div>
                        <!-- 不带文案的标签 -->
                        <img
                          v-if="item.groupPurchaseInfo.subProducts[0]?.markerUrl && !item.groupPurchaseInfo.subProducts[0]?.reducePrice"
                          :src="imgUrl + item.groupPurchaseInfo.subProducts[0]?.markerUrl"
                          class="activity-token"
                          alt
                        />
                        <!-- 带文案的标签 -->
                        <div
                          class="mark-text"
                          v-if="
                            item.groupPurchaseInfo.subProducts[0]?.markerUrl &&
                            item.groupPurchaseInfo.subProducts[0]?.reducePrice &&
                            (item.groupPurchaseInfo.subProducts[0]?.isControl != 1 ||
                              (item.groupPurchaseInfo.subProducts[0]?.isPurchase == true && item.groupPurchaseInfo.subProducts[0]?.isControl == 1))
                          "
                        >
                          <img :src="imgUrl + item.groupPurchaseInfo.subProducts[0].markerUrl" alt="药采节价" />
                          <h4>药采节价：{{ fixedtwo(item.groupPurchaseInfo.subProducts[0].reducePrice) }}</h4>
                        </div>
                        <!-- 促销图标 -->
                        <div
                          class="promotionSkuPrice"
                          v-if="
                            item.groupPurchaseInfo.subProducts[0].promotionSkuImageUrl &&
                            (item.groupPurchaseInfo.subProducts[0].isControl != 1 ||
                              (item.groupPurchaseInfo.subProducts[0].isPurchase == true && item.groupPurchaseInfo.subProducts[0].isControl == 1))
                          "
                        >
                          <img :src="imgUrl + item.groupPurchaseInfo.subProducts[0].promotionSkuImageUrl" alt />
                          <span v-if="item.groupPurchaseInfo.subProducts[0]?.limitFullDiscountActInfo?.limitFullDiscount > 0">
                            ￥{{ fixedtwo(item.groupPurchaseInfo.subProducts[0].limitFullDiscountActInfo.limitFullDiscount) }}
                          </span>
                          <span v-else>￥{{ fixedtwo(item.groupPurchaseInfo.subProducts[0].promotionSkuPrice) }}</span>
                        </div>
                        <div
                          class="active-tags"
                          v-if="(item.groupPurchaseInfo.subProducts[0].tags || {}).activityTag && (item.groupPurchaseInfo.subProducts[0].tags || {}).activityTag.tagNoteBackGroupUrl"
                        >
                          <img :src="imgUrl + item.groupPurchaseInfo.subProducts[0].tags.activityTag.tagNoteBackGroupUrl" alt />
                          <div class="timeStr" v-if="item.groupPurchaseInfo.subProducts[0].tags.activityTag.timeStr">
                            <span>{{ item.groupPurchaseInfo.subProducts[0].tags.activityTag.timeStr }}</span>
                          </div>
                          <div class="temtejia806">
                            <span
                              class="price806"
                              v-for="(item3, index) in item.groupPurchaseInfo.subProducts[0].tags.activityTag.skuTagNotes"
                              :style="{ color: '#' + item3.textColor }"
                              :key="index"
                              >{{ item3.text }}</span
                            >
                          </div>
                        </div>
                      </div>
                    </a>
                  </div>
                  <div
                    v-if="item.groupPurchaseInfo.subProducts[0].nearEffect || (item.groupPurchaseInfo.subProducts[0].tags.purchaseTags || []).filter(i=>i.type==4).length"
                    class="nearEffectBox"
                    :class="{leftText: (item.groupPurchaseInfo.subProducts[0].tags.purchaseTags || []).filter(i=>i.type==4).length}"
                    >
                    <span v-if="item.groupPurchaseInfo.subProducts[0].nearEffect">有效期 {{ item.groupPurchaseInfo.subProducts[0].nearEffect }}</span>
                    <span class="countTag" v-if="(item.groupPurchaseInfo.subProducts[0].tags.purchaseTags || []).filter(i=>i.type==4).length">{{ ((item.groupPurchaseInfo.subProducts[0].tags.purchaseTags || []).filter(i=>i.type==4)[0] || {}).text }}</span>
                  </div>
                  <div class="info" >
                    <!-- 价格  -->
                    <div class="price-wrap">
                      <div class="price-container">
                        <p>
                          <span class="priceDec">￥</span>
                          <span v-if="item.groupPurchaseInfo.subProducts[0].actPt.amount">
                            <span class="priceInt">{{String(item.groupPurchaseInfo.subProducts[0]?.actPt.amount.toFixed(2)).split('.')[0]}}</span>
                            <span class="priceFloat">.{{String(item.groupPurchaseInfo.subProducts[0]?.actPt.amount.toFixed(2)).split('.')[1] || '00'}}</span>
                          </span>
                        </p>
                      </div>
                    </div>
                    <a @click="toDetail(item.groupPurchaseInfo.subProducts[0].id, item.productInfo, index, 1)">
                      <div class="commonName">
                        <!-- 药名和规格 -->
                        <span class="name">{{ item.groupPurchaseInfo.subProducts[0].showName.trim() }}{{ item.groupPurchaseInfo.subProducts[0].spec }}</span>
                      </div>
                    </a>
                  </div>
                  <div style="display:flex;align-items: center;justify-content: space-between;">
                    <div class="content">
                      <div class="spread-add">
                        <div class="reduce" @click.stop.prevent="addProductCart('min',2,item);zhgbtnClick({product:item.groupPurchaseInfo.subProducts[0],index:1,text:'减'})">-</div>
                        <div class="cont">
                          <input
                            @click.stop.prevent="zhgbtnClick({product:item.groupPurchaseInfo.subProducts[0],index:2,text:item.groupPurchaseInfo.subProducts[0].actPt?.count})"
                            class="input-val"
                            type="tel"
                            :value="item.groupPurchaseInfo.subProducts[0].actPt?.count"
                            @input="inputChange($event,2,item)"
                            @change="inputCart($event,2,item)"
                          />
                        </div>
                        <div class="plus" @click.stop.prevent="addProductCart('add',2,item);zhgbtnClick({product:item.groupPurchaseInfo.subProducts[0],index:3,text:'加'})">+</div>
                      </div>
                    </div>
                    <div style="display:flex;align-items: center;width: 70px;height: 17px;justify-content: end;margin-right: 5px;" v-if="showFlushed" @click="changeProduct(item)">
                      <img style="width:14px;height:14px;" src="../assets/images/search/combination-flushed.png" alt="plus">
                      <div style="color: #666666;margin-left:3px;">换一个</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="combination-bottom">
                <div class="combinationCenter">
                  <div class="combinationCenterTop">
                    <div class="price-two">
                      <p>
                        <span class="priceDec">￥</span>
                        <span v-if="realPay">
                          <span class="priceInt">{{String(realPay.toFixed(2)).split('.')[0]}}</span>
                          <span class="priceFloat">.{{String(realPay.toFixed(2)).split('.')[1] || '00'}}</span>
                        </span>
                      </p>
                    </div>
                  </div>
                  <div class="combinationCenterBottom" v-if="combinedDiscountSub">
                    <div class="item">组合优惠：￥{{combinedDiscountSub}}</div>
                  </div>
                </div>
                <div class="combinationBuy"  @click="combinationBuyClick(item);moduleClickZhg({product:item.groupPurchaseInfo})">组合购</div>
              </div>
            </div>
          </li>
        </template>
      </template>
    </ul>
  </div>
</template>
    
  <script setup>
    import cartBtn from "./addCartBtn.vue";
    import cartBtnPt from "./addCartBtnPt.vue";
    import cartBtnPgby from "./addCartBtnPgby.vue";
    import priceBox from "./priceBox.vue";
    import CountdownTimer from './CountdownTimer.vue';
    import { putRequest, handleDiscount,changeCart,combinedInfoQuery,gotoSettle} from "@/http_pc/api";
    import groupProduct from './groupProduct.vue';
    import groupPgbyProduct from './groupPgbyProduct.vue';
    import groupLimitTime from './groupLimitTime.vue';
    import groupPgbyProductRun from './groupPgbyProductRun.vue';
    import groupProductRun from './groupProductRun.vue';
    import { onMounted, ref, watch, getCurrentInstance, nextTick ,defineExpose } from "vue";
    import { actionTracking } from '@/config/eventTracking';
    import { ElMessage } from "element-plus";
    
    const props = defineProps({
      goodsData: {
        default: []
      },
      trackInfo: {
        default: {}
      },
      licenseStatus: {
        default: 0,
      },
      body_color: {
        default: '#fafafa'
      }, 
      searchSortStrategyId: {
        default: ''
      },
      keyword: {
        default: ''
      },
      pages: {
        default: ''
      },
      pageNum: {
        default: ''
      },
      total: {
        default: ''
      },
      scmE:{
        default: ''
      },
      qtData: {
        default: {}
      },
      groupPurchase:{
        default:{}
      },
    });
    const detailUrl = import.meta.env.VITE_BASE_URL_PC;
    const showCarBtnIndex = ref(null);
    const datalist = ref(props.goodsData);
    const jgspid = sessionStorage.getItem("jgspid") || "";
    //datalist.value.forEach(item => {
    //  if (item.productInfo.tags.dataTags) {
    //    let arr = [];
    //    item.productInfo.tags.productTags.forEach(k => {
    //      if (item.productInfo.tags.dataTags.findIndex(f => f.text == k.text) == -1) {
    //        arr.push(k);
    //      }
    //    })
    //    item.productInfo.tags.productTags = arr;
    //  }
    //})

    const imgUrl = import.meta.env.VITE_IMG;
    const length = ref(10);
    const { proxy } = getCurrentInstance();
    const merchantId = proxy.$merchantId;//proxy.$merchantId
    // proxy.$merchantId = '1500125099'
    const combinedDiscountSub = ref(0.00);
    const discount = ref('0.00');
    const realPay = ref(0.00);
    const loading = ref(false);
    const submitDisable = ref(false); //防抖
    const showFlushed = ref(true); //隐藏换一个
    onMounted(() => {
      // datalist.value = props.goodsData;
      addDiscount(props.goodsData);
      if (props.groupPurchase) {
        if(props.groupPurchase.index || props.groupPurchase.index === 0){
          changeCombinationPrice(datalist.value[props.groupPurchase.index],1)
        }
      }
      // WS.Bus.$on("cart_isExtend", (isExtend, id) => {
      //   let original = document.querySelectorAll(`i[data-original-id]`);
      //   for (let i = 0; i < original.length; i++) {
      //     if (original[i].getAttribute("data-original-id") == id) {
      //       if (isExtend) {
      //         original[i].style.display = "none";
      //       } else {
      //         original[i].style.display = "inline-block";
      //       }
      //     }
      //   }
      //   let pageage = document.querySelectorAll(`div[data-pageage-id]`);
      //   for (let i = 0; i < pageage.length; i++) {
      //     if (pageage[i].getAttribute("data-pageage-id") == id) {
      //       if (isExtend) {
      //         pageage[i].style.display = "block";
      //         pageage[i].previousElementSibling.style.width = "40%";
      //       } else {
      //         pageage[i].previousElementSibling.style.width = "90%";
      //         pageage[i].style.display = "none";
      //       }
      //     }
      //   }
      // });
    })
    //埋点
    const getRank=(data)=>{
      // console.log(data)
      if(data){
        let qtSkuData=JSON.parse(data)
        return qtSkuData.rank-1
      }
      
    }
    /**组合购下单 */
    const combinationBuyClick = (item) => {
      if(item.groupPurchaseInfo.mainProduct.actPt.selectStatus == 0){
          ElMessage.error(item.groupPurchaseInfo.mainProduct.actPt.errMsg && item.groupPurchaseInfo.mainProduct.actPt.errMsg != '数量必须大于0' ? item.groupPurchaseInfo.mainProduct.actPt.errMsg : `您还没有加购${item.groupPurchaseInfo.mainProduct.showName}哦，请先加购再下单`);
          return false;
      }else if(item.groupPurchaseInfo.subProducts[0].actPt.selectStatus == 0){
          ElMessage.error(item.groupPurchaseInfo.subProducts[0].actPt.errMsg && item.groupPurchaseInfo.subProducts[0].actPt.errMsg != '数量必须大于0' ? item.groupPurchaseInfo.subProducts[0].actPt.errMsg : `您还没有加购${item.groupPurchaseInfo.subProducts[0].showName}哦，请先加购再下单`);
          return false;
      }
      let bizProducts = JSON.stringify(
        [{
            skuId:item.groupPurchaseInfo.mainProduct.id,
            quantity:item.groupPurchaseInfo.mainProduct.actPt.count,
            selectStatus:1,
        },
        {
            skuId:item.groupPurchaseInfo.subProducts[0].id,
            quantity:item.groupPurchaseInfo.subProducts[0].actPt.count,
            selectStatus:1,
        }]
      )
      let param = {
        bizProducts:bizProducts,
      }
      if (submitDisable.value) {
        return;
      }
      submitDisable.value = true;
      gotoSettle(param).then(res=>{
        if (res.status === 'success') {
          const url = `/merchant/center/order/settle.htm?bizProducts=${encodeURIComponent(
              bizProducts
          )}&storeStatus=true&useRedPacket=true`;
          window.parent.location.href = url;
          submitDisable.value = false;
        } else {
          ElMessage.error(res.errorMsg)
          submitDisable.value = false;
        }
      })
    }
    /**组合购副品数量 */
    const inputChange = (e,flag,item) => {
      let num = e.target.value;
      item.groupPurchaseInfo.subProducts[0].actPt.count = num;
    }
    /**修改组合购买数量 */
    const inputCart = (e,flag,item) => {
      let num = parseInt(e.target.value);
      num = isNaN(num) || num < 0 ? 0 : num;
      if(flag === 1){
          // item.groupPurchaseInfo.mainProduct.actPt.count = num;
          num =  item.groupPurchaseInfo.mainProduct.actPt.skuStartNum > num ? item.groupPurchaseInfo.mainProduct.actPt.skuStartNum : num;
      }else{
          // item.groupPurchaseInfo.subProducts[0].actPt.count = num;
          num =  item.groupPurchaseInfo.subProducts[0].actPt.skuStartNum > num ? item.groupPurchaseInfo.subProducts[0].actPt.skuStartNum : num;
          
      }
      loading.value = true
      changeCart({
        skuId: flag === 1 ? item.groupPurchaseInfo.mainProduct.id : item.groupPurchaseInfo.subProducts[0].id,
        quantity: num,
        merchantId: proxy.$merchantId,//'1500125099'
      }).then((res) => {
        loading.value = false;
        if (res.status === 'success') {
          if(res.data.qty || res.data.qty === 0){
            if(flag === 1){
              item.groupPurchaseInfo.mainProduct.actPt.count = res.data.qty === 0 ? item.groupPurchaseInfo.mainProduct.actPt.skuStartNum : res.data.qty;
              if(res.data.qty === 0){
                  item.groupPurchaseInfo.mainProduct.actPt.selectStatus = 0;
              }else{
                  item.groupPurchaseInfo.mainProduct.actPt.selectStatus = 1;
              }
            }else{
              item.groupPurchaseInfo.subProducts[0].actPt.count = res.data.qty === 0 ? item.groupPurchaseInfo.subProducts[0].actPt.skuStartNum : res.data.qty;
              if(res.data.qty === 0){
                  item.groupPurchaseInfo.subProducts[0].actPt.selectStatus = 0;
              }else{
                  item.groupPurchaseInfo.subProducts[0].actPt.selectStatus = 1;
              }
            }
          }
          if(res.data.message || res.data.actPurchaseTip){
            ElMessage.error(res.data.message ? res.data.message : res.data.actPurchaseTip)
            if(!res.data.qty && res.data.qty != 0){
              if(flag === 1){
                  item.groupPurchaseInfo.mainProduct.actPt.count = item.groupPurchaseInfo.mainProduct.actPt.skuStartNum;
              }else{
                  item.groupPurchaseInfo.subProducts[0].actPt.count = item.groupPurchaseInfo.subProducts[0].actPt.skuStartNum;
              }
            }
          }
          changeCombinationPrice(item);
        } else {
          if(flag === 1){
              item.groupPurchaseInfo.mainProduct.actPt.count = item.groupPurchaseInfo.mainProduct.actPt.skuStartNum; 
              // item.groupPurchaseInfo.mainProduct.actPt.selectStatus = 0; 
              item.groupPurchaseInfo.mainProduct.actPt.errMsg = res.errorMsg;  
          }else{
              item.groupPurchaseInfo.subProducts[0].actPt.count = item.groupPurchaseInfo.subProducts[0].actPt.skuStartNum; 
              // item.groupPurchaseInfo.subProducts[0].actPt.selectStatus = 0; 
              item.groupPurchaseInfo.subProducts[0].actPt.errMsg = res.errorMsg; 
          }
          changeCombinationPrice(item);
          ElMessage.error(res.errorMsg)
        }
      })
    }
    /**计算组合购优惠价格 */
    const changeCombinationPrice = (item,flag) => {
        let param = {
            merchantId:proxy.$merchantId,//'1500125099'
            combinedList:JSON.stringify([
                {
                    skuId:item.groupPurchaseInfo.mainProduct.id,
                    qty:item.groupPurchaseInfo.mainProduct.actPt.count === 0 ? item.groupPurchaseInfo.mainProduct.actPt.skuStartNum : item.groupPurchaseInfo.mainProduct.actPt.count, 
                    isMainProduct:1,
                    selectStatus:item.groupPurchaseInfo.mainProduct.actPt.selectStatus,
                },
                {
                    skuId:item.groupPurchaseInfo.subProducts[0].id,
                    qty:item.groupPurchaseInfo.subProducts[0].actPt.count === 0 ? item.groupPurchaseInfo.subProducts[0].actPt.skuStartNum : item.groupPurchaseInfo.subProducts[0].actPt.count, 
                    isMainProduct:0,
                    selectStatus:item.groupPurchaseInfo.subProducts[0].actPt.selectStatus,
                },
            ])
        }
        loading.value = true;
        combinedInfoQuery(param).then((res) => {
            if (res.status === 'success') {
                combinedDiscountSub.value = res.data.combinedDiscountSub;
                realPay.value = res.data.realPay;
                res.data.combinedList.forEach((it) => {
                    if (it.isMainProduct) {
                      item.groupPurchaseInfo.mainProduct.actPt.amount = it.costPrice ? it.costPrice : it.price;
                      item.groupPurchaseInfo.mainProduct.actPt.selectStatus = it.selectStatus;
                      item.groupPurchaseInfo.mainProduct.actPt.count = it.qty;
                      if(!it.selectStatus){
                          item.groupPurchaseInfo.mainProduct.actPt.count = 0;
                          if(res.data.errMsg){
                              item.groupPurchaseInfo.mainProduct.actPt.errMsg = res.data.errMsg;
                          }
                          showFlushed.value = false;
                      }else{
                          showFlushed.value = true;
                      }
                    } else {
                      discount.value = it.discount;
                      item.groupPurchaseInfo.subProducts[0].actPt.amount = it.costPrice ? it.costPrice : it.price
                      item.groupPurchaseInfo.subProducts[0].actPt.selectStatus = it.selectStatus;
                      item.groupPurchaseInfo.subProducts[0].actPt.count = it.qty;
                      if(!it.selectStatus){
                          item.groupPurchaseInfo.subProducts[0].actPt.count = 0
                          if(res.data.errMsg){
                              item.groupPurchaseInfo.subProducts[0].actPt.errMsg = res.data.errMsg;
                          }
                      }
                    }
                });
                nextTick(() => {
                  if(res.data.errMsg && flag != 1){
                      ElMessage.error(res.data.errMsg)
                    }
                    loading.value = false;
                })
            }else {
                ElMessage.error(res.errorMsg)
                item.groupPurchaseInfo.mainProduct.actPt.count = 0;
                item.groupPurchaseInfo.subProducts[0].actPt.count = 0;
                item.groupPurchaseInfo.mainProduct.actPt.amount = item.groupPurchaseInfo.mainProduct.limitFullDiscount ? item.groupPurchaseInfo.mainProduct.limitFullDiscount : item.groupPurchaseInfo.mainProduct.actPt.assemblePrice ;
                item.groupPurchaseInfo.subProducts[0].actPt.amount = item.groupPurchaseInfo.subProducts[0].limitFullDiscount ? item.groupPurchaseInfo.subProducts[0].limitFullDiscount : item.groupPurchaseInfo.subProducts[0].actPt.assemblePrice ;
                nextTick(() => {
                    loading.value = false;
                })
            }
        })
    };
    /**组合购修改数量 */
    const addProductCart = (type,flag,item) => {
      loading.value = true;
      let num = 0;
      if (type === "add") {
          if(flag === 1){
              num =  ((item.groupPurchaseInfo.mainProduct.actPt.count + item.groupPurchaseInfo.mainProduct.mediumPackageNum)) < item.groupPurchaseInfo.mainProduct.actPt.skuStartNum ? item.groupPurchaseInfo.mainProduct.actPt.skuStartNum : (item.groupPurchaseInfo.mainProduct.actPt.count +  item.groupPurchaseInfo.mainProduct.mediumPackageNum);
          }else{
              num =  ((item.groupPurchaseInfo.subProducts[0].actPt.count + item.groupPurchaseInfo.subProducts[0].mediumPackageNum)) < item.groupPurchaseInfo.subProducts[0].actPt.skuStartNum ? item.groupPurchaseInfo.subProducts[0].actPt.skuStartNum : (item.groupPurchaseInfo.subProducts[0].actPt.count + item.groupPurchaseInfo.subProducts[0].mediumPackageNum);
          }
      } else if (type === "min") {
          if(flag === 1){
              if(item.groupPurchaseInfo.mainProduct.actPt.count  <= 0){
                  ElMessage.error('购买数量不能小于0！');
                  loading.value = false;
                  return;
              }
              if (item.groupPurchaseInfo.mainProduct.actPt.count > 0) {
                  if(item.groupPurchaseInfo.mainProduct.actPt.count <= item.groupPurchaseInfo.mainProduct.actPt.skuStartNum) {
                      ElMessage.error('购买数量不能小于起拼数量！');
                      loading.value = false;
                      return;
                  }
                  if(item.groupPurchaseInfo.mainProduct.isSplit == 1){
                    num = item.groupPurchaseInfo.mainProduct.actPt.count - 1;
                  }else{
                    num = item.groupPurchaseInfo.mainProduct.actPt.count - item.groupPurchaseInfo.mainProduct.mediumPackageNum
                  }
              }
          }else{
              if(item.groupPurchaseInfo.subProducts[0].actPt.count  <= 0){
                  ElMessage.error('购买数量不能小于0！');
                  loading.value = false;
                  return;
              }
              if (item.groupPurchaseInfo.subProducts[0].actPt.count > 0) {
                  if(item.groupPurchaseInfo.subProducts[0].actPt.count <= item.groupPurchaseInfo.subProducts[0].actPt.skuStartNum) {
                      ElMessage.error('购买数量不能小于起拼数量！');
                      loading.value = false;
                      return;
                  }
                  if(item.groupPurchaseInfo.mainProduct.isSplit == 1){
                    num = item.groupPurchaseInfo.subProducts[0].actPt.count - 1;
                  }else{
                    num = item.groupPurchaseInfo.subProducts[0].actPt.count - item.groupPurchaseInfo.subProducts[0].mediumPackageNum;
                  }
              }
          }
      }
      changeCart({
        skuId: flag === 1 ? item.groupPurchaseInfo.mainProduct.id : item.groupPurchaseInfo.subProducts[0].id,
        quantity: num,
        merchantId: proxy.$merchantId,//'1500125099'
      }).then((res) => {
        loading.value = false;
        if (res.status === 'success') {
          if(res.data.qty || res.data.qty === 0){
            if(flag === 1){
              item.groupPurchaseInfo.mainProduct.actPt.count = res.data.qty === 0 ? item.groupPurchaseInfo.mainProduct.actPt.skuStartNum : res.data.qty;
              if(res.data.qty === 0){
                  item.groupPurchaseInfo.mainProduct.actPt.selectStatus = 0;
              }else{
                  item.groupPurchaseInfo.mainProduct.actPt.selectStatus = 1;
              }
            }else{
              item.groupPurchaseInfo.subProducts[0].actPt.count = res.data.qty === 0 ? item.groupPurchaseInfo.subProducts[0].actPt.skuStartNum : res.data.qty;
              if(res.data.qty === 0){
                  item.groupPurchaseInfo.subProducts[0].actPt.selectStatus = 0;
              }else{
                  item.groupPurchaseInfo.subProducts[0].actPt.selectStatus = 1;
              }
            }
          }
          if(res.data.message || res.data.actPurchaseTip){
            ElMessage.error(res.data.message ? res.data.message : res.data.actPurchaseTip)
          }
          changeCombinationPrice(item);
        } else {
          if(flag === 1){
            // item.groupPurchaseInfo.mainProduct.actPt.selectStatus = 0; 
            item.groupPurchaseInfo.mainProduct.actPt.errMsg = res.errorMsg; 
          }else{
            // item.groupPurchaseInfo.subProducts[0].actPt.selectStatus = 0; 
            item.groupPurchaseInfo.subProducts[0].actPt.errMsg = res.errorMsg; 
          }
          ElMessage.error(res.errorMsg)
        }
      })
    }
    /**组合购 */
    const changeProduct = (item)=>{
      if (
        item.groupPurchaseInfo &&
        item.groupPurchaseInfo.subProducts &&
        item.groupPurchaseInfo.subProducts[0] &&
        item.groupPurchaseInfo.subProducts[0] &&
        item.groupPurchaseInfo.subProducts[0].actPt
      ) {
        item.groupPurchaseInfo.subProducts[0].actPt.count =
          item.groupPurchaseInfo.subProducts[0].actPt.skuStartNum;
      }
      const firstProduct = item.groupPurchaseInfo.subProducts.shift();
       item.groupPurchaseInfo.subProducts.push(firstProduct);
       loading.value = true;
      changeCart({
        skuId:  item.groupPurchaseInfo.subProducts[0].id,
        quantity: item.groupPurchaseInfo.subProducts[0].actPt.count,
        merchantId:proxy.$merchantId,//'1500125099'
      }).then((res) => {
        loading.value = false;
        if (res.status === 'success') {
          if(res.data.qty || res.data.qty === 0){
              item.groupPurchaseInfo.subProducts[0].actPt.count = res.data.qty === 0 ? item.groupPurchaseInfo.subProducts[0].actPt.skuStartNum : res.data.qty;
              if(res.data.qty === 0){
                  item.groupPurchaseInfo.subProducts[0].actPt.selectStatus = 0;
              }
            }
          if(res.data.message || res.data.actPurchaseTip){
            ElMessage.error(res.data.message ? res.data.message : res.data.actPurchaseTip)
          }
          changeCombinationPrice(item);
        } else {
          // item.groupPurchaseInfo.subProducts[0].actPt.selectStatus = 0; 
          item.groupPurchaseInfo.subProducts[0].actPt.errMsg = res.errorMsg; 
          ElMessage.error(res.errorMsg)
        }
      })
    }
    const go = (e, operationInfo) => {
      e.stopPropagation();
      actionTracking('pc_search_Active_Click', {
        active_url: operationInfo.jumpUrl,
        active_title: operationInfo.title,
        active_type: operationInfo.showType,
      })
      window.open(operationInfo.jumpUrl);
    }
    const fixedtwo = (val) => {
      if (val === 0) return "0.00";
      if (!val) return "";
      return parseFloat(val).toFixed(2);
    }
  
    function parseRGBA(val) {
      val = val.trim().toLowerCase();  //去掉前后空格
      if (val.length > 8) {
        let color = {};
        try {
          let argb = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(val);
          color.r = parseInt(argb[2], 16);
          color.g = parseInt(argb[3], 16);
          color.b = parseInt(argb[4], 16);
          color.a = parseInt(argb[1], 16) / 255;
        } catch (e) {
          console.log(e)
        }
        return 'rgba(' + color.r + ',' + color.g + ',' + color.b + ',' + parseFloat(color.a).toFixed(1) + ')';
      } else {
        return val;
      }
    }
  let forceRenderKey=ref(0);
  defineExpose({forceRenderKey})
  watch(
      () => props.groupPurchase.mainProduct,
      (new_val) => {
        if (new_val) {
          try{
            if(props.groupPurchase.index || props.groupPurchase.index === 0){
              changeCombinationPrice(datalist.value[props.groupPurchase.index])
            }
          }catch(e){
            console.log(e)
          }
        }
      },
      {
        immediate: false,
        deep: true,
      },
    )
    watch(
      () => props.goodsData,
      (new_val) => {
        if (new_val) {
          addDiscount(new_val);
        }
      },
      {
        immediate: false,
        deep: true,
      },
    )
    watch(
      () => props,
      (new_val) => {
        if (new_val) {
          console.log("props", props.pageNum)
          jgAgentInfo = ref({
            sptype:props.trackInfo.sptype, 
            jgspid:jgspid, 
            sid:props.trackInfo.sid, 
            result_cnt:props.total,
            page_no:props.pageNum,
            page_size:20,
            total_page:props.pages,
            // rank:index, // item 相关
            search_sort_strategy_id:props.searchSortStrategyId || null,
            key_word: props.keyword,
            // operation_rank:index + 1,
          });
        }
      },
      {
        immediate: false,
        deep: true,
      },
    )
    const collection = (item, index) => {
      const url =
        item.favoriteStatus == 2
          ? "/merchant/center/collection/attention.json"
          : "/merchant/center/collection/cancelAttention.json";
      putRequest("get", url, { skuId: item.id })
        .then((res) => {
          if (res.status == "success") {
            if (item.favoriteStatus == 2) {
              item.favoriteStatus = 1;
              datalist.value.splice(index, 1, item);
            } else {
              item.favoriteStatus = 2;
              datalist.value.splice(index, 1, item);
            }
          } else {
            alert(res.errorMsg);
          }
        })
        .catch((err) => {
          console.log('收藏错误', err);
        });
    }


    const mouseOver = (id) => {
      showCarBtnIndex.value = id;
    }
    const mouseLeave = (id) => {
      showCarBtnIndex.value = null;
    }
    // 添加折后价
    const addDiscount = (data) => { 
      if (data && data.length > 0) {
        const idList = data.map(item => { return item.id });
        const params = {
          skuIds: idList.join(','),
          merchantId,
        }
        handleDiscount(params)
          .then((res) => {
            if (res.status == "success") {
              const priceArr = res.data;
              priceArr.map((item, index) => {
                 data.map((item2,index2) => {
                //   if (item.skuId == item2.id) {
                //     // this.$set(item2, 'zheHouPrice', item.price)
                //     const zheHouPrice = Number(item.price.split('￥')[1]);
                //     if (!isNaN(zheHouPrice) && zheHouPrice < item2.fob) {
                //       item2.zheHouPrice = item.price;
                //     }
                //   }
                if(item2.cardType==1){
                  //普通
                  
                    if (item.skuId == item2.productInfo.id) {
                      // this.$set(item2, 'zheHouPrice', item.price)
                      const zheHouPrice = Number(item.price.split('￥')[1]);
                      if (!isNaN(zheHouPrice)) {
                        item2.productInfo.zheHouPrice = item.price;
                      }
                    }
             
                }else{
                  //运营
                
                    if (item.skuId == item2.operationInfo.products[0].id) {
                      // this.$set(item2, 'zheHouPrice', item.price)
                      const zheHouPrice = Number(item.price.split('￥')[1]);
                      if (!isNaN(zheHouPrice) && zheHouPrice < item2.operationInfo.products[0].fob) {
                        item2.operationInfo.products[0].zheHouPrice = item.price;
                      }
                    }
                
                }
                })
               
              })
              
            }
          })
          .catch((err) => {
            console.log(err, "请求失败了");
          });
      }
      datalist.value = data;
    }
    const toStore = (url, operationInfo) => {
      // const a = document.createElement("a"); // 创建a标签
      // a.href = url;
      // a.click();
      if (operationInfo) {
        actionTracking('pc_search_Store_Click', {
          shop_name: operationInfo.products[0].shopName,
          shop_code: operationInfo.products[0].shopCode,
        })
      }
      window.open(url);

    }
    const toDetail = (id, item, index, type) => {
      console.log(item, "itemm")
      // type == 1 普通品  type == 2 运营位  type == 3 组合购
      if (type == 1) {
        actionTracking('pc_page_CommodityDetails', {
          commodityId: id,
          spid: props.trackInfo.spid,
          sid: props.trackInfo.sid,
          sptype: props.trackInfo.sptype,
          user_id: merchantId,
          search_sort_strategy_id: props.searchSortStrategyId || null
        })
        exposureAnalysysAgentPruductClick(1, item, index) ; // 上报    
      } else if (type == 2) {
        actionTracking('pc_action_Product_Click', {
          commodityId: id,
          commodityName: item.products[0].commonName,
          commodityCode: item.products[0].barcode,
          commodityCategory: item.products[0].categoryId,
          spid: props.trackInfo.spid,
          sid: props.trackInfo.sid,
          sptype: props.trackInfo.sptype,
          real: 2,
          search_sort_strategy_id: props.searchSortStrategyId || null,
          active_type: 0,
          click_item: 3,
          goods_groupid: item.products[0].operationExhibitionId,
          active_id: item.products[0].operationId,
          active_index: index
        })
        // window.AnalysysAgent.track("pc_resource_click", {
        //   $url: window.location.href,
        //   $url_domain: window.location.origin,
        //   page_id: '',
        //   $title: document.title,
        //   resource_id: item.products[0].operationId,
        //   rank: index + 1,
        //   product_id: item.products[0].id,
        //   product_name: item.products[0].shopName,
        //   product_original_price: item.products[0].fob,
        //   product_present_price: item.products[0].retailPrice,
        //   product_label: item.showName,
        //   shop_id: item.products[0].orgId,
        //   shop_name: item.products[0].shopName,
        //   key_word: props.keyword,
        // });
        exposureAnalysysAgentPruductClick(2,item.products[0], index) ; // 上报
      }
      if(item.enterProductDetailIsShowRecPurchase){
        window.open(`${detailUrl}search/skuDetail/${id}.htm?combination=1&type=${item.enterProductDetailShowRecPurchaseType}`);
      }else{
        window.open(`${detailUrl}search/skuDetail/${id}.htm`);
      }
    }
    const changePrice = (id, price) => {
      datalist.value.forEach((item) => {
        if (item.id == id) {
          item.activePrice = price;
          // this.$set(item, 'activePrice', price);
        }
      })
    }
    const getPrice = (item) => {
      if (item.actPt) {
        if (item.actPt.stepPriceStatus == 1) {
          //阶梯\
          return item.actPt.minSkuPrice;
        } else if (item.actPt.stepPriceStatus == 2 && item.actPt.assembleStatus == 1) {
          return item.actPt.assemblePrice;
        }
        return ''
      } else if (item.actPgby) {
        return item.actPgby.assemblePrice;
      } else if (item.priceType == 2 && item.skuPriceRangeList) {
        return item.skuPriceRangeList[item.skuPriceRangeList.length -1].price;
      } else {
        return item.fob
      }
    }
  
    const getActivityStatus = (item) => {
      return item.assembleStatus === 0 ? 'notStart' : item.assembleStatus === 1 ? 'inProgress' : '';
    }

  var jgAgentInfo = ref({
    sptype:props.trackInfo.sptype, 
    jgspid:jgspid, 
    sid:props.trackInfo.sid, 
    result_cnt:props.total,
    page_no:props.pageNum,
    page_size:20,
    total_page:props.pages,
    // rank:index, // item 相关
    search_sort_strategy_id:props.searchSortStrategyId || null,
    key_word: props.keyword,
    // operation_rank:index + 1,
  });

  // 各个页面商品卡片可请求跳转位置点击（进店也算，但商品数量加减按钮未发送请求不算，修改数量未发送请求不算）上报，一品一报，每次点击上报一次。点击按钮时，有点击、按钮、加购埋点。
  const exposureAnalysysAgentPruductClick = (type, item, index)=>{
    console.log(props, "rpro")
    // type == 1 商品 type == 2 运营位
    if(type == 1) {
      try {
        window.AnalysysAgent.track("action_list_product_click", {
          sptype:props.trackInfo.sptype, 
          jgspid:jgspid, 
          sid:props.trackInfo.sid, 
          result_cnt:props.total,
          page_no:props.pageNum,
          page_size:20,
          total_page:props.pages,
          rank: (props.pageNum - 1) * 20 + index + 1,
          key_word:props.keyword,
          search_sort_strategy_id:props.searchSortStrategyId && props.searchSortStrategyId != 'null' ? `${props.searchSortStrategyId}` : null,
          operation_id: item.operationId && item.operationId != 'null' ? `${item.operationId}` : null, 
          operation_rank: item.operationId && item.operationId != 'null' ? 1 : null,
          list_position_type:`${item.positionType}`,
          list_position_typename:`${item.positionTypeName}`,
          product_id: item.id,
          product_name:`${item.productName}`,
          product_first:`${item.categoryFirstId}`,
          product_present_price:getPrice(item),
          product_type:`${item.productType}`,
          product_shop_code:item.shopCode,
          product_shop_name:item.shopName,
        });
      } catch (error) {
      }
    }else {
        try {
          window.AnalysysAgent.track("action_list_product_click", {
            sptype:props.trackInfo.sptype, 
            jgspid:jgspid, 
            sid:props.trackInfo.sid, 
            result_cnt:props.total,
            page_no:props.pageNum,
            page_size:20,
            total_page:props.pages,
            rank: (props.pageNum - 1) * 20 + index + 1,
            key_word:props.keyword,
            search_sort_strategy_id:props.searchSortStrategyId && props.searchSortStrategyId != 'null' ? `${props.searchSortStrategyId}` : null,
            operation_id: item.operationId && item.operationId != 'null' ? `${item.operationId}` : null, 
            operation_rank: item.operationId && item.operationId != 'null' ? 1 : null,
            list_position_type:`${item.positionType}`,
            list_position_typename:item.positionTypeName,
            product_id: item.id,
            product_name:item.productName,
            product_first:`${item.categoryFirstId}`,
            product_present_price:getPrice(item),
            product_type:`${item.productType}`,
            product_shop_code:item.shopCode,
            product_shop_name:item.shopName,
          });
        } catch (error) {
        }
    }
    sessionStorage.setItem("jgInfo", JSON.stringify({
      sptype:props.trackInfo.sptype, 
      jgspid:jgspid, 
      sid:props.trackInfo.sid, 
      result_cnt:props.total,
      page_no:props.pageNum,
      key_word: props.keyword,
      page_size:20,
      total_page:props.pages,
      rank: (props.pageNum - 1) * 20 + index + 1,
      search_sort_strategy_id:props.searchSortStrategyId && props.searchSortStrategyId != 'null' ? `${props.searchSortStrategyId}` : null,
      operation_id: item.operationId && item.operationId != 'null' ? `${item.operationId}` : null, 
      operation_rank: item.operationId && item.operationId != 'null' ? 1 : null,
      list_position_type:`${item.positionType}`,
      list_position_typename:item.positionTypeName,
      product_id:item.id,
      product_name:item.productName,
      product_first:`${item.categoryFirstId}`,
      product_present_price:getPrice(item),
      product_type:`${item.productType}`,
      product_shop_code:item.shopCode,
      product_shop_name:item.shopName,
    }))
  }
  //PC商品详情页-热销商品 曝光
  const exposureView=(el,data)=>{
    // console.log(qtData.commonName,"lwq")
    if(props.qtData.commonName=="BigSearch"&&window.parent.getSpmE){
      let qtSkuData=JSON.parse(data.qt_sku_data)
      let params={
        "spm_cnt":`1_4.searchResult_0-0_0.${ props.qtData.goodsType==3?'recommendList':'searchList'}@5.prod@${qtSkuData.rank}.${window.parent.getSpmE()}`,
        "scm_cnt":`${ props.qtData.goodsType==3?'recommend':'search'}.${props.searchSortStrategyId||'0'}.all_0.prod-${data.id}.${props.scmE}`,
        "product_id": data.id,
        "product_name":data.name,
        "qt_list_data": props.qtData.qtListData,
        'qt_sku_data': data.qt_sku_data

      }
        aplus_queue.push({
              'action': 'aplus.record',
              'arguments': ['page_list_product_exposure', 'EXP',params]
      });
    }
  }  
  //运营位商品曝光
  const exposureViewOperation=(el,data)=>{
    if(props.qtData.commonName=="BigSearch"&&window.parent.getSpmE){
      console.log(data,"lwq")
      let qtSkuData=JSON.parse(data.qt_sku_data)
      let params={
          "spm_cnt":`<EMAIL>@${qtSkuData.rank}_prod@${qtSkuData.rank}-${qtSkuData.operation_rank}.${window.parent.getSpmE()}`,
          "scm_cnt":`search.${props.searchSortStrategyId||'0'}.${data.product.operationCustomerGroupId||'all'		}_${getStr(data.product.operationExhibitionId)}.prod-${data.id}.${props.scmE}`,
          "product_id": data.id,
          "product_name":data.name,
          "qt_list_data": props.qtData.qtListData,
          'qt_sku_data': data.qt_sku_data

        }
        console.log(params,"params")
        if(props.qtData.commonName=="BigSearch"&&window.parent.getSpmE){
            aplus_queue.push({
                  'action': 'aplus.record',
                  'arguments': ['page_list_product_exposure', 'EXP',params]
          });
        }
    }
   
       
  }
  //组合购曝光
  const exposureViewzhg=(el,data)=>{
    if(props.qtData.commonName=="BigSearch" && window.parent.getSpmE ){ // 
      // console.log(data.product,"yz")
      let qtSkuData=JSON.parse(data.product.qtSkuData)
      let params={
        "spm_cnt":`<EMAIL>@${qtSkuData.rank}_prod@${qtSkuData.rank}-${qtSkuData.sub_rank}.${window.parent.getSpmE()}`,//
        "scm_cnt":`search.${props.searchSortStrategyId||'0'}.all_0.prod-${data.product.id}.${props.scmE}`,
        "product_id": data.product.id,
        "product_name":data.product.showName,
        "qt_list_data": props.qtData.qtListData,
        'qt_sku_data': data.product.qtSkuData

      }
      console.log(params)
        aplus_queue.push({
              'action': 'aplus.record',
              'arguments': ['page_list_product_exposure', 'EXP',params]
      });
    }
  }
  //组合购商品点击
  const zhgClick=(data,scmE)=>{
    if(props.qtData.commonName=="BigSearch"&& window.parent.getSpmE  ){ 
      // console.log(data.product,"yz")
      let tyScmE=scmE||props.scmE+proxy.scmEActive(6)
      let qtSkuData=JSON.parse(data.product.qtSkuData)
      let params={
        "spm_cnt":`<EMAIL>@${qtSkuData.rank}_prod@${qtSkuData.rank}-${qtSkuData.sub_rank}.${window.parent.getSpmE()}`,//
        "scm_cnt":`search.${props.searchSortStrategyId||'0'}.all_0.prod-${data.product.id}.${tyScmE}`,
        "product_id": data.product.id,
        "product_name":data.product.showName,
        "qt_list_data": props.qtData.qtListData,
        'qt_sku_data': data.product.qtSkuData

      }
      console.log(params)
        aplus_queue.push({
              'action': 'aplus.record',
              'arguments': ['action_list_product_click', 'CLK',params]
      });
    }
  }
  //组合购商品按钮点击
  const zhgbtnClick=(data)=>{
    if(props.qtData.commonName=="BigSearch" && window.parent.getSpmE  ){ //&& window.parent.getSpmE
      // console.log(data.product,"yz")
      let tyScmE=props.scmE+proxy.scmEActive(6)
      let qtSkuData=JSON.parse(data.product.qtSkuData)
      let params={
        "spm_cnt":`<EMAIL>@${qtSkuData.rank}_prod@${qtSkuData.rank}-${qtSkuData.sub_rank}_btn@${data.index}.${window.parent.getSpmE()}`,//
        "scm_cnt":`search.${props.searchSortStrategyId||'0'}.all_0.prod-${data.product.id}_text-${data.text}.${tyScmE}`,
        "product_id": data.product.id,
        "product_name":data.product.showName,
        "qt_list_data": props.qtData.qtListData,
        'qt_sku_data': data.product.qtSkuData

      }
      zhgClick(data,tyScmE)
      console.log(params)
        aplus_queue.push({
              'action': 'aplus.record',
              'arguments': ['action_product_button_click', 'CLK',params]
      });
    }
  }
  //组合购模块点击
  const moduleClickZhg=(data)=>{
    if(props.qtData.commonName=="BigSearch" && window.parent.getSpmE ){ //
      let qtSkuData=JSON.parse(data.product.mainProduct.qtSkuData)
      let qt = {
        list_position_type_name: qtSkuData.list_position_type_name,
        main_product_id: qtSkuData.main_product_id,
        main_product_name: qtSkuData.main_product_name,
        list_position_type:qtSkuData.list_position_type,
        scm_id: props.scmE,
        sub_product_id_groups: [String(data.product.subProducts[0].id)],
        sub_product_name_groups: [data.product.subProducts[0].showName],
        sub_product_sub_rank_groups: [String(data.product.subProducts[0].subRank)],
        sub_product_num: data.product.subProducts[0].actPt.count > 0 ? 1 : 0,
      }
      let params={
        'spm_cnt':`<EMAIL>@${qtSkuData.rank}_btn@1.${window.parent.getSpmE()}`,//
        'scm_cnt':`search.${props.searchSortStrategyId||'0'}.all_0.prod-${data.product.mainProduct.id}_text-组合购.${proxy.scmEActive(14)}`,
        'qt_list_data':props.qtData.qtListData,
        'qt_sku_data': JSON.stringify(qt)
      }
      aplus_queue.push({
              'action': 'aplus.record',
              'arguments': ['action_sub_module_click', 'CLK',params]
      });
    }
  }
  const getStr=function(str){
        if(str){
            return str.replace(/[._\-~|@]/g, "")
        }else{
            return 0
        }
  
    }
  const getActId=(url)=>{
    const match = url.match(/\/(\d+)\.htm$/);
    return match ? match[1] : 0;
  }
    //商品点击
  const shopClick=(data)=>{
    try{
        if(props.qtData.commonName=="BigSearch"&&window.parent.getSpmE){
        //运营位点击
        if(data.isOperation){
          let tyScmE=props.scmE+proxy.scmEActive(6)
          let qtSkuData=JSON.parse(data.qt_sku_data)
          aplus_queue.push({
                'action': 'aplus.record',
                'arguments': ['action_list_product_click', 'CLK',{
                  "spm_cnt":`<EMAIL>@${qtSkuData.rank}_prod@${qtSkuData.rank}-${qtSkuData.operation_rank}.${window.parent.getSpmE()}`,
                  "scm_cnt":`search.${props.searchSortStrategyId||'0'}.${data.product.operationCustomerGroupId||'all'		}_${getStr(data.product.operationExhibitionId)}.prod-${data.id}.${tyScmE}`,
                  "product_id": data.id,
                  "product_name":data.name,
                  "qt_list_data": props.qtData.qtListData,
                  'qt_sku_data': data.qt_sku_data
                }]
          });
          //运营位活动/进店
          if(data.isGo||data.isShop){
              aplus_queue.push({
                  'action': 'aplus.record',
                  'arguments': ['action_product_button_click', 'EXP',{
                    "spm_cnt":`<EMAIL>@${qtSkuData.rank}_prod@${qtSkuData.rank}-${qtSkuData.operation_rank}_btn@${data.isShop?7:6}.${window.parent.getSpmE()}`,
                    "scm_cnt":`search.${props.searchSortStrategyId||'0'}.${data.product.operationCustomerGroupId||'all'}_${getStr(data.product.operationExhibitionId)}.prod-${data.id}_text-${getStr(data.isShop?'进店':data.operationInfo.title)}_${data.isShop?'shop':'h5'}-${data.isShop?data.shopCode:data.operationInfo.activityPageId||0}.${tyScmE}`,
                    "product_id": data.id,
                    "product_name":data.name,
                    "qt_list_data": props.qtData.qtListData,
                    'qt_sku_data': data.qt_sku_data
                  }]
            });
          }
        }else{
          let tyScmE=props.scmE+proxy.scmEActive(6)
          let qtSkuData=JSON.parse(data.qt_sku_data)
          aplus_queue.push({
                'action': 'aplus.record',
                'arguments': ['action_list_product_click', 'CLK', {
                  "spm_cnt": `1_4.searchResult_0-0_0.${ props.qtData.goodsType==3?'recommendList':'searchList'}@5.prod@${qtSkuData.rank}.${window.parent.getSpmE()}`,
                  "scm_cnt": `${ props.qtData.goodsType==3?'recommend':'search'}.${props.searchSortStrategyId||'0'}.all_0.prod-${data.id}.${tyScmE}`,
                  "product_id": data.id,
                  "product_name": data.name,
                  "qt_list_data": props.qtData.qtListData,
                  'qt_sku_data':data.qt_sku_data
              }]
          });
          if(data.isShop){
            //触发店铺点击
            function getParamValueUrl(paramName, url) {
              // const url = window.location.href;
              const queryParams = url.slice(url.indexOf('?') + 1).split('&');
              for (let i = 0; i < queryParams.length; i++) {
                const param = queryParams[i].split('=');
                if (param[0] === paramName) {
                  return param[1] ? decodeURIComponent(param[1]) : null;
                }
              }
              return null;
            }
            aplus_queue.push({
                  'action': 'aplus.record',
                  'arguments': ['action_product_button_click', 'CLK', {
                    "spm_cnt": `1_4.searchResult_0-0_0.${ props.qtData.goodsType==3?'recommendList':'searchList'}@5.prod@${qtSkuData.rank}_btn@7.${window.parent.getSpmE()}`,
                    "scm_cnt": `${ props.qtData.goodsType==3?'recommend':'search'}.${props.searchSortStrategyId||'0'}.all_0.prod-${data.id}_text-进店_shop-${data.shopCode}.${tyScmE}`,
                    "product_id": data.id,
                    "product_name": data.name,
                    "qt_list_data": props.qtData.qtListData,
                    'qt_sku_data':data.qt_sku_data
                }]
            });
          }
        }
      
      }
    }catch(e){

    }
    
  } 
  </script>
    
  <style lang="scss" scoped>
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(44, 43, 43, 0);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 18px;
    color: #161616;
    z-index: 999;
  }
  .combination-bottom{
    height: 65px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-right: 17px;
    .combinationCenter{
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      height: 42px;
      justify-content: space-around;
      .combinationCenterTop{
        .price-two {
            p {
              color: #FF2121;
            }
            letter-spacing: 0;
            span {
              font-weight: bold;
            }
            .priceDec {
              font-size: 14px;
            }
            .priceInt {
              font-size: 22px;
            }
            .priceFloat {
              font-size: 18px;
            }
          }
      }
      .combinationCenterBottom{
        .item{
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          letter-spacing: 0;
          line-height: 14px;
        }
      }

    }
    .combinationBuy {
      z-index: 9;
      cursor: pointer;
      width: 160px;
      height: 42px;
      margin-left: 10px;
      background: #00B955;
      border-radius: 2px;
      font-size: 16px;
      font-family: MicrosoftYaHei;
      text-align: center;
      color: #ffffff;
      line-height: 42px;
    }
  }
  .content {
    margin: 5px 0;
    display: flex;
    align-items: center;
    .spread-add {
      display: flex;
      display: -webkit-flex;
      flex-wrap: nowrap;
      -webkit-flex-wrap: nowrap;
      box-sizing: border-box;
      z-index: 9;
      border-radius: 6px;
      border: 1px solid #d7d7d7;
      overflow: hidden;
      .plus,
      .reduce {
        width: 26px;
        height: 30px;
        line-height: 30px;
        color: #757575;
        text-align: center;
        background: #EFEFEF;
        cursor: pointer;
        font-size: 20px;
      }
      .plus {
        border-left: 1px solid #d7d7d7;
      }
      .reduce {
        border-right: 1px solid #d7d7d7;
      }
      .cont {
        width: 44px;
        .input-val {
          box-sizing: border-box;
          padding: 0;
          width: 100%;
          text-align: center;
          color: #292933;
          border: none;
          outline: none;
          font-size: 16px;
          font-family: MicrosoftYaHei;
          text-align: left;
          color: #333333;
          line-height: 30px;
          text-align: center;
        }
      }
}
  }
  .templist {
    display: flex;
    display: -webkit-flex;
    gap: 5px;
    flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    margin-top: 10px;
    .templist-combination{
      background: #fff;
      position: relative;
      // overflow: hidden;
      width: 477px;
      box-sizing: border-box;
      // padding: 20px 17px 0 17px;
      height: 396px;
      border: 1px solid #f1f1f1;
      .combination{
        display: flex;
        height: 330px;
        border-bottom: 1px solid #f1f1f1;
      }
      .middle{
        height: 200px;
        user-select: none;
        img{
          width: 24px;
          height: 24px;
          margin-top: 83.5px;
        }
      }
      .templist-item-combination {
        background: #fff;
        position: relative;
        // overflow: hidden;
        width: 236px;
        box-sizing: border-box;
        // padding: 20px 17px 0 17px;
        padding: 0 10px ; 
        height: 294px;
        a {
          display: block;
        }
        .images {
          // width: 146px;
          // height: 99px;
          width: 185px;
          height: 180px;
          box-sizing: content-box;
          margin: auto;
          padding-right: 17px;
          position: relative;
          .sold-out {
            position: absolute;
            left: 50%;
            top: 50%;
            margin: -30px 0 0 -30px;
            width: 60px;
            height: 60px;
            background-color: rgba(0, 0, 0, 0.4);
            text-align: center;
            font-size: 14px;
            color: #fff;
            border-radius: 50%;
            line-height: 60px;
          }
          .pic {
            // max-width: 146px;
            // max-height: 99px;
            max-width: 185px;
            max-height: 180px;
            display: block;
            margin: auto;
            object-fit:contain;
          }
          .activity-token {
            position: absolute;
            width: 185px;
            height: auto;
            max-width: 100%;
            margin-top: -6px;
            top: 2px;
            left: 0px;
            z-index: 3;
          }
          .activity-token-combination {
            position: absolute;
            height: 26px;
            font-size: 14px;
            background: #FF6204;
            border-radius: 4px 0 0 4px;
            // max-width: 100%;
            margin-top: -2px;
            top: 2px;
            right: -12px;
            z-index: 5;
            div{
              margin: 5px 5px 0 5px;
              text-align: center;
              color: #FFFFFF;
              height: 14px;
              letter-spacing: 0;
              line-height: 14px;
              font-weight: 500;
            }
          }
          .mark-text {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            z-index: 5;
            img {
              width: 100%;
              height: 100%;
            }
            h4 {
              position: absolute;
              left: 0;
              bottom: 0;
              font-size: rem(20);
              color: #fff;
              width: 100%;
              text-align: center;
            }
          }
          .promotionSkuPrice {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 5;
            span {
              position: absolute;
              right: 0;
              bottom: 0;
              font-size: 14px;
              color: #ff6201;
              display: flex;
              display: -webkit-flex;
              justify-content: center;
              -webkit-justify-content: center;
              align-items: center;
              -webkit-align-items: center;
              width: rem(140);
              height: rem(40);
              text-shadow: 1px 1px 1px #ffd9b4;
              font-weight: 600;
            }
          }
          .active-tags {
            position: absolute;
            left: 50%;
            top: 16px;
            z-index: 5;
            margin-left: -92.5px;
            img {
              width: 185px;
              height: auto;
            }
            .timeStr {
              left: 0;
              position: absolute;
              bottom: 47px;
              text-align: center;
              width: 174px;
              height: 10px;
              line-height: 10px;
              span {
                color: #ffffff;
                display: block;
                font-size: 12px;
                text-align: center;
                transform: scale(0.65);
              }
            }
            .temtejia806 {
              font-size: 12px;
              text-shadow: none;
              width: auto;
              font-weight: 400;
              text-align: center;
              color: #fff;
              position: absolute;
              bottom: 25px;
              left: 0;
              width: 174px;
              overflow: hidden;
              height: 18px;
              span {
                display: inline-block;
              }
            }
          }
        }
        .nearEffectBox {
          background: rgba(0, 0, 0, 0.6);
          font-size: 12px;
          color: #FFFFFF;
          height: 22px;
          line-height: 22px;
          position: relative;
          padding-left: 10px;
          .countTag {
            text-align: right;
            padding-right: 10px;
            position: absolute;
            top: 0;
            right: 0;
            height: 22px;
            width: 60px;
            background: url("../assets/images/search/tag.png") no-repeat;
            background-size: 100% 100%;
          }
        }
        .leftText {
          padding-left: 8px;
          text-align: left;
        }
        .info {
          padding: 4px 0 0 0;
          overflow: hidden;
          .price-wrap {
            display: flex;
            align-items: baseline;
            font-weight: Medium;
            .price-container {
              color: #FF2121;
              p{
                color: red;
                span {
                  font-weight: bold;
                }
                .priceDec {
                  font-size: 14px;
                }
                .priceInt {
                  font-size: 22px;
                }
                .priceFloat {
                  font-size: 18px;
                }
              };
            }
          }
          .commonName {
            height: 47px;
            font-size: 16px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: left;
            color: #333333;
            line-height: 22px;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            /* autoprefixer: off */
            -webkit-box-orient: vertical;
            /* autoprefixer: on */
            -webkit-line-clamp: 2;
          }
          .actBox {
            white-space: nowrap;
            margin: 5px 0;
            .tagItem {
              margin-right: 8px;
              padding: 5px 4px;
              font-size: 12px;
              border-radius: 4px;
            }
          }
          .tagBox {
            margin: 6px;
            margin-left: 0;
            white-space: nowrap;
            display: inline-block;
            .tagItem {
              margin-right: 8px;
              padding: 1px 4px;
              font-size: 12px;
              border-radius: 4px;
            }
            .tagItemBig {
              padding: 4px;
              display: inline-block;
            }
            .tejia {
              color: #fff;
              background-image: linear-gradient(270deg, #FF0000 1%, #F96401 100%);
              border: 1px solid #FF9AA3;
              border-radius: 4px;
              padding: 3px 5px;
            }
            .zhijiang {
              color: #fff;
              background-image: linear-gradient(270deg, #8001EE 1%, #6A01F9 100%);
              border: 1px solid #E9D3FF;
              border-radius: 4px;
              padding: 3px 5px;
            }
          }
          .manufacturer {
            background: none;
            font-size: 12px;
            color: #999999;
            // margin: 2px 0 8px;
          }
          .collageBox {
            display: flex;
            align-items: center;
            font-size: 14px;
            .skText {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              width: 200px;
              height: 24px;
              background: url('../assets/images/search/msTime.png') no-repeat;
              background-size: 100%;
              padding: 0 30px;
              color: #222222;
            }
            .couponBg {
              // width: 200px;
              display: inline-block;
              font-size: 12px;
              color: #F1081C;
              height: 24px;
              line-height: 24px;
              background: url('../assets/images/search/coupon.png') no-repeat;
              background-size: 100% 100%;
              padding: 0 4px;
              margin-right: 4px;
            }
            .couponBg-zp {
              display: inline-block;
            }
          }
          .price-wrap {
              display: flex;
              display: -webkit-flex;
              flex-wrap: nowrap;
              -webkit-flex-wrap: nowrap;
              flex-direction: column;
              overflow: hidden;
              .price-container {
                display: flex;
                display: -webkit-flex;
                flex-wrap: nowrap;
                -webkit-flex-wrap: nowrap;
                justify-content: space-between;
                .controlTitleText {
                  color: #f49926;
                  font-weight: 500;
                  font-size: 18px;
                }
                .qualifications {
                  font-size: 18px;
                  font-family: PingFangSC, PingFangSC-Medium;
                  font-weight: 500;
                  text-align: center;
                  color: #e3293a;
                  line-height: 25px;
                }
                .nobuy {
                  font-size: 18px;
                  font-family: PingFangSC, PingFangSC-Medium;
                  font-weight: 500;
                  text-align: center;
                  color: #e3293a;
                  line-height: 25px;
                }
                .price-numer {
                  font-size: 18px;
                  font-family: PingFangSC, PingFangSC-Medium;
                  font-weight: 500;
                  text-align: center;
                  color: #e3293a;
                  line-height: 25px;
                  .price-permission {
                    color: #f39800;
                  }
                  .price-numer-i {
                    color: #ff2121;
                    font-size: 20px;
                    font-family: PingFangSC;
                    font-weight: bold;
                  }
                  .pricewapper {
                    .price-box {
                      overflow: hidden;
                      color: #FF2121;
                      .price-two {
                        p {
                          color: #FF2121;
                        }
                        
                        letter-spacing: 0;
                        span {
                          font-weight: bold;
                        }
                        .priceDec {
                          font-size: 14px;
                        }
                        .priceInt {
                          font-size: 22px;
                        }
                        .priceFloat {
                          font-size: 18px;
                        }
                        // p {
                        //   span {
                        //     color: #ff2121;
                        //   }
                        // }
                      }
                      .midPack {
                        line-height: rem(40);
                        text-align: right;
                        width: 35%;
                        font-size: rem(20);
                        color: #999;
                      }
                    }
                  }
                }
              }
              .control-hid {
                .control-box {
                  margin-top: 5px;
                }
              }
            }
          .label-box {
            margin: 2px 0 0 0;
            .labels {
              height: 18px;
              line-height: 18px;
              position: relative;
              font-size: 0;
              display: flex;
              display: -webkit-flex;
              flex-wrap: nowrap;
              -webkit-flex-wrap: nowrap;
              justify-content: space-between;
              .labels-span {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-left: 0;
                span {
                  margin-right: 5px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  height: 18px;
                  line-height: 18px;
                  background: #fe0800;
                  border-radius: 2px;
                  color: #ffffff;
                  font-size: 12px;
                  padding: 0 5px;
                  display: inline-block;
                  width: auto;
                  margin-left: 0;
                  border: 0;
                }
                .fullXReductionY {
                  position: relative;
                  background: rgba(255, 147, 143, 1);
                  color: rgba(255, 255, 255, 1);
                  letter-spacing: 1px;
                }
                .fullXReductionY:before {
                  content: "";
                  display: inline-block;
                  background-color: #fff;
                  width: rem(7);
                  height: rem(14);
                  border-radius: 0 rem(14) rem(14) 0;
                  position: absolute;
                  left: 0;
                  top: 50%;
                  transform: translateY(-50%);
                }
                .fullXReductionY:after {
                  content: "";
                  display: inline-block;
                  background-color: #fff;
                  width: rem(7);
                  height: rem(14);
                  border-radius: rem(14) 0 0 rem(14);
                  position: absolute;
                  right: 0;
                  top: 50%;
                  transform: translateY(-50%);
                }
              }
              /*标签样式*/
              @import "../assets/style/tagStyle";
              .more {
                max-width: rem(54);
                background: url("../../app/assets/images/dot.png") no-repeat center;
                background-size: 100%;
                width: rem(54);
              }
            }
          }
          .self-support {
            margin-top: 1px;
            height: 21px;
            display: flex;
            align-items: center;
            white-space: nowrap;
            .shrink0 {
              flex-shrink: 0;
            }
        
            span {
              background-color: #00B955;
              border-radius: 2px;
              color: #ffffff;
              font-size: 12px;
              padding: 2px;
              display: inline-block;
            }
            .shopName {
              flex-wrap: nowrap;
              background: none;
              font-size: 12px;
              color: #999999;
              display: inline-block;
              flex-grow: 1;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .maxWidthName {
              max-width: 70px;
            }
            .purchaseTags {
              background: none;
              color: #009D48;
            }
          }
        }
      }
      .templist-item-combination:hover {
        cursor: pointer;
      }
    }
    .templist-combination:hover {
      cursor: pointer;
    }
    .templist-item {
      background: #fff;
      position: relative;
      // overflow: hidden;
      width: 236px;
      box-sizing: border-box;
      // padding: 20px 17px 0 17px;
      padding: 0 17px;
      height: 396px;
      border: 1px solid #f1f1f1;
      .float-carButton {
        position: absolute;
        top: 0;
        left: 0;
        width: 236px;
        height: 396px;
        //box-shadow: 2px 2px 10px 0 rgba(0,0,0,0.20);
        border: 1px solid #00B955;
        box-sizing: border-box;
        display: block;
        .price-wrap {
          display: flex;
          align-items: baseline;
          font-weight: Medium;
          .price-container {
            color: #FF2121;
            span {
              font-weight: bold;
            }
            .priceDec {
              font-size: 14px;
            }
            .priceInt {
              font-size: 22px;
            }
            .priceFloat {
              font-size: 18px;
            }
          }
          .retailPrice {
            font-size: 12px;
            color: #666666;
            text-decoration: line-through;
            margin-left: 8px;
          }
          .zheHouPrice {
            color: #ff2121;
            font-size: 12px;
            font-weight: 400;
            margin-left: 6px;
          }
        }
        .collection {
          position: absolute;
          top: 5px;
          right: 0px;
          z-index: 100;
          display: block;
          height: 49px;
          width: 40px;
          border: 0px solid #ccc;
          overflow: hidden;
          vertical-align: middle;
          text-align: center;
          cursor: pointer;
          .zone-1 {
            height: 24px;
            overflow: hidden;
            position: relative;
            .top {
              position: relative;
              height: 24px;
              width: 40px;
            }
            .top-1 {
              animation: sliderDown 0.8s;
              animation-fill-mode: forwards;
            }
            @keyframes sliderDown {
              from {
                -webkit-transform: translateY(-24px);
                transform: translateY(-24px);
              }
              to {
                -webkit-transform: translateY(0);
                transform: translateY(0);
              }
            }
            .top-2 {
              -webkit-animation: sliderUp 0.8s;
              animation: sliderUp 0.8s;
              -webkit-animation-fill-mode: forwards;
              animation-fill-mode: forwards;
            }
            @keyframes sliderUp {
              from {
                -webkit-transform: translateY(24px);
                transform: translateY(24px);
              }
              to {
                -webkit-transform: translateY(0);
                transform: translateY(0);
              }
            }
            .w-icon-normal {
              margin-top: 6px;
            }
            .icon-normal-collectEpt {
              width: 16px;
              height: 16px;
              background: url(../assets/images/search/shoucang.png) no-repeat;
              display: inline-block;
            }
            .icon-normal-collectFull {
              width: 16px;
              height: 16px;
              background: url(../assets/images/search/shoucang2.png) no-repeat;
              display: inline-block;
            }
          }
          .zone-2 {
            height: 23px;
            overflow: hidden;
            position: relative;
            .bottom {
              height: 23px;
              font-size: 13px;
              line-height: 23px;
              color: #999;
              p {
                font-size: 12px;
                color: #adadad;
              }
            }
          }
        }
        .toStore {
          position: absolute;
          top: 5px;
          right: 7px;
          font-size: 12px;
          text-align: center;
          cursor: pointer;
          .storeIcon {
            width: 16px;
            height: 16px;
            margin: 4px auto;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .toStoreText {
            font-size: 12px;
            color: #adadad;
          }
        }
        .hoverBox {
          // width: 100%;
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          padding:10px 10px;
          background: rgb(255, 255, 255);
          font-size: 14px;
          font-family: MicrosoftYaHei;
          border-top-right-radius: 10px;
          border-top-left-radius: 10px;
          min-height: 174px;
          //color: #ffffff;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          gap: 10px;
          .controlTitleText {
            color: #f49926;
            font-weight: 400 !important;
            font-size: 18px;
          }
          .circleBg {
            position: absolute;
            top: -9px;
            left: 0;
            right: 0;
            background: url('../assets/images/search/yh.png') no-repeat;
            background-size: 100%;
            height: 10px;
          }
        }
        .levelHoverBox {
          .price-wrap {
            display: flex;
            align-items: baseline;
            font-weight: Medium;
            margin: 4px 0 6px 0;
            .price-container {
              display: flex;
              align-items: baseline;
              color: #FF2121;
              .priceDec {
                font-size: 14px;
              }
              .priceInt {
                font-size: 22px;
              }
              .priceFloat {
                font-size: 18px;
              }
            }
            .price-container-box {
              color: #FF2121;
              font-size: 20px;
            }
            .retailPrice {
              font-size: 12px;
              color: #666666;
              text-decoration: line-through;
              margin-left: 8px;
            }
          }
          .priceDesc {
            div {
              color: #222222;
              font-size: 12px;
            }
            margin: 10px 0;
          }
        }
        .progressBox {
          width: 148px;
          height: 4px;
          background: #F5F5F5;
          border-radius: 2px;
          position: relative;
          display: inline-block;
          margin-right: 10px;
          .progressContnet {
            height: 4px;
            background-image: linear-gradient(90deg, #FC6C41 0%, #F62526 100%);
            border-radius: 2px;
          }
          .progressSpot {
            position: absolute;
            top: -3px;
            width: 6px;
            height: 6px;
            border: 1.5px solid #fff;
            border-radius: 50%;
            background: #FF2121;
          }
        }
        .ptPercentage {
          color: #FF6F48;
        }
        .pCount {
          font-size: 12px;
          margin-top: 4px;
          .ypCount {
            color: #FF6F48;
          }
          .qpCount {
            color: #666666;
          }
        }
        .ptHoverBox {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          padding: 10px;
          border-top-right-radius: 10px;
          border-top-left-radius: 10px;
          background: white;
          display: flex;
          gap: 10px;
          flex-direction: column;
          justify-content: space-between;
          min-height: 174px;
          .controlTitleText {
            color: #f49926;
            font-weight: 400 !important;
            font-size: 18px;
          }
          // border-radius: 10px 10px 0 0;
          .circleBg {
            position: absolute;
            top: -9px;
            left: 0;
            right: 0;
            background: url('../assets/images/search/yh.png') no-repeat;
            background-size: 100%;
            height: 10px;
          }
          .ptPrice {
            color: #FF5B5B;
            font-size: 16px;
          }
          .priceDesc {
            div {
              color: #FFCD86;
              font-size: 12px;
            }
            margin: 10px 0;
          }
          .progressBox {
            width: 148px;
            margin-top: 10px;
          }
        }
        .ptShowName {
          font-size: 14px;
          margin-top: 10px;
          text-overflow: ellipsis;
          overflow: hidden;
          display: -webkit-box;
          /* autoprefixer: off */
          -webkit-box-orient: vertical;
          /* autoprefixer: on */
          -webkit-line-clamp: 3;
        }
        .inputNumber {
          width: 100%;
          height: 48px;
          position: absolute;
          right: 0;
          bottom: 0;
          background: #2f3846;
          .no-goods {
            img {
              position: absolute;
              top: rem(5);
              right: rem(10);
              width: rem(40);
              height: rem(40);
            }
          }
        }
      }
      a {
        display: block;
      }
      .images {
        // width: 146px;
        // height: 99px;
        width: 185px;
        height: 180px;
        box-sizing: content-box;
        margin: auto;
        padding-right: 17px;
        position: relative;
        .sold-out {
          position: absolute;
          left: 50%;
          top: 50%;
          margin: -30px 0 0 -30px;
          width: 60px;
          height: 60px;
          background-color: rgba(0, 0, 0, 0.4);
          text-align: center;
          font-size: 14px;
          color: #fff;
          border-radius: 50%;
          line-height: 60px;
        }
        .pic {
          // max-width: 146px;
          // max-height: 99px;
          max-width: 185px;
          max-height: 180px;
          display: block;
          margin: auto;
          object-fit:contain;
        }
        .activity-token {
          position: absolute;
          width: 185px;
          height: auto;
          max-width: 100%;
          margin-top: -6px;
          top: 2px;
          left: 0px;
          z-index: 3;
        }
        .mark-text {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          z-index: 5;
          img {
            width: 100%;
            height: 100%;
          }
          h4 {
            position: absolute;
            left: 0;
            bottom: 0;
            font-size: rem(20);
            color: #fff;
            width: 100%;
            text-align: center;
          }
        }
        .promotionSkuPrice {
          width: 100%;
          height: 100%;
          position: absolute;
          left: 0;
          top: 0;
          z-index: 5;
          span {
            position: absolute;
            right: 0;
            bottom: 0;
            font-size: 14px;
            color: #ff6201;
            display: flex;
            display: -webkit-flex;
            justify-content: center;
            -webkit-justify-content: center;
            align-items: center;
            -webkit-align-items: center;
            width: rem(140);
            height: rem(40);
            text-shadow: 1px 1px 1px #ffd9b4;
            font-weight: 600;
          }
        }
        .active-tags {
          position: absolute;
          left: 50%;
          top: 16px;
          z-index: 5;
          margin-left: -92.5px;
          img {
            width: 185px;
            height: auto;
          }
          .timeStr {
            left: 0;
            position: absolute;
            bottom: 47px;
            text-align: center;
            width: 174px;
            height: 10px;
            line-height: 10px;
            span {
              color: #ffffff;
              display: block;
              font-size: 12px;
              text-align: center;
              transform: scale(0.65);
            }
          }
          .temtejia806 {
            font-size: 12px;
            text-shadow: none;
            width: auto;
            font-weight: 400;
            text-align: center;
            color: #fff;
            position: absolute;
            bottom: 25px;
            left: 0;
            width: 174px;
            overflow: hidden;
            height: 18px;
            span {
              display: inline-block;
            }
          }
        }
      }
      .nearEffectBox {
        background: rgba(0, 0, 0, 0.6);
        font-size: 12px;
        color: #FFFFFF;
        height: 22px;
        line-height: 22px;
        position: relative;
        padding-left: 10px;
        .countTag {
          text-align: right;
          padding-right: 10px;
          position: absolute;
          top: 0;
          right: 0;
          height: 22px;
          width: 60px;
          background: url("../assets/images/search/tag.png") no-repeat;
          background-size: 100% 100%;
        }
      }
      .leftText {
        padding-left: 8px;
        text-align: left;
      }

      .info {
        padding: 4px 0 0 0;
        overflow: hidden;
        .commonName {
          font-size: 16px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          text-align: left;
          color: #333333;
          line-height: 22px;
          text-overflow: ellipsis;
          overflow: hidden;
          display: -webkit-box;
          /* autoprefixer: off */
          -webkit-box-orient: vertical;
          /* autoprefixer: on */
          -webkit-line-clamp: 2;
        }
        .actBox {
          white-space: nowrap;
          margin: 5px 0;
          .tagItem {
            margin-right: 8px;
            padding: 5px 4px;
            font-size: 12px;
            border-radius: 4px;
          }
        }
        .tagBox {
          margin: 6px;
          margin-left: 0;
          white-space: nowrap;
          display: inline-block;
          .tagItem {
            margin-right: 8px;
            padding: 1px 4px;
            font-size: 12px;
            border-radius: 4px;
          }
          .tagItemBig {
            padding: 4px;
            display: inline-block;
          }
          .tejia {
            color: #fff;
            background-image: linear-gradient(270deg, #FF0000 1%, #F96401 100%);
            border: 1px solid #FF9AA3;
            border-radius: 4px;
            padding: 3px 5px;
          }
          .zhijiang {
            color: #fff;
            background-image: linear-gradient(270deg, #8001EE 1%, #6A01F9 100%);
            border: 1px solid #E9D3FF;
            border-radius: 4px;
            padding: 3px 5px;
          }
        }
        .manufacturer {
          background: none;
          font-size: 12px;
          color: #999999;
          // margin: 2px 0 8px;
        }
        .collageBox {
          display: flex;
          align-items: center;
          font-size: 14px;
          .skText {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 200px;
            height: 24px;
            background: url('../assets/images/search/msTime.png') no-repeat;
            background-size: 100%;
            padding: 0 30px;
            color: #222222;
          }
          .couponBg {
            // width: 200px;
            display: inline-block;
            font-size: 12px;
            color: #F1081C;
            height: 24px;
            line-height: 24px;
            background: url('../assets/images/search/coupon.png') no-repeat;
            background-size: 100% 100%;
            padding: 0 4px;
            margin-right: 4px;
          }
          .couponBg-zp {
            display: inline-block;
          }
        }
        .price-add-wrap {
          display: flex;
          display: -webkit-flex;
          flex-wrap: nowrap;
          -webkit-flex-wrap: nowrap;
          justify-content: start;
          align-items: baseline;
          position: relative;
          padding-top: 4px;
          .price-wrap {
            display: flex;
            display: -webkit-flex;
            flex-wrap: nowrap;
            -webkit-flex-wrap: nowrap;
            flex-direction: column;
            overflow: hidden;
            .price-container {
              display: flex;
              display: -webkit-flex;
              flex-wrap: nowrap;
              -webkit-flex-wrap: nowrap;
              justify-content: space-between;
              .controlTitleText {
                color: #f49926;
                font-weight: 500;
                font-size: 18px;
              }
              .qualifications {
                font-size: 18px;
                font-family: PingFangSC, PingFangSC-Medium;
                font-weight: 500;
                text-align: center;
                color: #e3293a;
                line-height: 25px;
              }
              .nobuy {
                font-size: 18px;
                font-family: PingFangSC, PingFangSC-Medium;
                font-weight: 500;
                text-align: center;
                color: #e3293a;
                line-height: 25px;
              }
              .price-numer {
                font-size: 18px;
                font-family: PingFangSC, PingFangSC-Medium;
                font-weight: 500;
                text-align: center;
                color: #e3293a;
                line-height: 25px;
                .price-permission {
                  color: #f39800;
                }
                .price-numer-i {
                  color: #ff2121;
                  font-size: 20px;
                  font-family: PingFangSC;
                  font-weight: bold;
                }
                .pricewapper {
                  .price-box {
                    overflow: hidden;
                    color: #FF2121;
                    .price-two {
                      p {
                        color: #FF2121;
                      }
                      
                      letter-spacing: 0;
                      span {
                        font-weight: bold;
                      }
                      .priceDec {
                        font-size: 14px;
                      }
                      .priceInt {
                        font-size: 22px;
                      }
                      .priceFloat {
                        font-size: 18px;
                      }
                      // p {
                      //   span {
                      //     color: #ff2121;
                      //   }
                      // }
                    }
                    .midPack {
                      line-height: rem(40);
                      text-align: right;
                      width: 35%;
                      font-size: rem(20);
                      color: #999;
                    }
                  }
                }
              }
            }
            .control-hid {
              .control-box {
                margin-top: 5px;
              }
            }
          }
          .zheHouPrice {
            color: #ff2121;
            font-size: 12px;
            font-weight: 400 !important;
            margin-left: 6px;
          }
        }
        .label-box {
          margin: 2px 0 0 0;
          .labels {
            height: 18px;
            line-height: 18px;
            position: relative;
            font-size: 0;
            display: flex;
            display: -webkit-flex;
            flex-wrap: nowrap;
            -webkit-flex-wrap: nowrap;
            justify-content: space-between;
            .labels-span {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-left: 0;
              span {
                margin-right: 5px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                height: 18px;
                line-height: 18px;
                background: #fe0800;
                border-radius: 2px;
                color: #ffffff;
                font-size: 12px;
                padding: 0 5px;
                display: inline-block;
                width: auto;
                margin-left: 0;
                border: 0;
              }
              .fullXReductionY {
                position: relative;
                background: rgba(255, 147, 143, 1);
                color: rgba(255, 255, 255, 1);
                letter-spacing: 1px;
              }
              .fullXReductionY:before {
                content: "";
                display: inline-block;
                background-color: #fff;
                width: rem(7);
                height: rem(14);
                border-radius: 0 rem(14) rem(14) 0;
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
              }
              .fullXReductionY:after {
                content: "";
                display: inline-block;
                background-color: #fff;
                width: rem(7);
                height: rem(14);
                border-radius: rem(14) 0 0 rem(14);
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
              }
            }
            /*标签样式*/
            @import "../assets/style/tagStyle";
            .more {
              max-width: rem(54);
              background: url("../../app/assets/images/dot.png") no-repeat center;
              background-size: 100%;
              width: rem(54);
            }
          }
        }
        .self-support {
          margin-top: 1px;
          height: 21px;
          display: flex;
          align-items: center;
          white-space: nowrap;
          .shrink0 {
            flex-shrink: 0;
          }
      
          span {
            background-color: #00B955;
            border-radius: 2px;
            color: #ffffff;
            font-size: 12px;
            padding: 2px;
            display: inline-block;
          }
          .shopName {
            flex-wrap: nowrap;
            background: none;
            font-size: 12px;
            color: #999999;
            display: inline-block;
            flex-grow: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .maxWidthName {
            max-width: 70px;
          }
          .purchaseTags {
            background: none;
            color: #009D48;
          }
        }
      }
    }
    .templist-item:hover {
      cursor: pointer;
    }
    // .templist-item:nth-child(4n + 0) {
    //   border-right: 0;
    // }
    // .templist-item:last-child(4n + 0) {
    //   border-right: 0;
    // }
  }
  .divBorder {
    border: 2px solid red !important;
  }
  .subsidy {
    padding: 0;
    margin: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    background-color: red;
    align-items: center;
    height: 30px;
    position: absolute;
    z-index: 12;
    left: 0;
    .font {
      margin-right: 5px;
    }
    .image {
      width: 80px;
      background: url('../assets/images/xsSubsidy.svg') no-repeat;
      background-position: center;
      background-size: 100%;
      height: 18px;
      margin-left: 4px;
    }
  }
  </style>
