<template>
<div class="notice_container" v-if="showNotice">
  没有找到{{route.query.target}} 对应的活动，请退出重试
</div>
</template>

<script setup>
import { ref, onMounted, onActivated} from "vue";
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
import { actionTracking } from '@/config/eventTracking';

const route = useRoute();
const showNotice = ref(false);
const targetMap = {
    1001 : 'https://app-v4.ybm100.com/public/2022/6/19750.html', //拼团专区
    2001 : 'https://app-v4.ybm100.com/public/2022/6/19947.html',  //控销专区
    3001 : 'https://app-v4.ybm100.com/public/2022/3/18925.html'   //秒杀专区
};

onMounted(function (){
  let id = parseInt(route.query.target);
  console.log(`id= ${id}, target = ${targetMap[id]}`)
  if(id && targetMap[id]){
    window.location.href = targetMap[id];
  } else{
    showNotice.value = true;
  }

  actionTracking('QRscanjump', { targetid: (route.query || {}).target || 'null' });
});

</script>

<style lang="scss" scoped>
.notice_container{
  width: 100%;
  margin: 100px auto;
  font-size: 15px;
  text-align: center;
  font-weight: bold;
}

</style>