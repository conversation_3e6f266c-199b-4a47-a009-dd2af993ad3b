<template>
  <el-dialog
    v-model="showEditVis"
    title="修改采购数量"
    width="400px"
		:lock-scroll="false"
    :before-close="handleClose"
  >
    <div>
			<span style="color: #292933">设置采购数量: </span>
			<el-input style="width: 200px;" v-model="count" />
		</div>
		<div class="tips">
			<p>温馨提示：</p>
			<p>1. 采购数量修改后系统会重新进行匹配价格最优商品；</p>
			<p>2. 采购数量需填写大于0的整数，若商品有限购，起购、不可拆零则提交后系统会自动重置并自动替换原有采购数量</p>
		</div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleOk">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
	showEditVis: {
		default: false
	},
	cancelDialog: {
		type: Function,
		default: () => {}
	},
	okDialog: {
		type: Function,
		default: () => {}
	},
	defultCount: {
		default: 0,
	}
});
const showEditVis = computed({
	get: () => props.showEditVis,
	set: (value) => emit("cancelDialog", value),
})
const count = ref(props.defultCount);

const handleClose = () => {
	props.cancelDialog();
}
const handleOk = () => {
	const re = /^[1-9]\d*$/;
	if (!re.test(count.value)) {
		ElMessage.error('采购数量格式不正确');
		return;
	}
	props.okDialog(count.value);
}
</script>
<style lang="scss" scoped>
.dialog-footer button:first-child {
  margin-right: 10px;
}
.tips {
	color: #999999;
	margin-top: 20px;
	p {
		margin: 5px 0;
	}
}
</style>
