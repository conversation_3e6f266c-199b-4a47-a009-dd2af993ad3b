<template>
<div class="pingAnMerchantsDetails">
    <div class="myMerchant flex">
        <span></span>
        购物金变动明细
    </div>
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="全部" name="all"></el-tab-pane>
        <el-tab-pane label="收入" name="income"></el-tab-pane>
        <el-tab-pane label="支出" name="expenditure"></el-tab-pane>
    </el-tabs>
    <el-row>
        <el-col :span="12">
            <div class="querySwich">
                <div class="item">
                    <div class="label">交易类型</div>
                    <div class="content">
                        <el-select v-model="selectForm.transactionType" placeholder="请选择" clearable>
                            <el-option 
                                v-for="item in transactionTypeOptions" 
                                :key="item.value" 
                                :label="item.label" 
                                :value="item.value" />
                        </el-select>
                    </div>
                </div>
                <div class="item">
                    <div class="label">月份</div>
                    <div class="content">
                        <el-date-picker
                            v-model="selectForm.dateRange"
                            :clearable="false"
                            type="month"
                            placeholder="请选择月份"
                            :disabled-date="disabledDate"
                            @change="changeDate"
                        />
                    </div>
                </div>
            </div>
        </el-col>
        <el-col :offset="1" :span="8">
            <div class="btn">
                <el-button type="primary" class="fade-btn target-green" @click="getList">查询</el-button>
                <transition name="fade">
                    <el-button 
                        :style="{ opacity: activeName === 'all' ? 1 : 0 }" 
                        class="fade-btn" 
                        @click="uploadDetails"
                        :disabled="uploadBtnLoading">下载明细</el-button>
                </transition>
            </div>
        </el-col>
    </el-row>
    <el-table
        :data="transactionDetails"
        style="width: 100%"
        v-loading="tableLoading"
        class="my-custom-table"
        height="400"
    >
        <el-table-column label="时间" prop="createTimeStr">
            <template #default="scope">
                <div style="display: flex; align-items: center">
                    {{ formatDate(scope.row.createTimeStr, 'yyyy-MM-dd hh:mm:ss') }}
                </div>
            </template>
        </el-table-column>
        <el-table-column label="订单编号" prop="tranNo" />
        <el-table-column label="变动类型" prop="changeDesc">
            <template #default="scope">
                <div>{{ scope.row.changeDesc }}</div>
            </template>
        </el-table-column>
        <el-table-column label="变动金额(元)" prop="virtualGold">
            <template #default="scope">
                <div style="color: red" v-if="Number(scope.row.virtualGold) > 0"> +{{ scope.row.virtualGold }}</div>
                <div style="color: green" v-if="Number(scope.row.virtualGold) < 0"> {{ scope.row.virtualGold }}</div>
            </template>
        </el-table-column>
        <el-table-column label="余额" prop="afterChange" />
        <el-table-column label="操作" prop="operation">
            <template #default="scope">
                <div @click="seeOrderDetails(scope.row.orderId,scope.row.tranNo)" class="seeOrderDetails" v-if="isShowDetails(scope.row.tranNo)">查看详情</div>
                <div v-else>-</div>
            </template>
        </el-table-column>
    </el-table>
    <div class="pagination-container">
      <el-pagination
        v-model:currentPage="pageVo.pageNum"
        :page-size="pageVo.pageSize"
        :background="true"
        layout="prev, pager, next, jumper"
        :total="pageVo.total"
        @current-change="pagerChangePage"
      />
    </div>  
</div>
</template>

<script setup>
import {reactive,ref,getCurrentInstance,onMounted} from 'vue'
import { ElMessage } from 'element-plus'
import { formatDate,exportExcel } from '../../../../utils/index';
import { getGoldFlow,getGoldChangeType,exportGoldFlowExcel } from '@/http_pc/api';


const activeName = ref('all')
const activeNo = ref(1)
const selectForm = ref({
    transactionType: '',
    dateRange: new Date(),
    payStartTime: 0,
    payEndTime: 0,
})
const transactionTypeOptions = ref([])
const transactionDetails = ref([])
const uploadBtnLoading = ref(false)
const tableLoading = ref(false)
const pageVo = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0
})
const handleClick = (tab, event) => {
    switch(tab.props.name) {
        case 'all':
            activeNo.value = 1;
            break;
        case 'income':
            activeNo.value = 2;
            break;
        case 'expenditure':
            activeNo.value = 3;
            break;
    }
    getList();
}
const changeDate = () => {
    if (selectForm.value.dateRange) {
        // 获取选择的年月
        const selectedDate = new Date(selectForm.value.dateRange);
        const year = selectedDate.getFullYear();
        const month = selectedDate.getMonth();
        // 计算月份的第一天 (例如：2023-06-01 00:00:00)
        const startDate = new Date(year, month, 1);
        startDate.setHours(0, 0, 0, 0);
        
        // 计算月份的最后一天 (例如：2023-06-30 23:59:59)
        const endDate = new Date(year, month + 1, 0);
        endDate.setHours(23, 59, 59, 999);
        
        // 转换为时间戳
        selectForm.value.payStartTime = startDate.getTime();
        selectForm.value.payEndTime = endDate.getTime();
    } else {
        selectForm.value.payStartTime = 0;
        selectForm.value.payEndTime = 0;
    }
}
const disabledDate = (time) => {
    const currentDate = new Date();
    // 设置当前日期为当月1号
    const currentMonthStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    // 如果选择的时间大于当前月份，则禁用
    return time.getTime() > currentMonthStart.getTime();
}
const initOptions = () => {
    getGoldChangeType().then(res => {
        if(res.code === 1000) {
            transactionTypeOptions.value = res.data.map(item => {
                return {
                    value: item.value,
                    label: item.name
                }
            })
        }else {
            ElMessage.error(res.errorMsg || res.msg || "获取交易类型失败")
        }
    }).catch(err => {
        ElMessage.error("获取交易类型失败")
    })
}
onMounted(() => {
    changeDate()
    initOptions()
    getList()
})
const { proxy } = getCurrentInstance();
const getParams = () => {
    return {
        tradeCode: selectForm.value.transactionType,
        virtualGoldType: activeNo.value,
        pageNum: pageVo.value.pageNum,
        payStartTime: selectForm.value.payStartTime,
        payEndTime: selectForm.value.payEndTime,
        merchantId: proxy.$merchantId,
    } 
}
const getList = () => {
    tableLoading.value = true;
    const params = getParams();
    getGoldFlow(params).then(res => {
        if(res.code === 1000) {
            transactionDetails.value = res.data?.rows || [];
            pageVo.value.total = res.data?.total || 0
        }else {
            ElMessage.error(res.errorMsg || res.msg || "获取失败")
        }
    }).catch(err => {
        ElMessage.error("获取失败")
    }).finally(() => {
        tableLoading.value = false;
    })
}
const uploadDetails = () => {
    if(!selectForm.value.dateRange) {
        ElMessage.warning('请先选择月份');
        return
    }
    const params = getParams();
    uploadBtnLoading.value = true;
    exportGoldFlowExcel(params).then(res => {
        const contentDisposition = res.headers['content-disposition'];
        exportExcel(res.data, decodeURIComponent(contentDisposition.split('filename=')[1]));
    }).catch(err => {
        ElMessage.error("当前月份无数据")
    }).finally(() => {
        uploadBtnLoading.value = false;
    })
}
const seeOrderDetails = (id,value) => {
    if (id) {
        if (/^RS/.test(value)) {
            window.open("/merchant/center/order/findRefundOrderList/"+id+".htm");
        }else {
            window.open(`/merchant/center/order/detail/${id}.htm`);
        }
        
    }else {
        ElMessage.warning("暂无该订单详情")
    }
}
const pagerChangePage = (page) => {
    pageVo.value.pageNum = page;
    getList();
}
const isShowDetails = (value) => {
    return /^RS/.test(value) || /^YBM/.test(value)
}
</script>

<style lang='scss' scoped>
.pingAnMerchantsDetails {
    margin-top: 20px;
    .myMerchant {
        span {
            width: 4px;
            height: 16px;
            display: inline-block;
            background: #00B377;
            margin-right: 10px;
        }
        font-weight: 600;
        font-size: 18px;
        color: #292933;
        letter-spacing: 0;
        line-height: 20px;
        display: flex;
        align-items: center;
    }
    .demo-tabs {
        ::v-deep(.el-tabs__nav-wrap:after) {
            height: 1px !important; 
        }
    }
    .querySwich {
        display: flex;
        .item {
            width: 50%;
            display: flex;
            align-items: center;
            margin-right: 20px;
            .label {
                width: 50px;
                margin-right: 10px;
                white-space: nowrap;
                text-align: right;
            }
            .content {
                flex: 1;
                min-width: 0;
                .el-select,
                .el-input,
                .el-date-picker {
                    width: 100%;
                }
            }
        }
    }
    .btn {
        margin-bottom: 10px;
        .fade-btn {
            transition: all 0.3s ease;
            &:active {
                transform: scale(0.95);
            }
        }
        .target-green {
            background-color: #00B955;
        }
    }
    .seeOrderDetails {
        cursor: pointer;
        color: #00B955;
    }
    .pagination-container {
      margin: 20px 0;
      display: flex;
      justify-content: flex-end;
    }
}

// 淡入淡出效果
// 淡入淡出效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.my-custom-table {
    ::v-deep .el-table__header tr th { 
        background-color: #EFF1F5;
        color: #000;
    }
}
</style>
