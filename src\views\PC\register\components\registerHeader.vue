<template>
  <div class="headerBox">
    <div class="logoBox">
      <div class="logoLeft">
        <img class="logoImg" src="../../../../assets/images/school/logo_login.png" alt="" />
        <div class="pageTitle">{{ title }}</div>
      </div>
      <div class="gologin" v-if="isLogin" @click="tologout">
        当前已登录账号，点击
        <span style="marginLeft: 4px;">
          <a href="/login/logout.htm" class="activeText">退出</a>
        </span>
        <img class="moreIcon" src="../../../../assets/images/register/more.png" alt="" />
      </div>
      <div v-else class="gologin">
        已有账号，立刻去
        <span style="marginLeft: 4px;">
          <a href="/login/login.htm" class="activeText">登录</a>
        </span>
        <img class="moreIcon" src="../../../../assets/images/register/more.png" alt="" />
      </div>
    </div>
  </div>
</template>
<script setup>
  import { onMounted, ref } from "vue";
  import { actionTracking } from '@/config/eventTracking';
  import { getLoginAccountInfo } from '@/http_pc/api';

  const props = defineProps({
    title: {
      default: '',
    },
    isLogin: {
      default: false,
    }
  });

  const loginPhone = ref(0);

  const tologout = () => {
    actionTracking('pc_action_associationAudit_logout_click', { 
      phone: loginPhone.value,
    });
  }

  const getLoginInfo = () => {
    getLoginAccountInfo({}).then((res) => {
      loginPhone.value = ((res.data || {}).info || {}).mobile || 0;
    })
  }

  onMounted(() => {
    getLoginInfo();
  })

</script>
<style lang="scss" scoped>
.headerBox {
  background: #F8F8F8;
  width: 1200px;
  margin: 0 auto;
  color: #888888;
  text-align: center;
  .logoBox {
    width: 1200px;
    margin: 0 auto;
    height: 88px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .logoLeft {
      display: flex;
      align-items: center;
    }
    .pageTitle {
      margin: 10px 0 0 20px;
      padding-left: 20px;
      height: 26px;
      font-size: 20px;
      color: #222222;
      border-left: 1px solid #BBBBBB;
    }
  }
  .logoImg {
    width: 160px;
    height: 60px;
  }
  .gologin {
    height: 57px;
    line-height: 57px;
    padding: 15px 0 0 420px;
    color: #444;
    font-size: 14px;
    .moreIcon {
      width: 6px;
      height: 10px;
      margin-left: 4px;
    }
  }
  .activeText {
    color: #00B377;
  }
}
</style>