<template>
  <div class="shopCouponBox">
    <div class="couponListBox" v-if="couponList.length">
      <div v-for="item in couponList" :key="item.templateId">
        <ComponentItem :couponInfo="item" />
      </div>
      <div v-if="couponList.length" class="bottom-tip">
        <span>{{ bottomTip }}</span>
      </div>
    </div>
    <div class="noGoods" v-else>
      <div>
        <img src="../../../assets/images/xyy-sorry.png" alt="">
        <span class="text">暂无优惠券</span>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { ref, onMounted, getCurrentInstance } from "vue";
  import { useStore } from 'vuex';
  import { receiveCenterProductCoupon } from '@/http/api';
  import { actionTracking } from '@/config/eventTracking';
  import ComponentItem from './components/couponItem.vue';

  const store = useStore();
  const { proxy } = getCurrentInstance();
  const couponList = ref([]);
  const pageNo = ref(1);
  const loading = ref(false);
  const isEnd = ref(false);
  const bottomTip = ref('');

  // 上拉加载
  function pullUpLoading() {
    let scrollHeight = document.documentElement.scrollHeight;
    let scrollTop = window.scrollY;//滚动的高度；
    let clientHeight = document.documentElement.clientHeight;//滚动的高度；
    if (scrollHeight - scrollTop - clientHeight < 30) {
      if (!isEnd.value && !loading.value) {
        getInfo();
      }
    }
  };

  // 获取信息
  function getInfo() {
    loading.value = true;
    store.commit('app/showLoading', true);
    receiveCenterProductCoupon({
      merchantId: proxy.$merchantId,
      pageNo: pageNo.value,
      pageSize: 20
    }).then((res) => {
      loading.value = false;
      store.commit('app/showLoading', false);
      couponList.value = couponList.value.concat(res.couponList || []);
      (res.couponList || []).forEach((item) => {
        actionTracking('h5_couponCentre_Exposure', { couponId: item.templateId })
      });
      isEnd.value = res.isEnd;
      pageNo.value = res.nextPage;
      bottomTip.value = isEnd.value ? '没有更多了' : '查看更多';
    }).catch(() => {
      loading.value = false;
      store.commit('app/showLoading', false);
    })
  }

  onMounted(() => {
    getInfo();
    window.addEventListener('scroll', pullUpLoading);
  })

</script>
<style lang="scss" scoped>
.shopCouponBox {
  padding: rem(0) rem(30) rem(20);
  .couponListBox {
    padding-top: rem(20);
  }
  .noGoods{
    position: relative;
    text-align: center;
    height: rem(210);
    margin: rem(200) 0;
    img{
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width:rem(264);
      height:rem(174);
    }
    .text{
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      color: #A9AEB7;
    }
  }
}
</style>
