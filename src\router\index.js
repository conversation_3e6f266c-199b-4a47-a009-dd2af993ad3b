import {
  createRouter,
  createWebHistory,
  createWebHashHistory,
} from 'vue-router'
import { actionTracking, entryTracking } from '@/config/eventTracking'
import { getUrlParam } from '@/utils/index'
import Bridge from '@/config/Bridge'

const routes = [
  // {
  //   path: '/',
  //   redirect: '/search/index',
  // },

  {
    path: '/hotRanking/test',
    component: () => import('../views/H5/test/index.vue'),
    meta: { title: 'test' },
  },
  {
    path: '/classifiedRankingList/index',
    component: () => import('../views/H5/classifiedRankingList/index.vue'),
    meta: { title: '本地热销榜' },
  },
  {
    path: '/couponPackage/index',
    component: () => import('../views/H5/couponPackage/index.vue'),
    meta: { title: '券包专区' },
  },
  {
    path: '/couponPackage/purchaseRecord',
    component: () => import('../views/H5/couponPackage/purchaseRecord.vue'),
    meta: { title: '购买记录' },
  },
  {
    path: '/couponCenter/index',
    component: () => import('../views/H5/couponCenter/index.vue'),
    meta: { title: '领券中心' },
  },
  {
    path: '/couponCenter_pc/index',
    component: () => import('../views/PC/couponCenter_pc/index.vue'),
    meta: { title: '领券中心' },
  },
  {
    path: '/classifiedRankingList/skuListPage',
    component: () =>
      import('../views/H5/classifiedRankingList/skuListPage.vue'),
    meta: { title: '详情排行榜' },
  },
  {
    path: '/redEnvelope/rulesOfUse',
    component: () => import('../views/H5/redEnvelope/rulesOfUse.vue'),
    meta: { title: '红包使用说明' },
  },
  {
    path: '/qrcode/index',
    component: () => import('../views/H5/QRCodeReceiver/index.vue'),
    meta: { title: '扫码跳转' },
  },
  {
    path: '/intelligentProcurement/index',
    component: () => import('../views/PC/intelligentProcurement/index.vue'),
    meta: { title: '智能采购' },
  },
  {
    path: '/bill/index',
    component: () => import('../views/PC/bill/index.vue'),
    meta: { title: '我的账单' },
  },
  {
    path: '/schoolOfPharmacy/order',
    component: () => import('../views/PC/schoolOfPharmacy/order.vue'),
    meta: { title: '药学院' },
  },
  {
    path: '/register/index',
    component: () => import('../views/PC/register/index.vue'),
    meta: { title: '注册' },
  },
  {
    path: '/register/connectPharmacy',
    component: () => import('../views/PC/register/connectPharmacy.vue'),
    meta: { title: '关联药店' },
  },
  {
    path: '/register/connectShopManagement',
    component: () => import('../views/PC/register/connectShopManagement.vue'),
    meta: { title: '关联店铺管理' },
  },
  {
    path: '/register/examineShopRelation',
    component: () => import('../views/PC/register/examineShopRelation.vue'),
    meta: { title: '关联店铺审核' },
  },
  {
    path: '/register/examineShopInfo',
    component: () => import('../views/PC/register/examineShopInfo.vue'),
    meta: { title: '店铺信息审核' },
  },
  {
    path: '/register/selectLoginShop',
    component: () => import('../views/PC/register/selectLoginShop.vue'),
    meta: { title: '选择登录店铺' },
  },
  {
    path: '/register/uploadQualificationsInfo',
    component: () =>
      import('../views/PC/register/uploadQualificationsInfo.vue'),
    meta: { title: '上传资质信息' },
  },
  {
    path: '/search/index',
    component: () => import('../views/PC/search/index.vue'),
    meta: { title: '搜索结果页' },
  },
  {
    path: '/bigSearch/index',
    component: () => import('../views/PC/bigSearch/index.vue'),
    meta: { title: '大搜搜索结果页' },
  },
  {
    path: '/combination/index',
    component: () => import('../views/PC/combination/index.vue'),
    meta: { title: '详情组合购' },
  },
  {
    path: '/pinganMerchant/introduce',
    component: () => import('../views/H5/pinganMerchant/introduce.vue'),
    meta: { title: '平安商户介绍' },
  },
  {
    path: '/pinganMerchant/open',
    component: () => import('../views/H5/pinganMerchant/open.vue'),
    meta: { title: '开通平安商户' },
  },
  {
    path: '/pinganMerchant/my',
    component: () => import('../views/H5/pinganMerchant/my.vue'),
    meta: { title: '我的平安商户' },
  },
  {
    path: '/pinganMerchant/buyMoneyOut',
    component: () => import('../views/H5/pinganMerchant/buyMoneyOut.vue'),
    meta: { title: '转出到购物金' },
  },
  {
    path: '/shopMoney/buyMoneyOut',
    component: () => import('../views/H5/shopMoney/buyMoneyOut.vue'),
    meta: { title: '转出到平安商户' },
  },
  {
    path: '/pinganMerchant/account',
    component: () => import('../views/H5/pinganMerchant/account.vue'),
    meta: { title: '平安商户信息' }
  },
  {
    path: '/pinganMerchant/changeBankCard',
    component: () => import('../views/H5/pinganMerchant/changeBankCard.vue'),
    meta: { title: '修改银行卡' }
  },
  {
    path: '/pinganMerchant/recharge',
    component: () => import('../views/H5/pinganMerchant/recharge.vue'),
    meta: { title: '平安商户电汇充值' },
  },
  {
    path: '/pinganMerchant/pc/introduce',
    component: () => import('../views/PC/pinganMerchant/introduce.vue'),
    meta: { title: '平安商户介绍' },
  },
  {
    path: '/pinganMerchant/pc/open',
    component: () => import('../views/PC/pinganMerchant/open.vue'),
    meta: { title: '开通平安商户' },
  },
  {
    path: '/pinganMerchant/pc/my',
    component: () => import('../views/PC/pinganMerchant/my.vue'),
    meta: { title: '我的平安商户' },
  },
  {
    path: '/pinganMerchant/pc/recharge',
    component: () => import('../views/PC/pinganMerchant/recharge.vue'),
    meta: { title: '平安商户电汇充值' },
  },
  // 京东绑卡需求，查询绑卡结果页面
  {
    path: '/jdPay/result/:id',
    component: () => import('../views/H5/jdPay/index.vue'),
    meta: { title: '绑卡结果查询' },
  },
  {
    path: '/pinganMerchant/pc/loans',
    component: () => import('../views/PC/pinganLoans/index.vue'),
    meta: { title: '我的平安贷' },
  },
  {
    path: '/pinganMerchant/pc/loansdetail',
    component: () => import('../views/PC/pinganLoans/detail.vue'),
    meta: { title: '我的平安贷账单详情' },
  },
  {
    path: '/pinganMerchant/loans',
    component: () => import('../views/H5/pinganLoans/index.vue'),
    meta: { title: '我的平安贷' },
  },
  {
    path: '/pinganMerchant/loansdetail',
    component: () => import('../views/H5/pinganLoans/detail.vue'),
    meta: { title: '我的平安贷账单详情' },
  },
  {
      path: '/quelityNotice/index',
      component: () => import('../views/H5/qualityNotice/index.vue'),
      meta: { title: '质量公告' }
    },
    {
      path: '/quelityNotice/detail',
      component: () => import('../views/H5/qualityNotice/detail.vue'),
      meta: { title: '质量公告详情' }
    },
    {
      path: '/pinganMerchant/pc/account',
      component: () => import('../views/PC/pinganMerchant/account.vue'),
      meta: { title: '平安商户信息' }
    },
    {
      path: '/pinganMerchant/pc/changeBankCard',
      component: () => import('../views/PC/pinganMerchant/changeBankCard.vue'),
      meta: { title: '换绑银行卡' }
    },
  {
    path: '/invoiceService',
    component: () => import('../views/PC/invoice/invoiceService.vue'),
    meta: { title: '发票售后申请' },
  },
  {
    path: '/qualificationService',
    component: () => import('../views/PC/invoice/qualificationService.vue'),
    meta: { title: '资质售后申请' },
  },
  {
    path: '/invoiceServiceDetails',
    component: () => import('../views/PC/invoice/invoiceServiceDetails.vue'),
    meta: { title: '发票售后详情' },
  },
  {
    path: '/payPassword/pc/set',
    component: () => import('../views/PC/payPassword/set.vue'),
    meta: { title: '修改支付密码' },
  },
  {
    path: '/productSpu',
    component: () => import('../views/PC/spu/index.vue'),
    meta: { title: '商品spu组件' },
  },
  {
    path: '/skuSearchCouponCentre',
    component: () => import('../views/PC/spu/sku_search_coupon_centre.vue'),
    meta: { title: '凑单页商品列表' },
  },
  {
    path: '/reminderShipment',
    component: () => import('../views/PC/reminderShipment/index.vue'),
    meta: { title: '提醒发货进度' },
  },
  {
    path:'/shoppingGold/useDirection',
    component: () => import('../views/H5/shoppingGold/useDirection.vue'),
    meta: { title: '购物金使用说明' }
  },
  {
    path:'/shoppingVoucher/onlineRecharge',
    component: () => import('../views/PC/shoppingVoucher/onlineRecharge.vue'),
    meta: { title: '购物金在线充值' }
  },
  {
    path: '/shoppingVoucher/index',
    component: () => import('../views/PC/shoppingVoucher/index.vue'),
    meta: { title: '我的购物金' },
  },
  {
    path: '/pinganMerchant/pc/buyMoneyOut',
    component: () => import('../views/PC/pinganMerchant/buyMoneyOut.vue'),
    meta: { title: '转出到购物金' },
  },
  {
    path:'/jdApply',
    component: () => import('../views/H5/jdApply/index.vue'),
    meta: { title: '金蝶信用付预申请' }
  },
  {
    path:'/jdApplyResult',
    component: () => import('../views/H5/jdApply/result.vue'),
    meta: { title: '金蝶信用付预申请' }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  // history: createWebHistory(),
  routes,
})
router.beforeEach((to, from, next) => {
  if (to.meta.title === '本地热销榜') {
    Bridge.setAppTitle((to.query || {}).cname || '本地热销榜')
    document.title = (to.query || {}).cname || '本地热销榜'
  } else {
    Bridge.setAppTitle(to.meta.title)
    if (to.meta.title) {
      document.title = to.meta.title
    }
  }
  // 活动页进入埋点上报
  setTimeout(() => {
    entryTracking('h5_page_CommodityList', {
      sptype: 4,
      spid: `${String(new Date().getFullYear()).substring(2)}-${
        document.title
      }`,
      fromLaunchApp: getUrlParam('fromLaunchApp'),
    })
    entryTracking(`PV-${document.title}`, {
      pageUrl: window.location.href,
    })
    actionTracking('h5_page_ActivePage', {
      pageName: document.title,
    })
  }, 0)
  next()
})

export default router
