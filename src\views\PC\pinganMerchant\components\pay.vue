<template>
	<el-dialog :modal="false" v-model="dialogVisible" title="验证支付密码" width="500" :before-close="handleClose">
		<div id="xx-pay" class="xx-mask">
			<div class="xx-input-box">

				<div id="inputBox" @keydown="keydownFun" style="display:flex;gap:20px;margin-bottom:10px;">
					<input class="xx-input" type="password" autocomplete="new-password" ref="passwordInput"
						maxlength="0" sub="0" aria-autocomplete="none">
					<input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="1"
						aria-autocomplete="none">
					<input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="2"
						aria-autocomplete="none">
					<input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="3"
						aria-autocomplete="none">
					<input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="4"
						aria-autocomplete="none">
					<input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="5"
						aria-autocomplete="none">
				</div>
				<div class="xx-title">
					<span id="xx-pay-msg" style="color:#ec3d3d;font-size:14px;"></span>
					<span id="xx-pay-forget" @click="forget" style="color:#00C675;cursor: pointer;">忘记密码?</span>
				</div>
				<div id="xx-pay-submit" @click="submit">
					<span>立即支付</span>
				</div>
			</div>
		</div>
		<template #footer>
			<div class="dialog-footer">

			</div>
		</template>
	</el-dialog>
	<LoadingPC text="付款中" v-if="loading"></LoadingPC>
</template>

<script setup>
import { ref, watch, onMounted, nextTick, defineProps, defineEmits } from 'vue'
import { goldTransferIn, getResultByTranNo } from '@/http_pc/api';
import { ElMessage } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
import CryptoJS from '@/utils/CryptoJS.js';
import LoadingPC from '@/components/LoadingPc.vue'
const route = useRoute();
const { merchantId } = route.query;
const loading = ref(false)
const props = defineProps({
	inputMoney: {

	},
	tranNo: {}
})
const emit = defineEmits(['handleClose'])
const dialogVisible = ref(false)
const openDialog = () => {
	dialogVisible.value = true
	nextTick(() => {
		inputInit()
	})
}
const handleClose = () => {
	dialogVisible.value = false
}
const passwordInput = ref(null)
let BASEURL = import.meta.env.VITE_BASE_URL_PC;

let inputBox;
let inputList;
let passwordList = [];
//输入框组件
let pay;
//关闭按钮

//忘记密码按钮
let forgetBtn;
//红字提示
let msgEl;
//提交按钮
let submitBtn;

const inputInit = () => {
	inputBox = document.getElementById('inputBox');
	inputList = inputBox.children;
	
	passwordList = [];
	//输入框组件
	pay = document.getElementById('xx-pay');
	//关闭按钮

	//忘记密码按钮
	forgetBtn = document.getElementById('xx-pay-forget');
	//红字提示
	msgEl = document.getElementById('xx-pay-msg');
	msgEl.textContent =""
	//提交按钮
	submitBtn = document.getElementById('xx-pay-submit');
	for (let i = 0; i < inputList.length; i++) {
		passwordList[i] = '';
		inputList[i].value = ''
	}
	setTimeout(() => {
		inputList[0].focus()
	}, 200)
}
function setFocus(index) {
	const timer = setTimeout(() => {
		if (!inputList[index]) return;
		inputList[index].focus();
		clearTimeout(timer);
	}, 0)
}
function forget() {
	window.open(`https:${BASEURL}merchant/center/setPayPwd/index`);
}
function submit() {
	let target = true;
	passwordList.forEach((val, index) => {
		if (!val) {
			target = false;
			inputList[index].style.borderColor = 'red';
		}
	})
	if (!target) {
		msgEl.textContent = "支付密码未输入完整"
		return;
	} else {
		msgEl.textContent = ""
	}
	//密码加密
	var key = CryptoJS.enc.Utf8.parse("FmpHxGc9no95cvd4");  //十六位十六进制数作为密钥
	var srcs = CryptoJS.enc.Utf8.parse(passwordList.join(''));
	var encrypted = CryptoJS.AES.encrypt(srcs, key, { mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7, iv: key });
	let pass = encrypted.toString();
	let data = {
		tranNo: props.tranNo,
		pwd: pass,
		virtualGold: Number(props.inputMoney),
	}
	// yz
	loading.value = true
	goldTransferIn(data).then(data => {
		if (data.code == 1000) {
			msgEl.textContent = ""
			let timeOut = setInterval(() => {
				getResultByTranNo({
					tranNo: props.tranNo,
					merchantId
				}).then(
					res => {
						if (res.code == 1000) {
							if (res.payResult == 1) {
								clearInterval(timeOut)
								loading.value = false
								ElMessage.success("转出成功")
								dialogVisible.value = false
								emit("handleClose",true)
							}else if(res.payResult == 2){
								clearInterval(timeOut)
								loading.value = false						
								dialogVisible.value = false					
								ElMessage.error(res.errorMsg)
								emit("handleClose")
							}else if(res.payResult == 3){

							}							
						}else if(res.code==1001){
							clearInterval(timeOut)
							loading.value = false
							emit("handleClose")
							dialogVisible.value = false
						
							ElMessage.error(res.errorMsg)
						}
					}
				)
			}, 2000)
		} else {
			inputBox = document.getElementById('inputBox');
			inputList = inputBox.children;
			for (let i = 0; i < inputList.length; i++) {
			passwordList[i] = '';
			inputList[i].value = ''
	}
			loading.value=false
			msgEl.textContent = data.errorMsg			
		}
	}).catch(res => {
		ElMessage.error(data)
		loading.value=false
		// emit("handleClose")
		// dialogVisible.value=false
		// ElMessage.error(res)
	})

}
const keydownFun = (e) => {

	let index = Number(e.target.getAttribute('sub'));
	if (index > 5) return
	if (isNaN(index)) return;
	if (e.key == 'Backspace') {
		inputList[index].style.borderColor = '';
		inputList[index].value = '';
		passwordList[index] = '';
		setFocus(index - 1);
	}
	if (!isNaN(e.key) && e.key != ' ') {
		if (inputList[index].value) {
			index = index + 1
		}
		if (index > 5) return;
		inputList[index].value = String.fromCharCode(Math.random() * (0x9FA5 - 0x4E00) + 0x4E00);
		inputList[index].style.borderColor = '#009ecf';
		passwordList[index] = e.key
		if (inputList[index].value) {
			setFocus(index)
		} else {
			setFocus(index + 1)

		}
	}
	switch (e.key) {
		case 'ArrowLeft':
			setFocus(index - 1);
			break;
		case 'ArrowRight':
			setFocus(index + 1);
			break;
	}
}
onMounted(() => {

})
defineExpose({
	openDialog
})
</script>

<style lang="scss" scoped>
#xx-pay {}

#xx-pay .xx-input-box {

	// transform: translate(-50%, -50px);
	background: white;
	border-radius: 5px;
	padding: 20px;
}

#xx-pay .xx-title {
	display: flex;
	justify-content: space-between;
	margin: 10px 0;
	align-items: center;
}

#xx-pay .xx-input-box .xx-input {
	outline: none;
	text-align: center;
	border-radius: 5px;
	border: 1px #cdcdcd solid;
	font-size: 30px;
	padding: 0;
	width: 50px;
	height: 50px;
	transition: all 0.1s;
}

#xx-pay-submit {
	width: max-content;
	padding: 10px 40px;
	margin: 0 auto;
	background: #00C675;
	color: white;
	border-radius: 5px;
	cursor: pointer;
}
</style>
