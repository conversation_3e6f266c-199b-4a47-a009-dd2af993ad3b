<template>
    <div class="wrapper-box">
      <div class="head-part">
        <div class="head-content">
            <div class="part01">
              <div class="msg">可用额度（元）</div>
              <h1>{{ availableAmount }}</h1>
              <div class="msg amount">总额度（元）：{{ approveAmount }}</div>
            </div>
            <div class="part02">
              <div class="side side-left" @click="displayShow">
                <div class="msg" style="text-align: left;">剩余应还本金（元）<van-icon name="info-o" size="14"  style="margin: 0;display: inline;"/></div>
                <h3 style="text-align: left;">{{ curPrincipalAmountBalance }}</h3>
              </div>
              <div class="side side-right">
                <a :href="targetUrl" class="btn">
                  查账/还款
                  <img src="../../../assets/images/pinganloans/link-icon.png" alt="">
                </a>
              </div>
            </div>
        </div>
        <div class="tab-box">
            <div :class="['tab-item', offflagPostValue==='' ? 'activate' : '']" @click="handlepPyoffflag('')">全部</div>
            <div :class="['tab-item', offflagPostValue===1 ? 'activate' : '']"  @click="handlepPyoffflag(1)">已还清</div>
            <div :class="['tab-item', offflagPostValue===0 ? 'activate' : '']"  @click="handlepPyoffflag(0)">未还清</div>
            <div class="tab-item tab-last" @click="handleStatus">
              {{ monthStr }}
              <img src="../../../assets/images/pinganloans/link-icon.png" alt="">
            </div>
        </div>
        <div class="options"  v-if="maskVisable">
          <div class="option-content">
            <div :class="['option-item', monthPostValue===6 ? 'activate': '']" @click="handleOption(6)">近6个月账单</div>
            <div :class="['option-item', monthPostValue==='' ? 'activate': '']" @click="handleOption('')">全部 </div>
          </div>
          <div class="btn-wrap">
            <div class="btn reset" @click="reset">重置</div>
            <div class="btn submit" @click="submit">完成</div>
          </div>
        </div>
      </div>
      <div class="content-box">
        <div v-if="tableData.length>0">
          <div class="list-wrap" v-for="(column, index) in tableData" :key="index">
            <div class="list-item" @click="handleRowClick(column)">
              <div class="head-line">
                  <div class="side-left">
                    {{ column.billName }}
                    <img class="left-icon" src="../../../assets/images/pinganloans/left-icon.png"/>
                    <img class="status" v-if="column.payoffflag===2"  src="../../../assets/images/pinganloans/status01.png"/>
                    <img class="status" v-if="column.payoffflag===0" src="../../../assets/images/pinganloans/status02.png"/>
                    <img class="status" v-if="column.payoffflag===1" src="../../../assets/images/pinganloans/status03.png"/>
                  </div>
                  <div class="side-right-wrap">
                    <div class="title">剩余应还本金</div>
                    <div :class="['money', column.payoffflag!==1 ? 'error-color' : '']">{{ column.curPrincipalAmountBalance }}</div>
                  </div>
              </div>
              <div class="list-content">
                <div class="content-part01">
                  <div class="side-left">已还本金（元）</div>
                  <div class="side-right">{{ column.actPrincipalAmount }}</div>
                </div>
                <div class="content-part02">
                  <div class="side-left">已还利息（元）</div>
                  <div class="side-right">{{ column.actInterestAmount }}</div>
                </div>
                <div class="content-part03">
                  <div class="side-left">
                    平台贴息（元）
                  </div>
                  <div class="side-left">{{ column.actSubsidyInterestAmount }}</div>
                </div>
                <div class="content-part04" style="padding-top: 10px;">
                  <span :class="column.payoffflag===2 || column.payoffflag===0 ? 'warning-color' : ''">贴息截止日：{{ column.subsidyInterestDate }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="empty">
          <div>
            <img src="../../../assets/images/xyy-sorry.png" alt="">
            <span class="text">暂无数据</span>
          </div>
        </div>
      </div>
    </div>
    <div class="mask" v-if="maskVisable"></div>
</template>
<script setup>
import { onMounted, ref } from 'vue';
import { getAppLoans, getAppPingAnCreditBalance, getUrl } from '@/http/pinganLoans';
import { useRouter } from 'vue-router'
import { useStore } from 'vuex';
import { showDialog } from 'vant';

const router = useRouter()
const store = useStore()
const availableAmount = ref(0.00)
const approveAmount = ref(0.00)
const curPrincipalAmountBalance = ref(0.00)
const targetUrl = ref("")
const epayErr = ref("")
const maskVisable = ref(false)
const tableData = ref([])
const monthPostValue = ref(6);
const offflagPostValue = ref('')
const monthStr = ref('近6个月账单')
//显示选项
const handleStatus = () => {
  maskVisable.value = true
}
//选项卡切换
const handleOption = (v) => {
  monthPostValue.value = v
}
//重置
const reset = () =>{
  monthPostValue.value = 6
  monthStr.value = '近6个月账单'
}
//完成
const submit = () => {
  getList()
  maskVisable.value = false
  if(monthPostValue.value!==6){
    monthStr.value = '全部'
  }else{
    monthStr.value = '近6个月账单'
  }
}
//状态切换
const handlepPyoffflag = (v) =>{
  offflagPostValue.value = v
  getList()
}
const displayShow = () => {
  showDialog({
    title: '温馨提示',
    width: "80%",
    confirmButtonText: "我知道啦",
    confirmButtonColor: "#00B377",
    message: '截止今日00:00所有待还的订单本金，当日还款后请次日9:00查询最新待还',
  }).then(() => {
    // on close
  });
}
/** picker end */
onMounted(()=>{
    getList()
    getPingAnCreditBalanceCurrent()
    getUrls()
})
const getUrls = () => {
  getUrl({merchantId:localStorage.getItem('merchantId')}).then((res)=>{
      if(res.code===1000){
        targetUrl.value = res.data.pinganLoginCheckUrl
      } else {
        epayErr.value = res.msg
      }
  })
}
/*const targetEpay = () => {
  if(epayErr.value){
    showDialog({
      message: epayErr.value,
    }).then(() => {
      // on close
    });
    return
  }
  window.open(targetUrl.value)
}*/
//平安epay余额数据
const getPingAnCreditBalanceCurrent = () => {
  getAppPingAnCreditBalance({merchantId:localStorage.getItem('merchantId')}).then((res) => {
      if (res.code === 1000) {
          availableAmount.value = res.data.availableAmount
          approveAmount.value = res.data.approveAmount
      }
  }).catch(() => {
    console.log("系统异常")
  })
} 
const getList = () => {
  store.commit('app/showLoading', true)
  getAppLoans({queryMonth:monthPostValue.value,payoffflag:offflagPostValue.value,billCode:""}).then((res) => {
      if (res.code === 1000) {
          store.commit('app/showLoading', false)
          tableData.value = res.data.records
          curPrincipalAmountBalance.value = res.data.curPrincipalAmountBalance
      }
  }).catch(() => {
    console.log("系统异常")
  })
}
//查看详情
const handleRowClick = (row) => {
  router.push({ path: "/pinganMerchant/loansdetail", query: { billCode:row.billCode, billName:row.billName} })
}
</script>
<style lang="scss" scoped>
  div,span {
    font-size: rem(28);
    box-sizing: border-box;
  }
  .wrapper-box{
    padding-bottom: rem(20);
    background: #F7F7F8;
    height:100vh;
    .head-part{
      padding: rem(20) rem(20) 0 rem(20);
      position: fixed;
      top:0;
      left: 0;
      width: 100%;
      z-index: 3;
      background:#F7F7F8 url('../../../assets/images/pinganloans/header-bg.png') no-repeat;
      background-size: 100% rem(300);
      height: rem(561);
      .head-content{
        padding: rem(20);
        background: url('../../../assets/images/pinganloans/header-content-bg.png') no-repeat;
        background-size: 100% 100%;
        height: rem(431);
        width: 100%;
        .part01{
          text-align: center;
          padding: rem(40) rem(20) 0 rem(20);
          height: rem(300);
          h1 {
            padding: rem(20) 0;
            font-weight: 700;
            font-size: rem(80);
            color: #FF3000;
          }
          .amount{
            color: #676773;
            padding: ren(20) 0 0 0;
          }
        }
        .part02{
          display: flex;
          justify-content: space-between;
          text-align: center;
          .side-left {
            width: 65%;
            h3{
              text-align: right;
              padding: rem(20) rem(100) 0 0 ;
              font-weight:600
            }
          }
          .side-right{
            display: flex;
            justify-content: flex-end;
            align-items: center;
            .btn{
              width: rem(200);
              height: rem(60);
              line-height: rem(60);
              border: 1px solid #D3D3D3;
              border-radius: rem(44);
              display: block;
              color:#333333;
              background: none !important;
              img {
                margin-left: rem(5);
                width: rem(10);
                height: rem(14);
              }
            }
          }
        }
      }
      .tab-box {
        display: flex;
        justify-content: space-between;
        padding: rem(20) 0  rem(20) 00;
        .tab-item{
          width: rem(136);
          height: rem(60);
          text-align: center;
          line-height: rem(60);
          background: #ffffff;
          border-radius: 12px;
          margin-right: rem(20)
        }
        .tab-last{
          margin-left: auto;
          width: rem(209);
          height: rem(60);
          margin-right: 0;
          img{
            margin-left: rem(5);
            width: rem(10);
            height: rem(14);
            transform: rotate(90)
          }
        }
        .activate {
          color: #FF3000;
        }
      }
      .options{
        position: relative;
        background: #ffffff;
        padding: rem(20) rem(20) 0 rem(20);
        height: rem(394);
        .option-content{
          display: flex;
          justify-content: flex-start;
          .option-item {
            width: rem(223);
            height: rem(80);
            background: #F7F7F7;
            border-radius: rem(8);
            line-height: rem(80);
            text-align: center;
            margin-right: rem(20);
          }
          .activate{
            color:#00B377;
            border: 1px solid #00B377;
            color:#00B377;
          }
        }
        .btn-wrap {
          position: absolute;
          bottom: 0;
          width: 100%;
          display: flex;
          justify-content: space-between;
          left: 0;
          .btn{
            width: 50%;
            height: rem(88);
            line-height: rem(80);
            text-align: center;
            background: #F7F7F7;
            border-radius: 0;
          }
          .submit {
            background: #00B377;
            color: #ffffff;
          }
        }
      }
    }
    .content-box {
      padding:rem(561) rem(20) rem(20) rem(20);
      background: #F7F7F8;
      .options-wrap {
        display: flex;
        justify-content: flex-end;
        padding: rem(20) rem(20);
        background: #ffffff;
        .options-item{
          width: 49%;
          height: rem(55);
          border:1px solid #999999;
          border-radius: rem(55);
          overflow: hidden;
        }
        .timer {
          margin-right: rem(20)
        }
      }
      .list-wrap{
        margin-bottom: rem(20);
        padding: 0 rem(20);
        background: #ffffff;
        .list-item{
          .head-line{
              display: flex;
              justify-content: space-between;
              padding: rem(10) 0;
              .side-left{
                font-weight: 600;
                display: flex;
                align-items: center;
                .left-icon{
                  width: 7px;
                  height: 12px;
                  margin-left:5px
                }
                .status{
                  margin-left:rem(20);
                  width: 46px;
                  height: 20px;
                }
              }
              .side-right-wrap{
                text-align: right;
                .money{
                  margin-top: 5px;
                  font-weight: 500;
                }
              }
            }
        }
        .list-content {
          padding: rem(20) 0;
          .content-part01{
            display: flex;
            justify-content: space-between;
          }
          .content-part02{
            display: flex;
            justify-content: space-between;
            padding-top: rem(20);
          }
          .content-part03{
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: rem(20);
          }
        }
      }
      .empty{
        position: relative;
        text-align: center;
        height: rem(210);
        margin: 30% 0;
        img{
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          width:rem(264);
          height:rem(174);
        }
        .text{
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          color: #A9AEB7;
        }
      }
    }
  }
  .mask{
    width: 100%;
    height: 100vh;
    background: rgba(0,0,0, 0.5);
    position: fixed;
    z-index: 2;
    top: 0;
  }
  .btn{
    width: rem(250);
    border-radius: rem(60);
    height: rem(60);
    background: #dfdfdf;
    text-align: center;
    line-height: rem(60);
  }
  .error-color{
    color: #FE2021;
  }
  .warning-color{
    color: #FF7700;
  }
  .success-color{
    color: #13c775;
  }
  .van-cell--clickable{
    padding-top: 5px;
  }
</style>