<template>
  <div class="couponBox">
    <div v-for="item in couponList" :key="item.templateId" class="couponItem">
      <div class="newIcon">
        <img src="../../../../assets/images/newPeople.png" />
      </div>
      <div class="voucher-cost">
        <span v-if="item.discount" class="voucher-zhekou voucher-value">
          {{ item.discount }}
          <b class="discountB">折</b>
        </span>
        <span v-else class="voucher-value">
          <b class="moneyB">¥</b>
          {{ item.moneyInVoucher }}
        </span>
      </div>
      <div class="moneyDesc">{{ item.minMoneyToEnableDesc }}</div>
      <div class="voucherTitle textellipsis2">{{ item.voucherTitle }}</div>
      <!-- 操作按钮 -->
      <CouponBtn :couponInfo="item" />
    </div>
  </div>
</template>

<script setup>
  import CouponBtn from './couponBtn.vue';
  import { actionTracking } from '@/config/eventTracking';

  const props = defineProps({
    couponList: {
      default: []
    },
  });

  props.couponList.forEach((item) => {
    actionTracking('h5_couponCentre_Exposure', { couponId: item.templateId })
  })

</script>

<style lang="scss" scoped>
.couponBox {
  margin-top: rem(20);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow-x: scroll;
  flex-wrap: nowrap;
  .couponItem {
    background: url('../../../../assets/images/couponBig.png') no-repeat;
    background-size: 100% 100%;
    width: rem(260);
    flex-shrink: 0;
    height: rem(310);
    margin-right: rem(22);
    text-align: center;
    position: relative;
    .newIcon {
      position: absolute;
      top: 0;
      left: 0;
      img {
        width: rem(90);
        height: rem(32);
      }
    }
    .voucher-cost {
      margin-top: rem(40);
      height: rem(68);
      font-size: rem(64);
      .voucher-value {
        color: #f04134;
        font-weight: bold;
        b {
          font-size: rem(24);
          font-weight: bold;
        }
        .discountB {
          margin-left: rem(-16);
        }
        .moneyB {
          margin-right: rem(-16);
        }
        &.voucher-zhekou {
          margin-top: rem(-10);
          s {
            font-size: rem(30);
            font-style: normal;
            text-decoration: none;
            margin-left: rem(-20);
          }
        }
      }
    }
    .moneyDesc {
      font-size: rem(26);
      font-weight: bold;
      color: #BC4707;
      margin-top: rem(15);
    }
    .voucherTitle {
      margin: rem(15) rem(20);
      font-size: rem(22);
      font-weight: bold;
      color: #BC4707;
      opacity: 0.6;
      height: rem(60);
    }
  }
  .couponItem:nth-last-child(1) {
    margin-right: 0;
  }
}
.couponBox::-webkit-scrollbar {
  display: none;
}
</style>