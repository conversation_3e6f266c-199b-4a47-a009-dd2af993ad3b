<template>
  <!-- 立即领取 -->
  <el-button v-if="couponInfo.isLq === 0" round @click.stop="toGetVoucher(couponInfo)">立即领取</el-button>
  <!-- 去使用 -->
  <el-button v-else-if="couponInfo.isLq === 1" round @click="toUseCoupon(couponInfo)">去使用</el-button>
</template>
<script setup>
  import { toUse, getVoucher } from './btn';

  const props = defineProps({
    couponInfo: {
      default: {}
    },
  });

  function toUseCoupon(couponInfo) {
    toUse(couponInfo.pcUrl);
  }
  function toGetVoucher(info) {
    getVoucher(info);
  };

</script>
<style lang="scss" scoped>

</style>
