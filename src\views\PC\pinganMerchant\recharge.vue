<template>
  <div class="recharge">
    <div class="my flex" style="position: relative;">
      <span></span>
      我的平安商户-平安商户电汇充值
      <el-button @click="resetAccount" style="position: absolute;right: 5px;">返回</el-button>
    </div>
    <div class="explain">
      请使用您当前绑定的银行卡，向平台的待清算专户打款。打款成功后，您的平安商户余额会实时增加。<br />
      请特别注意以下事项：

      <p style="color: red;">
        1. 户名：平安银行电子商务交易资金待清算专户(药帮忙)，后面的括号是英文括号！！如果输入中文括号会打款失败
      </p>
      <p style="color: red;">
        2. 只能使用已绑定的银行卡打款！！使用其他银行卡会打款失败，钱会自动退回到打款银行卡
      </p>
    </div>
    <div class="info" style="margin:0 135px;">
      <h4 class="flex" style="font-weight: bold;">使用绑定的银行卡转帐至药帮忙交易资金待清算专户</h4>
      <div class="item flex">
        <span class="text">开户行号:</span>
        <span class="num">{{ formModal.backNo}}</span>
        <div class="copy" :data-clipboard-text="formModal.backNo" @click="copy($event)"><el-button
            size="small">复制</el-button> </div>
      </div>
      <div class="item flex">
        <span class="text">开户行名称:</span>
        <span class="num">{{ formModal.backName}}</span>
        <div class="copy" :data-clipboard-text="formModal.backName" @click="copy($event)"><el-button
            size="small">复制</el-button> </div>
      </div>
      <div class="item flex">
        <span class="text">账户名:</span>
        <span class="num">{{ formModal.backAccountName }}</span>
        <div class="copy" :data-clipboard-text="formModal.backAccountName" @click="copy($event)"><el-button
            size="small">复制</el-button> </div>
      </div>
      <div class="item flex">
        <span class="text">账户:</span>
        <span class="num">{{ formModal.backAccountNo}}</span>
        <div class="copy" :data-clipboard-text="formModal.backAccountNo" @click="copy($event)"><el-button
            size="small">复制</el-button> </div>
      </div>
    </div>
    <!-- <table>
      <tr>
        <td>开户行号:</td>
        <td>{{ formModal.backNo}}</td>
      </tr>
    </table> -->
    <div class="line" />
    <div class="info" style="margin:0 135px;">
      <h4 class="flex" style="font-weight: bold;">{{ cardStatus == 33 ? "您还没有绑定银行卡" : "您当前绑定的银行卡"}}</h4>
      <div class="bank-status" v-if="[33, 1, 2, 4, 8, 16 , 32].findIndex(item => item === cardStatus) > -1">
        <!-- <div class="info-form-img"> -->
        <!-- <img v-if="cardStatus === 0" src="@/assets/images/pinganMerchant/card-pgs.png" alt=""> -->
        <!-- <img v-if="cardStatus === 1 || cardStatus === 2 || cardStatus === 4" src="@/assets/images/pinganMerchant/card-pgs-ing.png" alt=""> -->
        <!-- <img v-if="cardStatus === 8 || cardStatus === 16" src="@/assets/images/pinganMerchant/card-pgs-error.png" alt=""> -->
        <!-- </div> -->
        <div v-if="cardStatus === 33" style="text-align: center;">
          <img src="./img/noCar.png" alt="" style="width: 120px;"><br>
          <div> 您还没有绑定银行卡，点击“前往绑卡”申请绑卡</div>
        </div>
        <!-- <div class="info-form-text" v-if="applyStep === 2">
          当前有一条绑卡申请记录<span class="warn-text">【银行卡审核中】</span>，审批周期预计5-10分钟，可点击“查看绑卡进度”查看详情
        </div>
        <div class="info-form-btn">{{ applyStep === 1 ? '前往绑卡' : '查看绑卡进度' }}</div>
        <div class="info-form-tips">
          <p>注：仅支持使用平安商户绑定的银行卡向交易资金待清算专户转账充值，使用未绑定的银行卡转账，资金将被退回</p>
        </div> -->
        <div class="info-form-text" v-if="cardStatus === 2">
          当前有一条绑卡申请记录【银行卡审核中】，审批周期预计5-10分钟，可点击“查看绑卡进度”查看详情
        </div>
        <div class="info-form-text" v-if="cardStatus === 8 || cardStatus === 16">
          当前有一条绑卡申请记录【银行卡审核失败】，请点击“查看绑卡进度”查看失败原因
        </div>
        <div class="info-form-text text-flex" v-if="cardStatus === 4">
          当前有一条绑卡申请记录【银行卡待验证】，请点击“查看绑卡进度”进行打款验证请在账户收到小额打款后48小时内进行打款验证，超时需重新发起绑卡申请
        
        </div>
        <div class="info-form-btn" @click="changeCard(formModal.creditCardDtoList.length)">{{ btnValue }}</div>
        <!-- <div class="info-form-tips">
            <p>注：仅支持使用平安商户绑定的银行卡向交易资金待清算专户转账充值，使用未绑定的银行卡转账，资金将被退回</p>
          </div> -->
      </div>
      <div class="bank-info" v-if="formModal&&formModal.creditCardDtoList">
        <div v-for="(item, index) in formModal.creditCardDtoList" style="margin-top: 10px;">
          <div class="item flex">
            <span class="text">账户名:</span>
            <span class="num">{{ item.accountName }}</span>
          </div>
          <div class="item flex">
            <span class="text">银行账号:</span>
            <span class="num">{{ item.acct }}</span>
          </div>
          <div class="item flex">
            <span class="text">开户银行:</span>
            <span class="num">{{ item.bankName }}</span>
          </div>
          <div class="item flex">
            <span class="text">开户支行:</span>
            <span class="num">{{ item.branchBankName }}</span>
          </div>
          <!-- <div class="item">
            <p>注：仅支持使用平安商户绑定的银行卡向交易资金待清算专户转账充值，使用未绑定的银行卡转账，资金将被退回</p>
          </div> -->
        </div>

      </div>

    </div>
  </div>
</template>

<script setup>
  import Clipboard from 'clipboard';
  import { reactive, ref, computed } from "vue";
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { queryPingAnAccountInfo, queryTopUpBankCardInfo } from '@/http_pc/api';
  import { useRouter, useRoute } from 'vue-router';

  const route = useRoute();
  const router = useRouter();
  const { merchantId } = route.query;
  const cardStatus = ref(2);  // 银行卡状态  //lwq 2
  const btnValue = computed(() => {
    return (cardStatus.value === 32 || cardStatus.value === 33) ? '前往绑卡' : '查看绑卡进度';
  })
  const formModal = reactive({
    backNo: '',
    backName: '',
    backAccountName: '',
    backAccountNo: '',
    accountName: '',
    acct: '',
    bankName: '',
    branchBankName: '',
    creditCardDtoList:[]
  });

  ElMessageBox.alert('平安商户余额，可以用作“平安贷”还款，也可转至您的购物金账户，购物金账户余额可用于支付货款', '提示', {
    confirmButtonText: '确定',
  })

  const changeCard = (res) => {
    if(res>=5){
    ElMessage.warning('每个平安商户最多绑定5张对公卡，当前已达上限。请解绑已有卡后再次尝试');
    return
  }
    router.push({
      path: '/pinganMerchant/pc/changeBankCard',
      query: { merchantId }
    });
  }
  const getInfos = () => {
    queryPingAnAccountInfo({ merchantId }).then((res) => {
      if (res.code === 1000) {
        const { data } = res;
        formModal.accountName = data.accountName;
        formModal.acct = data.acct;
        formModal.bankName = data.bankName;
        formModal.branchBankName = data.branchBankName;
        cardStatus.value = data.status;
        formModal.creditCardDtoList=data.creditCardDtoList||[]
      }else{
        //lwq
        // formModal.creditCardDtoList=[
        // {"accountName":"sit ullamco velit mollit","branchBankName":"cupidatat ex enim","branchBankCd":"aliqua","bankName":"officia eu ea commodo","acct":"mollit in Duis","mobileNo":"occaecat exercitation"}, {"accountName":"sit ullamco velit mollit","branchBankName":"cupidatat ex enim","branchBankCd":"aliqua","bankName":"officia eu ea commodo","acct":"mollit in Duis","mobileNo":"occaecat exercitation"}]
        // console.log(formModal);
      }
    });
    queryTopUpBankCardInfo({ merchantId }).then((res) => {
      if (res.code === 1000) {
        const { data } = res;
        formModal.backNo = data.backNo;
        formModal.backName = data.backName;
        formModal.backAccountName = data.backAccountName;
        formModal.backAccountNo = data.backAccountNo;
      }
    });
  };

  const copy = (e) => {
    const clipboard = new Clipboard(e.target);
    clipboard.on('success', e => {
      ElMessage.success('复制成功');
      clipboard.off('error');
      clipboard.off('success');
      clipboard.destroy();
    });
    clipboard.on('error', e => {
      clipboard.off('error');
      clipboard.off('success');
      clipboard.destroy();
    });
    clipboard.onClick(e);
  };
  getInfos();
  const resetAccount = () => {
    // router.go(-1);
    router.push({
      path: '/pinganMerchant/pc/my',
      query: { merchantId }
    });
  }
</script>

<style lang="scss" scoped>
#app {
  background: #fff;
}
.recharge {
  box-sizing: border-box;
  min-height: 800px;
  padding-bottom: 20px;
  background: #fff;
  width: 980px;
  margin: auto;
  padding: 30px 30px 20px 30px;
  .flex {
    display: flex;
    align-items: center;
  }
  .my {
    span {
      width: 4px;
      height: 16px;
      background: #00B377;
      margin-right: 10px;
    }
    font-weight: 600;
    font-size: 18px;
    color: #292933;
    letter-spacing: 0;
    line-height: 20px;
  }
  .explain {
    padding: 12px 0 12px 12px;
    line-height: 20px;
    font-size: 12px;
    color: #303133;
    letter-spacing: 0;
    // line-height: 14px;
    background: #FFF7EF;
    margin-top: 20px;
  }
  .mt22 {
    margin-top: rem(22);
  }
  .info {
    padding: 20px 0 0 0;
    background: #fff;
    h4 {
      font-weight: 500;
      font-size: 14px;
      color: #292933;
      letter-spacing: 0;
      line-height: 16px;
      margin-bottom: 10px;
      span {
        width: 4px;
        height: 4px;
        background: #00B377;
        border-radius: 50%;
        margin-right: 6px;
      }
    }
    .item:nth-child(n + 1) {
      border-bottom: none;
    }
    .item:nth-last-child(1){
      border-bottom: 1px solid #EAEAEA !important;
    }
    .item {
      border: 1px solid #EAEAEA;
      width: 650px;
      box-sizing:border-box !important;
      font-size: 14px;
      line-height: 14px;
      color: #000000;
      
    
      p {
        font-size: 12px;
        color: #FE2021;
        letter-spacing: 0;
        line-height: 14px;
        margin-top: 10px;
      }
      .text {
        padding: 10px;
        flex-shrink: 0;
        padding-right: 5px;
        background: #F4F6F8;
        display: inline-block;
        width: 150px;

      }
      .num {
        text-align: right;
        word-break: break-all;
        padding-left: 10px;
        display: inline-block;
        width: 410px;
        text-align: left;
      }
      .copy {
        padding-left: 5px;
        img {
          width: 10px;
          height: 9px;
        }
      }
    }
    .bank-status {
      display: flex;
      flex-direction: column;
      justify-content: center;
      // align-items: center;
      .info-form-img {
        width: 48px;
        height: 48px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .info-form-text {
        text-align: left !important;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #676773;
        // margin: 20px 0;
        // display: flex;
        align-items: center;
        .warn-span {
          // color: #FF7B03;
        }
        .error-span {
          color: #FF2121;
        }
      }
      .text-flex {
        flex-direction: column;
        div {
          display: flex;
        }
        .account-item-tips {
          margin-top: 10px;
          color: #ff7a06;
          font-size: 14px;
          font-face: PingFangSC;
          font-weight: 400;
          line-height: 24px;
        }
      }
      .info-form-btn {
        margin-top: 20px;
        margin-left: 250px;
        width: 150px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
        background: #00B955;
        border-radius: 2px;
        cursor: pointer;
      }
      .info-form-tips {
        margin-top: 20px;
        p {
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #FE2021;
        }
       
      }
    }
  }
  .line {
    height: 1px;
    background: #EEEEEE;
    margin: 20px 0 0 0;
  }
}
</style>