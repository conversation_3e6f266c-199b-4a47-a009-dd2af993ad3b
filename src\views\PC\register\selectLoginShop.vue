<template>
  <div class="registerWrapper">
    <RegisterHeader title="登录" :isLogin="true" />
    <div class="shopWrapper">
      <div class="shopHeader">
        <span class="title">请选择登录店铺</span>
        <span class="tip">需要关联新的店铺？<span class="activeText" @click="onAddShop">点击添加</span></span>
      </div>
      <div class="shopListBox">
        <div v-for="item in shopList" :key="item.merchantId" class="shopItem" @click="toLogin(item)">
          <div>
            <div class="shopName">
              {{ item.name }}
              <span v-if="item.status == 1" class="yellowSpan">待提审</span>
              <span v-if="item.status == 4 || item.status == 5 || item.merchantStatus == 1" class="greenSpan">审核中</span>
              <span v-if="item.status == 3 || item.merchantStatus == 3" class="redSpan">审核未通过</span>
            </div>
            <div class="shopAddress">{{ `${item.province}${item.city}${item.district}${item.street}` }}</div>
          </div>
          <div class="loginBtn">
            <span class="activeText">
              登录店铺
              <img class="moreIcon" src="../../../assets/images/register/more.png" alt="">
            </span>
          </div>
        </div>
      </div>
      <div class="pagination-container">
        <el-pagination
          v-model:currentPage="pageNo"
          v-model:page-size="pageSize"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="pagerChangeSize"
          @current-change="pagerChangePage"
        />
      </div>
    </div>
    <CheckAccount v-if="showCheckDialog" :currentMerent="currentMerent" :showVis="showCheckDialog" :loginPhone="loginPhone" @cancelDialog="handleCancel" />
    <RegisterFooter />
  </div>
</template>
<script setup>
  import { onMounted, ref } from "vue";
  import RegisterFooter from './components/registerFooter.vue';
  import RegisterHeader from './components/registerHeader.vue';
  import CheckAccount from './components/checkAccount.vue';
  import { getMerchantList, selectMerchant } from '@/http_pc/api';
  import { useRouter, useRoute } from 'vue-router';
  import { ElMessage } from "element-plus";
  import { actionTracking } from '@/config/eventTracking';

  const router = useRouter();
  const route = useRoute();
  const pageNo = ref(1);
  const pageSize = ref(10);
  const total = ref(0);
  const shopList = ref([]);
  const showCheckDialog = ref(false); // 账号登录验证弹窗
  const loginPhone = ref('');
  const currentMerent = ref('');

  // 获取店铺列表
  const getInfo = () => {
    getMerchantList({
      pageNo: pageNo.value,
      pageSize: pageSize.value,
    }).then((res) => {
      if (res.status === 'success') {
        shopList.value = (res.data || {}).list || [];
        total.value = (res.data || {}).total || 0;
      } else {
        ElMessage.error(res.msg);
      }
    })
  }

  const pagerChangeSize = (size) => {
    pageSize.value = size;
    getInfo();
  };
  const pagerChangePage = (page) => {
    pageNo.value = page;
    getInfo();
  };

  const toLogin = (row) => {
    if (row.status == 1) { // 待提审
      router.push(`/register/uploadQualificationsInfo?merchantId=${row.merchantId}`);
    } else if (row.status == 4 || row.status == 5 || row.merchantStatus == 1) { // 审核中
      if (row.status == 4 || row.merchantStatus == 1) {
        router.push(`/register/examineShopInfo?merchantId=${row.merchantId}`);
      } else {
        router.push(`/register/examineShopRelation?merchantId=${row.merchantId}`);
      }
    } else if (row.status == 3 || row.merchantStatus == 3) { // 审核未通过
      if (row.status == 3 && row.merchantStatus != 3) {
        router.push(`/register/uploadQualificationsInfo?merchantId=${row.merchantId}&fromType=seeDeatil`);
      } else {
        router.push('/register/connectPharmacy');
      }
    } else {
      selectMerchant({ merchantId: row.merchantId, }).then((res) => {
        if (res.status === 'success') {
          if (res.data.isCrawler) {
            showCheckDialog.value = true;
            currentMerent.value = row.merchantId;
            loginPhone.value = res.data.mobile;
          } else {
            const { redirectUrl } = route.query;
            if (redirectUrl) {
              window.location.href = redirectUrl;
            } else {
              window.location.href = '/';
            }
            actionTracking('pc_action_login', {
              user_id: row.merchantId,
            })
          }
        } else {
          ElMessage.error(res.msg);
        }
      })
    }
  }

  const handleCancel = () => {
    showCheckDialog.value = false;
  }

  const onAddShop = () => {
    router.push('/register/connectPharmacy')
  }

  onMounted(() => {
    getInfo();
  })

</script>
<style lang="scss" scoped>
.registerWrapper {
  background: #F8F8F8;
  width: 100%;
  min-height: 100vh;
  .activeText {
    color: #00B377;
  }
  .shopWrapper {
    background: #fff;
    width: 1200px;
    margin: 0 auto;
    padding: 70px 0;
    .shopHeader {
      margin: 0 auto;
      width: 758px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      .title {
        font-size: 16px;
        color: #333333;
        font-weight: bold;
      }
      .tip {
        font-size: 12px;
        color: #555;
      }
    }
    .shopListBox {
      border-top: 1px solid #EEE;
      width: 758px;
      margin: 0 auto;
      margin-top: 20px;
      height: 500px;
      overflow-y: scroll;
      .shopItem {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 14px 10px;
        border-bottom: 1px solid #EEE;
        background: #fff;
        .shopName {
          font-size: 14px;
          color: #222222;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          /* autoprefixer: off */
          -webkit-box-orient: vertical;
          /* autoprefixer: on */
          -webkit-line-clamp: 2;
          word-break: break-all;
          word-wrap: break-word;
          .selectSpan, .yellowSpan, .greenSpan, .redSpan {
            display: inline-block;
            margin-left: 6px;
            font-size: 10px;
            padding: 0px 3px;
          }
          .selectSpan {
            color: #fff;
            background: #00C675;
          }
          .yellowSpan {
            color: #FF8C1A;
            background: rgba(255,140,26,0.05);
            border: 1px solid rgba(255,140,26,0.50);
          }
          .greenSpan {
            color: #00B377;
            background: rgba(0,179,119,0.05);
            border: 1px solid rgba(0,179,119,0.50);
          }
          .redSpan {
            color: #FE2021;
            background: rgba(254,32,33,0.05);
            border: 1px solid rgba(254,32,33,0.45);
          }
        }
        .shopAddress {
          font-size: 12px;
          color: #888888;
          margin-top: 6px;
        }
        .loginBtn {
          min-width: 64px;
          .moreIcon {
            width: 6px;
            height: 10px;
            margin-left: 4px;
          }
        }
      }
      .shopItem:hover {
        background: #EBFBF4;
        .shopName {
          font-weight: bold;
        }
      }
    }
    .pagination-container {
      margin: 0 auto;
      margin-top: 20px;
      width: 758px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>