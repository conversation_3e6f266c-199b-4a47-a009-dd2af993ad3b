<template>
  <div>
    <div class="show-mask"  @click.stop="handleExit()"> </div>
    <div class="show-layer">
      <div class="header-wrap" > 优惠
        <span class="exit-wrap" @click.stop="handleExit()"><i class="exit" ></i></span>
      </div>
      <div class="active-wrap" >
        <div class="active-item-wrap" v-for="(item, ind) in props.maskList" :key="ind">
          <span class="active-item-type" :class="`span${item.uiType}`">{{item.name}}</span>
          <div class="active-item-explain">{{item.description}}</div>
        </div>
         <ul>
          <!-- 可领取优惠券 -->
          <div v-if="avaliableReceiveVoucherList && (avaliableReceiveVoucherList.length > 0)" >
            <div class="voucher_tips"> 可领取优惠券<span class="sub_voucher_tips">  领取后可用于购物车商品</span></div>
            <li v-for="item in avaliableReceiveVoucherList" :key="item.id">
                <Voucher :voucher="item" ></Voucher>
            </li>
          </div>
          <!-- 已领取优惠券 -->
          <div v-if="receivedVoucherList && receivedVoucherList.length > 0" >
            <div  class="voucher_tips"> 已领取优惠券<span class="sub_voucher_tips">  领取后可用于购物车商品</span></div>
            <li v-for="item in receivedVoucherList" :key="item.id">
              <Voucher :voucher="item" ></Voucher>
            </li>
          </div>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, watch, reactive, onMounted, onUnmounted } from 'vue';
  import { getskuActionInfo } from '@/http/api';
  import Voucher from "./voucher.vue";
// import func from '../../vue-temp/vue-editor-bridge';
  // defineEmits(['exit']); 
// 
  const props = defineProps({
    maskList: {
      default: []
    },
    hasMask: {
      default: false
    },
    skuId :{
      default:""
    },
    loading:{
      default:false
    }
  });

  const avaliableReceiveVoucherList = ref([]);
  const receivedVoucherList = ref([]);
  const emit = defineEmits(['exit']);

  function handleExit(){
    console.log("11111")
     emit('exit', false)
     document.documentElement.style.overflow = "scroll"; 
  }

  onMounted(()=>{
    getskuActionInfo({ csuId: props.skuId})
      .then((res) => {
        if (res.status == "success") {
          avaliableReceiveVoucherList.value = res.data.avaliableReceiveVoucherList;
          receivedVoucherList.value = res.data.receivedVoucherList;
        }
      })
      .catch((err) => {
        console.log(err, "请求失败了");
      });
   document.documentElement.style.overflow = "hidden"; 
  })

  function handleScroll(ev){
      let activeWrap = document.querySelectorAll('.active-wrap')[0];
      console.log(`scrollHeight = ${activeWrap.scrollHeight}, scrollTop = ${activeWrap.scrollTop}, clientHeight = ${activeWrap.clientHeight}`)
      if(
        !props.hasMask || 
          ((activeWrap.scrollHeight > activeWrap.clientHeight )
          // && (activeWrap.scrollTop + activeWrap.clientHeight< activeWrap.scrollHeight)
          )){
        //最好能换个位置展示，onUnmounted未调用
        document.documentElement.style.overflow = "scroll";
      }
      else{
        // document.documentElement.style.overflow = "hidden";
        // ev.preventDefault();
      }
  }

  function updateScrollState(){
    console.log("props.hasMask")
    function handler(event) {
      event.preventDefault();
    }
    let activeWrap = document.querySelectorAll('.active-wrap')[0];
    if(props.hasMask){
      activeWrap.addEventListener('touchmove', handler(event), false);
      document.documentElement.style.overflow = "hidden";
      if(activeWrap.scrollHeight >= activeWrap.clientHeight){
        activeWrap.removeEventListener('touchmove', handler(event), false);
      }
    } else {
      document.documentElement.style.overflow = "scroll";
      activeWrap.removeEventListener('touchmove', handler(event), false);
    }
  }
  
  watch(
    () => props.hasMask,
    (val) => {
      console.log('value changed', val)
      //  updateScrollState(val);
    }
  );
  

</script>

<style lang="scss" scoped>
  .show-mask {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100vh;
    opacity: .5;
    background-color: #000;
    z-index: 1000;
  }
  .show-layer {
    width: 100%;
    height: rem(660);
    position: fixed;
    bottom: 0;
    // top: 0;
    left: 0;
    border-radius:8px 8px 0 0;
    background-color: #f8f8f8;
    opacity: 1;
    z-index: 1001;
    transition: all 0.3s ease;
    // transform: translateY(100%);
    .header-wrap {
      width: 100%;
      height: rem(88);
      line-height: rem(88);
      text-align: center;
      font-size:rem(32);
      font-family:PingFangSC;
      font-weight:600;
      color:rgba(41,41,51,1);
      .exit-wrap {
        width: rem(88);
        height: rem(88);
        display: inline-block;
        position: absolute;
        right: 0;
        top: 0;
        .exit {
          width: rem(32);
          height: rem(32);
          background:url('../assets/images/icon-exit.png') no-repeat;
          background-size:100%;
          display: inline-block;
          position: absolute;
          top: rem(32);
          right: rem(32);
        }
      }
    }
    .active-wrap {
      height: rem(572);
      padding-left: rem(20);
      padding-right: rem(20);
      overflow-y: auto;

      .voucher_tips{
        font-size: rem(25);
        padding: rem(15) 0;

        span{
          font-size: rem(20);
          color : gray;
          padding-left: rem(25);
        }
      }
      .active-item-wrap {
        display: flex;
        display: -webkit-flex;
        flex-wrap: nowrap;
        -webkit-flex-wrap: nowrap;
        padding-bottom: rem(25);
        margin-top: rem(25);
        justify-content: space-between;
        border-bottom: 1px solid #efefef;
        .active-item-type {
          margin-right: rem(20);
          margin-top: rem(5);
          white-space: nowrap;
        }
        .active-item-explain {
          font-size: rem(28);
          font-family:PingFangSC;
          font-weight:400;
          color:rgba(41,41,51,1);
          line-height: rem(44);
          width: 100%;
        }
        .active-item-more {
          width: rem(30);
          height: rem(30);
          background:url('../assets/images/headline_icon2.png') no-repeat center;
          background-size:100% 100%;
          display: inline-block;
          margin-left: rem(44);
          margin-top: rem(5);
        }
        /*标签样式*/
        @import "../assets/style/tagStyle";
      }
    }
  }
  .CustomPopupContentShow {
    transform: translateY(0);
  }
</style>
