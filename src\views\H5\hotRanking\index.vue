
<template>
  <div class="hotRankingBox">
    <div class="bannerBox">
      <img src="//upload.ybm100.com/ybm/app/layout/cmsimages/2022-2/821f0136e35e264dbf89a68cf33f0db8.png">
    </div>
    <div class="catListBox" :class="{fixedTab: is_fixed}">
      <div class="catListWrapper">
        <div v-for="(item, index) in catList" :key="item.cid" class="catItem" :class="{activeItem: index === activeTab}" @click="changeTab(index)">{{ item.cname }}</div>
      </div>
    </div>
    <div class="cardListBox" v-if="cardList.length">
      <div v-for="(item, index) in cardList" :key="index">
        <div v-if="item.skuList.length" class="cardItem">
          <div class="titleBox" @click="onShowMore(item, 'title')">
            <div class="titleLeft">
              <img class="fireIcon" src="../../../assets/images/fire.png" alt="">
              <span class="nameText">{{item.name}}</span>
            </div>
            <div class="titleRight">
              <span class="linkText">{{item.type == 1 ? '查看更多' : item.linkText}}</span>
              <img class="more" src="../../../assets/images/more.png" alt="">
            </div>
          </div>
          <div class="goodsBox" @click="onShowMore(item, 'card')">
            <div v-for="(goodItem, goodIndex) in (item.skuList || []).slice(0,3)" :key="goodItem.id" class="goodItemBox">
              <a class="images">
                <div v-if="goodItem.status === 2" class="sold-out">售罄</div>
                <div class="sortBox" :class="`sortBox${goodIndex+1}`">{{ goodIndex + 1 }}</div>
                <div class="imgBox">
                  <img
                    class="pic"
                    :class="item.type === 1 ? 'ptImg' : ''"
                    v-lazy="imgUrl+'/ybm/product/min/'+goodItem.imageUrl"
                    :key="goodItem.imageUrl"
                    :alt="goodItem.showName"
                  />
                  <img
                    v-if="goodItem.markerUrl"
                    :src="imgUrl+goodItem.markerUrl"
                    class="activity-token"
                    alt
                  />
                </div>
                <div class="cur-price" v-if="item.type === 1">
                  拼团价
                  <span class="priceDec">￥</span>
                  <span v-if="(goodItem.actPt || {}).assembleStatus === 1">
                    <!-- 多阶梯拼团 -->
                    <span v-if="(goodItem.actPt || {}).stepPriceStatus === 1">
                      <span class="priceInt">{{String((goodItem.actPt || {}).minSkuPrice.toFixed(2)).split('.')[0]}}. </span>
                      <span class="priceDec">{{String((goodItem.actPt || {}).minSkuPrice.toFixed(2)).split('.')[1] || '00'}}</span>
                      <span class="priceDec">起</span>
                    </span>
                    <!-- 单阶梯拼团 -->
                    <span v-if="(goodItem.actPt || {}).stepPriceStatus === 2">
                      <span class="priceInt">{{String(((goodItem.actPt || {}).assemblePrice || '').toFixed(2)).split('.')[0]}}. </span>
                      <span class="priceDec">{{String(((goodItem.actPt || {}).assemblePrice || '').toFixed(2)).split('.')[1] || '00'}}</span>
                    </span>
                  </span>
                  <span class="priceNone" v-if="(goodItem.actPt || {}).assembleStatus === 0">？</span>
                </div>
              </a>
              <div class="showName textellipsis">{{ goodItem.showName }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="noGoods" v-else>
      <div>
        <img src="../../../assets/images/xyy-sorry.png" alt="">
        <span class="text">暂无商品</span>
      </div>
    </div>
    <div v-if="cardList.length" class="bottom-tip">
      <span>{{bottomTip}}</span>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted } from "vue";
  import { useStore } from 'vuex';
  import { useRouter } from 'vue-router';
  import { getRankingList } from '@/http/api';
  import Bridge from "@/config/Bridge";
  import { actionTracking } from '@/config/eventTracking';
  import { sharePage } from '@/utils';

  const store = useStore();
  const imgUrl = import.meta.env.VITE_IMG;
  const router = useRouter();
  const catList = ref([]);
  const cardList = ref([]);
  const activeTab = ref(0);
  const is_fixed = ref(false);
  const isEnd = ref(false);
  const loading = ref(false);
  const bottomTip = ref('');
  const pageInfo = ref({
    pageNum: 1,
    pageSize: 10,
  })
  const buriedPoint = ref({
    sptype: '',
    sid: '',
    spid: ''
  });

  // 顶部导航栏分享功能
  sharePage();
  // 设置导航栏背景色及导航栏文字颜色
  Bridge.setNavigationBar('#FA2340', '#ffffff');

  actionTracking('page_Chart', { page_title: window.document.title });
  // 获取信息
  function getList() {
    if (isEnd.value) return;
    if (loading.value) return;
    store.commit('app/showLoading', true);
    loading.value = true;
    let params = {
      ...pageInfo.value,
    }
    if ((catList.value[activeTab.value] || {}).cid) {
      params.cid = (catList.value[activeTab.value] || {}).cid;
    }
    getRankingList(params).then((res) => {
      loading.value = false;
      store.commit('app/showLoading', false);
      if (res.code == 1000) {
        isEnd.value = res.data.isEnd;
        bottomTip.value = res.data.isEnd ? '没有更多了' : '查看更多';
        catList.value = res.data.catList || [];
        cardList.value = cardList.value.concat(res.data.rows || []);
        pageInfo.value = {
          pageNum: res.data.requestParam.pageNum || 1,
          pageSize: res.data.requestParam.pageSize || 10,
        }
        buriedPoint.value = {
          sptype: res.data.sptype,
          sid: res.data.sid,
          spid: res.data.spid,
        };
        actionTracking('h5_check_tab', {
          title: catList.value[activeTab.value].cname,
          action: '',
        });
        actionTracking('h5_checkCard__Exposure', {
          title: catList.value[activeTab.value].cname,
          categoryName: cardList.value[activeTab.value].name,
          productId: cardList.value.map((item)=>{return item.skuList.slice(0,3).map(i => i.id)}).join()
        });  
      }
    }).catch(() => {
      store.commit('app/showLoading', false);
      loading.value = false;
    })
  }

  // 上拉加载
  function pullUpLoading() {
    let scrollHeight = document.documentElement.scrollHeight;
    let scrollTop = window.scrollY;//滚动的高度；
    let clientHeight = document.documentElement.clientHeight;//滚动的高度；
    if (scrollHeight - scrollTop - clientHeight < 30) {
      if (!isEnd.value) {
        getList();
      }
    }
  }

  function getElementToPageTop(el) {
    if (el && el.parentElement) {
      return getElementToPageTop(el.parentElement) + el.offsetTop
    }
    return (el || {}).offsetTop
  };

  function moveEvent() {
    pullUpLoading();
    let sheight = window.scrollY;//滚动的高度；
    let dom_to_top = getElementToPageTop(document.querySelector(".catListBox"));      
    if (sheight > dom_to_top) {
      is_fixed.value = true;
    } else {
      is_fixed.value = false;
    }
  };

  function changeTab(index) {
    if (loading.value) return;
    isEnd.value = false;
    activeTab.value = index;
    cardList.value = [];
    pageInfo.value = {
      pageNum: 1,
      pageSize: 10,
    }
    getList();
  };

  // 点击卡片
  function onShowMore(item, type) {
    const link = `/classifiedRankingList/index?cname=${encodeURI(item.name)}`;
    if (type === 'card') {
      actionTracking('h5_checkCard_Click', {
        categoryName: item.name,
        productId: item.skuList.slice(0,3).map(i => i.id),
        ...buriedPoint.value,
        action: link,
      });
    } else {
      actionTracking('h5_checkCard_more_Click', {
        title: catList.value[activeTab.value].cname,
        categoryName: item.name,
        action: link
      });
    }
    // type=1为拼团榜单，需跳转到配置路由
    if (item.type === 1) {
      const a = document.createElement('a');
      a.href = item.link;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    } else {
      router.push(link);
    }
  }

  onMounted(() => {
    changeTab(0);
    window.addEventListener('scroll', moveEvent);
  })

</script>
<style lang="scss" scoped>
.hotRankingBox {
  background: #F7F7F8;
  min-height: 100vh;
  .bannerBox {
    img {
      width: 100%;
    }
  }
  .fixedTab {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
  }
  .catListBox {
    padding: rem(20);
    font-size: rem(24);
    
    background: #F7F7F8;
    overflow-x: scroll;
    z-index: 110;
    .catListWrapper {
      display: flex;
      flex-wrap: nowrap;
      width: max-content;
    }
    .catItem {
      color: #292933;
      background: #fff;
      padding: rem(6) rem(12);
      margin-right: rem(10);
      border-radius: rem(6);
    }
    .catItem:nth-last-of-type(1) {
      margin-right: rem(0);
    }
    .activeItem {
      background-image: linear-gradient(132deg, #FF3635 0%, #FB6E3F 100%);
      color: #fff;
    }
  }
  .catListBox::-webkit-scrollbar {
    display: none;
  }
  .cardListBox {
    padding: 0 rem(20);
    .cardItem {
      background: #fff;
      font-size: rem(30);
      margin-bottom: rem(20);
      padding-bottom: rem(20);
      overflow: hidden;
      .titleBox {
        padding: rem(20);
        display: flex;
        align-items: center;
        justify-content: space-between;
        .titleLeft {
          .fireIcon {
            width: rem(22);
            height: rem(28);
            margin-right: rem(10);
          }
          .nameText {
            color: #292933;
            font-size: rem(30);
            font-weight: bold;
          }
        }
        .titleRight {
          background-image: linear-gradient(48deg, #FF3635 0%, #FB6E3F 100%);
          border-radius: rem(6) rem(6) 0 0;
          color: #fff;
          padding: rem(0) rem(10);
          .linkText {
            font-size: rem(22);
            margin-right: rem(6);
          }
          .more{
            width: rem(8);
            height: rem(14);
          }
        }
      }
      .goodsBox {
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 0 rem(15) rem(20);
        .goodItemBox {
          width: rem(180);
          border-radius: rem(6);
          padding-bottom: rem(20);
          .images {
            position: relative;
            width: rem(180);
            display: block;
          }
          .sold-out {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 1.1rem;
            height: 1.1rem;
            background-color: rgba(0, 0, 0, 0.4);
            text-align: center;
            font-size: 14px;
            color: #fff;
            border-radius: 50%;
            line-height: 1.1rem;
            z-index: 100;
          }
          .sortBox {
            position: absolute;
            top: rem(-2);
            left: rem(-2);
            width: rem(36);
            height: rem(38);
            color: #fff;
            font-weight: bold;
            font-size: rem(32);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
          }
          .sortBox1 {
            background: url('../../../assets/images/sort1.png') no-repeat;
            background-size: 100%;
          }
          .sortBox2 {
            background: url('../../../assets/images/sort2.png') no-repeat;
            background-size: 100%;
          }
          .sortBox3 {
            background: url('../../../assets/images/sort3.png') no-repeat;
            background-size: 100%;
          }
          .imgBox {
            width: rem(180);
            height: rem(180);
            .pic {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              max-width: rem(180);
              max-height: rem(180);
            }
            .ptImg {
              border: rem(2) solid #FF233B;
            }
            .activity-token {
              position: absolute;
              width: rem(180);
              height: rem(180);
              left: rem(15);
              top: rem(15);
              z-index: 3;
            }
          }
          .cur-price {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background-image: linear-gradient(270deg, #FF834A 0%, #FF834A 0%, #FF834A 0%, #FF834A 0%, #FF223B 100%);
            border-radius: rem(12) rem(12) 0 0;
            font-size: rem(16);
            color: #fff;
            font-weight: bold;
            padding: rem(4) 0;
            text-align: center;
            .priceDec {
              font-weight: bold;
              font-size: rem(16);
            }
            .priceInt {
              font-weight: bold;
              font-size: rem(26);
              margin: 0 rem(-4);
            }
            .priceNone {
              font-weight: bold;
              font-size: rem(16);
            }
          }
          .showName {
            position: absolute;
            width: rem(180);
            color: #292933;
            font-size: rem(24);
            margin-top: rem(10);
          }
        }
        .goodItemBox:nth-last-of-type(1) {
          margin-right: rem(0);
        }
      }
    }
  }
  .noGoods{
    position: relative;
    text-align: center;
    height: rem(210);
    margin: 30% 0;
    img{
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width:rem(264);
      height:rem(174);
    }
    .text{
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      color: #A9AEB7;
    }
  }
}
</style>
