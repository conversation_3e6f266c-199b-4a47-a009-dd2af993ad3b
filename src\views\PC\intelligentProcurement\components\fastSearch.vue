<template>
  <el-dialog
    v-model="showSearchVis"
    title="极速搜索"
    width="1200px"
		:lock-scroll="false"
		:close-on-click-modal="false"
    :before-close="handleClose"
  >
  <div style="margin-bottom:20px;display:flex;justify-content:space-between">
		<span>{{ defaultItem.excelCommonName||'-' }}</span>
	 	<span>{{ defaultItem.excelSpec||'-' }} </span>
		<span>{{ defaultItem.excelManufacturer||'-' }}</span>
	   	<span>{{ defaultItem.excelApprovalNumber||'-' }}</span>
	   	<span>{{ defaultItem.excelCode||'-' }}</span>
	   	<span>{{ defaultItem.excelBuyNum||'-' }}</span>
	   	<span>{{ defaultItem.excelPrice||'-' }}</span>
	
	</div>
    <el-form ref="ruleFormRef" :model="formModel" label-width="70px" :inline="true">
			<el-row>
				<el-form-item label="商品名称" prop="generalName">
					<el-input v-model.trim="formModel.generalName" placeholder="请输入" style="width: 160px" clearable />
				</el-form-item>
				<el-form-item label="规格" prop="spec">
					<!-- <el-input v-model.trim="formModel.spec" placeholder="请输入" style="width: 160px" clearable /> -->
					<el-select v-model="formModel.spec" placeholder="请选择" style="width: 160px"   clearable   filterable>
						<el-option v-for="(item,index) in specList" :key="index" :label="item" :value="item" />
					</el-select>
				</el-form-item>
				<el-form-item label="厂家" prop="manufacturerName">
					<!-- <el-input v-model.trim="formModel.manufacturerName" placeholder="请输入" style="width: 160px" clearable /> -->
					<el-select v-model="formModel.manufacturerName" placeholder="请选择" style="width: 160px"   clearable filterable>
						<el-option v-for="(item,index) in manufacturerList" :key="index" :label="item" :value="item" />
					</el-select>
				</el-form-item>
				<el-form-item label="商家" prop="shopCode">
					<el-select v-model="formModel.shopCode" placeholder="请选择" style="width: 160px"   clearable filterable>
						<el-option v-for="item in shopList" :key="item.shopCode" :label="item.showName" :value="item.shopCode" />
					</el-select>
				</el-form-item>
				<el-form-item label="排序" prop="sort">
					<el-select v-model="formModel.sort" placeholder="请选择" style="width: 160px">
						<el-option label="综合" :value="1" />
						<el-option label="销量" :value="2" />
						<el-option label="价格" :value="3" />
					</el-select>
				</el-form-item>
			</el-row>
			<div class="btnBox">
				<el-button type="primary" @click="toSerachForm()">搜索</el-button>
				<el-button @click="reset()">重置</el-button>
			</div>
		</el-form>
		<div class="hr"></div>
		<div v-loading="loading">
			<div v-if="list.length||curtab==1" class="itemBox">
				<GoodsItem 
			        v-if="curtab==1"
						:excelInfo="defaultItem"
					:goodsInfo="defaultItem.skus[0]"
					:hideSubTotal="true"
					:showMatchIcon="true"
					@addBuyPlan="addBuyPlan"
					:showSearchVis="showSearchVis"
				/>
				<div v-for="item in list" :key="item.skuId">
					<GoodsItem
							:excelInfo="defaultItem"
						:goodsInfo="item"
						:hideSubTotal="true"
						:showMatchIcon="item.showMatchIcon"
						@addBuyPlan="addBuyPlan"
						:showSearchVis="showSearchVis"
					/>
				</div>
			</div>
			<div v-else class="noGoods">
				<div>
					<img src="../../../../assets/images/xyy-sorry.png" alt="">
					<span class="text">暂无数据</span>
				</div>
			</div>
		</div>

    <!-- <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleOk(1)">确定</el-button>
				<el-button type="primary" @click="handleOk(2)">保持并继续</el-button>
      </span>
    </template> -->
  </el-dialog>
  
</template>

<script setup>
import { ref, onMounted, computed ,inject} from 'vue'
import { quickSearchProduct,bigSearchGoods } from '@/http_pc/api';
import GoodsItem from './goodsItem.vue';
import { ElMessage,ElMessageBox } from 'element-plus'

	const props = defineProps({
		showSearchVis: {
			default: false
		},
		// cancelDialog: {
		// 	type: Function,
		// 	default: () => {}
		// },
		changeNextActive: {
			type: Function,
			default: () => {}
		},
		defaultItem: {
			default: {}
		},
		addSkus: {
			type: Function,
			default: () => {}
		},
		allList: {
			default: [],
		},
		curtab:{
			default:1
		}
	});
	const manufacturerList = ref([])
	const specList = ref([])
	const emit = defineEmits(['cancelDialog','replaceData']);
	const sendGrandson = inject('replaceDataFromBigSearch');
	// // 子组件定义自己的visible
	const showSearchVis = computed({
		get: () => props.showSearchVis,
		set: (value) => emit("cancelDialog", value),
	})

	const formModel = ref({
		generalName:  '',
		spec: '',
		manufacturerName: '',
		sort: 3,
		shopCode: '',
	});

	const defaultIds = ref([]);
	const activeItem = ref(JSON.parse(JSON.stringify(props.defaultItem)));
	const list = ref([]);
	const shopList = ref([]);
	const loading = ref(false);

	const addBuyPlan = (item)=>{
		ElMessageBox.confirm(
   			 '确定后将替换掉采购计划中此品种的购买活动',
    		 '温馨提示',
    {
      confirmButtonText: '确定',
      type: 'warning',
    }
  )
    .then(() => {
      ElMessage({
        type: 'success',
        message: '已更新采购明细',
      })
	  //emit('replaceData',item,props.defaultItem.lineNum)
	  showSearchVis.value = false
	  sendGrandson(item,props.defaultItem.lineNum,props.defaultItem)


    })
    .catch(() => {
     
    })
	}

	const getBigSearchParams = () =>{
		return{
			generalName:formModel.value.generalName,
			lineNum:props.defaultItem.lineNum,
			buyNum:props.defaultItem.excelBuyNum,
			manufacturerName:formModel.value.manufacturerName,
			spec:formModel.value.spec,
			shopCode:formModel.value.shopCode,
			sort:formModel.value.sort,
			skuId:props.defaultItem.skus[0]?.skuId,
		}
	}
	const getDefaultIds = (activeItem) => {
		(activeItem.skus || []).forEach((i) => {
			i.selectStatus = 0;
			defaultIds.value.push(i.skuId);
		});
	}

	const initSearch = (activeItem) => {
		formModel.value = {
			generalName: activeItem.excelCommonName || '',
			spec: '',
			manufacturerName:  '',
			sort: 3,
			shopCode: '',
		};
		manufacturerList.value.push(activeItem.excelManufacturer)
		specList.value.push(activeItem.excelSpec)
	}

	const getNewActiveItem = (newItem) => {
		if (newItem) {
			initSearch(newItem);
			getDefaultIds(newItem);
			activeItem.value = newItem;
			getList();
		} else {
			ElMessage.warning('已经是最后一条了');
		}
	}

	defineExpose({ getNewActiveItem });

 	onMounted(() => {
		initSearch(props.defaultItem);
		getDefaultIds(JSON.parse(JSON.stringify(props.defaultItem)));
    getList(1);
  })

	const toSerachForm = () => {
	
		// if (formModel.value.shopCode) {
		// 	list.value = list.value.filter(i => i.shopCode === formModel.value.shopCode);
		// } else {
		// 	getList();
		// }
		getList();
	}

	const getList = (type) => {
		loading.value = true;
		bigSearchGoods({
			...getBigSearchParams()
		}).then((res) => {
			loading.value = false;
			list.value = (res.result || {}).matchSkuList || [];
			if(type==1)
			shopList.value = (res.result || {}).shopList || [];
			manufacturerList.value = (res.result || {}).manufacturerList
			specList.value = (res.result || {}).specList
			// 打标前置
			list.value.forEach((item, index) => {
				if (defaultIds.value.indexOf(item.skuId) > -1) {
					list.value.splice(index, 1);
					list.value.unshift(item);
					item.showMatchIcon = true;
				}
			});
		}).catch(() => {
			loading.value = false;
		})
	}

	const handleClose = () => {
		// props.cancelDialog();
		showSearchVis.value = false;
	}
	const handleOk = (type) => {
		let tempAddList = [];
		tempAddList = list.value.filter(i => i.selectStatus);
		if (tempAddList.length === 0) {
			ElMessage.error('请选择一条数据');
			return;
		}

		let sameLineNum = []
		props.allList.forEach((excel) => {
			(excel.skus || []).forEach((sku) => {
				tempAddList.forEach((item) => {
					if (sku.skuId === item.skuId && excel.lineNum !== activeItem.value.lineNum) {
						sameLineNum.push(excel.lineNum);
					}
				})
			})
		})

		if (Array.from(new Set(sameLineNum)).length) {
			ElMessage.error(`序号行：${Array.from(new Set(sameLineNum)).join()}已存在相同商品，无法添加`);
			return;
		}

		props.addSkus(tempAddList, type);
		// 保持并继续
		if (type === 2) {
			props.changeNextActive(activeItem.value.excelId);
		}
	}

	const reset = () => {
		formModel.value = {
			generalName: formModel.value.generalName,
			spec: '',
			manufacturerName: '',
			sort: 3,
			shopCode: '',
		};
		getList();
	}

</script>
<style scoped lang="scss">
.dialog-footer button:first-child {
  margin-right: 10px;
}
.hr {
	width: 100%;
	height: 10px;
	background: #F4F4F4;
}
.btnBox {
	padding-bottom:20px;
}
.itemBox {
	max-height: 410px;
	overflow-y: scroll;
}
.noGoods{
  position: relative;
  text-align: center;
  height: 140px;
  margin: 90px 0;
  img{
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 188px;
    height: 128px;
  }
  .text{
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    color: #A9AEB7;
  }
}
.el-form--inline .el-form-item {
	margin-right: 0;
}
</style>
