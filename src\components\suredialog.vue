<!-- 有确认按钮的提示 -->
<template>
	<div class="sureDialog">
		<div class="surecontent">
			<div class='dialog'>
				{{suredialog}}
			</div>
			<div class="ensure" @click="closeDialog">我知道啦</div>
		</div>
	</div>
</template>

<script setup>
  import { useStore } from "vuex";
  import { ref } from 'vue';
  const store = useStore();
  const props = defineProps({
    suredialog: {
      default: ''
    }
  });
  function closeDialog(){
    store.commit('app/changesureDialog', { dialogmsg: '', showsureDialog: false })
  }
</script>

<style lang="scss" scoped>
	.sureDialog{
		background-color: rgba(0,0,0,0.6);
		width:100%;
		height:100%;
    position: fixed; 
    left: 0;
    top: 0;
    display:flex;
    display:-webkit-flex;
    justify-content:center;
    -webkit-justify-content:center;
    align-items:center;
    -webkit-align-items:center;
    z-index:100;
    .surecontent{
			width: 5.6rem;
      height: 2.9rem;
      background: #fff;
      border-radius: 8px;
      .dialog{
        font-size: 14px;
        color: #000;
        line-height:18px;
        font-weight: bold;
        padding: 0.36rem 0.25rem 0.26rem;
        border-bottom: 1px solid #eee;
        height:1.48rem;
        display:flex;
        -webkit-display:flex;
        align-items:center;
        -webkit-align-items:center;
        justify-content:center;
        -webkit-justify-content:center;
			}
			.ensure{
        font-size:16px;
        line-height:.8rem;
        text-align:center;
        color: #30b6ff;
        font-weight: bold;
			}
    }
	}
</style>