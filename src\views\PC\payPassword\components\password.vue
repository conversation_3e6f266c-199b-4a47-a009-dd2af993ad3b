<template>
	<div :id="id" class="xx-pwd" @keydown="keydown">
		<input v-for="(item, i) in dataList" :key="i" :sub="i" :type="item.type" :value="item.value" :style="{width: size, height:size, borderColor: item.borderColor }" @blur="validate" maxlength="0"/>
	</div>
</template>
<script setup>
import { defineProps, onMounted, ref, watch } from 'vue';
import { useFormItem } from 'element-plus';
import { debugWarn } from "element-plus/es/utils/error"
const prop = defineProps(['length', 'size', 'value', 'second'])
const emit = defineEmits(['update:value', 'blur'])
const form = useFormItem();
const id = ref(String(Math.random()))
const dataList = ref([]);
const current = ref('');
const during = prop.second*1000;
for(let i = 0; i < prop.length; i++) {
	dataList.value.push({
		type: 'text',
		value: '',
		origin: '',
		borderColor: '',
		now: 0,
		el: null,
		t: null
	})
}
onMounted(() => {
	const inputList = document.getElementById(id.value).children;
	for(let i = 0; i < prop.length; i++) {
		dataList.value[i].el = inputList[i]
	}
})
function setFocus(index) {
	const timer = setTimeout(() => {
		if (!dataList.value[index]) return ;
		current.value = index;
		dataList.value[index].el.focus();
		clearTimeout(timer);
	},0)
} 
const keydown = (e) => {
	const index = Number(e.target.getAttribute('sub'));
	if (isNaN(index)) {
		sendData();
		return
	};
	if (e.key == 'Backspace') {
		clearTimeout(dataList.value[index].t);
		dataList.value[index].borderColor = '';
		dataList.value[index].value = '';
		dataList.value[index].type = 'text';
		setFocus(index - 1);
	}
	if (!isNaN(e.key) && e.key != ' ') {
		dataList.value[index].borderColor = '#00b377';
		dataList.value[index].value = e.key;
		hidden(index);
		setFocus(index + 1);
	}
	switch(e.key) {
		case 'ArrowLeft':
			setFocus(index - 1);
			break;
		case 'ArrowRight':
			setFocus(index + 1);
			break;
	}
	sendData();
	validate();
}
//将前面已经输入的给隐藏掉, during秒后将当前的给隐藏掉
const hidden = (index) => {
	for (let i = 0; i <= index - 1; i++) {
		dataList.value[i].type = 'password';
	}
	//记录输入当前值的时间
	const n = Date.now();
	//间隔小于1s，清除计时器记录时间
	if (n - dataList.value[index].now < during) {
		clearTimeout(dataList.value[index].t);
	} 
	dataList.value[index].t = setTimeout(() => {
		dataList.value[index].type = 'password';
		clearTimeout(dataList.value[index].t)
	}, during)
	dataList.value[index].now = n;
}

const sendData = () => {
	let result = '';
	dataList.value.forEach((item) => {
		result += item.value;
	})
	emit("update:value", result);
}
const validate = async () => {
	form.formItem.validate('blur', (isValid) => {
		if (isValid) {
			dataList.value.forEach(item => {
				item.borderColor = '#00b377';
			})
		} else {
			let target = true;
			dataList.value.forEach(item => {
				if (!item.value) {
					target = false;
					item.borderColor = 'red';
				}
			})
			if(target) {
				dataList.value.forEach(item => item.borderColor = 'red')
			}
			debugWarn(err)
		}
	});
	/* try {
		dataList.value.forEach(item => {
			item.borderColor = '#00b377';
		})
		await form.formItem.validate('blur');
	} catch(err) {
		let target = true;
		dataList.value.forEach(item => {
			if (!item.value) {
				target = false;
				item.borderColor = 'red';
			}
		})
		if(target) {
			dataList.value.forEach(item => item.borderColor = 'red')
		}
		debugWarn(err)
	} */
}
</script>
<style>
.xx-pwd {
	display: flex;
	width: 100%;
	gap: 10px;
}
.xx-pwd input {
	outline: none;
	border: 1px solid #dcdfe6;
	border-radius: 5px;
	text-align: center;
	transition: all 0.2s;
}
</style>