<template>
  <div class="groupProductBox">
    <a @click="toDetail(groupData.id)">
      <!-- 价格  -->
      <div class="controlTitleText" v-if="groupData.controlTitle && groupData.controlType != 5">
        {{ groupData.controlTitle }}
      </div>
      <div class="price-wrap" v-else>
        <div class="price-container">
          <span class="priceDec">￥</span>
          <span v-if="activityStatus === 'inProgress'">
            <!-- 多阶梯价格 -->
            <span v-if="(groupData.actPt || {}).stepPriceStatus == 1">
              <span class="priceInt">{{String((groupData.actPt || {}).minSkuPrice.toFixed(2)).split('.')[0]}}</span>
              <span class="priceFloat">.{{String((groupData.actPt || {}).minSkuPrice.toFixed(2)).split('.')[1] || '00'}}</span>
              起
            </span>
            <!-- 单阶梯价格 -->
            <span v-if="(groupData.actPt || {}).stepPriceStatus == 2">
              <span v-if="groupData.limitFullDiscountActInfo?.limitFullDiscount > 0">
                <span class="priceInt">{{String(groupData.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[0]}}</span>
                <span class="priceFloat">.{{String(groupData.limitFullDiscountActInfo.limitFullDiscount.toFixed(2)).split('.')[1] || '00'}}</span>
              </span>
              <span v-else>
                <span class="priceInt" v-if="activityStatus === 'inProgress'">{{String((groupData.actPt || {}).assemblePrice.toFixed(2)).split('.')[0]}}</span>
                <span class="priceFloat" v-if="activityStatus === 'inProgress'">.{{String((groupData.actPt || {}).assemblePrice.toFixed(2)).split('.')[1] || '00'}}</span>
              </span>
            </span>
          </span>
          <span class="priceInt" v-if="activityStatus === 'notStart'">??</span>
          <span class="priceFloat" v-if="activityStatus === 'notStart'">.??</span>
        </div>
        <!-- <div class="retailPrice" v-if="(groupData.actPt || {}).stepPriceStatus == 2">￥{{groupData.retailPrice.toFixed(2)}}</div> -->
         <div v-if="activityStatus === 'inProgress' && groupData.zheHouPrice && (((groupData.actPt || {}).stepPriceStatus == 2 && Number(groupData.zheHouPrice.split('￥')[1]) < (groupData.actPt || {}).assemblePrice) || (Number(groupData.zheHouPrice.split('￥')[1]) < (groupData.actPt || {}).minSkuPrice))" class="zheHouPrice">折后约￥{{ (groupData.zheHouPrice).split('￥')[1] || '' }}</div>
      </div>
      <!-- 标题 -->
      <div class="commonName">
        <!-- 药名和规格 -->
        <span class="name">{{ groupData.showName.trim() }}{{ groupData.spec }}</span>
        <!-- <span class="spec">{{ groupData.spec }}</span> -->
      </div>
      <div class="tagBox">
        <template v-for="(tag) in groupData.tags.productTags" :key="tag.text">
          <span v-if="tag.text == '次日达'">
              <span
                class="tagItem"
                v-if="tag.text && tag.uiStyle == 1"
                :style="{ color: parseRGBA(tag.textColor), background: parseRGBA(tag.bgColor), border: `1px solid ${parseRGBA(tag.borderColor)}`,padding: `1px 0px 1px 2px` }"
              >
              <img v-if="tag.pcIcon" style="height: 14px; width: auto; object-fit: cover;display: inline-block;" :src="imgUrl + tag.pcIcon">
              {{tag.text }}
            </span>
          </span>
        </template>
        <span v-for="(tag, index) in (groupData.tags.couponTags || [])" :key="tag.text">
          <span
            class="tagItem"
            v-if="tag.text  && tag.subType == 9 && index == 0"
            :style="{ color: parseRGBA(tag.textColor), background: parseRGBA(tag.bgColor), border: `1px solid ${parseRGBA(tag.borderColor)}`,padding: `1px 0px 1px 2px` }"
          >{{ tag.text }}</span>
        </span>
        <template v-for="(tag, index) in groupData.tags.productTags" :key="tag.text">
          <span v-if="tag.text != '次日达'">
              <span
                class="tagItem"
                v-if="tag.text && tag.uiStyle == 1 && index < (3 - ((groupData.tags.couponTags || []).length > 0 ? 1 : 0))"
                :style="{ color: parseRGBA(tag.textColor), background: parseRGBA(tag.bgColor), border: `1px solid ${parseRGBA(tag.borderColor)}` }"
              >
              <img v-if="tag.pcIcon" style="height: 14px; width: auto; object-fit: cover;display: inline-block;" :src="imgUrl + tag.pcIcon">
              {{ tag.text }}
            </span>
          </span>
        </template>
        <span v-for="(tag, index) in groupData.tags.dataTags" :key="tag.text">
          <span
            class="tagItem"
            v-if="tag.text && tag.uiStyle == 1 && index < (3- ((groupData.tags.couponTags || []).length > 0 ? 1 : 0) - ((groupData.tags.productTags || []).length > 3 ? 3 : (groupData.tags.productTags || []).length))"
            :style="{ color: parseRGBA(tag.textColor), background: parseRGBA(tag.bgColor), border: `1px solid ${parseRGBA(tag.borderColor)}` }"
          >{{ tag.text }}</span>
        </span>
      </div>
      <div class="manufacturer textellipsis">{{ groupData.manufacturer }}</div>
      
      <!-- 已拼/起拼 -->
      <div class="subsidy" v-if="groupData?.limitFullDiscountActInfo && groupData?.limitFullDiscountActInfo?.endTime > 0">
        <div class="subsidyLeft">
          <div class="image"></div>
        </div>
        <div class="subsidyRight">
          <div class="font">
            <CountdownTimer :endTime="Date.now() + groupData?.limitFullDiscountActInfo.endTime"/>
          </div>
        </div>
      </div>
      <div class="collageBox" v-else>
        <div class="startedText" v-if="activityStatus === 'inProgress'">
          <div class="orderNumText">已拼{{(groupData.actPt || {}).orderNum}}{{groupData.productUnit}}</div>
          <div class="skuStartNumText">{{(groupData.actPt || {}).skuStartNum}}{{groupData.productUnit}}起拼</div>
        </div>
        <div class="noStartText" v-if="activityStatus === 'notStart'">
          距开始&nbsp;
          <groupLimitTime :endTime="(groupData.actPt || {}).assembleStartTime" timeColor="#01B377" />
        </div>
      </div>
      <div class="self-support">
        <span
          style="marginRight: 2px; flex-shrink: 0;"
          v-if="(groupData.tags || {}).titleTags && (groupData.tags || {}).titleTags.length"
        >
          {{ ((groupData.tags || {}).titleTags[0] || {}).text }}
        </span>
        <span class="shopName" :class="{maxWidthName: (groupData.tags.purchaseTags || []).filter(i=>i.type==9).length && (groupData.tags || {}).titleTags && (groupData.tags || {}).titleTags.length}">{{ groupData.shopName }}</span>
        <span class="purchaseTags" v-if="(groupData.tags.purchaseTags || []).filter(i=>i.type==9).length">{{ ((groupData.tags.purchaseTags || []).filter(i=>i.type==9)[0] || {}).text }}</span>
      </div>
    </a>
  </div>
</template>
<script setup>
import groupLimitTime from './groupLimitTime.vue';
import CountdownTimer from './CountdownTimer.vue';
import { onMounted, ref, watch } from "vue";
import { actionTracking } from '@/config/eventTracking';
  const props = defineProps({
    groupData: {
      default: {}
    },
    trackInfo: {
      default: {}
    },
    licenseStatus: {
      default: 0,
    },
  });

  const activityStatus = ref(((props.groupData || {}).actPt || {}).assembleStatus === 0 ? 'notStart' : ((props.groupData || {}).actPt || {}).assembleStatus === 1 ? 'inProgress' : '')

  const BASEURL = import.meta.env.VITE_BASE_URL_PC;
  const imgUrl = import.meta.env.VITE_IMG;
  const target = ref(false);
  function parseRGBA(val) {
    val = val.trim().toLowerCase();  //去掉前后空格
    if (val.length > 8) {
      let color = {};
      try {
        let argb = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(val);
        color.r = parseInt(argb[2], 16);
        color.g = parseInt(argb[3], 16);
        color.b = parseInt(argb[4], 16);
        color.a = parseInt(argb[1], 16) / 255;
      } catch (e) {
        console.log(e)
      }
      return 'rgba(' + color.r + ',' + color.g + ',' + color.b + ',' + parseFloat(color.a).toFixed(1) + ')';
    } else {
      return val;
    }
  }

  // const activityStatus = () => {
  //   console.log('??????????', (props.groupData || {}).actPt);
  //   return ((props.groupData || {}).actPt || {}).assembleStatus === 0 ? 'notStart' : ((props.groupData || {}).actPt || {}).assembleStatus === 1 ? 'inProgress' : ''
  // }
  
  const toDetail = (id) => {
    actionTracking('pc_page_CommodityDetails', {
      spid: props.trackInfo.spid,
      sid: props.trackInfo.sid,
      sptype: props.trackInfo.sptype,
      user_id: proxy.$merchantId,
      commodityId: id,
    })
    window.open(`${BASEURL}search/skuDetail/${id}.htm`);
  }
  
</script>

<style lang="scss" scoped>
.subsidy {
    padding: 0;
    margin: 0;
    width: 100%;
    display: flex;
    // justify-content: space-between;
    align-items: center;
    height: 30px;
    z-index: 12;
    left: 0;
    .subsidyLeft{
      display: flex;
      align-items: center;
      padding: 0;
      margin: 0;
      background-color: red;
      border: red 1px solid;
      height: 20px;
      border-radius: 3px 0 0 3px;
    }
    .subsidyRight{
      display: flex;
      align-items: center;
      padding: 0;
      margin: 0;
      border: red 1px solid;
      height: 20px;
      border-radius: 0 3px 3px 0;
    }
    .font {
      margin-right: 5px;
    }
    .image {
      width: 70px;
      background: url('../assets/images/xsSubsidy1.svg') no-repeat center;
      background-position: center;
      background-size: auto;
      height: 12px;
      margin-left: 4px;
    }
  }
.groupProductBox {
  padding: 4px 0 0 0;
  .commonName {
    font-size: 16px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
    line-height: 22px;
    text-overflow: ellipsis;
    overflow: hidden;
    // white-space: nowrap;
    display: -webkit-box;
    /* autoprefixer: off */
    -webkit-box-orient: vertical;
    /* autoprefixer: on */
    -webkit-line-clamp: 2;
    .spec {
      margin-top: 4px;
      opacity: 0.8;
      font-size: 12px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: left;
      color: #666660;
      line-height: 18px;
    }
  }
  .tagBox {
    margin: 6px;
    margin-left: 0;
    white-space: nowrap;
    .tagItem {
      margin-right: 4px;
      padding: 1px 4px;
      font-size: 12px;
      border-radius: 4px;
    }
  }
  .controlTitleText {
    color: #f49926;
    font-weight: 500;
    font-size: 18px;
  }
  .price-wrap {
    display: flex;
    align-items: baseline;
    font-weight: Medium;
    .price-container {
      color: #FF2121;
      span {
        font-weight: bold;
      }
      .priceDec {
        font-size: 14px;
      }
      .priceInt {
        font-size: 22px;
      }
      .priceFloat {
        font-size: 18px;
      }
    }
    .zheHouPrice {
      color: #ff2121;
      font-size: 12px;
      font-weight: 400;
      margin-left: 6px;
    }
    .retailPrice {
      font-size: 12px;
      color: #666666;
      text-decoration: line-through;
      margin-left: 8px;
    }
  }
  .manufacturer {
    background: none;
    font-size: 12px;
    color: #999999;
    margin: 2px 0 8px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .couponBg-zp {
    // margin-bottom: 10px;
    // width: 200px;
    display: inline-block;
    // color: #F1081C;
    // height: 24px;
    // line-height: 24px;
    // background: url('../assets/images/search/coupon.png') no-repeat;
    // background-size: 100% 100%;
    margin-right: 8px;
    padding: 1px 4px;
    font-size: 12px;
    border-radius: 4px;
  }
  .collageBox {
    display: flex;
    align-items: center;
    font-size: 14px;
    .startedText {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 200px;
      height: 24px;
      background: url(../assets/images/search/ptTime.png) no-repeat;
      background-size: 100%;
      padding: 0 10px;
      .orderNumText {
        // font-size: 16px;
        color: #FFFFFF;
      }
      .skuStartNumText {
        color: #282828;
      }
    }
    .noStartText {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 200px;
      height: 24px;
      background: url('../assets/images/search/notStart.png') no-repeat;
      background-size: 100%;
      padding: 0 30px;
      color: #222222;
    }
  }
  .self-support {
    margin-top: 6px;
    height: 21px;
    display: flex;
    align-items: center;
    span {
      background-color: #00B955;
      border-radius: 2px;
      color: #ffffff;
      font-size: 12px;
      padding: 2px;
      display: inline-block;
    }
    .shopName {
      flex-wrap: nowrap;
      background: none;
      font-size: 12px;
      color: #999999;
      display: inline-block;
      flex-grow: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .maxWidthName {
      max-width: 68px;
    }
    .purchaseTags {
      background: none;
      color: #009D48;
    }
  }
}
</style>