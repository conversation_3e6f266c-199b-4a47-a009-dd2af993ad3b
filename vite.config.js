import vue from '@vitejs/plugin-vue'
import legacy from '@vitejs/plugin-legacy'
import { resolve } from 'path'

const pathResolve = (dir) => {
  return resolve(__dirname, ".", dir)
}

const alias = {
  '@': pathResolve("src")
}

// https://vitejs.dev/config/
export default ({ command }) => {
  const prodMock = true;
  return {
    base: '/newstatic/',
    resolve: {
      alias
    },
    // 打包配置
    build: {
      target: 'es2015',
      outDir: 'dist', //指定输出路径
      // minify: 'terser', // 混淆器，terser构建后文件体积更小
      // terserOptions: {
      //   compress: {
      //     drop_console: process.env.NODE_ENV === 'production'
      //   }
      // },
    },
    plugins: [
      vue(),
      legacy({
        targets: ['defaults', 'ie >= 11', 'chrome 48'], //需要兼容的目标列表，可以设置多个
        additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
      })
    ],
    server: {
      port: 3003,
      host: '0.0.0.0',
      open: true,
      proxy: { // 代理配置
        '/api': {
          // target: 'https://yapi.int.ybm100.com', // mock接口
          //target: 'https://new-app.test.ybm100.com/', // app代理接口域名
          target: 'https://new-www.test.ybm100.com/', // pc代理接口域名
          // target: 'http://localhost:8016/', // pc本地
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
          configure: proxy => {
            proxy.on("proxyReq", function (proxyReq, req) {
                    proxyReq.setHeader("Origin", "https://new-www.test.ybm100.com/")
                    proxyReq.setHeader(
                            "Cookie",
                            "web_cdaeb9bc6854423b9ab74b1827bc21a0=%7B%22sid%22%3A%200%2C%22updated%22%3A%200%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFIM%E5%B7%A5%E5%8D%95%E7%B3%BB%E7%BB%9F%5C%22%7D%22%7D; web_ec48e14052624b3294d74921b8c257ca=%7B%22sid%22%3A%200%2C%22updated%22%3A%200%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFIM%E5%B7%A5%E5%8D%95%E7%B3%BB%E7%BB%9F%5C%22%7D%22%7D; uid=rBQRYWhORpqXRlkDCCYcAg==; _abfpc=b5804dcb6a04ebf71eb926f45b8a3a93b7042d13_2.0; cna=b479add222b191174aed70a4b95b6a80; web_dc504156a0b54a04bd19c57da5422a32=%7B%22sid%22%3A%200%2C%22updated%22%3A%200%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%7D; xyy_token=eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************.xgTolkKF7s2NpTs5toCmH8Vjufpt4g6QeQ40SG9ZErGeSdjfTBQJJ9YAJTrwNzcIwg5iFJgGWrLs77yj9JlQMA; xyy_principal=1500123586&YjA2OWE3NGQzZGQ4MGYxOGIxNmQ4NDhjZWNjMDRkMzE1YWViMmIzZQ&1500123586; xyy_last_login_time=1749960392945; xyy=******************************; jg_login=0; isDeviceUpload=1; TGC=eyJhbGciOiJIUzUxMiJ9.ZXlKNmFYQWlPaUpFUlVZaUxDSmhiR2NpT2lKa2FYSWlMQ0psYm1NaU9pSkJNVEk0UTBKRExVaFRNalUySW4wLi5rWFVtLW1RWnF1amlSdng1aTZPNlZ3LlRpUTVwY1ZoYXZvVzFSclVNYXNYUmEyWUFiWkVMbHFCNEZzbkpjYlZCQjNjYjJNbnFLVHRXbXFsdkNOR2p1V1kwT2pEdWw2c3BIMzA0dlZsVkZMS3ZDcERNM082V2lWWWZfVU9lSEYwbUZsOG1BM1pYNzdIQlhsSUJJMHo0bGhtZXJVZUV3RHUxaFQyY1JoY1dKZFByQTM4Mmx6d3dqeW1FRnJVYms4OHNkMVVTbUl5ZjJJVkZPU09VRlNJZWtpTkc1YWFJSW9LUFgzSjdGVndUVkw4MXhHVlkyMGxFYUpxT1BiYVMwTURiMjZFcUZybVh3Mjd0Rkd2TEdaazFSdmlGcXNSczY4YWVjSjI0VG81QXQ5VDVBLmMtc0phZWx4cTFaMnBCOXczSjRwNXc=.vs4r-VHV10bGsDRv0y48Xvgs24WiDnHlfdogzK047eaf5V6dHgMdePS0KuhdLNZChp77R1i_acGtVPwipgRGXQ; Hm_lvt_9f4bfe0c69e174e92f282183ee72bed2=**********,**********; HMACCOUNT=11E5C9A0BDDC97E7; web_did=%7B%22did%22%3A%20%22b27ebdab-071f-48d8-ac82-80afd0e4e4a7%22%7D; web_info=%7B%22sid%22%3A%***************%2C%22updated%22%3A%***************%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%2C%22referrerDomain%22%3A%20%22new-www.test.ybm100.com%22%2C%22cuid%22%3A%20%220%22%7D; Hm_lpvt_9f4bfe0c69e174e92f282183ee72bed2=**********; qt_session=w1SsYAAd_1750061960620"
                    )
            })
          }
        }
      },
    },
    css: {
      // css预处理器
      preprocessorOptions: {
        scss: {
          charset: false,
          additionalData: `@use "./src/assets/scss/common.scss" as *;`,
          // additionalData: '@import "./src/assets/scss/common.scss";', // 添加公共样式
        },
      },
    },
  }
}
