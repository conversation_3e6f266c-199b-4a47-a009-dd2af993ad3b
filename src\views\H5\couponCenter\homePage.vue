<template>
  <div class="homePageBox" v-if="hasData">
    <!-- 已领可用券 -->
    <div v-if="Object.keys(recommendReceived).length">
      <AvailableCoupons :couponInfo="recommendReceived" />
    </div>
    <!-- 新人券 -->
    <div v-if="newCustomerCoupon.length">
      <div class="couponTitle">新人券</div>
      <NewCustomerCoupons :couponList="newCustomerCoupon" />
    </div>
    <!-- 跨店券 -->
    <div v-if="platformCoupon.length">
      <div class="couponTitle">平台券</div>
      <PlatformCoupons :couponList="platformCoupon" />
    </div>
    <!-- 精选店铺券 -->
    <div v-if="shopCouponSelected.length">
      <div class="couponTitle">精选店铺券</div>
      <ShopSelectedCoupons :couponList="shopCouponSelected" :bottomTip="bottomTip" />
    </div>
  </div>
  <div class="noGoods" v-else>
    <div>
      <img src="../../../assets/images/xyy-sorry.png" alt="">
      <span class="text">暂无优惠券</span>
    </div>
  </div>
</template>
<script setup>
  import { ref, onMounted, getCurrentInstance } from "vue";
  import { useStore } from 'vuex';
  import { receiveCenterIndex, shopSelect } from '@/http/api';
  import { actionTracking } from '@/config/eventTracking';
  import AvailableCoupons from './components/availableCoupons.vue';
  import NewCustomerCoupons from './components/newCustomerCoupons.vue';
  import PlatformCoupons from './components/platformCoupons.vue';
  import ShopSelectedCoupons from './components/shopSelectedCoupons.vue';

  const store = useStore();
  const { proxy } = getCurrentInstance();
  const loading = ref(false);
  const indexLoading = ref(false);
  const recommendReceived = ref({}); // 已领可用券
  const newCustomerCoupon = ref([]); // 新人券
  const platformCoupon = ref([]); // 跨店券
  const shopCouponSelected = ref([]); // 精选店铺券
  const hasData = ref(false);
  const pageNo = ref(1);
  const isEnd = ref(false);
  const bottomTip = ref('');

  // 获取信息
  function getInfo() {
    indexLoading.value = true;
    store.commit('app/showLoading', true);
    receiveCenterIndex({
      merchantId: proxy.$merchantId,
    }).then((res) => {
      indexLoading.value = false;
      store.commit('app/showLoading', false);
      recommendReceived.value = res.recommendReceived || {};
      newCustomerCoupon.value = res.newCustomerCoupon || [];
      platformCoupon.value = res.platformCoupon || [];
      
      // shopCouponSelected.value = res.shopCouponSelected || [];
      hasData.value = Object.keys(recommendReceived.value).length || newCustomerCoupon.value.length || platformCoupon.value.length || shopCouponSelected.value.length;
    }).catch(() => {
      store.commit('app/showLoading', false);
      indexLoading.value = false;
    })
  }

  // 上拉加载
  function pullUpLoading() {
    let scrollHeight = document.documentElement.scrollHeight;
    let scrollTop = window.scrollY;
    let clientHeight = document.documentElement.clientHeight;
    if (scrollHeight - scrollTop - clientHeight < 30) {
      if (!isEnd.value && !loading.value) {
        getShopSelectInfo();
      }
    }
  };

  
  // 获取精选店铺券
  function getShopSelectInfo() {
    loading.value = true;
    if (pageNo.value > 1) {
      store.commit('app/showLoading', true);
    }
    shopSelect({
      merchantId: proxy.$merchantId,
      pageNo: pageNo.value,
      pageSize: 20,
    }).then((res) => {
      loading.value = false;
      store.commit('app/showLoading', false);
      shopCouponSelected.value = shopCouponSelected.value.concat(res.couponList || []);
      isEnd.value = res.isEnd;
      pageNo.value = res.nextPage;
      bottomTip.value = isEnd.value ? '没有更多了' : '查看更多';
      hasData.value = Object.keys(recommendReceived.value).length || newCustomerCoupon.value.length || platformCoupon.value.length || shopCouponSelected.value.length;
      (res.couponList || []).forEach((item) => {
        actionTracking('h5_couponCentre_Exposure', { couponId: item.templateId })
      });
    }).catch(() => {
      store.commit('app/showLoading', false);
      loading.value = false;
    })
  }

  onMounted(() => {
    getInfo();
    getShopSelectInfo();
    window.addEventListener('scroll', pullUpLoading);
  })

</script>
<style lang="scss" scoped>
.homePageBox {
  padding: 0 rem(30) rem(20);
  .couponTitle {
    color: #292933;
    font-size: rem(30);
    font-weight: bold;
    margin-top: rem(20);
    // margin-bottom: rem(10);
  }
}
.noGoods{
  position: relative;
  text-align: center;
  height: rem(210);
  margin: rem(200) 0;
  img{
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width:rem(264);
    height:rem(174);
  }
  .text{
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    color: #A9AEB7;
  }
}
</style>

