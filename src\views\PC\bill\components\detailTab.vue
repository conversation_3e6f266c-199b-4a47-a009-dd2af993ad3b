<template>
  <div class="tableBox">    
    <el-table
      :data="tableData"
      :row-key="actTab === 1 ? 'orderNo' : 'refundOrderNo'"
      :header-cell-style="{background:'#eee'}"
      style="width: 100%;overflow-y: scroll;"
      height="500"
      show-summary
      sum-text="全部汇总"
      :summary-method="getSummaries"
      v-loading="tableLoading"
      :expand-row-keys="expands"
      @expand-change="expandChange"
    >
      <template v-if="actTab === 1">
        <el-table-column type="expand" width="60">
          <template #default="props">
            <div>
              <el-table :data="props.row.detailData" border style="width: 90%;margin:0 auto;">
                <el-table-column label="名称" prop="productName" />
                <el-table-column label="单价" prop="productPrice" />
                <el-table-column label="数量" prop="productAmount" />
                <el-table-column label="应付" prop="subTotal" />
                <el-table-column label="优惠" prop="discountAmount" />
                <el-table-column label="实付" prop="realPayAmount" />
                <el-table-column label="已退款数量" prop="refundProductAmount" />
                <el-table-column label="实际退款金额" prop="actualRefundFee" />
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="付款时间" prop="payTime">
          <template #default="scope">
            <div style="display: flex; align-items: center">
              {{ formatDate(scope.row.payTime, 'yyyy-MM-dd hh:mm:ss') }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="订单编号" prop="orderNo">
          <template #default="scope">
            <div class="activeText" @click="toOrderDetail(scope.row.id)">{{ scope.row.orderNo }}</div>
          </template>
        </el-table-column>
        <el-table-column label="应付(￥)" prop="totalAmount" />
        <el-table-column label="优惠(￥)" prop="discount" />
        <el-table-column label="实付(￥)" prop="money">
          <template #default="scope">
            <div style="display: flex; align-items: center">
              {{ scope.row.money }}
              <el-tooltip
                :content="`${scope.row.payTypeStr}${scope.row.payChannelStr ? `(${scope.row.payChannelStr})` : ''}`"
                placement="top"
              >
                <el-icon><Warning /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="已退款数量" prop="sumProductAmount" />
        <el-table-column label="实际退款金额(￥)" prop="refundFee">
          <template #default="scope">
            <div style="display: flex; align-items: center">
              {{ scope.row.refundFee }}
              <el-tooltip
                content="退款成功时间在当前时间段内且状态为退款成功的退款单总实退金额"
                placement="top"
              >
                <el-icon><Warning /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="店铺" prop="shopName">
          <template #default="scope">
            <div style="display: flex; align-items: center">
              {{ scope.row.shopName || '-' }}
              <el-tooltip
                :content="scope.row.companyName"
                placement="top"
              >
                <el-icon><Warning /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </template>
      <template v-if="actTab === 2">
        <el-table-column type="expand" width="60">
          <template #default="props">
            <div>
              <el-table :data="props.row.detailData" border style="width: 90%;margin:0 auto;">
                <el-table-column label="名称" prop="productName" />
                <el-table-column label="单价" prop="productPrice" />
                <el-table-column label="已退款数量" prop="productAmount" />
                <el-table-column label="扣除优惠" prop="discountAmount" />
                <el-table-column label="实际退款金额" prop="refundFee" />
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="付款时间" prop="payTime">
          <template #default="scope">
            <div style="display: flex; align-items: center">
              {{ formatDate(scope.row.payTime, 'yyyy-MM-dd hh:mm:ss') }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="订单编号" prop="orderNo">
          <template #default="scope">
            <div class="activeText" @click="toOrderDetail(scope.row.id)">{{ scope.row.orderNo }}</div>
          </template>
        </el-table-column>
        <el-table-column label="退款时间" prop="refundAuditTime">
          <template #default="scope">
            <div style="display: flex; align-items: center">
              {{ formatDate(scope.row.refundAuditTime, 'yyyy-MM-dd hh:mm:ss') }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="已退款数量" prop="refundNum" />
        <el-table-column label="扣除优惠" prop="refundDiscount" />
        <el-table-column label="实际退款金额(￥)" prop="refundFee" />
        <el-table-column label="配送商" prop="shopName">
          <template #default="scope">
            <div style="display: flex; align-items: center">
              {{ scope.row.shopName || '-' }}
              <el-tooltip
                :content="scope.row.companyName"
                placement="top"
              >
                <el-icon><Warning /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </template>
      <template v-if="actTab === 3">
        <el-table-column type="expand" width="60">
          <template #default="props">
            <div>
              <el-table :data="props.row.detailData" border style="width: 90%;margin:0 auto;">
                <el-table-column label="名称" prop="productName" />
                <el-table-column label="单价" prop="productPrice" />
                <el-table-column label="退款中数量" prop="productAmount" />
                <el-table-column label="扣除优惠" prop="discountAmount" />
                <el-table-column label="退款中金额" prop="refundFee" />
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="付款时间" prop="payTime">
          <template #default="scope">
            <div style="display: flex; align-items: center">
              {{ formatDate(scope.row.payTime, 'yyyy-MM-dd hh:mm:ss') }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="订单编号" prop="orderNo">
          <template #default="scope">
            <div class="activeText" @click="toOrderDetail(scope.row.id)">{{ scope.row.orderNo }}</div>
          </template>
        </el-table-column>
        <el-table-column label="申请退款时间" prop="refundCreateTime">
          <template #default="scope">
            <div style="display: flex; align-items: center">
              {{ formatDate(scope.row.refundCreateTime, 'yyyy-MM-dd hh:mm:ss') }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="退款中数量" prop="refundNum" />
        <el-table-column label="扣除优惠(￥)" prop="refundDiscount" />
        <el-table-column label="退款中金额(￥)" prop="refundFee" />
        <el-table-column label="配送商" prop="shopName">
          <template #default="scope">
            <div style="display: flex; align-items: center">
              {{ scope.row.shopName || '-' }}
              <el-tooltip
                :content="scope.row.companyName"
                placement="top"
              >
                <el-icon><Warning /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script setup>
  import { onMounted, ref, watch } from "vue";
  import { queryMyPurchaseOrderDetailFee, queryMyPurchaseRefunddetailFee, queryMyPurchaseRefundingdetailFee } from '@/http_pc/api';
  import { formatDate } from '../../../../utils/index';
  const props = defineProps({
    actTab: {
      default: 1
    },
    dataSource: {
      default: []
    },
    tableLoading: {
      default: false,
    }
  });
	
  const tableData = ref(props.dataSource);
  const expands = ref([]);

  const expandChange = (row, expandedRows) => {
    expands.value = [];
    if (expandedRows.length > 0) {
      row ? expands.value.push(props.actTab === 1 ? row.orderNo : row.refundOrderNo) : ''
    }
    if (props.actTab === 1) {
      queryMyPurchaseOrderDetailFee({ orderNo: row.orderNo }).then((res) => {
        row.detailData = res.data || [];
        if (row.detailData.length) {
          row.detailData.push({
            productName: '运费',
            realPayAmount: row.freightAmount || '-',
            actualRefundFee: row.refundFreightAmount || '-',
            productPrice: '-',
            productAmount: '-',
            subTotal: '-',
            discountAmount: '-',
            refundProductAmount: '-',
          })
        }
      })
    } else if (props.actTab === 2) {
      queryMyPurchaseRefunddetailFee({ refundOrderNo: row.refundOrderNo }).then((res) => {
        row.detailData = res.data || [];
        if (row.detailData.length) {
          row.detailData.push({
            productName: '运费',
            refundFee: row.freightAmount || '-',
            productPrice: '-',
            productAmount: '-',
            discountAmount: '-',
          })
        }
      })
    } else if (props.actTab === 3) {
      queryMyPurchaseRefundingdetailFee({ refundOrderNo: row.refundOrderNo }).then((res) => {
        row.detailData = res.data || [];
        if (row.detailData.length) {
          row.detailData.push({
            productName: '运费',
            refundFee: row.freightAmount || '-',
            productPrice: '-',
            productAmount: '-',
            discountAmount: '-',
          })
        }
      })
    }
  }

  const getSummaries = (param) => {
    const { columns, data } = param
    const sums = []
    columns.forEach((column, index) => {
      if (index === 0) {
        sums[index] = '全部汇总'
        return
      }
      const values = data.map((item) => Number(item[column.property]))
      if (column.label === '应付(￥)' || column.label === '优惠(￥)' || column.label === '实付(￥)' || column.label === '已退款数量' || column.label === '实际退款金额(￥)' || column.label === '扣除优惠' || column.label === '退款中数量' || column.label === '扣除优惠(￥)' || column.label === '退款中金额(￥)') {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr)
          if (!Number.isNaN(value)) {
            return ((prev * 1000) + (curr * 1000)) / 1000
          } else {
            return prev
          }
        }, 0)}`
      } else {
        sums[index] = '-'
      }
    })
    return sums
  }

  const toOrderDetail = (id) => {
    window.open(`/merchant/center/order/detail/${id}.htm`);
  }

  watch(
    () => props.dataSource,
    (new_val) => {
      if (new_val) {
        tableData.value = props.dataSource;
      }
    },
    {
      immediate: false,
      deep: true,
    },
  ),
  watch(
    () => props.actTab,
    (new_val) => {
      if (new_val) {
        expands.value = [];
      }
    },
  ),
  watch(
    () => props.tableLoading,
    (new_val) => {
      if (new_val) {
        // 每次重新请求接口清空expands
        expands.value = [];
      }
    },
  )
</script>
<style lang="scss" scoped>
  .activeText{
    color: #00B377;
  }
</style>
