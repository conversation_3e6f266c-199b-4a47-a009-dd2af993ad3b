<template>
<div class="merchant-details">
    <div class="tabs-container">
        <van-tabs v-model:active="activeName" color="green" @click-tab="handleChangeTab" sticky>
            <van-tab title="全部记录" name="all"></van-tab>
            <van-tab title="支出记录" name="pay"></van-tab>
            <van-tab title="收入记录" name="income"></van-tab>
        </van-tabs>
    </div>
    <div class="fontTitle">
        <div class="fontTitle-content">明细数据截止到昨日。仅支持展示180天内的数据，全部数据请至PC端查询</div>
    </div>
    <div class="content-container">
        <van-list
            v-model:loading="loading"
            :finished="finished"
            :finished-text="finishedText"
            @load="onLoad">
            <van-cell v-for="item in transcationList" :key="item.id">
                <div class="custom-cell">
                    <div class="left">
                        <div class="logLabel">
                            {{ item.tradeType }}
                        </div>
                        <div class="orderNo" @click=seeOrderDetails(item.businessOrderNo)>{{ item.businessOrderNo }}</div>
                    </div>
                    <div class="right">
                        <div class="amount" :style="{color: Number(item.tradeAmount ) < 0 ? 'green' : 'red'}">
                            <span v-if="Number(item.tradeAmount ) > 0">+</span>
                            {{item.tradeAmount }}
                        </div>
                        <div class="time">{{ item.payTimeStr }}</div>
                    </div>
                </div>
            </van-cell>
        </van-list>
    </div>
</div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { getPingAnMerchantDetail } from '@/http/pinganMerchant';
const activeName = ref('all');
const activeNo = ref(1)
const transcationList = ref([]);
const transcationListCopy = ref([]);
const loading = ref(false);
const finished = ref(false);
const finishedText = ref('没有更多了');
const pageNo = ref(1);

const route = useRoute();
const { merchantId } = route.query;

const handleChangeTab = (tab) => {
    switch(tab.name) {
        case 'all':
            activeNo.value = 1;
            break;
        case 'pay':
            activeNo.value = 3;
            break;
        case 'income':
            activeNo.value = 2;
            break;
    }
    // 重置页码和列表状态
    pageNo.value = 1;
    transcationList.value = [];
    transcationListCopy.value = [];
    finished.value = false;
    loading.value = false;
    
    // 手动触发加载
    onLoad();
}
const getParams = () => {
     return {
        pageType: activeNo.value,
        merchantId,
        page: pageNo.value,
    } 
}
const getList = () => {
    return new Promise((resolve, reject) => {
        getPingAnMerchantDetail(getParams()).then(res => {
            if (res.data?.rows) {
                // 如果是第一页，替换数据；否则，追加数据
                if (pageNo.value === 1) {
                    transcationList.value = res.data.rows;
                    transcationListCopy.value = res.data.rows || [] 
                } else {
                    transcationList.value = [...transcationList.value, ...res.data.rows];
                    transcationListCopy.value = res.data.rows || [] 
                }
                resolve();
            } else {
                // 如果没有数据，设置为空数组
                if (pageNo.value === 1) {
                    transcationList.value = [];
                    transcationListCopy.value = []
                }
                resolve();
            }
        }).catch(err => {
            ElMessage.error("获取失败");
            reject(err);
        });
    });
}
const onLoad = async () => {
    // 设置加载状态
    loading.value = true;
    
    try {
        // 获取当前页数据
        await getList();
        // 检查是否有更多数据
        if (transcationListCopy.value.length === 0 || transcationListCopy.value.length < 10) {
            // 如果返回的数据为空或少于预期，表示没有更多数据了
            finished.value = true;
        } else {
            // 还有更多数据，增加页码准备下次加载
            pageNo.value++;
            finished.value = false;
        }
    } catch (error) {
        console.error('加载数据失败:', error);
        ElMessage.error('加载数据失败');
        finished.value = true;
    } finally {
        // 无论成功失败，都结束加载状态
        loading.value = false;
    }
}
const seeOrderDetails = (orderNo) => {
    location.href = `ybmpage://orderdetail?order_id=${orderNo}`
}

// 添加组件挂载钩子
onMounted(() => {
    // 初始化数据
    onLoad();
});

// 监听路由参数变化，重新加载数据
watch(() => route.query.merchantId, (newVal, oldVal) => {
    if (newVal !== oldVal) {
        // 重置状态
        pageNo.value = 1;
        transcationList.value = [];
        transcationListCopy.value = []
        finished.value = false;
        loading.value = false;
        
        // 重新加载数据
        onLoad();
    }
});
</script>

<style lang='scss' scoped>
.fontTitle-content {
    font-size: 0.29rem;
    background-color: antiquewhite;
    padding: 0.2rem;
}
.content-container {
    max-height: 13rem;
    overflow-y: auto;
}
:deep(.van-tab--active) {
  span {
    font-weight: bold;
    font-size: 0.4rem;
    transition: all 0.3s ease;
  }
}

.custom-cell {
    display: flex;
    justify-content: space-between;
    .left {
        text-align: left;
        .logLabel {
            font-size: 0.4rem;
            color: #000;
        }
        .orderNo {
            font-size: 0.3rem;
            color: #999;
            margin-top: 0.1rem;
        }
    }
    .right {
        text-align: right;
        .amount {
            font-size: 0.4rem;
        }
        .time {
            font-size: 0.3rem;
            color: #999;
            margin-top: 0.1rem;
        }
    }
}
</style>
