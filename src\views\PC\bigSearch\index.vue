<template>
  <div class="wrapperBox">
    <div class="topBar">
      <el-cascader
        ref="categoryLevelRef"
        :options="categoryList"
        :props="props"
        v-model="selctedCategory"
        placeholder="全部分类"
        clearable
        style="width: 260px"
        @change="getCheckedNodes"
      />
      <div v-if="selectedCJ.length" class="selectedBox">
        <span class="triangle"></span>
        <span class="selectedItemBox">
          厂家：
          <el-tooltip
            :content="selectedCJ.join()"
            placement="bottom"
            :show-after="1000"
          >
            <span class="textellipsis selectedItem">{{
              selectedCJ.join()
            }}</span>
          </el-tooltip>
          <el-icon @click="clearCondition('selectedCJ')"><Close /></el-icon>
        </span>
      </div>
      <div v-if="selectedSJ.length" class="selectedBox">
        <span class="triangle"></span>
        <span class="selectedItemBox">
          商家：
          <el-tooltip
            :content="selectedSJ.join()"
            placement="bottom"
            :show-after="1000"
          >
            <span class="textellipsis selectedItem">{{
              selectedSJ.join()
            }}</span>
          </el-tooltip>
          <el-icon @click="clearCondition('selectedSJ')"><Close /></el-icon>
        </span>
      </div>
      <div v-if="selectedGG.length" class="selectedBox">
        <span class="triangle"></span>
        <span class="selectedItemBox">
          规格：
          <el-tooltip
            :content="selectedGG.join()"
            placement="bottom"
            :show-after="1000"
          >
            <span class="textellipsis selectedItem">{{
              selectedGG.join()
            }}</span>
          </el-tooltip>
          <el-icon @click="clearCondition('selectedGG')"><Close /></el-icon>
        </span>
      </div>
      <div v-if="selectedYXQ.length" class="selectedBox">
        <span class="triangle"></span>
        <span class="selectedItemBox">
          有效期：
          <el-tooltip
            :content="selectedYXQ.join()"
            placement="bottom"
            :show-after="1000"
          >
            <span class="textellipsis selectedItem">{{
              selectedYXQ.join()
            }}</span>
          </el-tooltip>
          <el-icon @click="clearCondition('selectedYXQ')"><Close /></el-icon>
        </span>
      </div>
      <div v-if="selectedJYLX.length" class="selectedBox">
        <span class="triangle"></span>
        <span class="selectedItemBox">
          经营类型：
          <el-tooltip
            :content="selectedJYLX.join()"
            placement="bottom"
            :show-after="1000"
          >
            <span class="textellipsis selectedItem">{{
              selectedJYLX.join()
            }}</span>
          </el-tooltip>
          <el-icon @click="clearCondition('selectedJYLX')"><Close /></el-icon>
        </span>
      </div>
      <span class="triangle"></span>
      <span>关键词：{{ keyword }}</span>
      <span
        v-if="
          selectedCJ.length ||
          selectedSJ.length ||
          selectedGG.length ||
          selectedJYLX.length
        "
        class="clearBtn"
        @click="clearCondition('all')"
      >
        <el-icon><Delete /></el-icon>&nbsp;清空
      </span>
    </div>
    <div class="searchRow">
      <div class="flexBox" :style="{ height: showMoreObj.CJ ? '' : '40px' }">
        <div class="searchTitle">厂家：</div>
        <div class="searchContent">
          <div class="searchItem" v-if="showMoreObj.CJ">
            <el-input
              v-model="keywordCJ"
              :prefix-icon="Search"
              style="width: 200px"
              placeholder="搜索厂家名称"
              clearable
              @input="searchName('CJ', $event)"
            />
            <div class="singleBtn" v-if="!isSingleObj.CJ">
              <el-button size="small" @click="clearCheckbox('CJ')"
                >取消</el-button
              >
              <el-button
                type="primary"
                size="small"
                @click="confirmCheckbox('CJ')"
                :disabled="checkListCJ.length === 0"
                >确定</el-button
              >
            </div>
          </div>
          <div v-if="manufacturerArr.length">
            <div
              class="itemBox"
              v-if="isSingleObj.CJ"
              :style="{ overflow: showMoreObj.CJ ? 'scroll' : 'hidden' }"
            >
              <div
                v-for="item in manufacturerArr"
                :key="item.key"
                @click="changeCondition('selectedCJ', item, item.key)"
              >
                <el-tooltip
                  :content="item.key"
                  placement="bottom"
                  :show-after="1000"
                >
                  <span
                    class="itemText textellipsis"
                    :class="{ activeText: selectedCJ.includes(item.key) }"
                    >{{ item.key }}</span
                  >
                </el-tooltip>
              </div>
            </div>
            <div class="itemBox" v-else>
              <el-checkbox-group v-model="checkListCJ">
                <el-checkbox
                  v-for="item in manufacturerArr"
                  :key="item.key"
                  :label="item.key"
                >
                  <el-tooltip
                    :content="item.key"
                    placement="bottom"
                    :show-after="1000"
                    >{{ item.key }}</el-tooltip
                  >
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div v-else class="itemBox">
            <div>抱歉，未找到对应厂家</div>
          </div>
        </div>
        <div class="searchBtn">
          <el-button @click="showMore('CJ')" size="small">
            <span v-if="showMoreObj.CJ">
              收起 <el-icon><ArrowUp /></el-icon>
            </span>
            <span v-else>
              展开 <el-icon><ArrowDown /></el-icon>
            </span>
          </el-button>
          <el-button @click="changSingle('CJ')" size="small">
            <span v-if="!isSingleObj.CJ">
              <el-icon><Minus /></el-icon>&nbsp;单选
            </span>
            <span v-else>
              <el-icon><Plus /></el-icon>&nbsp;多选
            </span>
          </el-button>
        </div>
      </div>
    </div>
    <div class="searchRow" v-if="fromPage === 'searchIndex'">
      <div class="flexBox" :style="{ height: showMoreObj.SJ ? '' : '40px' }">
        <div class="searchTitle">商家：</div>
        <div class="searchContent">
          <div class="searchItem" v-if="showMoreObj.SJ">
            <el-input
              v-model="keywordSJ"
              :prefix-icon="Search"
              style="width: 200px"
              placeholder="搜索商家名称"
              clearable
              @input="searchName('SJ', $event)"
            />
            <div class="singleBtn" v-if="!isSingleObj.SJ">
              <el-button size="small" @click="clearCheckbox('SJ')"
                >取消</el-button
              >
              <el-button
                type="primary"
                size="small"
                @click="confirmCheckbox('SJ')"
                :disabled="checkListSJ.length === 0"
                >确定</el-button
              >
            </div>
          </div>
          <div v-if="shopArr.length">
            <div
              class="itemBox"
              v-if="isSingleObj.SJ"
              :style="{ overflow: showMoreObj.SJ ? 'scroll' : 'hidden' }"
            >
              <div
                v-for="item in shopArr"
                :key="item.key"
                @click="
                  changeCondition('selectedSJ', item, item.showName, item.key)
                "
              >
                <el-tooltip
                  :content="item.showName"
                  placement="bottom"
                  :show-after="1000"
                >
                  <span
                    class="itemText textellipsis"
                    :class="{ activeText: selectedSJ.includes(item.showName) }"
                    >{{ item.showName }}</span
                  >
                </el-tooltip>
              </div>
            </div>
            <div class="itemBox" v-else>
              <el-checkbox-group v-model="checkListSJ">
                <el-checkbox
                  v-for="item in shopArr"
                  :key="item.showName"
                  :label="item.key"
                >
                  <el-tooltip
                    :content="item.showName"
                    placement="bottom"
                    :show-after="1000"
                    >{{ item.showName }}</el-tooltip
                  >
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div v-else class="itemBox">
            <div>抱歉，未找到对应商家</div>
          </div>
        </div>
        <div class="searchBtn">
          <el-button @click="showMore('SJ')" size="small">
            <span v-if="showMoreObj.SJ">
              收起 <el-icon><ArrowUp /></el-icon>
            </span>
            <span v-else>
              展开 <el-icon><ArrowDown /></el-icon>
            </span>
          </el-button>
          <el-button @click="changSingle('SJ')" size="small">
            <span v-if="!isSingleObj.SJ">
              <el-icon><Minus /></el-icon>&nbsp;单选
            </span>
            <span v-else>
              <el-icon><Plus /></el-icon>&nbsp;多选
            </span>
          </el-button>
        </div>
      </div>
    </div>
    <div class="searchRow">
      <div class="flexBox" :style="{ height: showMoreObj.GG ? '' : '40px' }">
        <div class="searchTitle">规格：</div>
        <div class="searchContent">
          <div class="searchItem" v-if="showMoreObj.GG">
            <el-input
              v-model="keywordCJ"
              style="width: 200px; visibility: hidden"
            />
            <div class="singleBtn" v-if="!isSingleObj.GG">
              <el-button size="small" @click="clearCheckbox('GG')"
                >取消</el-button
              >
              <el-button
                type="primary"
                size="small"
                @click="confirmCheckbox('GG')"
                :disabled="checkListGG.length === 0"
                >确定</el-button
              >
            </div>
          </div>
          <div v-if="specStats.length">
            <div
              class="itemBox"
              v-if="isSingleObj.GG"
              :style="{ overflow: showMoreObj.GG ? 'scroll' : 'hidden' }"
            >
              <div
                v-for="item in specStats"
                :key="item.key"
                @click="changeCondition('selectedGG', item, item.key)"
              >
                <el-tooltip
                  :content="item.key"
                  placement="bottom"
                  :show-after="1000"
                >
                  <span
                    class="itemText textellipsis"
                    :class="{ activeText: selectedGG.includes(item.key) }"
                    >{{ item.key }}</span
                  >
                </el-tooltip>
              </div>
            </div>
            <el-checkbox-group v-else v-model="checkListGG">
              <el-checkbox
                v-for="item in specStats"
                :key="item.key"
                :label="item.key"
              >
                <el-tooltip
                  :content="item.key"
                  placement="bottom"
                  :show-after="1000"
                  >{{ item.key }}</el-tooltip
                >
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <div v-else class="itemBox">
            <div>抱歉，未找到对应规格</div>
          </div>
        </div>
        <div class="searchBtn">
          <el-button @click="showMore('GG')" size="small">
            <span v-if="showMoreObj.GG">
              收起 <el-icon><ArrowUp /></el-icon>
            </span>
            <span v-else>
              展开 <el-icon><ArrowDown /></el-icon>
            </span>
          </el-button>
          <el-button @click="changSingle('GG')" size="small">
            <span v-if="!isSingleObj.GG">
              <el-icon><Minus /></el-icon>&nbsp;单选
            </span>
            <span v-else>
              <el-icon><Plus /></el-icon>&nbsp;多选
            </span>
          </el-button>
        </div>
      </div>
    </div>
    <div class="searchRow">
      <div class="flexBox" :style="{ height: '40px' }">
        <div class="searchTitle">有效期：</div>
        <div class="searchContent">
          <div>
            <div
              class="itemBox"
              :style="{ overflow: 'hidden' }"
            >
              <div
                v-for="item in yxqStats"
                :key="item.key"
                @click="changeCondition('selectedYXQ', item, item.key, item.value)"
              >
                <el-tooltip
                  :content="item.key"
                  placement="bottom"
                  :show-after="1000"
                >
                  <span
                    class="itemText textellipsis"
                    :class="{ activeText: selectedYXQ.includes(item.key) }"
                    >{{ item.key }}</span
                  >
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="searchRow" v-if="fromPage === 'searchIndex'">
      <div class="flexBox">
        <div class="searchTitle">经营类型：</div>
        <div class="searchContent">
          <div class="itemBox">
            <div
              v-for="item in drugClassification"
              :key="item.key"
              @click="
                changeCondition('selectedJYLX', item, item.label, item.key)
              "
            >
              <el-tooltip
                :content="item.label"
                placement="bottom"
                :show-after="1000"
              >
                <span
                  class="itemText textellipsis"
                  :class="{ activeText: selectedJYLX.includes(item.label) }"
                  >{{ item.label }}</span
                >
              </el-tooltip>
            </div>
          </div>
        </div>
        <div class="searchBtn"></div>
      </div>
    </div>
    <div class="tableBar">
      <div class="tableTop">
        <div class="tableTop-left">
          <el-button-group class="ml-4 search-rows">
            <el-button
              :type="property === 'smsr.sale_num' ? 'primary' : 'default'"
              @click="changeProperty('smsr.sale_num', 'comprehensive',1)"
            >
              综合
            </el-button>
            <el-button
              :type="property === 'spa.sale_num' ? 'primary' : 'default'"
              @click="changeProperty('spa.sale_num', 'sale',2)"
            >
              销量
              <el-icon><Bottom /></el-icon>
            </el-button>
            <el-button
              :type="property === 'fob' ? 'primary' : 'default'"
              @click="changeProperty('fob', 'price',3)"
            >
              价格
              <el-icon><Top /></el-icon>
            </el-button>
          </el-button-group>
          <div class="priceBox">
            <div class="row-line r-left"></div>
            <span>价格区间</span>
            <div class="priceInput">
              <el-input
                size="small"
                v-model="minPrice"
                style="width: 50px; margin: 0 5px"
              />
              <span>—</span>
              <el-input
                size="small"
                v-model="maxPrice"
                style="width: 50px; margin: 0 5px"
              />
            </div>
            <div class="priceBtn">
              <el-button
                size="small"
                style="margin: 0 0 4px 4px"
                @click="clearPrice"
                >清空</el-button
              >
              <el-button
                type="primary"
                size="small"
                style="margin: 0 4px 4px 0"
                @click="confirmPrice"
                >确认</el-button
              >
            </div>
          </div>
        </div>

        
        <div class="totalBox">
          <!-- 共&nbsp;<span style="color: #00B955;">{{ total }}</span>&nbsp;件商品 -->
          <div class="leftIcon pageIcon" @click="changePage('last')">
            <el-icon><ArrowLeft /></el-icon>
          </div>
          <span style="white-space: nowrap">{{ pageNum }} / {{ pages }}</span>
          <div class="rightIcon pageIcon" @click="changePage('next')">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
      <div class="checkboxWrapper">
        <el-checkbox-group
          v-model="checkBoxStatus"
          @change="changeCheckBoxStatus"
          class="search-rows"
        >
        <template v-for="(item,index) in dynamicLabelConfig">
          <el-checkbox
            :key="index"
            :value="item.paramKey"
            v-if="item.labelType != 3"
            >
            <img v-if="item.icon" style="height: 14px; width: auto; object-fit: contain;vertical-align: middle;margin-right: -2px;" :src="imgUrl + item.icon">
            {{item.labelName }}
          </el-checkbox>
          <div v-if="item.labelType == 3" class="el-checkbox">
            <el-popover :visible="popoverRef" popper-style="width: 100%; background: transparent; box-shadow:none; border:none;  padding: 0;" placement="bottom" width="1000px"  trigger="click" @show="()=>changeCheckBoxItemShow(item)"
              :popper-options="{
	              modifiers: [{
	              	name: 'offset',
	              	options: {
	              		offset: [0, -5]
	              	}
	              }]
	            }"
              @before-leave="()=>changeCheckBoxItemHide(item)">
              <template #reference>
                <div style="display: flex;align-items: center;" @click.stop="popoverRef = null">
                  <el-checkbox-group v-if="item.selectedItemNamesStr != null && item.selectedItemNamesStr.length > 0">
                    <el-checkbox :checked="true" @click.native.prevent="checkBoxHandleClick" style="margin-right: 8px;" size="large" />
                  </el-checkbox-group>
                  <el-checkbox-group v-else>
                    <el-checkbox :checked="false"  @click.native.prevent="checkBoxHandleClick" style="margin-right: 8px;" size="large"/>
                  </el-checkbox-group>
                  <div v-if="item.selectedItemNamesStr != null && item.selectedItemNamesStr.length > 0" class="checkBox-ellipsis">{{ item.selectedItemNamesStr }}</div>
                  <div v-else>{{item.labelName}}</div>
                  <el-icon :class="{ 'check-green-text': item.selectedItemNamesStr != null && item.selectedItemNamesStr.length > 0 }" style="margin-left: 2px;" v-if="checkBoxItemUpName == item.labelName"><ArrowUp /></el-icon>
                  <el-icon :class="{ 'check-green-text': item.selectedItemNamesStr != null && item.selectedItemNamesStr.length > 0 }" style="margin-left: 2px;" v-else><ArrowDown /></el-icon>
                </div>
              </template>
              <div class="popover_style">
                <div>
                  <el-checkbox-group
                    v-model="checkBoxItemStatus[item.labelKey]"
                    class="search-rows"
                  >
                    <el-checkbox
                      v-for="(ele,index) in item.items"
                      :key="index"
                      :value="ele.itemKey"
                      >
                      {{ele.itemName }}({{ ele.itemCount }})
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                  <el-button @click="()=>changeCheckBoxItemReset(item)">重置</el-button>
                  <el-button type="primary" style="background: #00B955;" @click="()=>changeCheckBoxItemStatus(item)">确定</el-button>
                </div>

              </div>
            </el-popover>
          </div>
        </template>
          
        </el-checkbox-group>
      </div>
    </div>
    
    <div class="douDiText" v-if="goodsType === 3">抱歉，没有找到商品</div>
    <div v-if="list.length">
      <tempMixedRow
        :scmE="scmE"
        :qtData="qtData"
        :goods-data="list"
        :groupPurchase="groupPurchase"
        :license-status="licenseStatus"
        :searchSortStrategyId="searchSortStrategyId"
        :spid="trackInfo.spid"
        :track-info="trackInfo"
        :keyword="keyword"
        :pageNum="pageNum"
        :pages="pages"
        :total="total"
        ref="tempMixedRowRef"
      />
    </div>
    <div v-if="!list.length&&!firstLoad" class="tableContent">
      <div class="noGoods">
        <img src="../../../assets/images/search-nodata.png" alt="" />
      </div>
    </div>
    <div class="pagination-container">
      <el-pagination
        v-model:currentPage="pageNum"
        :page-size="20"
        :background="true"
        layout="prev, pager, next, jumper"
        :total="total"
        @current-change="pagerChangePage"
      />
    </div>
  </div>
</template>

  
<script setup>
  import { actionTracking } from "@/config/eventTracking";
  import { onMounted, ref, getCurrentInstance, onUnmounted, nextTick, reactive, computed } from "vue";
  import { ElMessage } from "element-plus";
  import { Search } from "@element-plus/icons-vue";
  import { getAllCategory, getV2Aggs, getProductLists } from "@/http_pc/api";
  import tempMixedRow from "../../../components_pc/tempMixedRow2.vue";
  import { getUrlParam, exposureView, exposureAnalysysAgentView } from "@/utils/index";
  import { useStore } from "vuex";
  const groupPurchase = ref({});
  const firstLoad=ref(true);
  const imgUrl = import.meta.env.VITE_IMG;
  const { proxy } = getCurrentInstance();
  const store = useStore();
  const merchantId = proxy.$merchantId;
  const trackInfo = ref({});
  const licenseStatus = ref("");
  const dynamicLabelConfig = ref([]);
  const total = ref(0);
  const pageNum = ref(1);
  const pages = ref(0); // 总页数
  const goodsType = ref(1); // 1-正常搜索, 2-截断召回搜索, 3-兜底推荐
  const categoryLevelRef = ref(null);
  const selctedCategory = ref([]);
  // const pageSize = ref(20);
  const categoryList = ref([]);
  const property = ref("smsr.sale_num");
  const specStats = ref([]);
  const yxqStats = ref([
    {key: '6个月以下', value: 1},
    {key: '6-12个月', value: 2},
    {key: '12个月以上', value: 3},
    {key: '15个月以上', value: 4},
  ]);
  const shopStats = ref([]); // 接口返回的商家数据
  const shopArr = ref([]); // 前端实现搜索后的数据
  const manufacturerStats = ref([]); // 接口返回的厂家数据
  const manufacturerArr = ref([]); // 前端实现搜索后的数据
  const list = ref([]);
  const checkListCJ = ref([]); // 多选的厂家
  const checkListSJ = ref([]); // 多选的商家
  const checkListGG = ref([]); // 多选的规格
  const showMoreObj = ref({
    CJ: false, // 厂家
    SJ: false, // 商家
    GG: false, // 规格
  });
  // 单选/多选
  const isSingleObj = ref({
    CJ: true, // 厂家
    SJ: true, // 商家
    GG: true, // 规格
  });
  let isNextPageV=ref(0)
  const selectedSJKeys = ref([]); // shopCode
  const selectedSJ = ref([]); // 所选商家条件
  const selectedCJ = ref([]); // 所选厂家条件
  const selectedGG = ref([]); // 所选规格条件
  const oldSelectedSJ = ref([]); // 旧所选商家条件
  const oldSelectedCJ = ref([]); // 旧所选厂家条件
  const oldSelectedGG = ref([]); // 旧所选规格条件
  const selectedJYLX = ref([]); // 所选经营类型条件
  const selectedJYLXKeys = ref([]); // 所选经营类型key
  const selectedYXQ = ref([]); // 所选有效期条件
  const selectedYXQKeys = ref([]); // 所选有效期key
  
  const minPrice = ref("");
  const maxPrice = ref("");
  const keyword = ref(getUrlParam("keywordSearch"));
  const fromPage = ref(getUrlParam("fromPage")); // searchIndex-大搜；shop-店铺；selfShop-自营店铺
  const keywordCJ = ref("");
  const keywordSJ = ref("");
  const categoryIds = ref({
    categoryFirstId: getUrlParam("categoryFirstId"),
    categorySecondId: getUrlParam("categorySecondId"),
    categoryThirdId: getUrlParam("categoryThirdId"),
  }); // 所选分类id
  const checkBoxStatus = ref([]);
  const checkBoxStatusArr = ref([]);
  const jgClickCheckArr = ref([]);
  

  const isCheckBoxStatusFlag = ref(1); // 是否调用埋点 PC搜索二级动态筛选曝光   1、2【2表示点击埋点】 表示需要埋点 页面刷新checkBoxStatusArr会两次赋值

  const drugClassification = ref([
    {
      key: "1",
      label: "甲类OTC",
    },
    {
      key: "2",
      label: "乙类OTC",
    },
    {
      key: "3",
      label: "处方药RX",
    },
    {
      key: "4",
      label: "其他",
    },
  ]);
  const props = ref({
    checkStrictly: true,
    value: "key",
    label: "showName",
  });
  const checkBoxItemUpName = ref('');
  const checkBoxItemStatus = ref({});
  const checkBoxItemStorage = ref({});
  const popoverRef = ref(null)
  const checkBoxHandleClick = (event) => {
    event.preventDefault(); 
  }
  const filterDataFn = () => {
    const result = dynamicLabelConfig.value.filter(item => item.labelType == 3);
    let selectObj = {}
    result.forEach(item => {
      if(item.selectedItemNamesStr && item.selectedItemNamesStr.length > 0){
        selectObj[item.labelKey] = (item.selectedItemNamesStr || '').split('/').filter(item => item !== "") || [];
      }
    })
    let str = JSON.stringify(selectObj);
    let obj = JSON.parse(str);
    let obj2 = JSON.parse(str);
    checkBoxItemStorage.value = obj;
    checkBoxItemStatus.value = obj2;
  }
    // 下拉框筛选重置
    const changeCheckBoxItemReset = (e) => {
    checkBoxItemStatus.value[e.labelGroupKey] = [];
    console.log(e);
  };
  // 下拉框筛选确定
  const changeCheckBoxItemStatus = (e) => {
    let str = JSON.stringify(checkBoxItemStatus.value);
    let obj = JSON.parse(str);
    for (const key in obj) {
      if (Array.isArray(obj[key]) && obj[key].length === 0) {
        delete obj[key];
      }
    }
    checkBoxItemStorage.value = obj;
    popoverRef.value = false
    // -----------------------------------
    isCheckBoxStatusFlag.value = 2 ; 
    pageNum.value = 1;
    isNextPageV.value = 0;
    changeCategoryList();
    loadData();
  }
  // 弹窗展示
  const changeCheckBoxItemShow = (e) => {
    checkBoxItemUpName.value = e.labelName;
  }
  // 弹窗关闭
  const changeCheckBoxItemHide = (e) => {
    checkBoxItemUpName.value = '';
    let str = JSON.stringify(checkBoxItemStorage.value);
    let obj = JSON.parse(str);
    checkBoxItemStatus.value = obj;
  }
  // 获取各种厂家商家规格
  const getSearchCategory = () => {
    let params = getParams();
    if (hasFilterCheck()) {
      params.isFilter	= 1;
    } else {
      params.isFilter	= 0;
    }
    getV2Aggs(params).then((res) => {
      if (res.status === "success") {
        specStats.value = ((res.data || {}).aggregations || {}).specStats || [];
        shopStats.value = ((res.data || {}).aggregations || {}).shopStats || [];
        categoryList.value = ((res.data || {}).aggregations || {}).catStats || [];
        shopArr.value = shopStats.value;
        manufacturerStats.value =
          ((res.data || {}).aggregations || {}).manufacturerStats || [];
        manufacturerArr.value = manufacturerStats.value;
        dynamicLabelConfig.value = ((res.data || {}).aggregations || {}).dynamicLabelConfig || [];
        filterDataFn()
      }
    });
  };
  // 更新分类
  const changeCategoryList = (type) => {
    let params = getParams();
    if (hasFilterCheck()) {
      params.isFilter	= 1;
    } else {
      params.isFilter	= 0;
    }
    getV2Aggs(params).then((res) => {
      if (res.status === "success") {
        dynamicLabelConfig.value = ((res.data || {}).aggregations || {}).dynamicLabelConfig || [];
        categoryList.value = ((res.data || {}).aggregations || {}).catStats || [];
        singleRubishServerFilter(res, type);
        filterDataFn()
      }
    });
  };
  
  function singleRubishServerFilter(res, type) {
    if (res.data && res.data.aggregations) {
      let oldSeltSJ = findItem(
        "showName",
        oldSelectedSJ,
        res.data.aggregations.shopStats
      );
      let oldSelGG = findItem(
        "key",
        oldSelectedGG,
        res.data.aggregations.specStats
      );
      let oldSelCJ = findItem(
        "key",
        oldSelectedCJ,
        res.data.aggregations.manufacturerStats
      );
  
      if (type !== "selectedSJ") {
        if (oldSeltSJ.length > 0) {
          shopStats.value = (res.data.aggregations.shopStats || []).concat(
            oldSeltSJ
          );
        } else {
          shopStats.value = ((res.data || {}).aggregations || {}).shopStats || [];
        }
        shopArr.value = shopStats.value;
      }
  
      if (type !== "selectedGG") {
        if (oldSelGG.length > 0) {
          specStats.value = (
            ((res.data || {}).aggregations || {}).specStats || []
          ).concat(oldSelGG);
        } else {
          specStats.value = ((res.data || {}).aggregations || {}).specStats || [];
        }
      }
  
      if (type !== "selectedCJ") {
        if (oldSelCJ.length > 0) {
          manufacturerStats.value = (
            ((res.data || {}).aggregations || {}).manufacturerStats || []
          ).concat(oldSelCJ);
        } else {
          manufacturerStats.value =
            ((res.data || {}).aggregations || {}).manufacturerStats || [];
        }
        manufacturerArr.value = manufacturerStats.value;
      }
    }
  }
  
  //查询出来是否有相应的数据
  function findItem(key, selecteds, arr) {
    let arritems = [];
    selecteds.value.forEach((item) => {
      let finds = arr.find((row) => {
        return row[key] === item[key];
      });
      if (!finds) {
        arritems.push(item);
      }
    });
  
    return arritems;
  }
  
  // 获取所有节点
  const getCheckedNodes = (node) => {
    if ((node || []).length) {
      categoryIds.value = {
        categoryFirstId: node[0] || "",
        categorySecondId: node[1] || "",
        categoryThirdId: node[2] || "",
      };
      // 埋点
      let categoryNames = [];
      categoryNames = categoryLevelRef.value.getCheckedNodes()[0].pathLabels;
      const fromPage = getUrlParam("fromPage");
      if (node[2]) {
        fromPage === "searchIndex"
          ? actionTracking("search_third_classification", {
              classification: categoryNames[2],
            })
          : fromPage === "shop" || fromPage === "selfShop"
          ? actionTracking("shop_search_third_classification", {
              classification: categoryNames[2],
            })
          : "";
      } else if (node[1]) {
        fromPage === "searchIndex"
          ? actionTracking("search_second_classification", {
              classification: categoryNames[1],
            })
          : fromPage === "shop" || fromPage === "selfShop"
          ? actionTracking("shop_search_second_classification", {
              classification: categoryNames[1],
            })
          : "";
      } else if (node[0]) {
        fromPage === "searchIndex"
          ? actionTracking("search_first_classification", {
              classification: categoryNames[0],
            })
          : fromPage === "shop" || fromPage === "selfShop"
          ? actionTracking("shop_search_first_classification", {
              classification: categoryNames[0],
            })
          : "";
      }
    } else {
      categoryIds.value = {
        categoryFirstId: "",
        categorySecondId: "",
        categoryThirdId: "",
      };
    }
    pageNum.value = 1;
    isNextPageV.value = 0;
    changeCategoryList();
    loadData();
  };
  const changeCheckBoxStatus = (e) => {
    isCheckBoxStatusFlag.value = 2 ; // 点击筛选框状态 也要上报
    pageNum.value = 1;
    isNextPageV.value = 0;
    let arr = [];
    let labelArr = [];
    dynamicLabelConfig.value.forEach(item => {
      if (e.findIndex(k => k == item.paramKey) > -1) {
        arr.push(item.labelGroupKey);
        labelArr.push(item.labelName);
      }
    })
    checkBoxStatusArr.value = labelArr;
    jgClickCheckArr.value = arr;
    changeCategoryList();
    loadData();
    const fromPage = getUrlParam("fromPage");
    fromPage === "searchIndex"
      ? actionTracking("search_quick_filter", { item: arr.join() })
      : fromPage === "shop" || fromPage === "selfShop"
      ? actionTracking("shop_search_quick_filter", { item: arr.join() })
      : "";
    exposureAnalysysAgentCheckListFn() ; 
  };
  const getParams = (from) => {
    let tagObj = reactive({});
    checkBoxStatus.value.forEach(item => {
      const configItem = dynamicLabelConfig.value.find(k => k.paramKey == item) || {};
      tagObj[item] = configItem.selectedParamValue;
    })
    let categoryIdsStr=""
    if(categoryIds.value.categoryFirstId){
      categoryIdsStr=categoryIdsStr+categoryIds.value.categoryFirstId
    }
    if(categoryIds.value.categorySecondId){
      categoryIdsStr=categoryIdsStr+","+categoryIds.value.categorySecondId
    }
    if(categoryIds.value.categoryThirdId){
      categoryIdsStr=categoryIdsStr+","+categoryIds.value.categoryThirdId
    }
    let checkMulti = {};
    Object.keys(checkBoxItemStorage.value).forEach(key => {
      const obj = dynamicLabelConfig.value.find(k => k.labelKey == key) || {};
      if (obj.labelType == 3 && obj.isDynamicParam == 0) {
        checkMulti[obj.paramKey] = checkBoxItemStorage.value[key];
      }
      if (obj.labelType == 3 && obj.isDynamicParam == 1) {
        if (checkMulti[obj.paramKey] == null) {
          checkMulti[obj.paramKey] = {};
        }
        checkMulti[obj.paramKey][obj.dynamicParamKey] = checkBoxItemStorage.value[key];
      }
      
    });
    Object.keys(checkMulti).forEach(key => {
      checkMulti[key] = JSON.stringify(checkMulti[key]);
    });
    console.log(tagObj,"111")
    //去除underfine
    Object.keys(tagObj).forEach(key => {
      if (tagObj[key] === undefined) {
        delete tagObj[key];
      }
    });
    return {
        queryWord: keyword.value, // 关键词
      ...categoryIds.value, // 分类id
      categoryIdsStr:categoryIdsStr,
      ...tagObj,
      ...checkMulti,
      categoryId:
        getUrlParam("categoryFirstId") ||
        getUrlParam("categorySecondId") ||
        getUrlParam("categoryThirdId"), // 不区分123级的分类id
        manufacturers:JSON.stringify(selectedCJ.value), // 厂家
      shopCodes: getUrlParam("shopCode") || selectedSJKeys.value.join(), // 商家
      specs: JSON.stringify(selectedGG.value), // 规格
      drugClassificationsStr: selectedJYLXKeys.value.join(), // 经营类型
      sortStrategy: sortStrategy.value, // 排序
      minPrice: minPrice.value, // 最低价格
      maxPrice: maxPrice.value, // 最高价格
      pageNum: pageNum.value,
      pageSize: 20,
      searchScene: getUrlParam("fromPage") === "searchIndex" ? 1 : 1, // 搜索页面来源：1-大搜；2-店铺内搜索；3-专区搜索；
      requestType: 1, // 请求方式：0-feed流，1-分页，默认=0
    //   direction: property.value === "fob" ? "asc" : "desc", // desc - 降序，asc-升序
      type: from === "changePage" ? goodsType.value : 1, // 1-正常搜索, 2-截断召回搜索, 3-兜底推荐
    };
  };
  const searchSortStrategyId = ref('');
  const fenleiValue = ref([]);

  const hasFilterCheck = (() => {
    return (selectedSJ.value.length || selectedCJ.value.length || selectedGG.value.length || selectedJYLX.value.length || selectedYXQ.value.length) || fenleiValue.value.length || checkBoxStatusArr.value.length || Object.keys(checkBoxItemStorage.value).length ||(minPrice.value !== "" && minPrice.value > -1 && maxPrice.value !== "" && maxPrice.value > -1);
  });
  let qtListData=ref('')
  let tempMixedRowRef=ref()

  // 执行搜索接口
  const loadData = (from) => {
    let params = getParams(from);
    console.log(params,"xwh")
    params.nearEffect = selectedYXQKeys.value.join();
    let jgspid = sessionStorage.getItem("jgspid");
    params.jgspid = jgspid;
    if (trackInfo.value.sid) {
      params.sid = trackInfo.value.sid;
      params.sptype = trackInfo.value.sptype;
    }
    store.commit("app/showLoading", true);
    fenleiValue.value = [];
    for(let i in categoryIds.value) {
      if (categoryIds.value[i] != "") {
        fenleiValue.value.push(categoryIds.value[i]);
      }
    }
    if (hasFilterCheck()) {
      params.isFilter	= 1;
    } else {
      params.isFilter	= 0;
    }
    // if (pageNum.value != 1) {
    //   params.isNextPage	= 1;
    // } else {
    //   params.isNextPage	= 0;
    // }
    params.isNextPage=isNextPageV.value
    if(scmE.value){
      params.scmId=scmE.value
        }
      // params.queryWord = '小儿';
      // params.isGroupBuyingOrWholesale = '1';
      // params.manufacturers = '["湖北美宝药业股份有限公司"]';
    getProductLists(params).then((res) => {
      firstLoad.value=false;
      store.commit("app/showLoading", false);
      if (res.status == "success") {
        console.log(tempMixedRowRef)
        if(tempMixedRowRef.value){
          console.log( tempMixedRowRef.value.forceRenderKey)
          tempMixedRowRef.value.forceRenderKey+=1;
        }   
        list.value = (res.data || {}).rows || [];
        isNextPageV.value=1
        list.value.forEach((item,index) => {
          if(!item.productInfo) {
            Reflect.set(item, "productInfo", {})
          }
          if(item.groupPurchaseInfo && item.groupPurchaseInfo.mainProduct && item.groupPurchaseInfo.subProducts) {
            item.groupPurchaseInfo.subProducts.forEach(it=>{
              if(it.actPt){
                it.actPt.count = it.actPt.skuStartNum
                it.actPt.startAmount = 0
                it.actPt.amount = 0
                it.actPt.selectStatus = 1
              }
            })
            // item.groupPurchaseInfo.subProducts =  item.groupPurchaseInfo.subProducts.filter(it=>it.productInfo.actPt)
            item.groupPurchaseInfo.mainProduct.actPt.count = item.groupPurchaseInfo.mainProduct.actPt.skuStartNum;
            item.groupPurchaseInfo.mainProduct.actPt.startAmount = 0
            item.groupPurchaseInfo.mainProduct.actPt.amount = 0
            item.groupPurchaseInfo.mainProduct.actPt.selectStatus = 1
            groupPurchase.value = JSON.parse(JSON.stringify(item.groupPurchaseInfo));
            groupPurchase.value.index = index;
          }
        })
        console.log(list.value,groupPurchase.value,'qiyu');
        
        total.value = (res.data || {}).totalCount || 0;
        pages.value = (res.data || {}).totalPage || 0;
        goodsType.value = (res.data || {}).type || 1;
        licenseStatus.value = (res.data || {}).licenseStatus;
        searchSortStrategyId.value=(res.data || {}).searchSortStrategyId
        trackInfo.value = {
          spid: (res.data || {}).spid,
          sid: (res.data || {}).sid,
          sptype: (res.data || {}).sptype,
          searchSortStrategyId:(res.data || {}).searchSortStrategyId
        };
        if ((res.data || {}).jgspid) {
          localStorage.setItem("jgspid", (res.data || {}).jgspid);
        }
        let eles = document.querySelectorAll("[data-analysysagent-exposure]");
        eles = [...eles];
        eles.forEach((item) => {
          item.setAttribute('jg-hastrack', false);
        }) 
        nextTick(() => {
          postHeight();
          exposureView();
          exposureAnalysysAgentView();
        });
        actionTracking("pc_page_Commoditylist", {
          spid: trackInfo.value.spid,
          sid: trackInfo.value.sid,
          sptype: trackInfo.value.sptype,
          user_id: merchantId,
        });
        let obj = reactive({});
        let text = sortStrategy.value == 1 ? "综合" : sortStrategy.value == 2 ? "销量从高到低" : "价格从低到高";
      //   categoryIds.value = {
      //   categoryFirstId: "",
      //   categorySecondId: "",
      //   categoryThirdId: "",
      // };
        
        if (params.isFilter) {
          obj = {
            paixu: text,
            is_filter: params.isFilter,
            changjia: selectedCJ.value,
            shangjia: selectedSJ.value,
            guige: selectedGG.value,
            yplx: selectedJYLX.value,
            period_validity:selectedYXQ.value.join(",") || null, //todo
            fenlei: fenleiValue.value,
            minprice:minPrice.value !== "" ? minPrice.value * 1 : null,
            maxprice:maxPrice.value !== "" ? maxPrice.value * 1 : null,
            tag_filter_type: jgClickCheckArr.value, // todo
            tag_filter: checkBoxStatusArr.value
          }
        } else {
          obj = {
            paixu: text,
            is_filter: params.isFilter,
          };
        }
        window.AnalysysAgent.track("page_list_build", {
          key_word:keyword.value,
          sid: trackInfo.value.sid,
          jgspid: jgspid,
          sptype: trackInfo.value.sptype,
          search_sort_strategy_id: searchSortStrategyId.value && searchSortStrategyId.value != 'null' ? `${searchSortStrategyId.value}` : null,
          result_cnt: total.value,
          page_no: pageNum.value,
          page_size: 20,
          total_page: pages.value,
          ...delObjNull(obj)
      });

        isCheckBoxStatusFlag.value = 1;
        exposureAnalysysAgentCheckListFn() ;// 埋点

        // let active_index = '';
        // let commodityId=[]
        // res.data.row.forEach((val, index) => {
        //   if (val.cardType == 2) {
        //     active_index = index;
        //     commodityId.push(val.operationInfo.products[0].id)
        //   } 
        // })
        // actionTracking('pc_search_Active_Exposure', {
        //   sptype: trackInfo.value.sptype,
        //   spid: trackInfo.value.spid,
        //   sid: trackInfo.value.sid,
        //   commodityId: commodityId.join('_'),
        //   search_sort_strategy_id: res.data.searchSortStrategyCode,
        //   active_type: 0,
        //   goods_groupid: res.data.row.operationInfo.products[0].operationExhibitionId,
        //   active_id: res.data.row.operationInfo.products[0].operationId,
        //   active_index: active_index
        // })
        //qt 列表生成
        let dynamic_filter_names=[]
        let dynamic_filter_types=[]
        dynamicLabelConfig.value.forEach(e=>{
           // 单选
           if(e.isSelected && e.labelType != 3){
            dynamic_filter_names.push(e.labelName)
            dynamic_filter_types.push(e.paramKey)
           }
           // 多选
           if(e.labelType == 3){
            e.items.forEach((key) => {
              if(key.isSelected){
                dynamic_filter_names.push(`${e.labelName}###${key.itemName}`)
                dynamic_filter_types.push(`${e.labelGroupKey}###${key.itemKey}`)
              }
            })
           }
        })
        let ssData={       
          'is_filter':params.isFilter,
          'manufacturers':String(JSON.stringify(selectedCJ.value)),
          'distributors': JSON.stringify(selectedSJ.value),
          'specs':JSON.stringify(selectedGG.value),
          'drug_classifications':JSON.stringify(selectedJYLX.value),
          'period_validity':selectedYXQ.value.join(),
          'categorys':params.categoryIdsStr?JSON.stringify(params.categoryIdsStr.split(',')):'',
          'min_price':minPrice.value,
          'max_price':maxPrice.value,
          'dynamic_filter_names':JSON.stringify(dynamic_filter_names),
          'dynamic_filter_types':JSON.stringify(dynamic_filter_types),
        }
        Object.keys(ssData).forEach(key=>{
          if(ssData[key]===''||ssData[key]=='[]'){
            delete ssData[key]
          }
        })
        if(Object.keys(ssData).includes('max_price')){
          ssData.max_price=Number(ssData.max_price)
        }
        if(Object.keys(ssData).includes('min_price')){
          ssData.min_price=Number(ssData.min_price)
        }
        console.log(ssData,'xwh')
        if(res.data.scmId){
          scmE.value=res.data.scmId
        }    
        qtListData.value=res.data.qtListData
        if (window.aplus_queue) {
          aplus_queue.push({
            'action': 'aplus.record',
            'arguments': ['page_list_build', 'OTHER', {
              "spm_cnt": `1_4.searchResult_0-0_0.${goodsType.value==3?'recommendList':'searchList'}@5.0.${window.getSpmE ? window.getSpmE() : ''}`,
              'scm_cnt': `${goodsType.value==3?'recommend':'search'}.${searchSortStrategyId.value||'0'}.all_0.0.${scmE.value}`,
              'qt_list_data': res.data.qtListData,
              'sort': sortStrategy.value == 1 ? "综合" : sortStrategy.value == 2 ? "销量从高到低" : "价格从低到高",
              // 'key_word': keyword.value,
              ...ssData
            }]
          });
        }     
      } else if (res.code == 5000) {
        ElMessage.error(res.msg);
      }
    });
  };
  const delObjNull = (obj) => {
    for(let i in obj) {
      if (obj[i] !== 0) {
        if (obj[i] == null || obj[i] == "" || obj[i] == undefined || !obj[i] || (Array.isArray(obj[i]) && !obj[i].length)) {
          delete obj[i];
        }
      } 
    }
    return obj;
  }
  // 切换排序
  let sortStrategy=ref(1)
  const changeProperty = (type, name,val) => {
    sortStrategy.value=val
    property.value = type;
    pageNum.value = 1;
    isNextPageV.value = 0;
    changeCategoryList();
    loadData();
    // 埋点
    const fromPage = getUrlParam("fromPage");
    fromPage === "searchIndex"
      ? actionTracking("search_quick_filter", { item: name })
      : fromPage === "shop" || fromPage === "selfShop"
      ? actionTracking("shop_search_quick_filter", { item: name })
      : "";
    exposureAnalysysAgentFn();
  };
  // 展开/收起
  const showMore = (type) => {
    showMoreObj.value[type] = !showMoreObj.value[type];
  };
  // 单选/多选
  const changSingle = (type) => {
    isSingleObj.value[type] = !isSingleObj.value[type];
    // 多选时自动展开
    if (!isSingleObj.value[type]) {
      showMoreObj.value[type] = true;
      // 默认勾选已选的数据
      switch (type) {
        case "CJ":
          checkListCJ.value = selectedCJ.value;
          break;
        case "SJ":
          checkListSJ.value = selectedSJKeys.value;
          break;
        case "GG":
          checkListGG.value = selectedGG.value;
          break;
      }
    }
  };
  // 点击条件项
  const changeCondition = (type, item, val, key) => {
    switch (type) {
      case "selectedCJ":
        isSingleObj.value.CJ
          ? changeSelectedData(
              selectedCJ,
              oldSelectedCJ,
              undefined,
              item,
              val,
              undefined
            )
          : "";
        break;
      case "selectedSJ":
        isSingleObj.value.SJ
          ? changeSelectedData(
              selectedSJ,
              oldSelectedSJ,
              selectedSJKeys,
              item,
              val,
              key
            )
          : "";
        break;
      case "selectedGG":
        isSingleObj.value.GG
          ? changeSelectedData(
              selectedGG,
              oldSelectedGG,
              undefined,
              item,
              val,
              undefined
            )
          : "";
        break;
      case "selectedYXQ":
        changeSelectedData(
          selectedYXQ,
          undefined,
          selectedYXQKeys,
          item,
          val,
          key
        );
        break;
      case "selectedJYLX":
        changeSelectedData(
          selectedJYLX,
          undefined,
          selectedJYLXKeys,
          item,
          val,
          key
        );
        break;
    }
    pageNum.value = 1;
    isNextPageV.value = 0;
    changeCategoryList(type);
    loadData();
  };
  
  function changeSelectedData(
    selecteds,
    oldSelected,
    selectedKeys,
    item,
    val,
    key
  ) {
    const index = selecteds.value.findIndex((item) => item === val);
    if (index === -1 || selecteds.value.length > 1) {
      selecteds.value = [val];
      if (oldSelected) {
        oldSelected.value = [item];
      }
    } else {
      selecteds.value.splice(index, 1);
      if (oldSelected) {
        oldSelected.value.splice(index, 1);
      }
    }
    if (key) {
      const keyIndex = selectedKeys.value.findIndex((item) => item === key);
      if (keyIndex === -1 || selectedKeys.value.length > 1) {
        selectedKeys.value = [key];
      } else {
        selectedKeys.value.splice(keyIndex, 1);
      }
    }
    exposureAnalysysAgentFn() ; // 上报 
  }
  
  // 单个清空已选查询条件
  const clearCondition = (type) => {
    switch (type) {
      case "selectedCJ":
        selectedCJ.value = [];
        oldSelectedCJ.value = [];
        break;
      case "selectedSJ":
        selectedSJ.value = [];
        selectedSJKeys.value = [];
        oldSelectedSJ.value = [];
        break;
      case "selectedGG":
        selectedGG.value = [];
        oldSelectedGG.value = [];
        break;
      case "selectedJYLX":
        selectedJYLX.value = [];
        selectedJYLXKeys.value = [];
        break;
      case "selectedYXQ":
        selectedYXQ.value = [];
        selectedYXQKeys.value = [];
        break;
      case "all":
        selectedCJ.value = [];
        selectedSJ.value = [];
        selectedSJKeys.value = [];
        selectedGG.value = [];
        selectedJYLX.value = [];
        selectedJYLXKeys.value = [];
        checkListSJ.value = [];
        checkListCJ.value = [];
        checkListGG.value = [];
        oldSelectedGG.value = [];
        oldSelectedSJ.value = [];
        oldSelectedCJ.value = [];
        selectedYXQ.value = [];
        selectedYXQKeys.value = [];
        break;
      
    }
    pageNum.value = 1;
    isNextPageV.value = 0;
    changeCategoryList();
    loadData();
  };
  // 确定多选
  const confirmCheckbox = (type) => {
    const fromPage = getUrlParam("fromPage");
    switch (type) {
      case "CJ":
        selectedCJ.value = checkListCJ.value;
        oldSelectedCJ.value = manufacturerArr.value.filter((item) => {
          return checkListCJ.value.includes(item.key);
        });
        showMoreObj.value.CJ = false;
        isSingleObj.value.CJ = true;
        // 埋点
        fromPage === "searchIndex"
          ? actionTracking("search_multi_choice_confirmation", {
              item: "manufactor",
            })
          : fromPage === "shop" || fromPage === "selfShop"
          ? actionTracking("shop_search_multi_choice_confirmation", {
              item: "manufactor",
            })
          : "";
        break;
      case "SJ":
        selectedSJ.value = [];
        checkListSJ.value.forEach((item) => {
          shopArr.value.forEach((item2) => {
            if (item === item2.key) {
              selectedSJ.value.push(item2.showName);
            }
          });
        });
        oldSelectedCJ.value = shopArr.value.filter((item) => {
          return selectedSJ.value.includes(item.showName);
        });
        // selectedSJ.value = checkListSJ.value;
        selectedSJKeys.value = [...new Set(checkListSJ.value)];
        showMoreObj.value.SJ = false;
        isSingleObj.value.SJ = true;
        // 埋点
        fromPage === "searchIndex"
          ? actionTracking("search_multi_choice_confirmation", { item: "shop" })
          : fromPage === "shop" || fromPage === "selfShop"
          ? actionTracking("shop_search_multi_choice_confirmation", {
              item: "shop",
            })
          : "";
        break;
      case "GG":
        selectedGG.value = checkListGG.value;
        showMoreObj.value.GG = false;
        isSingleObj.value.GG = true;
        oldSelectedGG.value = specStats.value.filter((item) => {
          return selectedGG.value.includes(item.key);
        });
  
        // 埋点
        fromPage === "searchIndex"
          ? actionTracking("search_multi_choice_confirmation", { item: "desc" })
          : fromPage === "shop" || fromPage === "selfShop"
          ? actionTracking("shop_search_multi_choice_confirmation", {
              item: "desc",
            })
          : "";
        break;
    }
    pageNum.value = 1;
    isNextPageV.value = 0;
    exposureAnalysysAgentFn(1) ; // 上报 
    changeCategoryList("selected" + type);
    loadData();
  };
  // 取消多选
  const clearCheckbox = (type) => {
    switch (type) {
      case "CJ":
        checkListCJ.value = [];
        showMoreObj.value.CJ = false;
        isSingleObj.value.CJ = true;
        break;
      case "SJ":
        checkListSJ.value = [];
        showMoreObj.value.SJ = false;
        isSingleObj.value.SJ = true;
        break;
      case "GG":
        checkListGG.value = [];
        showMoreObj.value.GG = false;
        isSingleObj.value.GG = true;
        break;
    }
  };
  // 确认选择价格区间
  const confirmPrice = () => {
    let maxMoney = eval(
      `Math.max(${[minPrice.value, maxPrice.value].toString()})`
    );
    let minMoney = eval(
      `Math.min(${[minPrice.value, maxPrice.value].toString()})`
    );
    minPrice.value = minMoney;
    maxPrice.value = maxMoney;
    pageNum.value = 1;
    isNextPageV.value = 0;
    changeCategoryList();
    loadData();
    exposureAnalysysAgentFn();
    // 埋点
    const fromPage = getUrlParam("fromPage");
    fromPage === "searchIndex"
      ? actionTracking("search_quick_filter", { item: "price_range" })
      : fromPage === "shop" || fromPage === "selfShop"
      ? actionTracking("shop_search_quick_filter", { item: "price_range" })
      : "";
  };
  // 清空价格区间
  const clearPrice = () => {
    minPrice.value = "";
    maxPrice.value = "";
    pageNum.value = 1;
    isNextPageV.value = 0;
    changeCategoryList();
    loadData();
    exposureAnalysysAgentFn();
  };
  
  // 前端实现搜索框筛选
  const searchName = (type, val) => {
    if (type === "CJ") {
      if (val) {
        manufacturerArr.value = manufacturerStats.value.filter(
          (i) => String(i.key).indexOf(val) > -1
        );
      } else {
        manufacturerArr.value = manufacturerStats.value;
      }
    } else if (type === "SJ") {
      if (val) {
        shopArr.value = shopStats.value.filter(
          (i) => String(i.showName).indexOf(val) > -1
        );
      } else {
        shopArr.value = shopStats.value;
      }
    }
  };
  
  const pagerChangePage = (page) => {
    pageNum.value = page;
    isCheckBoxStatusFlag.value = 1 ; // 翻页重新上报
    // changeCategoryList();
    loadData("changePage");
  };
  const changePage = (type) => {
    if (type === "last") {
      if (pageNum.value > 1) {
        pagerChangePage(pageNum.value - 1);
      }
    } else if (type === "next") {
      if (pageNum.value < pages.value) {
        pagerChangePage(pageNum.value + 1);
      }
    }
  };
  
  const scrollTimer = ref(null);
  const scrollTimeStart = ref(0);
  const scrollTimeEnd = ref(0);
  
  const handleScrollStart = () => {
    clearTimeout(scrollTimer.value);
    scrollTimer.value = setTimeout(handleScrollEnd, 100);
    scrollTimeStart.value = document.body.scrollTop;
    postHeight();
  };
  const handleScrollEnd = () => {
    scrollTimeEnd.value = document.body.scrollTop;
    if (scrollTimeEnd.value === scrollTimeStart.value) {
      clearTimeout(scrollTimer.value);
      // 露出曝光，反复曝光
      exposureView();
      exposureAnalysysAgentView();
    }
  };
  
  const calcPageHeight = (doc) => {
    const cHeight = Math.max(
      doc.body.clientHeight,
      doc.documentElement.clientHeight
    );
    const sHeight = Math.max(
      doc.body.scrollHeight,
      doc.documentElement.scrollHeight
    );
    const height = Math.max(cHeight, sHeight);
    return height;
  };
  
  // 动态设置iframe高度
  const postHeight = () => {
    let height = calcPageHeight(document);
    window.parent.postMessage(
      {
        message: "getSearchHeight",
        height,
      },
      "*"
    );
  };


  // 埋点方法 搜索结果页，除二级动态筛选外其他筛选排序选型点击变动时，上报筛选点击事件
  const exposureAnalysysAgentFn = ()=>{
      let text = sortStrategy.value == 1 ? "综合" : sortStrategy.value == 2 ? "销量从高到低" : "价格从低到高";
      try {
          window.AnalysysAgent.track("pc_action_search_filter_click", {
            key_word:keyword.value,
            changjia:selectedCJ.value,
            shangjia:selectedSJ.value,
            guige:selectedGG.value,
            yplx:selectedJYLX.value,
            period_validity:selectedYXQ.value.join(",") || null, //todo
            paixu:text,
            minprice:minPrice.value !== "" ? minPrice.value * 1 : null,
            maxprice:maxPrice.value !== "" ? maxPrice.value * 1 : null,
        });
      } catch (error) {
        
      }
      
  }
  // 二级动态筛选框露出，上报一次。翻页刷新页面重新上报。 || 搜索结果页，二级动态筛选框内选项每次点击，上报一次。
  const exposureAnalysysAgentCheckListFn = ()=> {
    const tagTypeArr = ref([]);
    const tagLabelArr = ref([]);
    dynamicLabelConfig.value.map(item => {
      tagTypeArr.value.push(item.labelGroupKey);
      tagLabelArr.value.push(item.labelName);
    })
    if(isCheckBoxStatusFlag.value == 1) {
      try {
        isCheckBoxStatusFlag.value = 0 ;  // 防止重复触发
        window.AnalysysAgent.track("pc_page_search_dynamic_filter_exposure", {
          key_word:keyword.value,
          tag_filter_type: tagTypeArr.value, // todo
          tag_filter: tagLabelArr.value
        });
      } catch (error) {
        
      }
    }else if(isCheckBoxStatusFlag.value == 2) {

      isCheckBoxStatusFlag.value = 0 ;  // 防止重复触发
      try {
          window.AnalysysAgent.track("pc_action_search_dynamic_filter_click", {
            key_word:keyword.value,
            tag_filter_type: jgClickCheckArr.value, // todo
            tag_filter: checkBoxStatusArr.value
          });
      } catch (error) {
      }
    }
  }



  onMounted(() => {
    getSearchCategory();
    loadData();
    window.addEventListener("scroll", handleScrollStart);
    //页面曝光 qt
    if(window.aplus_queue){
      aplus_queue.push({
				'action': 'aplus.record',
				'arguments': ['page_exposure', 'EXP', {
        'key_word': keyword.value,
				"spm_cnt":`1_4.searchResult_0-0_0.0.0.${window.parent.getSpmE?window.parent.getSpmE():''}`
				}]
			});
    }
  
  });
  
  onUnmounted(() => {
    window.removeEventListener("scroll", handleScrollStart);
  });
  let scmE=ref('')
let qtData = computed(() => {
  return {
    commonName: 'BigSearch',
    goodsType: goodsType.value,
    qtListData:qtListData.value,
    pageNum:pageNum.value
  }
})
  </script>
  <style lang="scss" scoped>
  .popover_style {
    margin: 0 auto;
     background: #ffffff;
      width: 1200px;
       padding: 10px;
        box-sizing: border-box;
         box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
          border: 1px solid #e4e7ed;
           transform: translateX(-5px);
            border-radius: 5px;
             position: relative;
              z-index: -2;
  }
  .wrapperBox {
    width: 1200px;
    background: #fff;
    margin: 0 auto;
    padding-bottom: 30px;
    padding-top: 10px;
    font-size: 12px;
    .triangle {
      margin-left: 10px;
      width: 0;
      height: 0;
      border-width: 5px;
      border-style: solid;
      border-color: transparent transparent transparent #999999;
      display: inline-block;
    }
    .topBar {
      // margin-top: 10px;
      border: 1px solid #eeeeee;
      background: #f9f9f9;
      line-height: 40px;
      padding-left: 20px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      font-size: 12px;
      .selectedBox {
        display: flex;
        align-items: center;
        .selectedItemBox {
          display: flex;
          align-items: center;
          padding: 0 10px;
          background: #ffffff;
          border: 1px solid #d6d6d6;
          border-radius: 2px;
          height: 24px;
          .el-icon:hover {
            color: #00b955;
          }
        }
        .selectedItem {
          display: inline-block;
          max-width: 180px;
          margin-right: 4px;
        }
      }
      .clearBtn {
        color: #666666;
        margin-left: 10px;
        display: flex;
        align-items: center;
      }
      .clearBtn:hover {
        color: #00b955;
      }
    }
    .searchRow {
      background: #f9f9f9;
      border: 1px solid #eeeeee;
      border-top: none;
      .flexBox {
        display: flex;
        overflow: hidden;
        .searchTitle {
          background: #f9f9f9;
          width: 83px;
          border-right: 1px solid #eeeeee;
          min-height: 40px;
          line-height: 40px;
          text-align: center;
          font-size: 12px;
        }
        .searchContent {
          background: #fff;
          width: 946px;
          padding-left: 10px;
          .searchItem {
            min-height: 32px;
            margin-top: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            .singleBtn {
              margin: -8px 20px 0 0;
            }
          }
          .itemBox {
            display: flex;
            flex-wrap: wrap;
            max-height: 142px;
            overflow-y: scroll;
            div {
              padding-top: 12px;
              font-size: 14px;
            }
            .el-checkbox {
              height: 24px;
              margin: 0px 20px 5px 0;
            }
            .itemText {
              display: inline-block;
              margin-right: 12px;
              color: #333333;
              max-width: 168px;
              cursor: pointer;
            }
            .itemText:hover {
              color: #00b955;
            }
            .activeText {
              background: #00b955;
              color: #fff;
              border-radius: 4px;
              padding: 1px 4px;
            }
            .activeText:hover {
              color: #fff;
            }
          }
        }
        .searchBtn {
          background: #fff;
          flex-grow: 1;
          display: flex;
          padding-top: 8px;
          div {
            display: flex;
            align-items: center;
            margin-right: 10px;
            border: 1px solid #e2e2e2;
            padding: 6px 10px;
            border-radius: 2px;
          }
        }
      }
    }
    .search-rows {
      display: flex;
      flex-direction: row;
    }
    .tableBar {
      margin-top: 10px;
      background: #f9f9f9;
      border: 1px solid #eeeeee;
      font-size: 12px;
    }
    .checkBox-ellipsis {
        line-height: 40px;
        max-width: 85px;
        height: 40px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #00B377;
      }
      .check-green-text {
        color: #00B377;
      }
    .tableTop {
      padding: 5px 10px 0px 10px;
      display: flex;
      flex-direction: row;
      align-items: center;
      background: #f9f9f9;
      height: auto;
      font-size: 12px;
      justify-content: space-between;
      .tableTop-left {
        display: flex;
        .el-button-group {
          display: flex;
        }
      }
      
      .priceBox {
        display: flex;
        align-items: center;
        position: relative;
        z-index: 9;
        .priceInput {
          display: flex;
          align-items: center;
          position: relative;
          z-index: 9;
        }
        .priceBtn {
          display: none;
        }
        .row-line {
          height: 14px;
          width: 1px;
          background: #bbbbbb;
          margin: 0 8px;
        }
      }
      .priceBox:hover .priceBtn {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        position: absolute;
        box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.08);
        border-radius: 2px;
        height: 65px;
        width: 140px;
        left: 55px;
        top: -6px;
        background: #fff;
        z-index: 0;
        padding: 0 6px;
        box-sizing: border-box;
      }
      .totalBox {
        display: flex;
        align-items: center;
        .pageIcon {
          width: 36px;
          height: 26px;
          border: 1px solid #e0e0e0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .leftIcon {
          margin: 0 6px 0 10px;
        }
        .rightIcon {
          margin-left: 6px;
        }
      }
    }
    .checkboxWrapper {
        padding: 0 10px 0 10px;
        display: flex;
        align-items: center;
        position: relative;
        .el-checkbox {
          margin-right: 14px;
        }
        .el-checkbox-group {
          flex-wrap: wrap;
        }
      }
    .douDiText {
      text-align: center;
      margin: 30px 0;
    }
    .tableContent {
      border: 1px solid #e0e0e0;
      border-top: none;
      .noGoods {
        position: relative;
        height: 140px;
        padding: 30px 0;
        img {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          width: 636px;
          height: 140px;
        }
      }
    }
    .pagination-container {
      margin: 20px 0;
      width: 758px;
      display: flex;
      justify-content: flex-end;
    }
  
    .itemBox::-webkit-scrollbar {
      display: none;
    }
  }
  </style>
  