<template>
    <div class="info1">
        <div>
            <div v-if="itemGoodsData.status === 2" class="sold-out">售罄</div>
            <img class="pic" v-lazy="imgUrl + '/ybm/product/min/' + itemGoodsData.imageUrl"
                :key="itemGoodsData.imageUrl" :alt="itemGoodsData.showName" />
            <img v-if="(itemGoodsData.markerUrl || '').length && !itemGoodsData.reducePrice"
                :src="imgUrl + itemGoodsData.markerUrl" class="activity-token" alt />
            <!-- 促销图标 -->
            <div class="promotionSkuPrice"
                v-if="(itemGoodsData.promotionSkuImageUrl) && (itemGoodsData.isControl != 1 || (itemGoodsData.isPurchase == true && itemGoodsData.isControl == 1))">
                <img :src="imgUrl + itemGoodsData.promotionSkuImageUrl" alt />
                <span>￥{{ itemGoodsData.promotionSkuPrice.toFixed(2) }}</span>
            </div>
            <div class="active-tags" v-if="itemGoodsData.activityTag && itemGoodsData.activityTag.tagNoteBackGroupUrl">
                <img :src="imgUrl + itemGoodsData.activityTag.tagNoteBackGroupUrl" alt />
                <div class="tejia806">
                    <span class="discount"> {{ itemGoodsData.activityTag.timeStr }} </span>
                    <div class="labelBox">
                        <span class="price806" v-for="(item3, index) in itemGoodsData.activityTag.skuTagNotes"
                            :key="index" :style="{ color: '#' + item3.textColor }">{{ item3.text }}</span>
                    </div>
                </div>
            </div>
            <!-- 店铺活动标签 -->
            <div class="active-tags shop-active-tags"
                v-if="itemGoodsData.activityTag && itemGoodsData.activityTag.tagNoteBackGroupUrl && +itemGoodsData.activityTag.sourceType === 2">
                <img :src="itemGoodsData.activityTag.tagNoteBackGroupUrl" alt />
                <div v-if="itemGoodsData.activityTag.customTopNote" class="customTopNote">{{
                    itemGoodsData.activityTag.customTopNote
                }}</div>
                <div>
                    <span class="shop-discount"> {{ itemGoodsData.activityTag.timeStr }} </span>
                    <span class="shop-text" v-for="(item3, index) in itemGoodsData.activityTag.skuTagNotes"
                        :key="index">{{ item3.text }}</span>
                </div>
                <div v-if="itemGoodsData.activityTag.customBottomNote" class="customBottomNote">{{
                    itemGoodsData.activityTag.customBottomNote
                }}</div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { reactive, ref, computed } from "vue";
import { useStore } from "vuex";
import CartBtn from './cartBtn.vue';
import PriceBox from "./priceBox.vue";
import Layer from './layer.vue';

const props = defineProps({
    productItem: {
        default: {}
    },
    licenseStatus: {
        default: ''
    },
    // 埋点数据
    buriedPoint: {
        default: {
            spid: '',
            sid: '',
            sptype: ''
        },
    }
});

const store = useStore();
const detailUrl = computed(() => store.state.app.detailBaseUrl);
const itemGoodsData = reactive(props.productItem);
const pageTitle = window.document.title;
const hasMask = ref(false);
const maskList = ref([]);
const imgUrl = import.meta.env.VITE_IMG;

function exit() {
    hasMask.value = false;
    // maskList.value = [];
}

function showAll(arr) {
    maskList.value = arr;
    hasMask.value = true;
};

</script>
<style lang="scss" scoped>
.info1 {
    padding-left: rem(20);
    width: rem(200);
    height: rem(200);
    padding-right: rem(15);
    border-radius: rem(10);
    position: relative;
    margin-top: rem(25);

    .sold-out {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 1.1rem;
        height: 1.1rem;
        background-color: rgba(0, 0, 0, 0.4);
        text-align: center;
        font-size: 14px;
        color: #fff;
        border-radius: 50%;
        line-height: 1.1rem;
        z-index: 100;
    }

    .pic {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: rem(200);
        max-height: rem(200);
    }

    .activity-token {
        position: absolute;
        width: rem(200);
        height: rem(200);
        left: rem(15);
        top: rem(15);
        z-index: 3;
    }

    .mark-text {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        z-index: 5;

        img {
            width: 100%;
            height: 100%;
        }

        h4 {
            position: absolute;
            left: 0;
            bottom: 0;
            font-size: rem(20);
            color: #fff;
            width: 100%;
            text-align: center;
        }
    }

    .promotionSkuPrice {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 5;

        span {
            position: absolute;
            right: 0;
            bottom: 0;
            font-size: 14px;
            color: #ff6201;
            display: flex;
            display: -webkit-flex;
            justify-content: center;
            -webkit-justify-content: center;
            align-items: center;
            -webkit-align-items: center;
            width: rem(140);
            height: rem(40);
            text-shadow: 1px 1px 1px #ffd9b4;
            font-weight: 600;
        }
    }

    .active-tags {
        position: absolute;
        left: rem(20);
        bottom: rem(10);

        img {
            width: rem(200);
            height: rem(200);
        }

        .tejia806 {
            position: absolute;
            bottom: rem(25.7);
            font-size: rem(20);
            text-align: center;
            width: rem(200);
            height: rem(24);

            .discount {
                font-size: rem(24);
                position: absolute;
                height: rem(24);
                line-height: rem(24);
                top: rem(-14);
                text-align: center;
                color: #fff;
                left: 50%;
                white-space: nowrap;
                transform: translateX(-50%) scale(0.3);
            }

            .labelBox {
                width: 160%;
                transform: scale(0.6);
                -webkit-transform-origin-x: 0%;
                padding-left: rem(7);

                span {
                    color: #fff;
                    display: inline-block;
                    font-size: rem(22);
                    overflow: hidden;
                    height: rem(34);
                }
            }
        }
    }

    .shop-active-tags {
        width: rem(200);
        height: rem(200);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        img {
            width: 100%;
            height: 100%;
        }

        .customTopNote {
            width: 200%;
            position: absolute;
            color: #fff;
            top: rem(5);
            left: 0;
            font-size: rem(26);
            line-height: rem(40);
            height: rem(40);
            text-align: center;
            transform: scale(0.5);
            display: inline-block;
            transform-origin: 0 0;
        }

        .customBottomNote {
            position: absolute;
            color: #fff;
            bottom: rem(-12);
            text-align: center;
            font-size: rem(20);
            line-height: rem(28);
            height: rem(28);
            transform: scale(0.5);
            width: 200%;
            display: inline-block;
            transform-origin: 0 0;
        }

        .shop-discount {
            width: 100%;
            position: absolute;
            color: #fff;
            font-size: rem(26);
            line-height: rem(32);
            height: rem(32);
            bottom: rem(29);
            left: rem(3);
            transform: scale(0.5);
            width: 200%;
            display: inline-block;
            transform-origin: 0 0;
        }

        .shop-text {
            width: 100%;
            position: absolute;
            color: #E23B3B;
            font-size: rem(32);
            line-height: rem(44);
            height: rem(44);
            bottom: rem(-3);
            text-align: center;
            transform: scale(0.5);
            width: 200%;
            display: inline-block;
            transform-origin: 0 0;
        }
    }
}
</style>
