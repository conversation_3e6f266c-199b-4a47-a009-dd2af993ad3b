<!-- 发票售后详情页 -->
<template>
  <div class="container">
    <div class="container_left">
      <ApplicationDetails :info="serviceInfo"/>
      <!-- 专票信息详情 -->
      <div v-if="serviceInfo.specialInvoiceInfo != null && serviceInfo.afterSalesType == 1" style="margin-top: 26px">
        <div class="left_title">
          <div class="green_style"></div>
          <h2>专票信息</h2>
        </div>
        <div class="info_list">
          <div class="info_item">
            <div>是否接受电子专票：</div>
            <div>{{ serviceInfo.specialInvoiceInfo.isElectronicInvoice == 1 ? '是' : '否' }}</div>
          </div>
          <div class="info_item">
            <div>公司名称：</div>
            <div>{{ serviceInfo.specialInvoiceInfo.companyName }}</div>
          </div>
          <div class="info_item">
            <div>纳税人识别号：</div>
            <div>{{ serviceInfo.specialInvoiceInfo.enterpriseRegistrationNo }}</div>
          </div>
          <div class="info_item">
            <div>地址：</div>
            <div>{{ serviceInfo.specialInvoiceInfo.registerAddress }}</div>
          </div>
          <div class="info_item">
            <div>电话：</div>
            <div>{{ serviceInfo.specialInvoiceInfo.phone }}</div>
          </div>
          <div class="info_item">
            <div>开户银行：</div>
            <div>{{ serviceInfo.specialInvoiceInfo.bankName }}</div>
          </div>
          <div class="info_item">
            <div>银行账号：</div>
            <div>{{ serviceInfo.specialInvoiceInfo.acct }}</div>
          </div>
        </div>
      </div>
      <!-- 补发资质详情 -->
      <div v-if="serviceInfo.afterSalesType == 2" style="margin-top: 26px">
        <div class="left_title">
          <div class="green_style"></div>
          <h2>需要补发资质</h2>
        </div>
        <div class="info_list">
          <div class="info_item" style="display: flex; flex-wrap: wrap; align-items: center;">
            <div class="info_item_text" style="width: 100px;">企业相关资质：</div>
            <div
              style="display: flex; align-items: center; margin-left: 6px"
              v-for="item in serviceInfo?.corpCredential?.split(',')"
              :key="item"
            >
              <el-checkbox disabled :checked="true" size="large" />
              <div style="margin-left: 6px; margin-top: 1px">{{ item }}</div>
            </div>
          </div>
          <div>
            <div class="info_item_text">企业相关资质：</div>
            <div style="height: 10px"></div>
            <div class="data_table_row" v-for="item in serviceInfo?.productCredentialList" :key="item.skuId">
              <img style="width: 44px; height: 44px" :src="item.imageUrl" alt="" />
              <div>
                <div class="data_table_row_text">
                  <p>{{ item.productName }}</p>
                  <p>
                    {{ item.spec != '' ? `/${item.spec}` : '' }}
                  </p>
                </div>
                <div class="data_table_row_check">
                  <el-checkbox
                    disabled
                    :checked="true"
                    size="large"
                    style="margin-right: 6px"
                    v-if="item.credentialType?.includes('1')"
                  />
                  <span style="margin-right: 40px; margin-top: 1px" v-if="item.credentialType?.includes('1')"
                    >药检报告</span
                  >

                  <el-checkbox
                    disabled
                    :checked="true"
                    size="large"
                    style="margin-right: 6px"
                    v-if="item.credentialType?.includes('2')"
                  />
                  <span v-if="item.credentialType?.includes('2')" style="margin-top: 1px">首营资质</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="container_right">
      <div style="display: flex; justify-content: end;">
        <el-button type="primary" @click="jumpSalesFn()">返回售后列表</el-button>
      </div>
      <div class="left_title" style="margin-bottom: 10px">
        <div class="green_style"></div>
        <h2>{{ processState?.auditProcessStateName ?? '' }}</h2>
        <div class="residue_time" v-if="processState?.countDownTime != null">
          <div class="residue_time_left">剩余时间</div>
          <div class="residue_time_right">{{  formatTimestampToDHM(processState.countDownTime) }}</div>
        </div>
      </div>
      <!-- 时间轴 -->
      <div v-if="processState?.auditProcessList != null" class="custom-wrap">
        <div v-for="(item, index) in processState.auditProcessList" :class="{ left70: index != 2 }"
          style="display: flex; flex-direction: column">
          <div style="display: flex" class="timeline-content">
            <div class="dot" :class="{ 'active-dot': item.isHighlight == 1 || index == 0 || index == 1 }"></div>

            <div class="item" v-show="index != 2"
              :class="{ active_border_style: (index == 2 && item.isHighlight == 1) || index == 0 }"></div>
          </div>

          <div class="timeline-title">
            <span :style="{
              color: index == 1 ? '#00b377' : '',
              transform:
                index == 2
                  ? 'translateX(-40%)'
                  : 'translateX(-30%)',
            }" style="display: inline-block">
              {{ item.labelTitle }}
            </span>
          </div>
        </div>
      </div>
      <!-- 商家已补发提示信息 -->
      <div v-if="processState?.tips != null" class="prompt_info">
        {{ processState.tips }}
        <div v-if="serviceInfo?.isShowCancelBtn == 1">
          <el-button style="margin-left: 390px;margin-top:5px;" @click="formBackDialogVisible = true">撤回售后申请</el-button>
        </div>
      </div>
      <!-- 表单 -->
      <div v-if="processState.auditProcessState == 6" class="form_back">
        <p class="form_title">请及时确认发票已退回</p>
        <p class="form_waring">若您超时未处理，您的售后申请将自动关闭。</p>
        <p class="required_icon form_select_title">物流信息</p>
        <div style="display: flex; margin-bottom: 12px">
          <el-input v-model="expressCompany" placeholder="请输入快递公司名称" style="margin-left: 10px;width: 300px;" />
          <el-input v-model="expressInput" placeholder="请输入快递单号" style="margin-left: 10px" />
        </div>
        <div style="display: flex; flex-wrap: wrap;">
          <div style="width: 73px">
            <div>
              <div style="font-size: 14px; color: #606266">上传凭证：</div>
              <div class="input_describe">（最多9张）</div>
            </div>
          </div>
          <div v-loading="uploadLoaind">
          <el-upload class="my-upload" action v-model:file-list="imgFileList" list-type="picture-card" :limit="9" :http-request="toUploadImg"
          :before-upload="beforeUpload" accept="image/*"
          :on-remove="imageRemove" :on-preview="imagePreview" style="width: 100%;" :on-exceed="onExceed">
          <el-icon>
            <Plus />
          </el-icon>
        </el-upload>
        </div>
        </div>
        <div class="bottom_btns">
          <el-button @click="formBackDialogVisible = true">撤回售后申请</el-button>
          <el-button @click="submitInvoice">确认发票已退回</el-button>
        </div>
      </div>
      <!-- 物流地址信息 -->
      <div v-if="serviceInfo.sellerAddressInfo != null" class="delivery_info">
        <div style="display: flex; align-items: center">
          <p class="delivery_title">请及时确认发票已退回</p>
          <button class="delivery_copy" @click="copyFn">一键复制</button>
          <input type="text" id="copyInput" value="" style="width: 1px; height: 1px; border: none; opacity: 0" />
        </div>
        <div class="delivery_list">
          <div class="delivery_item">
            <div>收货人：</div>
            <div>{{ serviceInfo.sellerAddressInfo.recipient }}</div>
          </div>
          <div class="delivery_item">
            <div>收货地址：</div>
            <div>{{ serviceInfo.sellerAddressInfo.deliveryAddress }}</div>
          </div>
          <div class="delivery_item">
            <div>联系电话：</div>
            <div>{{ serviceInfo.sellerAddressInfo.receivingPhone }}</div>
          </div>
          <div class="delivery_item" style="margin-bottom: 0px">
            <div>快递说明：</div>
            <div>{{ serviceInfo.sellerAddressInfo.expressRemarks }}</div>
          </div>
        </div>
      </div>
      <!-- 协商历史 -->
      <div class="consult_history">
        <div class="left_title">
          <div class="green_style"></div>
          <h2 style="margin-top: 1px">协商历史</h2>
        </div>
        <div class="history_box">
          <el-timeline>
            <el-timeline-item v-for="(item, index) in serviceInfo.auditRecords" :key="index" :timestamp="item.createTime"
              placement="top">
              <div class="history_info">
                <div style="width: 38px">
                  <el-avatar :size="38"
                    :src="item.logo == null ? 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png' : item.logo" />
                </div>
                <div class="history_info_content">
                  <!-- 名称 -->
                  <p class="history_info_name">{{ item.chatter }}</p>
                  <!-- 流程节点 -->
                  <p class="history_info_title">{{ item?.auditProcessStateName ?? '' }}</p>
                  <!-- 售后类型 -->
                  <p class="history_info_type">售后类型：{{ item.afterSalesTypeName }}</p>
                  <!-- 资质 -->
                  <p v-if="item.reissueCredential?.corpCredential != null" class="history_info_type">企业资质：{{ item.reissueCredential?.corpCredential }}</p>
                  <!-- 首营 -->
                  <p v-if="item.reissueCredential?.productCredential != null" class="history_info_type">商品首营资料：{{ item.reissueCredential?.productCredential }}</p>
                  <!-- 药检 -->
                  <p v-if="item.reissueCredential?.drugSupervisionReport != null" class="history_info_type">商品药检报告：{{ item.reissueCredential?.drugSupervisionReport }}</p>
                  <!-- 说明 -->
                  <p class="history_info_desc">补充说明：{{ item.remarks }}</p>
                  <!-- 凭证图片 -->
                  <div style="display: flex">
                    <div class="history_info_desc">凭证：</div>
                    <div style="display: flex; flex-wrap: wrap">
                      <div
                        v-for="(imgUrl, index) in (item?.evidences != null && item?.evidences.length > 0) ? item.evidences : []"
                        :key="index" @click="() => checkedImage(imgUrl)">
                        <img style="
                            width: 60px;
                            height: 60px;
                            margin-right: 8px;
                            margin-bottom: 2px;
                            border-radius: 4px;
                          " :src="imgUrl" alt="" />
                      </div>
                      
                    </div>
                  </div>
                  <!-- 物流地址信息 -->
                  <div v-if="item?.sellerAddressInfo != null" style="width: 430px; margin-bottom: 10px;">
                    <el-collapse  v-model="cardExpnded">
                     <el-collapse-item title="收货信息：" name="1">
                       <div>
                      <div class="delivery_item" style="display: flex;">
                        <div>收货人：</div>
                        <div>{{ item?.sellerAddressInfo.recipient }}</div>
                      </div>
                      <div class="delivery_item" style="display: flex;">
                        <div>收货地址：</div>
                        <div>{{ item?.sellerAddressInfo.deliveryAddress }}</div>
                      </div>
                      <div class="delivery_item" style="display: flex;">
                        <div>联系电话：</div>
                        <div>{{ item?.sellerAddressInfo.receivingPhone }}</div>
                      </div>
                      <div class="delivery_item" style="margin-bottom: 0px; display: flex;">
                        <div>快递说明：</div>
                        <div>{{ item?.sellerAddressInfo.expressRemarks }}</div>
                      </div>
                    </div>
                      </el-collapse-item>
                    </el-collapse>
                   
                  </div>
                  <!-- 物流公司 -->
                  <p v-if="item?.expressName != null && item?.expressName.length > 0" class="history_info_type">物流公司：{{ item?.expressName }}</p>
                  <!-- 运单号 -->
                  <p v-if="item?.expressNo != null && item?.expressNo.length > 0" class="history_info_type">运单号：{{ item?.expressNo }}</p>
                  <div>
                    <!-- 发票中有误的信息 -->
                    <div v-if="item.incorrectInvoiceInfo != null" class="history_info_desc" style="margin-top: 6px">
                      发票中有误信息：{{ item.incorrectInvoiceInfo }}
                    </div>
                    <!-- 专票信息 -->
                    <div v-if="item.specialInvoiceInfo != null">
                      <p class="history_info_desc">专票信息：</p>
                      <p class="history_info_desc">
                        是否接受电子专票：{{
                          item.specialInvoiceInfo.isElectronicInvoice == 1 ? '是' : '否'
                        }}
                      </p>
                      <p class="history_info_desc">
                        公司名称：{{ item.specialInvoiceInfo.companyName }}
                      </p>
                      <p class="history_info_desc">
                        纳税人识别号：{{ item.specialInvoiceInfo.enterpriseRegistrationNo }}
                      </p>
                      <p class="history_info_desc">
                        地址：{{ item.specialInvoiceInfo.registerAddress }}
                      </p>
                      <p class="history_info_desc">
                        电话：{{ item.specialInvoiceInfo.phone }}
                      </p>
                      <p class="history_info_desc">
                        开户银行：{{ item.specialInvoiceInfo.bankName }}
                      </p>
                      <p class="history_info_desc">
                        银行账号：{{ item.specialInvoiceInfo.acct }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      <div style="width: 100%; text-align: center;padding-top: 10px;">
        <el-button v-if="serviceInfo?.isShowApplyBtn == 1" type="primary" @click="onSubmitApplyFn"
          style="width: 142px; height: 46px; background-color: #00b377">再次发起售后</el-button>
      </div>
    </div>
  </div>
  <el-dialog v-model="formBackDialogVisible" title="确认撤回售后申请吗？" width="30%">
    <span>撤回后本次售后将关闭。</span>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="formBackDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="backApplyFn"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
  <div class="demo-image__preview">
    <el-image-viewer hide-on-click-modal @close="closeImagePreview" v-if="imgDialogVisible" :url-list="[imgDialogImageUrl]"/>
  </div>
</template>

<script setup>
import { ref, onBeforeMount ,onMounted} from 'vue'
import { ElMessage } from 'element-plus'
import ApplicationDetails from './components/applicationDetails.vue'
import { uploadvoucherImg, invoiceServiceDetails, queryProcessState, saveOperate } from '@/http_pc/api'
import { useRoute ,useRouter} from 'vue-router';
const route = useRoute();
const router = useRouter();
// 撤回申请弹层
const formBackDialogVisible = ref(false)
// 选中快递公司
const expressCompany = ref('')
// 快递单号
const expressInput = ref('')
// 申请详情
const serviceInfo = ref({});
const processState = ref({});
// 是否放大图片
const imgDialogVisible = ref(false)
const imgDialogImageUrl = ref('')
const imgFileList = ref([])
const imgPaths = ref([])
const afterSalesNo = ref()
const accountId = ref()
const uploadLoaind = ref(false)
// 弹层展示图片
const cardExpnded = ['1'];
const pageLocation = ref('');

onBeforeMount(() => {
  afterSalesNo.value = route.query?.afterSalesNo ?? '';
  accountId.value = route.query?.accountId  ?? '';
  // 获取售后详情
  invoiceServiceDetails({ afterSalesNo: afterSalesNo.value }).then((res) => {
    if (res.status === 'success') {
      serviceInfo.value = res.data;
      console.log(serviceInfo.value);
    }
  })
  // 获取售后状态节点
  queryProcessState({ afterSalesNo: afterSalesNo.value }).then((res) => {
    if (res.status === 'success') {
      processState.value = res.data;
    }
  })
})
// 图片删除
const imageRemove = (file) => {
  imgPaths.value = imgPaths.value.filter((e) => e.uid != file.uid)
}
const beforeUpload = (file) => {
    const isJPG = file.type === 'image/jpg' || file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/bmp' || file.type === 'image/gif';
    if (!isJPG) {
      ElMessage.error('请上传正确格式的图片');
      return false;
    }
    const size = 5;
    const isLt2M = file.size / 1024 / 1024 < size;
    if (!isLt2M) {
      ElMessage.error(`图片大小不得超过${size}M`);
      return false;
    }
  };
// 图片上传
const toUploadImg = async (file) => {
  uploadLoaind.value = true
try {
  var res =  await uploadvoucherImg(file)
  if (res.status === 'success') {
      imgPaths.value.push({
        path: res.data.downloadPath[0],
        uid: file.file.uid
      })
}
uploadLoaind.value = false
} catch (error) {
  uploadLoaind.value = false
  imgFileList.value.pop()
  ElMessage.error('上传失败')
}
}
// 上传超出
const onExceed = (fie,lis) => {
  ElMessage.warning('最多上传9张图片')
}
// 复制文本
const copyFn = () => {
  const copyText = document.getElementById('copyInput')
  copyText.value = `收货人：${serviceInfo.value.sellerAddressInfo.recipient} 收货地址：${serviceInfo.value.sellerAddressInfo.deliveryAddress} 联系电话：${serviceInfo.value.sellerAddressInfo.receivingPhone} 快递说明：${serviceInfo.value.sellerAddressInfo.expressRemarks}`
  copyText.select()
  copyText.setSelectionRange(0, 99999) // 适用于不同浏览器

  // 将文本复制到剪贴板
  var res = document.execCommand('copy')
  if (res) {
    ElMessage.success('复制成功')
  }
}
// 查看图片凭证
const checkedImage = (url) => {
  imgDialogVisible.value = true
  imgDialogImageUrl.value = url
  stop()
}
// 关闭图片凭证
const closeImagePreview =() => {
  imgDialogVisible.value = false
  move()
}
// 撤回申请
const backApplyFn = () => {
  formBackDialogVisible.value = false
  saveOperate({ operateType: 2, afterSalesNo: afterSalesNo.value, accountId: accountId.value }).then((res) => {
    if (res.status === 'success') {
      router.go(0)
    }
  })
}
// 再次发起售后
const onSubmitApplyFn = () => {
  if (serviceInfo.value.afterSalesType == 1) {
      router.push({
        path:'/invoiceService',
        query:{
          orderNo:serviceInfo.value.orderNo,
          accountId:accountId.value,
          orgId:serviceInfo.value.orgId
        }
      })
  }else {
    router.push({
        path:'/qualificationService',
        query:{
          orderNo:serviceInfo.value.orderNo,
          accountId:accountId.value,
          orgId:serviceInfo.value.orgId
        }
      })
  }
}
// 发票退回提交
const submitInvoice = () => {
  if (expressCompany.value == '' || expressInput.value.trim().length < 1) {
    return ElMessage.warning('请填写快递公司和快递单号')
  }
  var data = {
    operateType: 7,
    afterSalesNo: afterSalesNo.value,
    accountId: accountId.value,
    expressNo: expressInput.value,
    expressName: expressCompany.value
  }
  if (imgPaths.value.length > 0) {
    var lis = [];
    imgPaths.value.forEach((e) => {
      lis.push(e.path);
    })
    data['expressEvidence'] = JSON.stringify(lis)
  }
  saveOperate(data).then((res) => {
    if (res.status === 'success') {
      router.go(0)
    }
  })
}
// 跳转售后列表
function jumpSalesFn(){
  const baseUrl = import.meta.env.VITE_BASE_URL_PC;
  parent.window.open(`https:${baseUrl}merchant/center/order/index.htm?status=89`, "_self");
}

function formatTimestampToDHM(timestamp) {
  // 将时间戳转换为毫秒
  var totalMilliseconds = timestamp * 1000;

  // 计算天、小时和分钟
  var days = Math.floor(totalMilliseconds / (1000 * 60 * 60 * 24));
  var hours = Math.floor((totalMilliseconds % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  var minutes = Math.floor((totalMilliseconds % (1000 * 60 * 60)) / (1000 * 60));

  // 构建格式化的字符串
  var formattedTime = days + "天 " + hours + "小时 " + minutes + "分钟";

  return formattedTime;
}
// 预览图片
const imagePreview = (file) => {
  imgDialogVisible.value = true
  imgDialogImageUrl.value = file.url
  stop()
  
}

// 禁止滚动-在显示遮罩层的时候调用
  function stop() {
    let scrollTop = window.scrollY;//滚动的高度；
    pageLocation.value = scrollTop;
    document.body.style.position = 'fixed';
    document.body.style.top = '-' + scrollTop + 'px';
};
// 取消滑动限制-在关闭遮罩层的时候调用
function move() {
  document.body.style.position = 'static';
  window.scrollTo(0, pageLocation.value);
}
</script>

<style lang="scss" scoped>
.container {
  width: 980px;

  display: flex;

  .container_left {
    width: 400px;
    box-sizing: border-box;
    padding: 23px 20px;
    border-radius: 2px;
    background: #ffffff;
  }

  .left_title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .info_item {
    display: flex;
    margin-bottom: 12px;
  }

  .info_item div:nth-child(1) {
    width: 70px;
    font-size: 14px;
    margin-right: 24px;
    color: #666666;
    line-height: 14px;
  }

  .info_item div:nth-child(2) {
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    line-height: 14px;
  }
  .info_item_text {
    font-size: 14px;
    margin-right: 5px;
    color: #666666;
    line-height: 14px;
  }
  .data_table_row {
    display: flex;
    align-items: center;
    padding: 2px 20px 2px 10px;
    box-sizing: border-box;
    width: 360px;
    height: 52px;
    border: 1px solid #eeeeee;
    margin-top: -1px;
  }
  .data_table_row_text {
    display: flex;
    align-items: center;
    margin-left: 10px;
  }
  .data_table_row_text p:nth-child(2) {
    font-size: 12px;
    color: #676773;
  }
  .data_table_row_check {
    display: flex;
    align-items: center;
    height: 20px;
    margin-top: 4px;
    margin-left: 10px;
  }

  .container_right {
    width: 570px;
    box-sizing: border-box;
    margin-left: 10px;
    padding: 23px 20px;
    border-radius: 2px;
    background: #ffffff;

    .residue_time {
      display: flex;
      margin-left: 10px;
      font-size: 12px;

      .residue_time_left {
        width: 72px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background-image: linear-gradient(90deg, #fc7148 0%, #f62626 100%);
        border-radius: 2px 0 0 2px;
        color: #ffffff;
      }

      .residue_time_right {
        width: 123px;
        height: 24px;
        box-sizing: border-box;
        line-height: 24px;
        text-align: center;
        background: #fff7f7;
        border: 1px solid #f62626;
        border-radius: 2px 0 0 2px;
        color: #f62827;
      }
    }

    .form_back {
      padding: 12px;
      width: 530px;
      box-sizing: border-box;
      min-height: 274px;
      background: #fffcf4;
      border: 1px solid #ff950066;
      border-radius: 2px;

      .form_title {
        font-weight: 500;
        font-size: 14px;
        color: #332b29;
        line-height: 13px;
      }

      .form_waring {
        font-weight: 400;
        font-size: 12px;
        color: #f62827;
        margin-top: 8px;
        margin-bottom: 20px;
        line-height: 13px;
      }

      .form_select_title {
        margin-bottom: 4px;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        letter-spacing: 0.51px;
      }

      .input_describe {
        font-size: 12px;
        color: #bbbbbb;
      }

      .bottom_btns {
        display: flex;
        justify-content: end;
      }

      .bottom_btns button:nth-child(2) {
        background-color: #00b377;
        color: #ffffff;
      }
    }

    .delivery_info {
      box-sizing: border-box;
      width: 530px;
      padding: 12px;
      margin-top: 10px;
      background: #f7f7f8;
      border-radius: 2px;

      .delivery_title {
        font-weight: 500;
        font-size: 14px;
        color: #292933;
        line-height: 13px;
        margin-right: 7px;
      }

      .delivery_copy {
        height: 20px;
        background: #ffffff;
        border: 1px solid #dddddd;
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        text-align: center;
        line-height: 18px;
      }
      .delivery_list {
        margin-top: 5px;
      }
      .delivery_item {
        display: flex;
        margin-bottom: 6px;
      }

      .delivery_item div:nth-child(1) {
        font-size: 12px;
        color: #9494a6;
      }

      .delivery_item div:nth-child(2) {
        font-size: 12px;
        color: #333333;
      }
    }

    .consult_history {
      margin-top: 26px;

      .history_box {
        width: 530px;
        min-height: 354px;
        padding: 20px;
        box-sizing: border-box;
        background: #ffffff;
        border: 1px solid #f3f3f3;

        .history_info {
          display: flex;

          .history_info_content {
            margin-left: 10px;

            .history_info_name {
              margin-bottom: 6px;
              font-size: 14px;
              color: #7d7d7d;
              line-height: 14px;
            }

            .history_info_title {
              margin-bottom: 6px;
              font-weight: 600;
              font-size: 14px;
              color: #333333;
              line-height: 14px;
            }

            .history_info_type {
              margin-bottom: 6px;
              font-size: 12px;
              color: #333333;
              line-height: 14px;
            }

            .history_info_desc {
              margin-bottom: 6px;
              font-size: 12px;
              color: #333333;
              line-height: 14px;
            }
          }
        }
      }
    }
  }

  .prompt_info {
    width: 100%;
    padding: 12px;
    box-sizing: border-box;
    background: #f7f7f8;
    border-radius: 2px;
    font-size: 12px;
    color: #606266;
    line-height: 13px;
  }
}

.green_style {
  background-color: #01b377;
  margin-right: 8px;
  width: 3px;
  height: 18px;
  border-radius: 1px;
}

/*时间轴*/
.custom-wrap {
  width: 100%;
  display: flex;
  position: relative;
  height: 70px;
  margin-bottom: 10px;
  margin-left: 10px;
  justify-content: center;
  align-items: center;

  .left70 {
    width: 200px;
  }

  .timeline-content {
    position: relative;

    .timeline-title {
      position: absolute;
      left: -2px;
      top: -22px;
      //text-align: left;
      //margin-left: 50px;
      text-align: center;
      color: #333333;
      font-size: 14px;
      font-weight: 500;
    }

    .dot {
      border: 2px solid #dcdfe6;
      width: 20px;
      height: 20px;
      border-radius: 30px;
      background: white;
      margin: 6px 0;
      box-sizing: border-box;
      cursor: pointer;
    }
  }

  .item {
    flex: 1;
    border-bottom: 3px solid #dcdfe6;
    margin-bottom: 15px;
    box-sizing: border-box;
  }

  .active-dot {
    background-color: #00b377 !important;
    /*border: 5px solid #67C23A;*/
    margin-top: 2px;
    box-sizing: content-box;
    cursor: pointer;
  }

  .active_border_style {
    border-bottom: 3px solid #00b377 !important;
  }
}

.required_icon::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}
::v-deep .my-upload .el-upload-list {

    width: 89x;
    .el-upload--picture-card {
      height: 80px;
    width: 80px;
    }
   
}
::v-deep .el-upload-list--picture-card .el-upload-list__item-actions span+span {
      margin-left: 10px;
    }
::v-deep .el-upload-list--picture-card .el-upload-list__item  {
  height: 80px;
    width: 80px;
 }

 ::v-deep .el-image-viewer__mask {
        opacity: .7;
    }
    ::v-deep .el-image-viewer__img {
      width: 50%;
      height: 50%;
    }

</style>
