<template>
  <div class="my">
    <div class="myMerchant flex">
      <span></span>
      我的平安商户 
      <span class="user" style="width: 100px;background: none;font-size: 15px;" @click="goAccountInfo">
        账户信息<el-icon style="vertical-align: middle;top: -1px;"><ArrowRight /></el-icon>
      </span>
    </div>
    <div class="myMerchant-content">
      <div class="balance">
        <span style="font-size: 14px;">账户余额:</span>
        <h4 class="amountStyle" style="display: flex;">
          <div>
            <p><span>¥</span>{{ balance }}</p>
            <div class="explain flex">
              <span>平安商户钱包用于部分支付渠道收付款</span>
              <span class="exp flex" @click="handleGoIntroduce">
                查看说明<el-icon style="vertical-align: middle;top: -1px;"><ArrowRight /></el-icon>
              </span>
            </div>
          </div>
          <div style="width: 322px;text-align: right;">
            <div class="recharge" @click="openBugMoney" style="display: inline-block;">
              转出到购物金
            </div>
            <div class="recharge" @click="handleGoRecharge" style="display: inline-block;">
              充值
            </div>
            <div class="rechargeTitle" v-if="redPacketAmount">
              <div class="triangle-up"></div>
              <div class="promotion-tip">
                <i class="promotion-icon"></i>
                <span class="promotion-text">{{ rechargeDiscount }}<span style="color: #F70015">{{ redPacketAmount }}</span></span>
              </div>
            </div>  
          </div>  
          <!-- <div class="account" @click="goAccountInfo">
            账户信息
          </div> -->
          <!-- <span class="buyMoney" @click="openBugMoney">转出到购物金</span> -->
        </h4>
      </div>
    </div>
    <pay ref="payRef" :inputMoney="inputMoney" :tranNo="tranNo" @handleClose='handleClose'></pay>
    <!-- 平安商户明细 -->
    <pingAnMerchantsDetails></pingAnMerchantsDetails>
    <el-dialog
    v-model="dialogVisible"
    title="转出到购物金"
    width="500"
    :before-close="handleClose"
  >
    <div>
      <div class="content-buyMoney">
        <span>转出金额：</span>
        <div style="margin-top: -8px;">
          <div><el-input autocomplete="off" @input="verity" :controls="false" v-model.sync="inputMoney"  style="width: 300px;" placeholder="请输入转入金额"></el-input>&nbsp;&nbsp;元</div>
          <div style="color: red;" class="explain-buy" v-if="msg" >{{ msg }}</div>
          <div v-else class="explain-buy"><span style="opacity: 0.6;">本次最多可转出{{balance.toFixed(2)}}元</span><span class="all-out" @click="inputMoney=balance;verity()">全部转出</span></div>    
          <div style="height: 10px;"></div>
        </div>     
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <!-- <el-button @click="dialogVisible = false">Cancel</el-button> -->
        <el-button type="primary" @click="commit" :class="{buttonDis:buttonDis}" :disabled="buttonDis">
          确认转出
        </el-button>
      </div>
    </template>
  </el-dialog>
  </div>
</template>

<script setup>
  import pay from "./components/pay.vue"
  import pingAnMerchantsDetails from "./components/pingAnMerchantsDetails.vue";
  import { queryPingAnAccountBalance,queryTopUpBankCardInfo,payPwdQueryState,getTranNo,getPaymentDescPC } from '@/http_pc/api';
  import { getRechargeDiscount } from '@/http_pc/shoppingGold/index.js';
  import { ref,defineComponent,watch ,nextTick,getCurrentInstance  } from "vue";
  import { useRouter, useRoute } from 'vue-router';
  import { ElMessageBox,ElLoading,ElMessage } from 'element-plus';
  import  install from "@/utils/qt"
  if(!window.aplus_queue) {
    install()
  }
  defineComponent({
    pay,
    pingAnMerchantsDetails 
  })
  // try{
  //   const parentElement =  window.parent.document.getElementById('external-frame');
  //   if (parentElement) {
  //     parentElement.style.border = 'none';
  //   }
  // }catch(err){
  //   console.log("没有引入")
  // }
  const router = useRouter();
  const route = useRoute();
  const balance = ref(0);
  const { merchantId } = route.query;
  const dialogVisible=ref(false)//转出购物金弹窗
  const inputMoney=ref("")//转出金额
  const buttonDis=ref(true)
  const msg=ref('')
  const payRef=ref()
  const tranNo=ref("")
  // 购物金充值文案
  const rechargeDiscount = ref('')
  // 反红包金额
  const redPacketAmount = ref('')
  queryPingAnAccountBalance({ merchantId }).then((res) => {
    if (res.code === 1000) {
      balance.value = res.data.availableBalance;
    }
  })
 const handleClose=()=>{
  queryPingAnAccountBalance({ merchantId }).then((res) => {
    if (res.code === 1000) {
      balance.value = res.data.availableBalance;
    }
  })
 }
 handleClose()
 //校验输入
 watch(() => inputMoney.value, (newVal,ordVal) => { 
  if(/^([0]([.][0-9]{0,2})?|[1-9][0-9]*([.][0-9]{0,2})?)?$/.test(newVal)){
    inputMoney.value=newVal
  }else{
   nextTick(()=>{
    inputMoney.value=ordVal
  })
  }
})
 const verity=()=>{ 
  if(Number(inputMoney.value)>Number(balance.value)){
    msg.value='超出可转金额上限'
    buttonDis.value=true
  }else if(!inputMoney.value||Number(inputMoney.value)<=0){
    buttonDis.value=true
    msg.value = ''
  }  
  else{
    msg.value=''
    buttonDis.value=false
  }
 } 
 const BASEURL = import.meta.env.VITE_BASE_URL_PC;
 const openBugMoney=() =>{
  inputMoney.value=""
  msg.value=""
  getTranNo({
    merchantId
  }).then(res=>{
    tranNo.value=res.tranNo
    router.push({
      path: '/pinganMerchant/pc/buyMoneyOut',
      query: {tranNo:tranNo.value,inputMoney:inputMoney.value,balance:balance.value,merchantId}
    });
  })
  // dialogVisible.value=true
 }
 const commit=()=>{
  const loadingInstance = ElLoading.service()
  payPwdQueryState().then(data=>{
    if (data.data.state == 1) {
				//有支付密码
        payRef.value.openDialog()     
			} else {
				//没有支付密码
            ElMessageBox.confirm("为了您的资金安全，请先设置支付密码", '支付密码设置提醒', {
            confirmButtonText: '去设置',
            cancelButtonText: "关闭",
            customClass:'del-model'
          }).then(res=>{
            window.open(`https:${BASEURL}merchant/center/setPayPwd/index`)
          })
			}
  }).finally(e=>{
    loadingInstance.close()
  })
}
  const handleGoIntroduce = () => {
    router.push({
      path: '/pinganMerchant/pc/introduce',
      query: { from: 'my'},
    });
  };
  const goAccountInfo = () => {
    router.push({
      path: '/pinganMerchant/pc/account',
      query: { merchantId }
    });
  };
  const handleGoRecharge = () => {
    router.push({
          path: '/pinganMerchant/pc/recharge',
          query: { merchantId }
        });
    // queryTopUpBankCardInfo({merchantId}).then(res =>{
     
    //   // if(res.code === 1000){
    //   //   router.push({
    //   //     path: '/pinganMerchant/pc/recharge',
    //   //     query: { merchantId }
    //   //   });
    //   // }else if(res.code === 9999){
    //   //   ElMessageBox.alert('平安商户余额目前仅支持用于平安贷还款和转移至购物金，您未开通平安贷产品，暂时不允许充值', '提示', {
    //   //     confirmButtonText: '确定',
    //   //   })
    //   // }
    // })
    
  };
  // qt埋点
  // 页面曝光
  const handlePageExposure = () => {
    try {
      if(window.aplus_queue) {
        window.updateSpmESix();
        aplus_queue.push({
          'action': 'aplus.record',
          'arguments': ['page_exposure', 'EXP', {
            'spm_cnt': `1_4.myPingAnAccount_0-0_0.0.0.${window.getSpmE()}`
          }]
        });
      }
    }catch (e) {
      console.log(e)
    }
  }
  handlePageExposure()
  const initRechargeDiscount = () => {
    getRechargeDiscount({channel: 1}).then(res=>{
      if(res.code === 1000) {
        rechargeDiscount.value = res.data.data.highLevelRedPacketMsgUpperHalf;
        redPacketAmount.value = res.data.data.highLevelRedPacketMsgLowerHalf;
      }
    })
  }
  initRechargeDiscount()
</script>

<style lang="scss" scoped>
::v-deep .el-input-number .el-input__inner{
  text-align: left;
}
#app {
  background: #fff;
}

.my {
 height: 800px;
  background: #fff;
  width: 980px;
  margin: auto;
  padding: 30px 30px 0 30px;
  box-sizing: border-box;
  .user{
    cursor: pointer;
    color: #00B377;
    margin-left: 20px;
    font-size: 16px;
  }
  .buyMoney{
    color: #00B377;
    text-decoration: underline;
    margin-left: 20px;
    margin-top: 8px;
    cursor: pointer;
  }
  .buttonDis{
      background-color: #999;
      border: none;
    }
  .content-buyMoney{
    padding: 20px 10px;
    display: flex;
   
    .explain-buy{
      font-size: 13px;
      padding-top: 10px;
      margin-left: 6px;
   
    }
    .all-out{
      color: rgb(0,198,117);
      margin-left: 5px;
      cursor: pointer;
      opacity: 1 !important;
    }
  }
  .myMerchant-content {
    margin-top: 20px;
    width: 100%;
    border: 1px solid #EEEEEE;
    border-radius: 2px;
    display: inline-block;
    flex-direction: column;
    padding: 18px 20px;
    box-sizing: border-box;
  }
  .flex {
    display: flex;
    align-items: center;
  }
  .amountStyle {
    box-sizing: border-box;
    justify-content: space-between;
    p {
      font-size: 32px;
      font-face: MiSans;
      font-weight: 600;
      line-height: 30px;
      letter-spacing: 0;
      paragraph-spacing: 0;
      span {
        font-size: 14px;
      }
    }
  }
  .myMerchant {
    span {
      width: 4px;
      height: 16px;
      background: #00B377;
      margin-right: 10px;
    }
    font-weight: 600;
    font-size: 18px;
    color: #292933;
    letter-spacing: 0;
    line-height: 20px;
  }
  .balance {
    text-align: left;
    font-size: 12px;
    color: #292933;
    letter-spacing: 0;
    line-height: 16px;
    h4 {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      padding-top: 4px;
    }
    span {
      font-size: 12px;
    }
  }
  .explain {
    box-sizing: border-box;
    font-size: 12px;
    color: #999;
    letter-spacing: 0;
    line-height: 14px;
    margin-top: 15px;
    .exp {
      font-weight: 500;
      font-size: 12px;
      color: #00B955;
      letter-spacing: 0;
      line-height: 14px;
      margin-left: 10px;
      cursor: pointer;
    }
  }
  .transfer{
    width: 108px;
    height: 26px;
    background: #FFFFFF;
    border: 1px solid #D9D9D9;
    border-radius: 2px;
    border-radius: 4px;
    margin-left: 22px;
    text-align: center;
    font-size: 12px;
    font-weight: 540;
    cursor: pointer;
  }
  .recharge, .account {
    box-sizing: border-box;;
    margin-left: 22px;
    width: 100px;
    height: 26px;
    background: #00B955;
    border-radius: 2px;
    font-weight: 500;
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
    line-height: 26px;
    text-align: center;
    cursor: pointer;
  }
  .rechargeTitle {
    margin-top: 10px;
    margin-right: 120px;
    max-width: 320px;
    display: inline-block;
    text-align: left;
    position: relative;
    
    .triangle-up {
      width: 0;
      height: 0;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-bottom: 6px solid #FFF2F2;
      position: absolute;
      top: -6px;
      left: 133px; /* 调整位置，使其对准"转出到购物金"按钮 */
    }
    
    .promotion-tip {
      padding: 4px 10px;
      background-color: #FFEEEF;
      border-radius: 4px;
      display: flex;
      align-items: center;
      
      .promotion-icon {
        display: inline-block;
        width: 14px;
        height: 14px;
        background: url('@/assets/images/red-packet.png') no-repeat;
        background-size: contain;
        margin-right: 6px;
      }
      
      .promotion-text {
        font-size: 12px;
        line-height: 1.5;
        white-space: nowrap;
      }
    }
  }
  .account {
    border: 1px solid #D6D6D6;
    background: #fff;
    color: #333333;
    margin-left: 10px;
  }
}
</style>
<style>
.del-model {
 
  .el-button:nth-child(2) {
    margin-right: 10px;
 
    outline: none !important;
  }
}

</style>
