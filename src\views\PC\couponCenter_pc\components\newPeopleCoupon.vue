<template>
  <div class="box">
    <div class="discount">
      <template v-if="couponInfo.discount">
        <span class="big">{{couponInfo.discount?String(couponInfo.discount).split('.')[0]:''}}</span><span>{{couponInfo.discount?'.'+((String(couponInfo.discount).split('.') || [])[1] || 0) : ''}}</span>折
      </template>
      <template v-else>
        ￥<span class="big">{{couponInfo.moneyInVoucher}}</span>
      </template>
    </div>
    <div class="desc">{{ couponInfo.minMoneyToEnableDesc }}</div>
    <div class="explain">{{ couponInfo.voucherTitle }}</div>
    <template v-if="couponInfo.isLq === 0">
      <el-button round class="lq" @click.stop="toGetVoucher(couponInfo)">立即领取</el-button>
    </template>
    <template v-if="couponInfo.isLq === 1">
      <el-button round @click="toUseCoupon(couponInfo)">去使用</el-button>
    </template>
  </div>
</template>

<script setup>
  import { toUse, getVoucher } from './btn';

  const props = defineProps({
    couponInfo: {
      default: {}
    },
  });
  function toUseCoupon(couponInfo) {
    toUse(couponInfo.pcUrl);
  }
  function toGetVoucher(info) {
    getVoucher(info);
  };

</script>

<style scoped lang="scss">
.box{
  width: 262px;
  height: 310px;
  background: url("../../../../assets/images/couponCenter_pc/new_p.png");
  background-size: 100% 100%;
  margin: 0 10px 20px;
  box-sizing: border-box;
  padding:20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  .discount{
    margin-top: 20px;
    font-weight: 500;
    font-size: 24px;
    color: #FE2021;
    .big{
      font-size: 56px;
      color: #FF0000;
    }
  }
  .desc{
    font-weight: 500;
    font-size: 24px;
    color: #BC4707;
  }
  .explain{
    margin-top: 8px;
    font-weight: 400;
    font-size: 22px;
    color: #CE5919;
    line-height: 30px;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 这里是超出几行省略 */
    overflow: hidden;
  }
  .el-button{
    height: 57px;
    margin-top: 30px;
    border: 1.4px solid #FC0000;
    font-weight: 500;
    font-size: 26px;
    color: #FF0000;
    border-radius: 28px;
  }
  .el-button.lq{
    color: #FFFFFF;
    background-image: linear-gradient(180deg, #FF6B10 0%, #FD1801 100%);
  }
}
</style>
