
import Bridge from "@/config/Bridge";
export default function install(){
    //qt 埋点全局函数
   (function(w, d, s, q) { 
       w[q] = w[q] || []; 
       var f = d.getElementsByTagName(s)[0],j=d.createElement(s); 
       j.async=true; 
       j.id='beacon-aplus'; 
       j.src='https://o.alicdn.com/QTSDK/quicktracking-sdk/qt_web.umd.js'; 
       f.parentNode.insertBefore(j, f);
   })(window, document, 'script', 'aplus_queue');
  
   const QTDEVAPPKEY = "2cslafa5071qn0cun68rmoqe"; //测试appKey
   const QTPRODAPPKEY = "u9lsetdax5yk6fog7eqbvvqm";  //生产appKey
   const QTDOMAIN = "qt.ybm100.com";  // 埋点上报域名
   const PROD_HOSTNAMES = ['www.ybm100.com', 'www-new.stage.ybm100.com'];//生产和预发都走生产key
   
   const isProdOrStage = PROD_HOSTNAMES.includes(location.hostname);
   const appKey = isProdOrStage ? QTPRODAPPKEY : QTDEVAPPKEY;
   // 设置appkey
   aplus_queue.push({ action: 'aplus.setMetaInfo', arguments: ['appKey', appKey]  });
   // 设置数据收集域名
   aplus_queue.push({ action: 'aplus.setMetaInfo', arguments: ['aplus-rhost-v', QTDOMAIN]});
   //页面自动上报默认是开启的，如果需要关闭自动页面上报，true表示关闭，false表示开启
   aplus_queue.push({action: 'aplus.setMetaInfo',arguments: ['aplus-disable-apv', true]}); 
   // 通过设置true，开启全埋点功能，默认为开启，指定为false关闭本地全埋点功能
   aplus_queue.push({action: 'aplus.setMetaInfo',arguments: ['aplus-autotrack-enabled', false]});
    let chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    let Session="********";
    window.$sessionSpmE ='********'+result;
    try{
      Bridge.getQtTrackParams((data)=>{
        aplus_queue.push({
          action: 'aplus.setMetaInfo',
          arguments: ['_user_id', localStorage.getItem("merchantId")||'']
        });
        aplus_queue.push({
          action: 'aplus.appendMetaInfo', //追加全局属性
          arguments: ['globalproperty', {
              account_id: localStorage.getItem("accountId") || "",
          }]
        });
        console.log('拿到session',data)
        Session=(JSON.parse(data).session||'********')
        window.getSpmEV2=function(){
          let chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
          let result = '';
          for (let i = 0; i < 6; i++) {
              result += chars.charAt(Math.floor(Math.random() * chars.length));
          }
          return  Session+result;
        }             
        window.$sessionSpmE =(JSON.parse(data).session||'********')+result;
        if(window.isHaveSession){
          window.isHaveSession();
        }
      })
      
      
    }catch(e){
      window.getSpmEV2=function(){
        let chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < 6; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return  '********'+result;
      }
      window.$sessionSpmE ='********'+result;
    }
}