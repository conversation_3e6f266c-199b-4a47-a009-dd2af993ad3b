<template>
  <div class="change-card">
    <div class="my flex">
      <div class="change-head-left">
        <span></span>
        平安商户信息-换绑银行卡
      </div>
      <div style="display: inline-block; cursor: pointer;margin-left: auto; color: rgb(0,198,117);font-weight: 600;font-size: 12px;" @click="jumpServicePdf">《平安银行“产业结算通”会员服务协议》</div> 
      <div class="change-head-right" style="margin-left: 30px;" @click="resetAccount">
       返回
      </div>
    </div>
    <div class="account-content"> 
      <div class="account-step">
        <div class="account-step-box" style="width: 100%;">
          <mySteps :step="step" :stepStatus="stepStatus"/>
        </div>
       
        <div class="account-tips-info" v-if="step !== 1">
          <!-- <div class="step1-tips" v-if="step === 1">
            <p>1、提交修改后，当前已绑定的银行卡会被解绑，绑定新卡前将无法充值或提现</p>
            <p>2、企业只允许绑定“对公卡”，个体工商户支持绑定“对公卡”或“法人个人卡”</p>
          </div> -->
          <div class="step2-tips" v-if="step === 2 && stepStatus">您的银行卡信息已提交至银行进行审批，审批周期预计5-10分钟，请耐心等待！</div>
          <div class="step2-tips" v-if="step === 2 && !stepStatus">
            <div style="color:#303133">
              <span class="redCol">您的银行卡信息银行审核未通过或超时未做打款验证，</span>请参考驳回原因编辑信息重新提交
              <span class="step2-error" @click="rejectResult = true">查看驳回原因</span>
            </div>
          </div>
          <el-dialog width="30%" class="reject-dialog" v-model="rejectResult" title="驳回原因">
            <span>{{ accountInfo.message }}</span>
            <template #footer>
              <span class="dialog-footer">
                <el-button type="primary" @click="rejectResult = false">
                  确定
                </el-button>
              </span>
            </template>
          </el-dialog>
          <div class="step3-tips" v-if="step === 3">您的银行卡信息银行已审批通过，银行会向您的公账账户打款，打款可能存在一定延时，请您在账户收到打款后，48小时内进入此页面进行打款验证</div>
        </div>
      </div>
      <div class="account-form">
        <div class="bank-step1" v-if="step === 1 || (step === 2  && !stepStatus)">
          <div class="bank-form" style="margin-left: 180px;">
            <el-form class="form" ref="changeCardForm" :model="form" :rules="rules">
              <el-form-item label="企业名称" prop="enterpriseName" class="item">
                <!-- <p>统一社会信用代码</p> -->
                <el-input placeholder="请输入企业营业名称" maxlength="20" disabled v-model.trim="form.enterpriseName" style="width: 100%"  />
              </el-form-item>
              <el-form-item label="企业类型" props="businessType" class="item">
                <!-- <p>公账户名</p> -->
                <el-input placeholder="请输入企业类型" disabled v-model.trim="form.businessType" style="width: 100%;" />
              </el-form-item>
              <!-- <el-form-item label="卡类型" prop="cardType" class="item">
                <el-select
                  id="select1"
                  v-model="form.cardType"
                  placeholder="请选择公账账户开户银行，支持模糊搜索"
                  style="width: 100%"
                >
                :filterable="true"
                  :filter-method="getBankList"
                  @change="bankChange"
                  :key="form.cardType"
                  @hook:mounted="cancalReadOnly"
                  @visible-change="cancalReadOnly"
                  <el-option
                    v-for="item in cardTypeList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.label"
                  ></el-option>
                </el-select>
              </el-form-item> -->
              <el-form-item label="银行账号" prop="acct" class="item">
                <!-- <p>公账账户</p> -->
                <el-input placeholder="请输入银行账号" maxlength="30" v-model.trim="form.acct" style="width: 100%" />
              </el-form-item>
              <el-form-item label="开户银行" prop="bankName" class="item">
                <!-- <p>公账账户</p> -->
                <!-- <p>开户银行</p> -->
                <el-select
                  v-model="form.bankName"
                  placeholder="请选择公账账户开户银行，支持模糊搜索"
                  style="width: 100%"
                  :filterable="true"
                  :filter-method="getBankList"
                  @change="bankChange"
                  :key="form.bankName"
                >
                  <el-option
                    v-for="item in bankList"
                    :key="item.bankName"
                    :label="item.bankName"
                    :value="item.bankName"
                  ></el-option>
                </el-select>
                <!-- <el-input placeholder="请输入开户银行" maxlength="30" v-model.trim="form.bankName" style="width: 100%" /> -->
              </el-form-item>
              <el-form-item label="开户支行" prop="branchBankName" class="item">
                <!-- <p>公账账户</p> -->
                <!-- <el-input placeholder="请输入开户支行" maxlength="30" v-model.trim="form.branchBankName" style="width: 100%" /> -->
                <el-select
                  v-model="form.branchBankName"
                  placeholder="请选择公账账户开户银行，支持模糊搜索"
                  style="width: 100%"
                  :filterable="true"
                  :filter-method="searchSubBank"
                  @change="subBankChange"
                  :key="form.branchBankName"
                >
                  <el-option
                    v-for="item in subBankList"
                    :key="item.bankName"
                    :label="item.bankName"
                    :value="item.bankName"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="药店负责人手机号" prop="corporatePhone" class="item">
                <!-- <p>公账账户</p> -->
                <el-input placeholder="请输入药店负责人手机号" maxlength="11" v-model.trim="form.corporatePhone" style="width: 100%" />
              </el-form-item>
              <el-form-item label="验证码" prop="activeCode" class="item otpItem">
                <!-- <p>公账账户</p> -->
                <el-input placeholder="请输入验证码" maxlength="30" v-model.trim="form.activeCode" style="width: 100%" />
                <el-button class="otpBtn" type="primary" @click="sendActiveCode" :disabled="sendDisabled">获取验证码{{ sendDisabled ? `(${sendCountDown})` : '' }}</el-button>
              </el-form-item>
            </el-form>
            <div class="bank-submit">
              <div class="bank-btn" @click="submitForm">{{ step === 2 && !stepStatus ? '提交修改' : '提交新卡'}}</div>
            </div>
          </div>
        </div>
        <div class="bank-step2" v-if="(step === 2  && stepStatus) || step === 3">
          <div class="bank-item-content">
            <div class="bank-item-list">
              <div class="bank-span">账户名：</div>
              <el-input v-model="accountInfo.accountName" disabled></el-input>
            </div>
            <div class="bank-item-list">
              <div class="bank-span">银行账号：</div>
              <el-input v-model="accountInfo.acct" disabled></el-input>
            </div>
            <div class="bank-item-list">
              <div class="bank-span">开户行：</div>
              <el-input v-model="accountInfo.bankName" disabled></el-input>
            </div>
            <div class="bank-item-list">
              <div class="bank-span">开户支行：</div>
              <el-input v-model="accountInfo.branchBankName" disabled></el-input>
            </div>
            <div class="bank-item-list">
              <div class="bank-span">药店负责人手机号：</div>
              <el-input v-model="accountInfo.corporatePhone" disabled></el-input>
            </div>
          </div>
          <div class="bank-payment" v-if="step === 3">
            <div class="bank-payment-box">
              <div class="bank-payment-item">
                <div class="payment-title">
                  <span><b>*</b>小额打款金额：</span>
                </div>
                <el-input placeholder="请输入企业对公账户收到的打款金额" maxlength="30" v-model.trim="form.publicPrice" />
              </div>
              <div class="bank-payment-item">
                <div class="payment-title">
                  <span><b>*</b>鉴权序号：</span>
                </div>
                <el-input placeholder="请输入短信鉴权序号" maxlength="30" v-model.trim="form.signNo" />
              </div>
              <div class="bank-payment-item">
                <div class="bank-tips">
                  <p style="color: #333333;">未收到鉴权序号短信<span v-if="!resetSendDisabled" @click="openResetOtpFlag">&nbsp;重新发起验证></span><b v-else>{{ resetSendCountDown }}s后重新发起验证</b></p>
                </div>
              </div>
              <div class="item">
                <div class="item-box">
                  <el-checkbox
                    v-model="checkAgreement"
                    style="height: auto"
                  />
                  <div class="agreement">我已阅读并同意<span @click="handleCheckAgreement('https://my.orangebank.com.cn/orgLogin/hd/act//jianzb/jzbxym.html')">《平安银行电子商务“见证宝”商户服务协议》</span>和<span @click="handleCheckAgreement('https://auth.orangebank.com.cn/#/m/cDealOne?showNavBar=1')">《平安银行数字口袋协议》</span></div>
                </div>
              </div>
              <el-dialog width="450px" class="reset-dialog" v-model="resetOtpFlag" title="重新发起验证">
                <div class="reset-text">若当前商户手机号<span>{{form.corporatePhone}}</span>无法收到平安银行的鉴权短信，建议您更换手机号重新发起验证</div>
                <div class="bank-payment-item">
                  <div class="payment-title">
                    <span><span style="color: red;font-size: 15px;">*</span> 商户手机号</span>
                  </div>
                  <el-input type="number"  placeholder="请输入商户手机号" maxlength="11" oninput="if(value.length>11)value=value.slice(0,11)" v-model.trim="resetOtp.corporatePhone" style="width: 100%" />
                </div>
                <div class="bank-payment-item">
                  <div class="payment-title">
                    <span><span style="color: red;font-size: 15px;">*</span>验证码</span>
                  </div>
                  <div class="payment-content otpItem">
                    <el-input placeholder="请输入商户手机号收到的验证码" maxlength="30" v-model.trim="resetOtp.activeCode" style="width: 100%" />
                    <el-button class="otpBtn" type="primary" style="color:black;border: 1px solid #CDCDCD;" @click="sendResetOtp" :disabled="sendResetDisabled">获取验证码{{ sendResetDisabled ? `(${sendResetCountDown})` : '' }}</el-button>
                  </div>
                </div>
                <template #footer>
                  <span class="dialog-footer">
                    <!-- <el-button @click="resetOtpFlag = false">取消</el-button> -->
                    <el-button type="primary" @click="resetCheckOtp">
                      提交
                    </el-button>
                  </span>
                </template>
              </el-dialog>
              <div class="bank-submit">
                <div class="bank-btn" @click="verify">提交验证</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import mySteps from '../components/steps.vue';
import { reactive, ref, computed, onMounted } from "vue";
import { useRouter, useRoute } from 'vue-router';
import { useStore } from 'vuex';
import { ElMessage, ElMessageBox } from 'element-plus';
import { createPingAnAccount, queryBankList, querySubBankList, sendVerificationCode, paymentAuth, queryPingAnAccountInfo ,createPingAnAccountNew } from '@/http_pc/api';
const router = useRouter();
const route = useRoute();
const step = ref(1);
const stepStatus = ref(false);
const rejectResult = ref(false);
const store = useStore();
const accountInfo = computed(() => store.state.app.accountInfo || {});
const { merchantId } = route.query;
console.log(route.params, 'router')
const checkAgreement = ref(false);
const resetOtpFlag = ref(false);
const cardTypeList = reactive([
  {label: '企业对公卡'}
])
let form = reactive({
  enterpriseName: '', //企业名称
  businessType: '企业', //企业类型
  acct: '', //银行账号
  bankName: '', // 开户银行
  branchBankName: '', // 开户支行
  corporatePhone: '', //药店负责人手机号
  activeCode: '', //验证码
  publicPrice: "",
  signNo: ""
})
const resetOtp = reactive({
  corporatePhone: "",
  activeCode: ""
})
const bankList = ref([]);
const subBankList = ref([]);
onMounted(() => {
  queryPingAnData();
})
const onBeforeClose = (action) => {
  if (action === "confirm") {

  } else {
    return true;
  }
}
const originAcct = ref('');
const queryPingAnData = (isToaccount) => {
  queryPingAnAccountInfo({ merchantId }).then((res) => {
    if (res.code === 1000) {
      const { data } = res;
      originAcct.value = data.acct;
      if (res.code === 1000) {
        for(let i in form) {
          if (data[i]) {
            form[i] = data[i];
          }
        }
        switch(data.status) {
          case 1:
            step.value = 1;
            stepStatus.value = true;
            break;
          case 2:
            step.value = 2;
            stepStatus.value = true;
            break;
          case 4:
            step.value = 3;
            stepStatus.value = true;
            break;
          case 8:
            step.value = 2;
            stepStatus.value = false;
            break;
          case 16:
            step.value = 2;
            stepStatus.value = false;
            break;
          // case 32:
          //   step.value = 2;
          //   stepStatus = true;
          //   break;
        }
        store.commit('app/setAccountInfo', data);
        if(isToaccount && data.status == 32){
          router.replace({
            path: '/pinganMerchant/pc/account',
            query: { merchantId }
          });
        }
      }
    }
    //lwq
    // accountInfo.value={}
    // step.value=1
    // stepStatus.value=false
  })
}
const sendDisabled = ref(false);
const sendCountDown = ref(60);
const sendResetDisabled = ref(false);
const sendResetCountDown = ref(60);
const resetSendDisabled = ref(false);
const resetSendCountDown = ref(120);
const changeCardForm = ref(null);
const rules = {
  acct: [
    { required: true, message: '请输入银行账号', trigger: 'blur' },
  ],
  bankName: [
    { required: true, message: '请输入开户银行', trigger: 'blur' },
  ],
  branchBankName: [
    { required: true, message: '请输入开户支行', trigger: 'blur' },
  ],
  corporatePhone: [
    { required: true, message: '请输入药店负责人手机号', trigger: 'blur' },
  ],
  activeCode: [
    { required: true, message: '请输入短信验证码', trigger: 'blur' },
  ],
}


  const getBankList = (val) => {
    queryBankList({ bankName: val || ''}).then((res) => {
      if (res.code === 1000) {
        bankList.value = res.data;
      }
    })
  };
  getBankList();

  const searchSubBank = async(val) => {
    querySubBankList({bankName: form.bankName, subBankName: val}).then((res) => {
      if (res.code === 1000) {
        subBankList.value = res.data;
      }
    })
    //const res = await querySubBankList({bankName: this.accountVo.bankName, subBankName: val})
  };
  const bankChange = () => {
    form.branchBankName = '';
    searchSubBank();
  };

  const subBankChange = (val) => {
    if (val) {
      const value = subBankList.value.find((item) => {
        return item.bankName === val;
      })
      if (value) {
        form.branchBankCd = value.bankCode;
      }
    } else {
      form.branchBankCd = '';
    }
  };

const resetAccount = () => {
  router.go(-1);
}
const handleCheckAgreement = (url) => {
  location.href = url;
};
const sendResetOtp = async () => {
  if (resetOtp.corporatePhone) {
    sendResetDisabled.value = true;
    sendResetCountDown.value = 60;
    timeOutSendReset();
    const res = await sendVerificationCode({ mobileNumber: resetOtp.corporatePhone })
    if (res && res.code === 1000) {
      ElMessage.success('验证码发送成功')
    } else {
      sendResetDisabled.value = false;
      sendResetCountDown.value = 60;
      ElMessage.error(res.msg)
    }
  } else {
    ElMessage.warning('请输入商户手机号！');
  }
};
const openResetOtpFlag = () => {
  resetOtpFlag.value = true;
  resetOtp.corporatePhone = form.corporatePhone;
  resetOtp.activeCode = "";
  sendResetDisabled.value = false;
  sendResetCountDown.value = 60;
}
const timeOutTimer = ref(null);
const timeOutSendTimer = ref(null);
const timeOutSendResetOutTimer = ref(null);
const timeOutSendReset = () => {
  timeOutSendTimer.value && clearInterval(timeOutSendTimer.value);
  timeOutSendTimer.value = setInterval(() => {
    if (sendResetCountDown.value > 0) {
      sendResetCountDown.value --;
    } else {
      timeOutSendTimer.value && clearInterval(timeOutSendTimer.value);
      sendResetDisabled.value = false;
      sendResetCountDown.value = 60;
    }
  }, 1000)
};
const timeOutSendResetOutCheck = () => {
  timeOutSendResetOutTimer.value && clearInterval(timeOutSendResetOutTimer.value);
  timeOutSendResetOutTimer.value = setInterval(() => {
    if (resetSendCountDown.value > 0) {
      resetSendCountDown.value --;
    } else {
      timeOutSendResetOutTimer.value && clearInterval(timeOutSendResetOutTimer.value);
      resetSendDisabled.value = false;
      resetSendCountDown.value = 60;
    }
  }, 1000)
};
const resetCheckOtp = () => {
  console.log("重新发起鉴权")
  const params = { ...accountInfo.value, ...form, resendCode: 1, ...resetOtp };
  params.merchantId = merchantId;
  createPingAnAccount(params).then((res) => {
    console.log(res,' res')
    if (res.code === 1000) {
      ElMessage.success(res.msg);
      queryPingAnData();
      resetOtpFlag.value = false;
      resetSendDisabled.value = true;
      timeOutSendResetOutCheck();
    } else {
      ElMessage.error(res.msg);
    }
  });
}
const submitForm = () => {
  changeCardForm.value.validate((valid) => {
    if (valid) {
      const params = { ...accountInfo.value, ...form };
      params.mobileNo=  form.corporatePhone;
      params.merchantId = merchantId;
      params.update = step.value === 2 && !stepStatus.value ? 1 : 0;
      if(params.update){
        params.originAcct = originAcct.value
      }
     
      //是否重发写死0
      params.resendCode=  0;
      createPingAnAccountNew(params).then((res) => {
        console.log(res,' res')
        if (res.code === 1000) {
          ElMessage.success(res.msg);
        } else {
          ElMessage.error(res.msg);
        }
        queryPingAnData(true);
      });
    }
  })
}
const verifyDisabled = ref(false);
const verify = () => {
  if (step.value === 3) {
    if (form.publicPrice === "") {
      ElMessage.warning("请输入打款金额！");
      return;
    } else if (form.signNo === "") {
      ElMessage.warning("请输入鉴权序号！");
      return;
    } else if (!checkAgreement.value) {
      ElMessage.warning('请阅读并同意《平安银行电子商务“见证宝”商户服务协议》和《平安银行数字口袋协议》');
      return;
    }
  }
  if (verifyDisabled.value) {
    return;
  }
  verifyDisabled.value = true;
  const params = { ...accountInfo.value, ...form };
  params.merchantId = merchantId;
  paymentAuth(params).then((res) => {
    if (res.code === 1000) {
      // ElMessage.success(res.msg);
      setTimeout(() => {
        router.push({
          path: '/pinganMerchant/pc/account',
          query: { merchantId }
        });
      }, 100)
    } else {
      let str = '';
      if (res.code === 1001) {
        str = `银行卡验证失败。${res.msg}`;
      }
      if (res.code === 1002) {
        str = '银行卡验证失效，请重新提交开户申请';
      }
      ElMessageBox.alert(str, '提示', {
        confirmButtonText: '确定',
        callback: () => {
          queryPingAnData();
        },
      })
    }
    verifyDisabled.value = false;
  })
}
const sendActiveCode = async () => {
  if (form.corporatePhone) {
    sendDisabled.value = true;
    sendCountDown.value = 60;
    timeOutSend();
    const res = await sendVerificationCode({ mobileNumber: form.corporatePhone })
    if (res && res.code === 1000) {
      ElMessage.success('验证码发送成功')
    } else {
      sendDisabled.value = false;
      sendCountDown.value = 60;
      ElMessage.error(res.msg || res.errorMsg)
    }
  }
};
const timeOutSend = () => {
  timeOutTimer.value && clearInterval(timeOutTimer.value);
  timeOutTimer.value = setInterval(() => {
    if (sendCountDown.value > 0) {
      sendCountDown.value --;
    } else {
      timeOutTimer.value && clearInterval(timeOutTimer.value);
      sendDisabled.value = false;
      sendCountDown.value = 60;
    }
  }, 1000)
};
// 跳转协议
const jumpServicePdf = () => {
  window.open(`https://my.orangebank.com.cn/orgLogin/hd/act/jianzb/B2BClearing.html?name=${accountInfo.value.accountName == null ? '' : accountInfo.value.accountName}`);
}
</script>

<style lang="scss" scoped>
#app {
  background: #fff;
}
::v-deep .el-dialog__footer{
  border-top:none !important;
}
::v-deep .el-dialog__header{
  border-bottom: none !important;
}
.change-card {
  background: #fff;
  width: 980px;
  margin: auto;
  padding: 30px 0;
  .flex {
    display: flex;
    align-items: center;
  }
  .my {
    justify-content: space-between;
    padding: 0 20px;
    box-sizing: border-box;
    .change-head-left {
      display: flex;
      align-items: center;
      span {
        width: 4px;
        height: 16px;
        background: #00B377;
        margin-right: 10px;
      }
      font-weight: 600;
      font-size: 18px;
      color: #292933;
      letter-spacing: 0;
      line-height: 20px;
    }
    .change-head-right {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 14px;
      color: #666666;
      padding: 6px 8px;
      box-sizing: border-box;
      border: 1px solid #DDDDDD;
      border-radius: 2px;
      cursor: pointer;
    }
  }
  .account-content {
    .account-step {
      padding: 35px 20px 25px 20px;
      border-bottom: 1px solid #eee;
      box-sizing: border-box;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      .account-step-box {
        width: 50%;
      }
      .account-tips-info {
        margin-top: 20px;
        text-align: left;
        padding:13px 300px;
        flex: 1;
        background: #FFF7EF;
        // padding: 13px 20px;
        box-sizing: border-box;
        font-size: 12px;
        font-face: PingFangSC;
        font-weight: 400;
        color: #7a2809;
        line-height: 20px;
        display: flex;
        justify-content: center;
        flex-direction: column;
        .step1-tips {
          p {
            font-size: 12px;
            font-face: PingFangSC;
            font-weight: 400;
            color: #7a2809;
            line-height: 20px;
            &:first-child {
              color: #fe2021;
            }
          }
        }
        .step2-tips, .step3-tips {
          .redCol {
            color: #fe2021;
          }
          .step2-error {
            font-family: PingFangSC-Medium;
            font-weight: 500;
            font-size: 12px;
            color: #00B955;
            letter-spacing: 0;
            line-height: 14px;
            text-decoration: underline;
            cursor: pointer;
          }
        }
      }
    }
    .account-form {
      width: 100%;
      padding-top: 20px;
      box-sizing: border-box;
      .bank-submit {
        display: flex;
        justify-content: flex-end;
        .bank-btn {
          width: 300px;
          height: 32px;
          background: #00B955;
          border-radius: 2px;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          font-size: 14px;
          color: #FFFFFF;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
      }
      .bank-step2 {
        .bank-item-content {
          width: 50%;
          margin-left: 170px;
          background: #fff;
          display: flex;
          flex-direction: column;
          padding-bottom: 0;
          box-sizing: border-box;
          .bank-item-list {
            display: flex;
            margin-bottom: 10px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #000000a6;
            .bank-span {
              display: flex;
              white-space: nowrap;
              color: #676773;
              justify-content: flex-end;
              align-items: center;
              margin-right: 10px;
            }
            .bank-p {
              flex: 1;
              color: #292933;
              white-space: nowrap;
            }
          }
        }
        .bank-payment {
          // background: #FFF7EF;
          // border: 1px solid #FFC183;
          // margin-top: 10px;
          // padding: 30px 20px;
          margin-left: 280px;
          padding: 0px 0 20px 0;
          box-sizing: border-box;
          .bank-payment-box {
            width: 380px;
            // padding: 0 20px;
            // box-sizing: border-box;
          }
          .bank-payment-item {
            display: flex;
            // flex-direction: column;
            margin-bottom: 10px;
            align-items: center;
            .bank-tips {
              width: 100%;
              display: flex;
              justify-content: flex-end;
              p {
                width: 300px;
                font-size: 14px;
                font-face: PingFangSC;
                font-weight: 400;
                color: #9595a6;
                span {
                  color: #00B955;
                  cursor: pointer;
                }
                b {
                  font-family: PingFangSC-Regular;
                  font-weight: 400;
                  font-size: 14px;
                  color: #9595A6;
                }
              }
            }
          }
          .payment-title {
            // margin-bottom: 20px;
            // width: 36%;
            display: inline-block;
            text-align: right;
            width: 100px;
            margin-right: 10px;
            >span {
              font-family: PingFangSC-Medium;
              font-weight: 500;
              font-size: 12px;
              color: #676773;
              b {
                color: red;
              }
            }
            p {
              font-size: 12px;
              font-face: PingFangSC;
              font-weight: 400;
              line-height: 0;
              >span {
                color: #00b955;
              }
            }
          }
          .payment-content {
            flex: 1;
            display: flex;
            overflow: hidden;
            // width: 270px;
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.change-card {
  .el-input__wrapper:focus {
    outline: none !important;
  }
  .bank-form {
    background: #FFFFFF;
    width: 50%;
  }
  .otpItem {
    .el-button {
      height: 32px;
      margin-left: 10px;
      background: #fff;
      border-radius: 2px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #01B377;
      border: 1px solid #00B955;
    }
  }
  // margin-top: 10px;
  
  
  .el-checkbox__label {
    display: inline-grid;
    white-space: pre-line;
    word-wrap: break-word;
    overflow: hidden;
    line-height: 20px;
  }
  // .el-form-item {
  //   display: block;
  // }
  .el-input, .el-select {
    width: 300px !important;
    height: 32px;
    .el-input__wrapper {
      border-radius: 2px;
    }
    .el-input__inner {
      border: none;
      border-radius: 2px;
      outline: none !important;
      :focus {
        outline: none !important;
      }
    }
  }
  .bank-step2 {
    .reset-text {
      color: #666666;
      font-size: 14px;
      font-face: PingFangSC;
      font-weight: 400;
      margin-bottom: 20px;
      line-height: 20px;
      span {
        color: #00B377;
      }
    }
    .el-dialog__header {
      border-bottom: 1px solid #EFEFEF;
      margin-right: 0;
    }
    .el-dialog__footer {
      border-top: 1px solid #EFEFEF;
    }
    
    .el-input {
      width: 270px !important;
    }
    .reset-dialog {
      
    }
    .reject-dialog {
      span {
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 14px;
        color: #676773;
      }
    }
    .bank-payment {
      .el-input {
        height: 32px;
      }
    }
  }
  .bank-payment-item {
    .payment-title {
      // flex: 1;
      display: flex;
      justify-content: flex-end;
      width: 20%;
    }
    .el-input {
      // width: 300px;
      flex: 1;
    }
  }
  .bank-item-list {
    .bank-span {
      flex: 1;
    }
    .el-input {
      width: 300px;
    }
  }
  .item {
    // margin: 0 0 rem(40) 0;
    display: flex;
    justify-content: flex-end;
    .item-box {
      width: 300px;
      margin-bottom: 20px;
    }
    p {
      font-weight: 500;
      font-size: 12px;
      color: #676773;
      line-height: 18px;
    }
    .agreement {
      font-size: 12px;
      // line-height: rem(33);
      margin-left: 10px;
      display: inline;
      vertical-align: top;
      span {
        color: #01B377;
        cursor: pointer;
      }
    }
    .el-form-item__label {
      flex: 1;
    }
    .el-form-item__content {
      flex: inherit;
      width: 300px !important;
      display: flex;
      flex-wrap: inherit;
    }
    .el-checkbox {
      align-items: flex-start;
    }
  }
}
</style>