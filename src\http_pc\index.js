import axios from 'axios';

const MODE = import.meta.env.MODE;
const BASEURL = import.meta.env.VITE_BASE_URL_PC;

console.log('-----MODE----测试stage环境', MODE);
const service = axios.create({
  baseURL: MODE === 'development' ? '/api' : BASEURL,
  timeout: 50000
});
service.defaults.headers['X-Requested-With'] = 'XMLHttpRequest';

// 请求前的统一处理
service.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  function(response) {
    const { headers } = response;
    if (headers.sessionstatus) {
      const redirectUrl = window.parent.location.href.replace(window.parent.location.origin, '');
      if(headers.sessionstatus == 'sessionTimeOut') {
        window.parent.location.href = '/login/login.htm?redirectUrl=' + encodeURIComponent(redirectUrl);
      } else if(headers.sessionstatus == 'passwordTimeOut') {
        window.parent.location.href = '/login/login.htm';
      } else if(headers.sessionstatus == 'isCrawler') {
        window.parent.location.href = '/login/login.htm';
      } else if(headers.sessionstatus == 'isKickOut') {
        window.parent.location.href = '/login/login.htm';
      }
      response = { error: '登录过期' };
      return Promise.reject(response);
    }
    return Promise.resolve(response);
  },
  function(err) {
    return Promise.reject(err);
  }
)

export default {
  /**
   * get方法，对应get请求
   * @param {String} url [请求的url地址]
   * @param {Object} params [请求时携带的参数]
   */
  get(url, params, config, exportExcel) {
    return new Promise((resolve, reject) => {
      service
        .get(url, {
          params,
          ...config
        })
        .then((res) => {
          if (exportExcel) {
            resolve(res);
            return false;
          }
          resolve(res.data)
        })
        .catch((err) => {
          reject(err)
        })
    })
  },
  /**
   * post方法，对应post请求
   * @param {String} url [请求的url地址]
   * @param {Object} params [请求时携带的参数]
   */
  post(url, params, config, exportExcel) {
    return new Promise((resolve, reject) => {
      service
        .post(url, params, config)
        .then((res) => {
          if (exportExcel) {
            resolve(res);
            return false;
          }
          resolve(res.data)
        })
        .catch((err) => {
          reject(err)
        })
    })
  },
  postV2(url, params, config, exportExcel) {
    return new Promise((resolve, reject) => {
      service
        .post(url, params, config)
        .then((res) => {
          if (exportExcel) {
            resolve(res);
            return false;
          }
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  },
  /**
   * postFormData方法，对应post请求，用来提交文件+数据
   * @param {String} url [请求的url地址]
   * @param {Object} params [请求时携带的参数]
   */
  postFormData(url, params, config, exportExcel) {
    return new Promise((resolve, reject) => {
      service({
        headers: {
          // 'Content-Type': 'multipart/form-data'
          'Content-Type': 'application/x-www-form-urlencoded'
        },

      
        // transformRequest: [
        //   (data) => {
        //     // 在请求之前对data传参进行格式转换
        //     const formData = new FormData()
        //     Object.keys(data).forEach((key) => {
        //       formData.append(key, data[key])
        //     })
        //     return formData
        //   }
        // ],
        transformRequest: [function(data){
          let ret = '';
          for(let key in data){
            ret+=encodeURIComponent(key)+'='+encodeURIComponent(data[key])+'&'
          }
          return ret
        }],
        url,
        method: 'post',
        data: params,
        ...config
      })
        .then((res) => {
          if (exportExcel) {
            resolve(res);
            return false;
          }
          resolve(res.data)
        })
        .catch((err) => {
          reject(err)
        })
    })
  },
  
  postFormDataMultipart(url, params, config, exportExcel) {
    return new Promise((resolve, reject) => {
      service({
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        transformRequest: [
          (data) => {
            // 在请求之前对data传参进行格式转换
            const formData = new FormData()
            Object.keys(data).forEach((key) => {
              formData.append(key, data[key])
            })
            return formData
          }
        ],
        url,
        method: 'post',
        data: params,
        ...config
      })
        .then((res) => {
          if (exportExcel) {
            resolve(res);
            return false;
          }
          resolve(res.data)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }
}

