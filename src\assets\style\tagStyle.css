
@charset "UTF-8";
span {
  display: inline-block;
  border-radius: rem(2);
  font-size: rem(24);
  height: rem(36);
  text-align: center;
  line-height: rem(34);
  color: #ffffff;
  letter-spacing: 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 0 rem(8);
}

.span1 {
  border: 1px solid rgba(255, 114, 0, 0.5);
  color: #FF7200;
  background: #fef9f2;
}

.span2 {
  border: 1px solid rgba(255, 71, 65, 0.5);
  color: #FF4741;
  background: #FFF6F5;
}

.span3 {
  color: #fff;
  background: #ff5b5b;
}

.span4 {
  background: #FAFAFA;
  color: #9494A6;
  border: 1px solid #d1d1d6;
}

.span5 {
  background: #F2FBFB;
  color: #00b3b3;
  border: 1px solid rgba(0, 179, 179, 0.5);
}

/*
// 自营
.span1 {
  background: rgba(0, 179, 119, 0.05);
  border: 1px solid rgba(0, 179, 119, 0.5);
  color: #00B377;
}

//自定义
.span2 {
  background: rgba(255, 71, 65, 0.05);
  border: 1px solid rgba(255, 71, 65, 0.5);
  color: rgba(255, 71, 65, 1);
}

//临期 近效期
.span3 {
  background: rgba(247, 141, 0, 0.05);
  border: 1px solid rgba(255, 114, 0, 0.5);
  color: rgba(255, 114, 0, 1);
}

//不返利 不支持余额支付
.span4 {
  background: rgba(250, 250, 250, 1);
  border: 1px solid rgba(209, 209, 214, 1);
  color: rgba(103, 103, 115, 1);
}

//返利券 返利 券
.span5 {
  background: rgba(255, 33, 33, 0.05);
  border: 1px solid rgba(255, 33, 33, 0.5);
  color: rgba(255, 33, 33, 1);
}

//协
.span6 {
  background: rgba(67, 98, 244, 0.05);
  border: 1px solid rgba(67, 98, 244, 0.5);
  color: rgba(67, 98, 244, 1);
}

//套餐 特价 满减 满折 满赠 满减赠
.span7 {
  background: rgba(255, 33, 33, 0.05);
  border: 1px solid rgba(255, 33, 33, 0.5);
  color: rgba(255, 33, 33, 1);
}

//直降
.span8 {
  background: rgba(239, 128, 67, 0.05);
  border: 1px solid rgba(239, 128, 67, 0.5);
  color: rgba(239, 128, 67, 1);
}

//医保
.span9 {
  background: rgba(0, 179, 179, 0.05);
  border: 1px solid rgba(0, 179, 179, 0.5);
  color: rgba(0, 179, 179, 1);
}

//独家
.span10 {
  background: rgba(143, 112, 49, 0.05);
  border: 1px solid rgba(143, 112, 49, 0.5);
  color: rgba(143, 112, 49, 1);
}

//高毛
.span11 {
  background: rgba(133, 53, 231, 0.05);
  border: 1px solid rgba(133, 53, 231, 0.5);
  color: rgba(133, 53, 231, 1);
}

//战略合作
.span12 {
  background: rgba(217, 63, 63, 0.05);
  border: 1px solid rgba(217, 63, 63, 0.5);
  color: rgba(217, 63, 63, 1);
}

//仿制药一致性评价
.span16 {
  background: rgba(76, 128, 255, 0.05);
  border: 1px solid rgba(76, 128, 255, 0.5);
  color: rgba(76, 128, 255, 1);
}
*/
/*# sourceMappingURL=tagStyle.css.map */