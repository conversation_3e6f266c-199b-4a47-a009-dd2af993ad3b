<template>
    <div class="wrapperBox">
        <el-row class="myMerchant">
            <el-col>
                <span></span>
                我的平安贷
            </el-col>
        </el-row>
        <div class="part part01">
            <el-row :gutter="20">
                <el-col :span="12" class="side side-left">
                    <el-row :gutter="20">
                        <el-col :span="12" style="position: relative;height: 110px;border-right: 1px solid #999999;">
                            <div class="head-line">可用额度（元）</div>
                            <h1 class="money">{{availableAmount}}</h1>
                            <div class="sum-money" style="position: absolute;bottom:0;">总额度（元）：{{approveAmount}}</div>
                        </el-col>
                        <el-col :span="12" style="padding-left: 20px;">
                            <div class="head-line" @click="handleClickTags">
                                剩余应还本金（元）
                                <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    content="截止今日00:00所有待还的订单本金，当日还款后请次日9:00查询最新待还"
                                    placement="top-start"
                                >
                                    <el-icon :size="size" :color="color"><Warning /></el-icon>
                                </el-tooltip>
                            </div>
                            <h1 class="money difference-money">{{ curPrincipalAmountBalance }}</h1>
                        </el-col>
                    </el-row>
                </el-col>
                <el-col :span="12" class="side side-right">
                    <el-card class="box-card" shadow="never">
                        <template #header>
                        <div class="card-header">
                            <h2>平安贷交易回单查询</h2>
                        </div>
                        </template>
                        <div  class="text item">1、登录平安银行电子商务见证业务查询系统</div>
                        <div  class="text item">
                            <a href="https://ebank.orangebank.com.cn/corporbank/fastSearch.do" target="_blank">
                                https://ebank.orangebank.com.cn/corporbank/fastSearch.do
                            </a>
                        </div>
                        <div  class="text item">2、进入“交易资金查询-电商见证宝平台”模块</div>
                        <div  class="text item">平台名称：查询筛选“药帮忙”</div>
                        <div  class="text item">证件号码：注册公司营业执照号码</div>
                        <div  class="text item">手机号码：注册公司开通平安商户时的预留手机号</div>
                    </el-card>
                </el-col>
            </el-row>
        </div>
        <div class="part part02">
            <div class="table-header-line">
                <el-row :gutter="20">
                    <el-col :span="10" class="abstract">历史账单</el-col>
                    <el-col :span="6">
                        <el-form-item label="时间">
                            <el-select v-model="queryMonth" placeholder="请选择 " @change="getList">
                                <el-option label="全部" value="" />
                                <el-option :label="'近6个月账单'" :value="6" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5">
                        <el-form-item label="状态">
                            <el-select v-model="payoffflag" placeholder="请选择 " @change="getList">
                                <el-option label="全部" value="" />
                                <el-option label="未结清" value="0" />
                                <el-option label="已结清" value="1" />
                                <el-option label="未出账" value="2" />
                                <el-option label="无需还款" value="3" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="3">
                        <el-button type="success"  class="last-float-right" @click="toDownLoad">下载账单</el-button>
                    </el-col>
                </el-row>
            </div>
            <div class="table-box">
                <el-table 
                    :data="tableData" border  
                    :cell-style = "cellStyle" 
                    :header-cell-style="{textAlign: 'center', background: '#f2f2f2', height:'60px'}"
                    @row-click="handleRowClick"
                    v-loading="loading"
                    height="600"
                >
                    <el-table-column 
                        v-for="(item, index) in columns" 
                        :key="index" 
                        :prop="item.prop" 
                        :label="item.label"  
                        :formatter="formatData(item.prop)"
                        align="center"
                    >
                        <template #default="scope">
                            <div v-if="'operation' === item.prop && scope.row.payoffflag !==3 " style="color:#13c775;cursor:default;">查看详情</div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </div>
    <DownloadDialog
      v-if="showDownload"
      :cancelDialog="handleCancel"
    />
</template>
<script setup>
	import { actionTracking } from '@/config/eventTracking';
    import { onMounted, ref, nextTick  } from "vue";
    import { getLoans, getPingAnCreditBalance } from '@/http_pc/pinganLoans';
    import { useRouter,useRoute } from 'vue-router'
    import DownloadDialog from './components/downloadDialog.vue';

    const loading = ref(true)
    const router = useRouter()
    const route = useRoute()
    const availableAmount = ref(0.00)
    const approveAmount = ref(0.00)
    const curPrincipalAmountBalance = ref(0.00)
    const queryMonth = ref(6)
    const payoffflag = ref("")
    const showDownload = ref(false);
    const merchantId = ref("")
    const columns = ref([
        { prop: 'billName', label: '账单信息' },
        { 
            prop: 'payoffflag', 
            label: '状态' 
        },
        { prop: 'curPrincipalAmountBalance', label: '剩余应还本金' },
        { prop: 'actPrincipalAmount', label: '已还本金' },
        { prop: 'actInterestAmount', label: '已还利息' },
        { prop: 'actSubsidyInterestAmount', label: '平台贴息' },
        { prop: 'subsidyInterestDate', label: '贴息截止日'},
        { prop: 'operation', label: '操作' },
    ])
    const tableData = ref([])
    const childComp = ref(null);
    const formatData = (prop) => {
        return (row, column, cellValue, index) => {
          if(prop==="payoffflag"){
            switch(cellValue){
                case 0:
                    return "未结清"
                break;
                case 1:
                    return "已还清"
                break;
                case 2:
                    return "未出账"
                break;
                case 3:
                    return "无需还款"
                break;
            }
          }
          return cellValue
        }
    }
    onMounted(()=>{
        merchantId.value = route.query.merchantId
        localStorage.setItem("merchantId",route.query.merchantId)
        //埋点
        actionTracking('pc_action_pingan_loan', { 
            pageName: '我的平安贷' ,
        })
        getList()
        getPingAnCreditBalanceCurrent()
    })
    //平安epay余额数据
    const getPingAnCreditBalanceCurrent = () => {
        getPingAnCreditBalance({merchantId:merchantId.value}).then((res) => {
            if (res.code === 1000) {
                availableAmount.value = res.data.availableAmount
                approveAmount.value = res.data.approveAmount
			}
		}).catch(() => {
			console.log("系统异常")
		})
    } 
    //表格数据
    const getList = () => {
		getLoans({merchantId:merchantId.value,queryMonth:queryMonth.value,payoffflag:payoffflag.value,billCode:""}).then((res) => {
            if (res.code === 1000) {
                loading.value = false
				tableData.value = res.data.records
                curPrincipalAmountBalance.value = res.data.curPrincipalAmountBalance
			}
		}).catch(() => {
			console.log("系统异常")
		})
	}
    //查看详情
    const handleRowClick = (row,columnName) => {
        if (columnName.property==="operation"){
            router.push({ path: "/pinganMerchant/pc/loansdetail", query: { billCode:row.billCode, payoffflag:row.payoffflag } })
        }
    }
    /* 单元格样式 */
    const cellStyle = (row) => {
        if(row.column.property==="actSubsidyInterestAmount"){
            return {color:'#ff6600'}
        }
        if(row.column.property==="payoffflag") {
            if(row.row.payoffflag===0){
                return {color:'red'}
            }
            if(row.row.payoffflag===1 || row.row.payoffflag === 3){
                return {color:'#13c775'}
            }
        }
        if(row.column.property==="subsidyInterestDate"){
            if(row.row.payoffflag===0 || row.row.payoffflag===2){
                return {color:'red'}
            }
        }
        if(row.column.property==="curPrincipalAmountBalance"){
            if(row.row.payoffflag===0 || row.row.payoffflag===2){
                return {color:'red'}
            }
        }
    }
    //下载账单
    const toDownLoad = async () => {
        showDownload.value = true
        // await nextTick()
        // childComp.value.getLastMonthDate()
    }
    //关闭账单
    const handleCancel = () => {
        showDownload.value = false
    }
</script>
<style lang="scss" scoped>
    .wrapperBox{
        background: #ffffff;
        font-size: 14px;
        margin: 0 auto;
        padding: 30px 30px 0 30px;
        .myMerchant{
            span {
                width: 4px;
                height: 16px;
                background: #00B377;
                margin-right: 10px;
                display: inline-block;
            }
            font-weight: 600;
            font-size: 18px;
            color: #292933;
            letter-spacing: 0;
            line-height: 20px;
        }
        .part01 {
            padding: 10px 0;
            border-bottom: 1px solid #dfdfdd;
            .side {
                .money{
                    margin-top: 20px;
                }
                .difference-money {
                    color: red
                }
                .item{
                    line-height: 20px;
                }
            }
            .side-left{
                height: 220px;
                background: #fafafa;
                padding: 60px 20px !important;
            }
            .box-card{
                background: #fafafa;
            }
        }
        .part02 {
            margin-top: 20px;
            .abstract {
                font-size: 18px;
                color: #292933;
            }
            .last-float-right {
                float: right;
            }
        }
    }
    /* 修改表头背景色和边框 */
    .el-table th.is-highlight.is-fixed {
        background-color: #2a4061;
    }
    .warning-color {
        color: #ff6600;
    }
    .tableHeader{
        background: #dfdfdd;
    }
</style>