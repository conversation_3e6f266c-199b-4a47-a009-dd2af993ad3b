import { getUrlParam, getCookieValue } from '@/utils/index';
export default function install(){
    //qt 埋点全局函数
try{

 }catch(e){
   console.log(e)
 }
   (function(w, d, s, q) { 
       w[q] = w[q] || []; 
       var f = d.getElementsByTagName(s)[0],j=d.createElement(s); 
       j.async=true; 
       j.id='beacon-aplus'; 
       j.src='https://o.alicdn.com/QTSDK/quicktracking-sdk/qt_web.umd.js'; 
       f.parentNode.insertBefore(j, f);
   })(window, document, 'script', 'aplus_queue');
  
   const QTDEVAPPKEY = "2chgx8zef60sgf2maq973lm2"; //测试appKey
   const QTPRODAPPKEY = "aa5onf1m4h8rg42a252kuqd3";  //生产appKey
   const QTDOMAIN = "qt.ybm100.com";  // 埋点上报域名
   const PROD_HOSTNAMES = ['www.ybm100.com', 'www-new.stage.ybm100.com'];//生产和预发都走生产key
   
   const isProdOrStage = PROD_HOSTNAMES.includes(location.hostname);
   const appKey = isProdOrStage ? QTPRODAPPKEY : QTDEVAPPKEY;
   // 设置appkey
   aplus_queue.push({ action: 'aplus.setMetaInfo', arguments: ['appKey', appKey]  });
   // 设置数据收集域名
   aplus_queue.push({ action: 'aplus.setMetaInfo', arguments: ['aplus-rhost-v', QTDOMAIN]});
   //页面自动上报默认是开启的，如果需要关闭自动页面上报，true表示关闭，false表示开启
   aplus_queue.push({action: 'aplus.setMetaInfo',arguments: ['aplus-disable-apv', true]}); 
   // 通过设置true，开启全埋点功能，默认为开启，指定为false关闭本地全埋点功能
   aplus_queue.push({action: 'aplus.setMetaInfo',arguments: ['aplus-autotrack-enabled', false]});
   
   
    var cookie = getCookieValue("xyy_principal") || "";
    var accountId = "";
    var cookieMerchantId= "";
    var cookieArr = cookie.split("&");
    if (cookieArr.length > 2) {
        cookieMerchantId = cookieArr[2];
        accountId = cookieArr[0];
    }
    aplus_queue.push({
      action: 'aplus.setMetaInfo',
      arguments: ['_user_id', cookieMerchantId]
  });

    aplus_queue.push({
        action: 'aplus.appendMetaInfo', //追加全局属性
        arguments: ['globalproperty', {
            account_id: accountId || "",
            /* merchant_id:cookieMerchantId || "" */
        }]
      });
 window.updateSpmESix = function () {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < 6; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        window.otherSixShop=result;
        // localStorage.setItem("otherSix-shop",result)

    };
      window.updateSpmESix();
    //获取spme
    window.getSpmE=function(){
        return  (getCookieValue("qt_session")||"_").split('_')[0]+window.otherSixShop
    }
   // window.AnalysysAgent.registerSuperProperty("merchant_id", cookieMerchantId || "");
   // window.AnalysysAgent.registerSuperProperty("account_id", accountId || "");
// console.log(localStorage.getItem('merchantId'),localStorage.getItem('accountId'),"yz");

}