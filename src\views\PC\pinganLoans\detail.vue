<template>
    <div class="wrapperBox">
        <el-row class="myMerchant">
            <el-col :span="12">
                <span></span>
                {{ billCodeStr }} - 平安贷账单明细
            </el-col>
            <el-col :span="12">
                <el-button type="info" style="float: right;" @click="handleClickBack">返回我的平安贷</el-button>
            </el-col>
        </el-row>
        <div class="part part01">
            <el-row>
                <el-col :span="24" class="side side-left">
                    <el-row :gutter="20">
                        <el-col :span="10" style="position: relative;height: 80px;border-right: 1px solid #999999;text-align: center;">
                            <div class="head-line">剩余应还本金（元）</div>
                            <h1 class="money difference-money">{{ curPrincipalAmountBalance }}</h1>
                        </el-col>
                        <el-col :span="14" style="padding-left: 20px;">
                            <div class="head-line">已还本金：￥{{ actPrincipalAmount }}</div>
                            <div class="sum-money" style="padding-top:20px;">已还利息：￥{{ actInterestAmount }}（<span style="color: #ff6600;">平台贴息：{{ actSubsidyInterestAmount }}</span>）</div>
                            <div class="money" style="position: absolute;bottom:0;color:red;">平台贴息截止日：{{ subsidyInterestDate }}</div>
                        </el-col>
                    </el-row>
                </el-col>
            </el-row>
        </div>
        <div class="part part02">
            <div class="table-header-line">
                <el-row :gutter="20">
                    <el-col :span="15" class="abstract">账单明细 </el-col>
                    <el-col :span="6">
                        <el-form-item label="还款状态">
                            <el-select v-model="payoffflag" placeholder="请选择 " @change="getDetail">
                                <el-option label="全部" value="" />
                                <el-option label="已还清" value="1" />
                                <el-option label="未还清" value="0" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="3">
                        <el-button type="success"  class="last-float-right" @click="toDownLoad">下载账单</el-button>
                    </el-col>
                </el-row>
            </div>
            <div class="table-box">
                <el-table
                    v-loading="loading"
                    :data="tableData" border  
                    :header-cell-style="{textAlign: 'center', background: '#dfdfdf', height:'60px'}"
                    @row-click="handleRowClick"
                    :cell-style = "cellStyle" 
                    width="100%"
                    height="600"
                >
                    <el-table-column 
                        v-for="(column, index) in columns" 
                        :key="index" 
                        :prop="column.prop" 
                        :label="column.label" 
                        :formatter="formatData(column.prop)"
                        align="center"
                        :width="column.width"
                    >
                        <template #default="scope">
                            <div
                              v-if="'operation' === column.prop" 
                              style="color:#13c775;cursor:default;" 
                              @click="getGoodsHref(scope.row.businessPayNo)"
                              >查看订单</div>
                            <div v-if="'actPrincipalAmount' === column.prop">
                                <div>{{ scope.row.actPrincipalAmount}}</div>
                                <div
                                    v-if="scope.row.refund===1" 
                                    style="background:#ff6600;padding:2px 5px;border-radius: 5px;color: #ffffff;text-align: center;font-size: 12px;">
                                    有原路退款
                                </div>
                            </div>
                            <div v-if="'order' === column.prop" style="text-align: left;">
                                <div style="color: red;">金额：￥{{ scope.row.tradeAmount}}</div>
                                <div>订单号：{{ scope.row.businessPayNo}}</div>
                                <div>下单：{{ scope.row.payDayFormat}}</div>
                                <div>店铺：{{ scope.row.receiverName}}</div>
                            </div>
                            <div v-if="'actSubsidyInterestAmount' === column.prop" style="color: #ff6600;">
                                {{ scope.row.actSubsidyInterestAmount}}
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </div>
    <DownloadDialog
      v-if="showDownload"
      :cancelDialog="handleCancel"
    />
</template>
<script setup>
    import { actionTracking } from '@/config/eventTracking';
    import { onMounted, ref, computed } from "vue";
    import { getLoansDetail, getLoans } from '@/http_pc/pinganLoans';
    import { useRouter, useRoute } from 'vue-router'
    import { useStore } from "vuex"
    import { removeLeadingZeroes } from '@/utils/index';
    import DownloadDialog from './components/downloadDialog.vue';
    import { getOrderId } from "../../../http_pc/pinganLoans";
    import { ElMessage } from 'element-plus';

    const router = useRouter()
    const route = useRoute()
    const store = useStore()
    const billCode = ref("")
    const payoffflag = ref("")
    const billDate = ref("")
    const billCodeStr = ref("")
    const curPrincipalAmountBalance = ref(0.00)
    const actPrincipalAmount = ref(0.00) 
    const actInterestAmount = ref(0.00)
    const actSubsidyInterestAmount = ref(0.00)
    const subsidyInterestDate = ref("")
    const loading = ref(true)
    const showDownload = ref(false)
    //const detailUrl = computed(() => store.state.app.detailBaseUrl);
    onMounted(async () => {
        
        //console.log(detailUrl.value)
        billCode.value = route.query.billCode
        //拆分billCode
        const arr = billCode.value.split("-")
        const monthStr = removeLeadingZeroes(arr[1])
        billCodeStr.value = `${arr[0]}年${monthStr}月`
        //payoffflag.value = parseInt(route.query.payoffflag) === 2 ? "" : route.query.payoffflag
        //埋点
        actionTracking('pc_action_pingan_loan_detail', { 
            pageName: '平安贷账单明细'
        })
        getCurrentLoans()
        getDetail()
    })
    
    const BASEURL = import.meta.env.VITE_BASE_URL_PC
    //const BASEURL = "https://new-www.test.ybm100.com"
    const getGoodsHref = (ybm) => {
        //调用接口获取订单id
        getOrderId({ybm:ybm}).then((res) => {
            if(res.code===1000){
                const url = BASEURL+"/merchant/center/order/detail/"+res.data+".htm"
                window.open(url, '_blank');
            } else {
                ElMessage.error(res.errorMsg)
            }
		}).catch(() => {
			console.log("系统异常")
		})
    }
    const columns = ref([
        { prop: 'payoffflag', label: '还款状态',width:"90" },
        { prop: 'order', label: '订单信息',width:"270" },
        { prop: 'curRepayDate', label: '最后还款日',width:"120" },
        { prop: 'actRepayDate', label: '实际还款日',width:"120"},
        { prop: 'curPrincipalAmountBalance', label: '剩余应还本金',width:"120"},
        { prop: 'actPrincipalAmount', label: '已还本金',width:"120"},
        { prop: 'actInterestAmount', label: '已还利息',width:"120"},
        { prop: 'actSubsidyInterestAmount', label: '已还利息平台贴息',width:"120"},
        { prop: 'operation', label: '操作',width:"120"},
    ])
    const tableData = ref([])
    const formatData = (prop) => {
        return (row, column, cellValue, index) => {
          if(prop==="payoffflag"){
            switch(cellValue){
                case 0:
                    return "未还清"
                break;
                case 1:
                    return "已还清"
                break;
            }
          }
          return cellValue
        }
    }
    //当前账单
    const getCurrentLoans = () => {
        getLoans({billCode:billCode.value,merchantId:localStorage.getItem('merchantId')}).then((res) => {
            if (res.code === 1000) {
                curPrincipalAmountBalance.value = res.data.records[0].curPrincipalAmountBalance
                actPrincipalAmount.value = res.data.records[0].actPrincipalAmount
                actInterestAmount.value = res.data.records[0].actInterestAmount
                actSubsidyInterestAmount.value = res.data.records[0].actSubsidyInterestAmount
                subsidyInterestDate.value = res.data.records[0].subsidyInterestDate
			}
		}).catch(() => {
			console.log("系统异常")
		})
    }
    //详情列表数据
    const getDetail = () => {
		getLoansDetail({billCode: billCode.value,payoffflag:payoffflag.value,merchantId:localStorage.getItem('merchantId')}).then((res) => {
            if (res.code === 1000) {
                loading.value = false
                let resData = []
                res.data.forEach(e => {
                    if(e.records.length>0){
                        console.log(e.records)
                        for(let i=0;i<e.records.length;i++){
                            resData.push(e.records[i])
                        }
                    }
                });
				tableData.value = resData
			}
		}).catch(() => {
			console.log("系统异常")
		})
	}
    //下载账单
    const toDownLoad = async () => {
        showDownload.value = true
        // await nextTick()
        // childComp.value.getLastMonthDate()
    }
    //关闭账单
    const handleCancel = () => {
        showDownload.value = false
    }
    //查看订单
    const handleRowClick = (row,columnName) => {
        if (columnName.property==="operation"){
            router.push({ path: `` })
        }
    }
    //返回平安贷
    const handleClickBack = () => {
        router.push({ path: `/pinganMerchant/pc/loans`,query: { merchantId:localStorage.getItem('merchantId') } })
    }
    /* 单元格样式 */
    const cellStyle = (row) => {
        if(row.column.property==="payoffflag") {
            if(row.row.payoffflag===0){
                return {color:'red'}
            }
            if(row.row.payoffflag===1){
                return {color:'#13c775'}
            }
        }
        if(row.column.property==="curPrincipalAmountBalance"){
            if(row.row.payoffflag===0){
                return {color:'red'}
            }
        }
    }
</script>
<style lang="scss" scoped>
    .wrapperBox{
        font-size: 14px;
        margin: 0 auto;
        padding: 30px 30px 0 30px;
        background: #ffffff;
        .myMerchant{
            span {
                width: 4px;
                height: 16px;
                background: #00B377;
                margin-right: 10px;
                display: inline-block;
            }
            font-weight: 600;
            font-size: 18px;
            color: #292933;
            letter-spacing: 0;
            line-height: 20px;
        }
        .part01 {
            padding: 10px 0;
            border-bottom: 1px solid #dfdfdd;
            .side {
                .money{
                    margin-top: 20px;
                }
                .difference-money {
                    color: red
                }
            }
            .side-left{
                height: 150px;
                background: #fafafa;
                padding: 40px 20px !important;
                
            }
        }
        .part02 {
            margin-top: 20px;
            .abstract {
                font-size: 18px;
                color: #292933;
            }
            .last-float-right {
                float: right;
            }
        }
    }
    /* 修改表头背景色和边框 */
    .el-table th.is-highlight.is-fixed {
        background-color: #2a4061;
    }
</style>