<template>
  <div class="buyMoneyOut">
    <div class="notice" v-if="noticeText">
        <div class="notice-icon"><i class="promotion-icon"></i></div>
        <div class="notice-text" ref="textContainer" :class="{ scroll: shouldScroll }">
            <div class="text-inner">{{ noticeText }}</div>
        </div>
    </div>
    <div class="recharge-container">
      <div class="selectMoney">
            <div class="money-list">
                <div 
                    v-for="(item, index) in selectMoneyList" 
                    :key="index"
                    class="money-item"
                    :class="{ 
                        'money-item-active': item.selected,
                    }"
                    @click="toggleSelectMoney(index)"
                >
                    <div class="money-item-img" v-if="item.recommendStr">{{ item.recommendStr }}</div>
                    <div class="money-item-content">
                        <span style="font-size: 0.3rem;">¥</span>
                        <span style="font-size: 0.6rem;font-weight: 600;">{{ item.rechargeAmount }}</span>
                    </div>
                    <div class="money-item-title" v-if="item.discountStr">{{ item.discountStr }}</div>
                </div>
            </div>
        </div>
    </div>
    <div class="content">
      <div class="money" :class="{ 'input-focused-div': inputFocused }">
        <span style="font-weight: 600;font-size: 0.3rem;width: 2.5rem;text-align: right;">其他金额</span>
        <span style="font-weight: 500;font-size: 0.4rem;margin:0 0.2rem;">￥</span>
        <van-field 
          @click="openEnter" 
          style="font-size: 0.8rem;height: 1.5rem;padding: 0 0.2rem;"
          :style="{backgroundColor: inputFocused ? '#F9FFFD' : '#F8F8FA'}" 
          class="money-input"
          size="large"
          v-model="number"
          maxlength="10"
          placeholder="请输入充值金额"  
          type="number" 
          @input="verity" 
          @focus="focusedMoneyInput"
          @blur="inputFocused = false"
          :class="{ 'input-focused': inputFocused }"
          clearable/>
      </div>
      <div style="margin-top: 0.2rem;font-size: 0.43rem" v-if="!msg">&nbsp;可用余额&nbsp;￥{{ money.toFixed(2) }} <span class="green"
          @click="allTransferOut">全部转出</span></div>
      <div v-else style="margin-top: 0.2rem;font-size: 0.4rem;color: red;">
        {{ msg }}
        <span style="color: #606266;">&nbsp;可用余额&nbsp;<span class="green">{{money.toFixed(2)}}元</span></span>
        <span class="green" @click="allTransferOut">全部转出</span>
      </div>
      
      <div style="text-align: center;">
        <van-button 
          :style="{'text-align': 'center','background-color': isButtonDisabled ? 'rgb(54, 54, 55) !important' : ''}" 
          :class="{ butt: true, dis: isButtonDisabled }" 
          :disabled="isButtonDisabled"
          @click="commit">确认转出
        </van-button>
        <div v-if="showService" style="margin-top: 0.2rem;font-size: 0.35rem;font-weight: 600;color: rgb(0, 185, 85);">
          &nbsp;<span @click="jumpServicePdf" ref="servicePdf">平台代收款说明</span>
        </div>
      </div>
    </div>
    <Pay ref="payRef" :tranNo="tranNo" :number="number"></Pay>
    <van-dialog v-model:show="show" show-cancel-button style="width: 75%;" @confirm="toConfig" @cancel="show=false"  confirmButtonColor="#00B955"
      confirmButtonText="去设置" cancelButtonText="稍后去设置" cancelButtonColor="rgb(148,148,165)">
      <div style="padding: 1rem;text-align: center">
        为了您的资金安全，请先设置支付密码
      </div>
    </van-dialog>
  </div>
</template>
<script setup>
import { ref, defineComponent, watch, nextTick, onMounted, getCurrentInstance, computed } from 'vue';
import { queryPingAnAccountBalance,payPwdQueryState,getTranNo,getPaymentDesc,getRechargeDiscount } from '@/http/pinganMerchant';
import Pay from "./components/pay.vue"
import { ElMessage } from 'element-plus'
import { useRouter, useRoute } from 'vue-router';
import  install from "@/utils/qtH5"
import { showToast } from 'vant';
import Bridge from "@/config/Bridge";
const noticeText = ref('');
const shouldScroll = ref(false);
const textContainer = ref(null)
// 检测文本是否需要滚动
const checkOverflow = () => {
    if (!textContainer.value) return
    const container = textContainer.value
    const content = container.querySelector('.text-inner')
    shouldScroll.value = content.scrollWidth > container.offsetWidth
}
// 处理窗口变化
const handleResize = () => {
    checkOverflow()
}
const selectMoneyList = ref([])
const selectMoneyIndex = ref(-1)
const toggleSelectMoney = (index) => {
  if (selectMoneyList.value[index].selected) {
    selectMoneyList.value[index].selected = false;
    selectMoneyIndex.value = -1;
  } else {
    selectMoneyList.value.forEach(item => {
      item.selected = false;
    });
    selectMoneyList.value[index].selected = true;
    selectMoneyIndex.value = index;
    number.value = "";
  }
}
// 获取购物金活动充值信息
const getRechargeInfo = () => {
    //channel: 1 平安商户充值  channel: 0 购物金充值
    const formData = new FormData();
    formData.append("channel", 1);
    getRechargeDiscount(formData).then((res) => {
        const stairsList = res.data.stairsList || [];
        // 给每个都加上selected标识
        stairsList.forEach((item, index) => {
            item.selected = false;
        })
        selectMoneyList.value = stairsList;
        noticeText.value = res.data.topUpStr;
        nextTick(() => {
            checkOverflow()
        })
    })
}
getRechargeInfo()
// Bridge.setAppRightMenu("0")
// import { Dialog } from 'vant';
defineComponent({
  Pay
})
onMounted(()=>{
  getTranNo({
    timestamp:new Date().getTime(),
    merchantId: merchantId
  }).then(data => {
    tranNo.value = data.tranNo
  })
  checkOverflow()
  window.addEventListener('resize', handleResize)
})
const tranNo=ref("")
const number = ref("")
const money = ref(0)
const msg = ref("")
const show = ref(false)
const payRef = ref("")//密码键盘
const enterRef = ref("")//数字输入键盘
const buttonDis = ref(true)
const route = useRoute();
const { merchantId } = route.query;
const showService = ref(false)
queryPingAnAccountBalance({ merchantId }).then((res) => {
  if (res.code === 1000) {
    money.value = res.data.availableBalance;
  }
})
// setTimeout(() => {
//   number.value=0
// }, 3000)
watch(() => number.value, (newVal,ordVal) => { 
  if(/^([0]([.][0-9]{0,2})?|[1-9][0-9]*([.][0-9]{0,2})?)?$/.test(newVal)){
    number.value=newVal
  }else{
   nextTick(()=>{
    number.value=ordVal
  })
  }
})
// const inputNum=(e)=>{
//   number.value=e.target.value
// }
Bridge.getVersion(e=>{
  console.log(`----bug ${e}`);
  let ver = parseInt(e);
  console.log(`----bug2 ${ver}`);
    showService.value = ver >= 12006
})
//校验输入
const verity = () => {
 if (Number(number.value) > Number(money.value)) {
    msg.value = '超出可转金额上限'
    buttonDis.value = true
  }else if(!number.value||Number(number.value)<=0){
    buttonDis.value = true
    msg.value = ''
  }
  else {
    msg.value = ''
    buttonDis.value = false
  }

}
const commit = () => {
  if(selectMoneyIndex.value !== -1) {
    if(selectMoneyList.value[selectMoneyIndex.value].rechargeAmount > money.value) {
      return ElMessage.warning('充值金额需小于等于可用余额');
    }  
    number.value = selectMoneyList.value[selectMoneyIndex.value].rechargeAmount;
  }else {
      if(Number(number.value) < 1) {
          return ElMessage.warning('充值金额需大于等于1元');
      }
  }
  payPwdQueryState({
    timestamp:new Date().getTime(),
    merchantId: merchantId
  }).then(data => {
   
    if (data.data.state == 1) {
      //有支付密码
      payRef.value.openDialog()

    } else {
      //没有支付密码
      show.value = true

    }
  })
}
const isButtonDisabled = computed(() => {
  // 如果选择了预设金额，或者手动输入的金额有效，则按钮可用
  return buttonDis.value && selectMoneyIndex.value === -1;
});
const toConfig=()=>{
  window.location.href='ybmpage://setpaypw?payPwdState=0&settingStatus=1'
}
// 如果点击其他金额框，取消选中态
const focusedMoneyInput = () => {
  selectMoneyList.value.forEach(item => {
    item.selected = false;
  });
  selectMoneyIndex.value = -1;
  inputFocused.value = true
}
// const openEnter = () => {
//   enterRef.value.openDialog()
// }
// qt埋点六期
// 页面曝光
const pageExposure = () => {
  try {
    if(window.aplus_queue) {
      aplus_queue.push({
        'action': 'aplus.record',
        'arguments': ['page_exposure', 'EXP', {
          'spm_cnt': `1_3.transferToVirtualGold_0-0_0.0.0.${window.$sessionSpmE}`
        }]
      })
    }
  }catch (e) {
    console.log(e)
  }
}
window.isHaveSession=()=>{
  pageExposure();
}
install();
const { proxy } = getCurrentInstance();
const servicePdf = ref(null)
const handleModelClick = (fileType,fileId) => {
  try {
    const innerText = servicePdf.value.innerText
    if(window.aplus_queue) {
      aplus_queue.push({
        'action': 'aplus.record',
        'arguments': ['action_sub_module_click','CLK', {
          'spm_cnt': `<EMAIL>@3.${window.$sessionSpmE}`,
          'scm_cnt': `order.0.all_0.text-${innerText}_fileType-${fileType}_fileId-${fileId}.${proxy.scmEActive(14)}`
        }]
      })
    }
  }catch (e) {
    console.log(e)
  }
}
// 跳转协议
const jumpServicePdf = () => {
  getPaymentDesc({
  }).then(data => {
    let {fileType,fileId} = data.data
    handleModelClick(fileType,fileId)
    var orderNo = tranNo.value;
    var fileName = data.data.agreementName;
    var pdfUrl = data.data.agreementUrl;
    window.location.href=`ybmpage://aptitudexyypdf?fileType=12&orderNo=${orderNo}&pdfUrl=${pdfUrl}&title=${fileName}&contractName=${fileName}&urlString=${pdfUrl}&fileId=${data.data.fileId}`;
  })
}
// 添加输入框焦点状态
const inputFocused = ref(false);
const allTransferOut = () => {
  number.value = money.value;
  verity();
  selectMoneyList.value.forEach(item => {
    item.selected = false;
  });
  selectMoneyIndex.value = -1;
}
</script>
<style scoped lang="scss">
.money {
  position: relative;
}
::v-deep.money-input .van-field__body {
  height: 100% !important;
}
::v-deep.money-input .van-field__control::placeholder {
  font-size: 0.4rem !important; /* 调整字体大小 */
  color: #999; /* 可选：修改 placeholder 颜色 */
  line-height: 1.5rem;
  position: absolute;
  top: 0.35rem;
  font-weight: 400;
}
::v-deep.dis{
    background-color: rgb(93, 93 ,98) !important;
  }
.buyMoneyOut {

  font-family: PingFangSC-Medium;
  padding: 0 0.4rem;
  background-color: white;

  .dis {
    background-color: rgb(148, 148, 165) !important;
  }

  .green {
    color: rgb(0, 185, 85);
    margin-left: 0.2rem;
  }

  .content {
    margin-top: 0.5rem;

    .butt {
      background-color: rgb(0, 185, 85);
      color: white;
      width: 60vw;
      font-size: 0.5rem;
      font-weight: 800 !important;
      margin-top: 0.8rem;
      border: none !important;
      outline: none;

    }
    .buttonDis{
      background: rgb(93, 93 ,98) !important;
    }

    .money {
      font-size: 0.8rem;
      margin-top: 0.1rem;
      display: flex;
      align-items: center;
      height: 1.5rem;
      border-radius: 0.1rem;
      background: #F8F8FA;
      box-sizing: border-box;
      ::v-deep .van-field__control {
          background: #F8F8FA;
        }
      .money-input {
        background: #F8F8FA;
        &.input-focused {
          background: #F9FFFD;
          border-top: 2px solid #00B955;
          border-bottom: 2px solid #00B955;
          ::v-deep .van-field__control {
            background: #F9FFFD;
          }
        }
      }
    }
    .input-focused-div {
      background: #F9FFFD;
      border: 2px solid #00B955;
    }
  }
}
.notice {
  display: flex;
  align-items: center;
  height: 0.8rem;
  width: 100%;
  background-color: #FFF8E6;
  border-radius: 0.15rem;
  margin: 0.5rem 0;
  .notice-icon {
      padding: 0 0.2rem;
      .promotion-icon {
          display: inline-block;
          width: 0.4rem;
          height: 0.4rem;
          background: url('@/assets/images/notice.png') no-repeat;
          background-size: contain;
          margin-right: 0.2rem;
      }
  }
  .notice-text {
      flex-grow: 1;
      overflow: hidden;
      position: relative;
  }
}
.text-inner {
  display: inline-block;
  white-space: nowrap;
  transition: 0.3s transform;
}

.notice-text.scroll .text-inner {
  animation: scroll 40s linear infinite;
  padding-left: 100%;
}

.notice-text.active:hover .text-inner {
  animation-play-state: paused;
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}
.selectMoney {
        display: flex;
        max-height: 8rem;
        overflow: auto;
        margin-top: 0.1rem;
        padding-top: 0.25rem;
        .money-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.3rem;
            .money-item {
                width: 2.7rem;
                height: 2rem;
                background: #F6F6F9;
                border-radius: 0.2rem;
                cursor: pointer;
                position: relative;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                border: 0.03rem solid #F6F6F9;
                &.money-item-disabled {
                    background-color: #F5F7FA;
                    border-color: #E4E7ED;
                    color: #C0C4CC;
                    cursor: not-allowed;
                    &:hover {
                        border-color: #E4E7ED;
                    }    
                    .money-item-title {
                        background-color: #909399;
                        opacity: 0.5;
                    }                    
                    .money-item-img {
                        background: #F5F7FA;
                        color: #C0C4CC;
                        border: 0.03rem solid #E4E7ED;
                    }
                }
                .money-item-img {
                    position: absolute;
                    top: -0.2rem;  /* 上移1px */
                    left: -0.15rem;  /* 左移1px */
                    background: #FFF3F3;
                    color: #FF223B;
                    padding: 0.05rem 0.1rem;
                    border-radius: 0.01rem 0 0.01rem 0;
                    box-shadow: 0 0 0.01rem rgba(0,0,0,0.1);
                    z-index: 10; 
                }
                .money-item-content {
                    font-size: 0.5rem;
                    font-weight: bold;
                    margin-bottom: 0.1rem;
                }
                .money-item-title {
                    padding: 0 0.15rem;
                    height: 0.5rem;
                    background: #FC443D;
                    border-radius: 0.08rem;
                    color: #FFFFFF;
                    line-height: 0.5rem;
                    text-align: center;
                }
            }
            .money-item-active {
                background: #F9FFFD;
                border: 0.03rem solid #00B955;
                border-radius: 0.2rem;
                &::after {
                    content: "";
                    position: absolute;
                    width: 16px;  /* 图标尺寸 */
                    height: 16px;
                    right: -1px;  /* 对齐边框外侧 */
                    bottom: -1px;
                    background: url('@/assets/images/checkBoxMoney.png');
                    background-size: contain;
                }
            }
        }
    }
</style>
<style>
.buyMoneyOut input {
  caret-color: green;
  font-weight: 600;
}

</style>
