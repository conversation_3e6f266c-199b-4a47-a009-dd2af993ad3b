import axios from 'axios';
import Bridge from "@/config/Bridge";

const MODE = import.meta.env.MODE;
console.log('-----MODE——app----', MODE);
axios.defaults.baseURL = MODE === 'development' ? '/api' : '/';
axios.defaults.timeout = 50000;
axios.defaults.headers.terminalType = 3;

function getMerchantId() {
  return new Promise((resolve, reject) => {
    Bridge.getMerchantId((merchantId, accountId) => {
      if (accountId) {
        resolve({merchantId, accountId})
      } else {
        resolve(merchantId)
      }
    });
  })
}

function getToken() {
  return new Promise((resolve, reject) => {
    Bridge.getToken(res => {
      resolve(res)
    });
  })
}

function getVersion() {
  return new Promise((resolve, reject) => {
    Bridge.getVersion(res => {
      resolve(res);
    });
  });
}

/*本地起项目需在localStorage中种植appToken，appVersion，merchantId
* 例如merchantId: **********,
* appToken: '9x94yF2yF2_0DaFBp92pFC_3E@D0!4D~90*65%73<4E:F8.3'
* appVersion: 3
*/

// 请求前的统一处理
axios.interceptors.request.use(
  async (config) => {
    try {
      console.log('开始获取请求凭证');
      const token = await getToken();
      const version = await getVersion();
      const responseData = await getMerchantId();
      console.log('获取凭证结果:', { token, version, responseData });
      
      let merchantId = '';
      let accountId = '';
      if (typeof responseData === 'object') {
        merchantId = responseData.merchantId;
        accountId = responseData.accountId;
      } else {
        merchantId = responseData;
      }
      // config.params.merchantId = Number(merchantId);
      config.headers.merchantId = Number(merchantId) || localStorage.getItem("merchantId");
      //config.headers.merchantId = **********;
      //判断运营或者自然人
      if (Number(merchantId) == 0) {
        config.headers.isAdmin = true;
      } else {
        // config.headers.merchantId = Number(merchantId) || localStorage.getItem("merchantId");
      }
      config.headers.token = token || localStorage.getItem("appToken");
      config.headers.version = version || localStorage.getItem("appVersion");
      config.headers.accountId = accountId || '';
      config.headers["Cache-Control"]="no-cache,no-store";
      //config.headers.accountId = **********;
      console.log('请求配置完成:', config);
      return config;
    } catch (error) {
      console.error('请求拦截器出错:', error);
      return Promise.reject(error);
    }
  },
  (error) => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
)

axios.interceptors.response.use(
  function(response) {
    return response;
  },
  function(err) {
    return Promise.reject(err);
  }
)

export default {
  /**
   * get方法，对应get请求
   * @param {String} url [请求的url地址]
   * @param {Object} params [请求时携带的参数]
   */
  get(url, params, config) {
    return new Promise((resolve, reject) => {
      axios
        .get(url, {
          params,
          ...config
        })
        .then((res) => {
          resolve(res.data)
        })
        .catch((err) => {
          reject(err)
        })
    })
  },
  /**
   * post方法，对应post请求
   * @param {String} url [请求的url地址]
   * @param {Object} params [请求时携带的参数]
   */
  post(url, params, config) {
    return new Promise((resolve, reject) => {
      axios
        .post(url, params, config)
        .then((res) => {
          resolve(res.data)
        })
        .catch((err) => {
          reject(err)
        })
    })
  },
  /**
   * postFormData方法，对应post请求，用来提交文件+数据
   * @param {String} url [请求的url地址]
   * @param {Object} params [请求时携带的参数]
   */
  postFormData(url, params, config) {
    return new Promise((resolve, reject) => {
      axios({
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        transformRequest: [
          (data) => {
            // 在请求之前对data传参进行格式转换
            const formData = new FormData()
            Object.keys(data).forEach((key) => {
              formData.append(key, data[key])
            })
            return formData
          }
        ],
        url,
        method: 'post',
        data: params,
        ...config
      })
        .then((res) => {
          resolve(res.data)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }
}

