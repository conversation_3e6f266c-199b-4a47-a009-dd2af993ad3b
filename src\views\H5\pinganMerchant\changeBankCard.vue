<template>
  <div class="bank-card">
    <div class="bank-card-loading"  v-show="loadingFlag">
      <van-loading />
    </div>
    <mySteps :step="step" :stepStatus="stepStatus"/>
    <div class="bank-card-tips" v-if="step !== 1">
      <div class="step1-tips" v-if="step === 1">
        <!-- <p>1、提交修改后，当前已绑定的银行卡会被解绑，绑定新卡前将无法充值或提现</p> -->
        <!-- <p>2、企业只允许绑定“对公卡”，个体工商户支持绑定“对公卡”或“法人个人卡”</p> -->
      </div>
      <div class="step2-tips" v-if="step === 2 && stepStatus">您的银行卡信息已提交至银行进行审批，审批周期预计5-10分钟，请耐心等待！</div>
      <div class="step2-tips" v-if="step === 2 && !stepStatus">
        <div>
          <span class="redCol">您的银行卡信息银行审核未通过或超时未做打款验证，</span>请参考驳回原因编辑信息重新提交
          <span class="step2-error" @click="errorResult">查看原因</span>
        </div>
       
      </div>
      <div class="step3-tips" v-if="step === 3">您的银行卡信息银行已审批通过，银行会向您的公账账户打款，打款可能存在一定延时，请您在账户收到打款后，48小时内进入此页面进行打款验证</div>
    </div>
    <div class="bank-step1" v-if="step === 1 || (step === 2  && !stepStatus)">
      <div class="bank-form">
        <el-form class="form" ref="changeCardForm" :model="form" :rules="rules">
          <el-form-item label="企业名称" prop="enterpriseName" class="item">
            <!-- <p>统一社会信用代码</p> -->
            <el-input placeholder="请输入企业名称" readonly class="disabled-input" maxlength="20" v-model.trim="form.enterpriseName" style="width: 100%"  />
          </el-form-item>
          <el-form-item label="企业类型" props="businessType" class="item">
            <!-- <p>公账户名</p> -->
            <el-input readonly value="企业" class="disabled-input" style="width: 100%;" />
          </el-form-item>
          <!-- <el-form-item label="卡类型" prop="cardType" class="item">
            <el-select
              id="select1"
              v-model="form.cardType"
              placeholder="请选择公账账户开户银行，支持模糊搜索"
              style="width: 100%"
            >
              <el-option
                v-for="item in cardTypeList"
                :key="item.label"
                :label="item.label"
                :value="item.label"
              ></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="银行账号" prop="acct" class="item">
            <!-- <p>公账账户</p> -->
            <el-input placeholder="请输入银行账号" maxlength="30" v-model.trim="form.acct" style="width: 100%" />
          </el-form-item>
          <el-form-item label="开户银行" prop="bankName" class="item">
            <!-- <p>公账账户</p> -->
            <!-- <el-input placeholder="请输入开户银行" maxlength="30" v-model.trim="form.bankName" style="width: 100%" /> -->
            <!-- <p>开户银行</p> -->
            <el-select
              id="select1"
              v-model="form.bankName"
              placeholder="请选择公账账户开户银行，支持模糊搜索"
              style="width: 100%"
              :filterable="true"
              :filter-method="getBankList"
              @change="bankChange"
              :key="form.bankName"
              @hook:mounted="cancalReadOnly"
            >
              <el-option
                v-for="item in bankList"
                :key="item.bankName"
                :label="item.bankName"
                :value="item.bankName"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开户支行" prop="branchBankName" class="item">
            <!-- <p>公账账户</p> -->
            <!-- <el-input placeholder="请输入开户支行" maxlength="30" v-model.trim="form.branchBankName" style="width: 100%" /> -->
            <!-- <p>开户支行</p> -->
            <el-select
              id="select2"
              v-model="form.branchBankName"
              placeholder="请选择公账账户开户银行，支持模糊搜索"
              style="width: 100%"
              :filterable="true"
              :filter-method="searchSubBank"
              @change="subBankChange"
              :key="form.branchBankName"
              @hook:mounted="cancalReadOnly"
            >
              <el-option
                v-for="item in subBankList"
                :key="item.bankName"
                :label="item.bankName"
                :value="item.bankName"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="药店负责人手机号" prop="corporatePhone" class="item">
            <!-- <p>公账账户</p> -->
            <el-input placeholder="请输入药店负责人手机号" maxlength="11" v-model.trim="form.corporatePhone" style="width: 100%" />
          </el-form-item>
          <el-form-item label="验证码" prop="activeCode" class="item otpItem">
            <!-- <p>公账账户</p> -->
            <el-input placeholder="请输入验证码" maxlength="30" v-model.trim="form.activeCode" style="width: 100%" />
            <el-button class="otpBtn" type="primary" @click="sendActiveCode" :disabled="sendDisabled">获取验证码{{ sendDisabled ? `(${sendCountDown})` : '' }}</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="bank-submit">
        <div class="bank-btn" @click="submitForm">{{ step === 2 && !stepStatus ? '提交修改' : '提交'}}</div>
      </div>
    </div>
    <div class="bank-step2" v-if="(step === 2  && stepStatus) || step === 3">
      <div class="bank-item-content">
        <div class="bank-item-list">
          <div class="bank-span">账户名</div>
          <div class="bank-p">{{ accountInfo.accountName || "" }}</div>
        </div>
        <div class="bank-item-list">
          <div class="bank-span">银行账号：</div>
          <div class="bank-p">{{ accountInfo.acct || "" }}</div>
        </div>
        <div class="bank-item-list">
          <div class="bank-span">开户行</div>
          <div class="bank-p">{{ accountInfo.bankName || ""}}</div>
        </div>
        <div class="bank-item-list">
          <div class="bank-span">开户支行</div>
          <div class="bank-p">{{ accountInfo.branchBankName || ""}}</div>
        </div>
        <div class="bank-item-list">
          <div class="bank-span">药店负责人手机号</div>
          <div class="bank-p">{{ accountInfo.corporatePhone || ""}}</div>
        </div>
      </div>
      <div class="bank-payment" v-if="step === 3">
        <div class="bank-payment-item">
          <div class="payment-title">
            <span><b>*</b>小额打款金额</span>
          </div>
          <el-input placeholder="请输入企业对公账户收到的打款金额" maxlength="30" v-model.trim="form.publicPrice" style="width: 100%" />
        </div>
        <div class="bank-payment-item">
          <div class="payment-title">
            <span><b>*</b>鉴权序号</span>        
          </div>
          <el-input placeholder="请输入短信鉴权序号" maxlength="30" v-model.trim="form.signNo" style="width: 100%" />
          <p class="reset-otp">未收到短信，<span class="reset-otp" v-if="!resetSendDisabled" @click="openResetOtpFlag">重新发送</span><b v-else class="reset-otp">{{ resetSendCountDown }}s后重新发起验证</b></p>
        </div>
        <div class="item" style="display: flex;flex-wrap: nowrap;align-items: start;">
          <el-checkbox
            v-model="checkAgreement"
            style="height: auto;margin: 0;border-radius: 50%;"
          />
          <div class="agreement">我已阅读并同意<span @click="handleCheckAgreement('https://my.orangebank.com.cn/orgLogin/hd/act//jianzb/jzbxym.html')">《平安银行电子商务“见证宝”商户服务协议》</span>和<span @click="handleCheckAgreement('https://auth.orangebank.com.cn/#/m/cDealOne?showNavBar=1')">《平安银行数字口袋协议》</span></div>
        </div>
        <van-dialog v-model:show="resetOtpFlag" @confirm="resetCheckOtp" :before-close="onBeforeClose" title="重新发起验证" show-cancel-button confirm-button-text="提交" cancel-button-text="关闭" confirm-button-color="#00b955">
          <div class="reset-text">若当前商户手机号<span>{{form.corporatePhone}}</span>无法收到平安银行的鉴权短信，建议您更换手机号重新发起验证</div>
          <div class="bank-payment-item">
            <div class="payment-title">
              <span>商户手机号</span>
            </div>
            <el-input placeholder="请输入商户手机号" maxlength="11" v-model.trim="resetOtp.corporatePhone" style="width: 100%" />
          </div>
          <div class="bank-payment-item">
            <div class="payment-title">
              <span>验证码</span>
            </div>
            <div class="payment-content otpItem">
              <el-input placeholder="请输入验证码" maxlength="30" v-model.trim="resetOtp.activeCode" style="width: 100%" />
              <el-button class="otpBtn" style="background: #00B955;color: white;border-radius: 5px;" type="primary" @click="sendResetOtp" :disabled="sendResetDisabled">获取验证码{{ sendResetDisabled ? `(${sendResetCountDown})` : '' }}</el-button>
            </div>
          </div>
        </van-dialog>
        <div class="bank-submit">
          <div class="bank-btn" @click="verify">提交验证</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue';
import mySteps from './components/steps.vue';
import { showDialog } from 'vant';
import { createPingAnAccount, queryBankList, querySubBankList, sendVerificationCode, paymentAuth, queryPingAnAccountInfo ,createPingAnAccountNew} from '@/http/pinganMerchant';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
const router = useRouter();
const route = useRoute();
const { merchantId } = route.query;

const loadingFlag = ref(false);
const step = ref(1);
const stepStatus = ref(false);
const checkAgreement = ref(false);
const resetOtpFlag = ref(false);
const form = reactive({
  enterpriseName: '', //企业名称
  businessType: '企业', //企业类型
  cardType: '', // 卡类型
  acct: '', //银行账号
  bankName: '', // 开户银行
  branchBankName: '', // 开户支行
  corporatePhone: '', //药店负责人手机号
  activeCode: '', //验证码
  publicPrice: "",
  signNo: ""
})
const resetOtp = reactive({
  corporatePhone: "",
  activeCode: ""
})
const bankList = ref([]);
const subBankList = ref([]);
const resetSendDisabled = ref(false);
const resetSendCountDown = ref(120);
const changeCardForm = ref(null);
const cardTypeList = reactive([
  {label: '企业对公卡'}
])
const accountInfo = ref(null);
const rules = {
  acct: [
    { required: true, message: '请输入银行账号', trigger: 'blur' },
  ],
  bankName: [
    { required: true, message: '请输入开户银行', trigger: 'blur' },
  ],
  branchBankName: [
    { required: true, message: '请输入开户支行', trigger: 'blur' },
  ],
  corporatePhone: [
    { required: true, message: '请输入药店负责人手机号', trigger: 'blur' },
  ],
  activeCode: [
    { required: true, message: '请输入短信验证码', trigger: 'blur' },
  ],
}
const sendDisabled = ref(false);
const sendCountDown = ref(60);


const sendResetDisabled = ref(false);
const sendResetCountDown = ref(60);

const sendActiveCode = async () => {
  if (form.corporatePhone) {
    sendDisabled.value = true;
    sendCountDown.value = 60;
    timeOutSend();
    const res = await sendVerificationCode({ mobileNumber: form.corporatePhone })
    if (res && res.code === 1000) {
      ElMessage.success('验证码发送成功')
    } else {
      sendDisabled.value = false;
      sendCountDown.value = 60;
      ElMessage.error(res.errorMsg || res.msg)
    }
  }
};
const sendResetOtp = async () => {
  if (resetOtp.corporatePhone) {
    sendResetDisabled.value = true;
    sendResetCountDown.value = 60;
    timeOutSendReset();
    const res = await sendVerificationCode({ mobileNumber: resetOtp.corporatePhone })
    if (res && res.code === 1000) {
      ElMessage.success('验证码发送成功')
    } else {
      sendResetDisabled.value = false;
      sendResetCountDown.value = 60;
      ElMessage.error(res.msg)
    }
  } else {
    ElMessage.warning('请输入商户手机号！');
  }
};
const getBankList = (val) => {
  queryBankList({ bankName: val || ''}).then((res) => {
    if (res.code === 1000) {
      bankList.value = res.data;
    }
  })
};

getBankList();
const searchSubBank = async(val) => {
  querySubBankList({bankName: form.bankName, subBankName: val}).then((res) => {
    if (res.code === 1000) {
      subBankList.value = res.data;
    }
  })
  //const res = await querySubBankList({bankName: this.accountVo.bankName, subBankName: val})
};

const bankChange = () => {
  form.branchBankName = '';
  searchSubBank();
};
const subBankChange = (val) => {
  if (val) {
    const value = subBankList.value.find((item) => {
      return item.bankName === val;
    })
    if (value) {
      form.branchBankCd = value.bankCode;
    }
  } else {
    form.branchBankCd = '';
  }
};
const cancalReadOnly = (onOff) => {
  nextTick(() => {
    if (!onOff) {
      document.getElementById('select1').removeAttribute('readonly');
      document.getElementById('select2').removeAttribute('readonly');
    }
  });
}
const handleCheckAgreement = (url) => {
  location.href = url;
};
const timeOutTimer = ref(null);
const timeOutSendTimer = ref(null);
const timeOutSendResetOutTimer = ref(null);
const timeOutSendReset = () => {
  timeOutSendTimer.value && clearInterval(timeOutSendTimer.value);
  timeOutSendTimer.value = setInterval(() => {
    if (sendResetCountDown.value > 0) {
      sendResetCountDown.value --;
    } else {
      timeOutSendTimer.value && clearInterval(timeOutSendTimer.value);
      sendResetDisabled.value = false;
      sendResetCountDown.value = 60;
    }
  }, 1000)
};
const timeOutSend = () => {
  timeOutTimer.value && clearInterval(timeOutTimer.value);
  timeOutTimer.value = setInterval(() => {
    if (sendCountDown.value > 0) {
      sendCountDown.value --;
    } else {
      timeOutTimer.value && clearInterval(timeOutTimer.value);
      sendDisabled.value = false;
      sendCountDown.value = 60;
    }
  }, 1000)
};
const openResetOtpFlag = () => {
  resetOtpFlag.value = true;
  resetOtp.corporatePhone = form.corporatePhone;
  resetOtp.activeCode = "";
  sendResetDisabled.value = false;
  sendResetCountDown.value = 60;
}
const submitForm = () => {
  changeCardForm.value.validate((valid) => {
    if (valid) {
      loadingFlag.value = true;
      const params = { ...accountInfo.value, ...form };
      params.merchantId = merchantId;
        //是否重发写死0
      params.resendCode=  0;
      params.update = step.value === 2 && !stepStatus.value ? 1 : 0;
          if(params.update){
            params.originAcct = originAcct.value
          }
      createPingAnAccountNew(params).then((res) => {
        if (res.code === 1000) {
          ElMessage.success(res.msg);
        } else {
          ElMessage.error(res.msg);
        }
        loadingFlag.value = false;
        queryPingAnData(true);
      });
    }
  })
}
const verifyDisabled = ref(false);
const verify = () => {
  if (step.value === 3) {
    if (form.publicPrice === "") {
      ElMessage.warning("请输入打款金额！");
      return;
    } else if (form.signNo === "") {
      ElMessage.warning("请输入鉴权序号！");
      return;
    } else if (!checkAgreement.value) {
      ElMessage.warning('请阅读并同意《平安银行电子商务“见证宝”商户服务协议》和《平安银行数字口袋协议》');
      return;
    }
  }
  loadingFlag.value = true;
  const params = { ...accountInfo.value, ...form };
  params.merchantId = merchantId;
  paymentAuth(params).then((res) => {
    if (res.code === 1000) {
      // ElMessage.success(res.msg);
      setTimeout(() => {
        router.push({
          path: '/pinganMerchant/account',
          query: { merchantId }
        });
      }, 100)
    } else {
      let str = '';
      if (res.code === 1001) {
        str = `银行卡验证失败。${res.msg}`;
      }
      if (res.code === 1002) {
        str = '银行卡验证失效，请重新提交开户申请';
      }
      ElMessageBox.alert(str, '提示', {
        confirmButtonText: '确定',
        callback: () => {
          queryPingAnData();
        },
      })
    }
    loadingFlag.value = false;
  })
}
const originAcct = ref('');
const queryPingAnData = (isToaccount) => {
  queryPingAnAccountInfo({ merchantId }).then((res) => {
    if (res.code === 1000) {     
      const { data } = res;
      originAcct.value = data.acct;
      if (res.code === 1000) {
        for(let i in form) {
          if (data[i]) {
            form[i] = data[i];
          }
        }
        switch(data.status) {
          case 1:
            step.value = 1;
            stepStatus.value = true;
            break;
          case 2:
            step.value = 2;
            stepStatus.value = true;
            break;
          case 4:
            step.value = 3;
            stepStatus.value = true;
            break;
          case 8:
            step.value = 2;
            stepStatus.value = false;
            break;
          case 16:
            step.value = 2;
            stepStatus.value = false;
            break;
          // case 32:
          //   step.value = 2;
          //   stepStatus = true;
          //   break;
        }
        // store.commit('app/setAccountInfo', data);
        accountInfo.value = data;
        searchSubBank();
        if(isToaccount && data.status == 32){
          if(route.query.from=="account"){
            router.go(-1)
          }else{
              router.replace({
              path: '/pinganMerchant/account',
              query: { merchantId }
            });
          }
        }
      }
    }
    //lwq
    // accountInfo.value={}
    // step.value=2
    // stepStatus.value=false
  })
}
onMounted(() => {
  queryPingAnData();
})
const errorResult = () => {
  showDialog({
    title: '驳回原因',
    width: "80%",
    confirmButtonText: "确定",
    confirmButtonColor: "#00B377",
    message: accountInfo.value.message,
  }).then(() => {
    // on close
  });
}

const resetCheckOtp = () => {
  console.log("重新发起鉴权")
  if (resetOtp.corporatePhone === "") {
    ElMessage.warning("请输入商户手机号！");
    return;
  } else if (resetOtp.activeCode === "") {
    ElMessage.warning("请输入验证码！");
    return;
  }
  loadingFlag.value = true;
  const params = { ...accountInfo.value, ...form, resendCode: 1, ...resetOtp };
  params.merchantId = merchantId;
  createPingAnAccount(params).then((res) => {
    console.log(res,' res')
    if (res.code === 1000) {
      ElMessage.success(res.msg);
      queryPingAnData();
      resetOtpFlag.value = false;
      resetSendDisabled.value = true;
      timeOutSendResetOutCheck();
    } else {
      ElMessage.error(res.msg);
    }
    loadingFlag.value = false;
  });
}
const timeOutSendResetOutCheck = () => {
  timeOutSendResetOutTimer.value && clearInterval(timeOutSendResetOutTimer.value);
  timeOutSendResetOutTimer.value = setInterval(() => {
    if (resetSendCountDown.value > 0) {
      resetSendCountDown.value --;
    } else {
      timeOutSendResetOutTimer.value && clearInterval(timeOutSendResetOutTimer.value);
      resetSendDisabled.value = false;
      resetSendCountDown.value = 60;
    }
  }, 1000)
};
const onBeforeClose = (action) => {
  if (action === "confirm") {

  } else {
    return true;
  }
}
</script>
<style lang="scss">
.bank-card {
  .bank-card-loading {
    .van-loading__circular {
      color: #fff;
    }
  }
  .otpItem {
    .el-form-item__content {
      display: flex;
      flex-wrap: inherit;
    }
    .el-button {
      height: rem(80);
      margin-left: rem(10);
      background: #fff;
      border-radius: rem(1);
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: rem(28);
      color: #00B955;
    }
  }
  .bank-form {
    background: #FFFFFF;
    margin-top: 10px;
    .form {
      padding: rem(40);
      .item {
        // margin: 0 0 rem(40) 0;
        p {
          font-weight: 500;
          font-size: rem(28);
          color: #676773;
          line-height: rem(30);
          margin: 0 0 rem(20) 0;
        }
        .agreement {
          font-size: rem(24);
          // line-height: rem(33);
          margin-left: rem(10);
          display: inline;
          vertical-align: top;
          span {
            color: #00B377;
          }
        }
      }
      .disabled-input {
        .el-input__wrapper {
          background: #FCFCFC;
        }
      }
      .el-checkbox__label {
        display: inline-grid;
        white-space: pre-line;
        word-wrap: break-word;
        overflow: hidden;
        line-height: 20px;
      }
      .el-form-item {
        display: block;
      }
      .el-input {
        // height: rem(80);
        .el-input__wrapper {
          // border-radius: 4px;
        }
        .el-input__inner {
          border: none;
          border-radius: 1px;
        }
      }
    }
  }
  .bank-payment {
    .agreement {
      font-size: rem(24);
      line-height: rem(33);
      margin-left: rem(10);
      // display: inline;
      flex: 1;
      vertical-align: top;
      span {
        color: #00B377;
      }
    }
    .el-input {
      // height: rem(80);
      .el-input__wrapper {
        // border-radius: 4px;
      }
      .el-input__inner {
        border: none;
        border-radius: 1px;
      }
    }
  }
  .bank-step2 {
    .van-dialog__content {
      padding: rem(20);
      box-sizing: border-box;
      .reset-text {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: rem(26);
        color: #292933;
        line-height: rem(36);
        margin-bottom: rem(20);
        span {
          color: #00B377;
        }
      }
    }
  }
}
</style>
<style>

</style>
<style lang="scss" scoped>
::v-deep .el-form-item__label{
  font-size: 16px !important;
}
::v-deep .el-checkbox__inner {
border-radius: 50% !important;
}
.reset-otp{
  text-align: right;
  margin-top: 10px;
  color: #00B955;
  font-weight: bold;
}
.bank-card {
  width: 100%;
  min-height: 100vh;
  background: #F7F7F8;
  position: relative;
  padding-bottom: rem(140);
  box-sizing: border-box;
  .bank-card-loading {
    background: rgba(0,0,0,0.3);
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9998;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .bank-card-tips {
    color: #666666;
    background: white;
    padding: rem(18) rem(40);
    box-sizing: border-box;
    .step1-tips {
      p {
        font-size: rem(26);
        font-face: PingFangSC;
        font-weight: 400;
        color: #7a2809;
        line-height: rem(36);
        &:first-child {
          color: #fe2021;
        }
      }
    }
    .step2-tips, .step3-tips {
      font-size: rem(26);
      font-face: PingFangSC;
      font-weight: 400;
      // color: #7a2809;
      line-height: rem(36);
      .redCol {
        color: #fe2021;
      }
      .step2-error {
        // display: flex;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        // font-size: rem(22);
        color: #00B955;
        align-items: center;
        margin-top: rem(10);
        img {
          width: rem(10);
          height: rem(15);
          margin-left: rem(10);
        }
      }
    }
  }
  .bank-step2 {
    margin-top: rem(10);
    .bank-item-content {
      background: #fff;
      display: flex;
      flex-direction: column;
      padding: rem(30) rem(20);
      padding-bottom: 0;
      box-sizing: border-box;
      .bank-item-list {
        // display: flex;
        margin-bottom: rem(30);
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: rem(26);
        .bank-span {
          margin: 3px;
          font-size: 16px;
          width: 36%;
          display: flex;
          white-space: nowrap;
          color: #676773;
        }
        .bank-p {
          flex: 1;
          color: #292933;
          line-height: 30px;
          height: 30px;
          background: #EFF1F4;
          border: 1px solid #CDCDCD;
          border-radius: 4px;
          padding-left: 5px;
          
          // white-space: nowrap;
        }
      }
    }
    .bank-payment {
      background: white;
      // border: 1px solid #FFC183;
      margin-top: rem(10);
      padding: rem(30) rem(20);
      box-sizing: border-box;
      .bank-payment-item {
        display: flex;
        flex-direction: column;
        margin-bottom: rem(20);
      }
      .payment-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: rem(10);
        >span {
          font-family: PingFangSC-Medium;
          font-weight: 500;
          font-size: rem(28);
          color: #676773;
          display: flex;
          align-items: center;
          b {
            color: red;
          }
        }
        p {
          font-size: rem(24);
          font-face: PingFangSC;
          font-weight: 400;
          line-height: 0;
          >span {
            color: #00b955;
          }
        }
      }
      .payment-content {
        display: flex;
      }
    }
  }
  .bank-submit {
    width: 100%;
    padding: rem(20) rem(40);
    box-sizing: border-box;
    background: #fff;
    margin-top: rem(10);
    position: absolute;
    bottom: 0;
    left: 0;
    .bank-btn {
      height: rem(88);
      background: #00B955;
      border-radius: 4px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: rem(32);
      color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>