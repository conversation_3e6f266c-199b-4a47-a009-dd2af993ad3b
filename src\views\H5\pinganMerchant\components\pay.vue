<template>
    <div class="pay-Money">
        <van-popup v-model:show="dialog" position="bottom" style="height:11.5rem">
            <div class="header">
                <div class="title">验证支付密码</div>
                <span class="left"><van-icon name="arrow-left" @click="closeDialog" /></span>
                <span class="forget" @click="forgetPwd">忘记密码</span>
            </div>
            <div style="height: 0.6rem;color: red;text-align: center;">{{ content }}</div>
            <van-password-input :gutter="10" style="" :value="value" :focused="showKeyboard"
                @focus="showKeyboard = true" />
            <!-- 数字键盘 -->
            <van-number-keyboard :show="true" extra-key="" @blur="show = false" v-model="value"
                :hide-on-click-outside="false" @input="onInput" @delete="onDelete">
                <template #extra-key class="required_icon">
                    <div style="background-color: rgb(242,243,245);width: 100%;height: 100%;" @click.stop></div>
                </template>
                <template #delete class="required_icon">
                    <div style="background-color: rgb(242,243,245);width: 100%;height: 100%;display: flex; justify-content: center ;align-items: center;"
                        @click.stop>
                        <img style="width: 1rem;" src="@/assets/images/shurufa.png" alt="">
                    </div>
                </template>
            </van-number-keyboard>
        </van-popup>
        <LoadingApp text="付款中" v-if="loading"></LoadingApp>
    </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch, defineProps } from 'vue'
import LoadingApp from '@/components/LoadingApp.vue';
import { ElMessage } from 'element-plus';
import CryptoJS from '@/utils/CryptoJS.js';
import { showToast,showDialog } from 'vant';
import { goldTransferIn, getResultByTranNo } from '@/http/pinganMerchant';
import { useRouter, useRoute } from "vue-router"
const router = useRouter();
const route = useRoute();
const { merchantId } = route.query;
const props = defineProps({
    number: {

    },
    tranNo: {
        type: String
    }

})
const loading = ref(false)
//提示
const content = ref("")
const show = ref(true);
const value = ref('');
const onInput = () => {


    // value.value=value
};
watch(value, (newVal) => {
    if (newVal.length == 6) {
        var key = CryptoJS.enc.Utf8.parse("FmpHxGc9no95cvd4");  //十六位十六进制数作为密钥
        var srcs = CryptoJS.enc.Utf8.parse(value.value);
        var encrypted = CryptoJS.AES.encrypt(srcs, key, { mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7, iv: key });
        let pass = encrypted.toString();
        loading.value = true
        goldTransferIn({
            pwd: pass,
            tranNo: props.tranNo,
            virtualGold: Number(props.number),
            timestamp: new Date().getTime()
        }).then(
            res => {
                if (res.code === 1000) {
                    let timeOut = setInterval(
                        () => {
                            getResultByTranNo({tranNo: props.tranNo}).then(res => {
                                if (res.code == 1000) {
                                    if(res.payResult == 1){
                                        clearInterval(timeOut)
                                        showToast('转出成功');
                                        loading.value = false
                                        router.back(-1)
                                    }else if(res.payResult == 2){
                                        clearInterval(timeOut)
                                   
                                        loading.value = false
                                        showDialog({
                                            title: '提示',
                                            width: "80%",
                                            confirmButtonText: "确定",
                                            confirmButtonColor: "#00B377",
                                            message: res.errorMsg,
                                        }).then(() => {
                                            router.back(-1)
                                        });   
                                        
                                    }else if(res.payResult == 3){
                                        
                                    }
                                   
                                }else if(res.code == 1001){
                                    clearInterval(timeOut)
                               
                                    loading.value = false
                                    showDialog({
                                            title: '提示',
                                            width: "80%",
                                            confirmButtonText: "确定",
                                            confirmButtonColor: "#00B377",
                                            message: res.errorMsg,
                                        }).then(() => {
                                            router.back(-1)
                                        }); 
                                }
                            }).catch(e => {
                                // showToast(e);
                                // loading.value = false
                                // dialog.value=false
                            })
                        }, 2000
                    )
                    // showToast('转出成功');
                    // router.back(-1)

                } else {
                    loading.value = false
                    content.value = res.errorMsg
                }
            }
        ).finally(r => {
            value.value = ''
            // dialog.value=false
        }).catch(e => {
            showToast(e);
            loading.value = false
            // dialog.value=false
        })



    }
})
const onDelete = () => {

};
const forgetPwd = () => {
    window.location.href = 'ybmpage://setpaypw?payPwdState=1&settingStatus=2'
}
const closeDialog = () => {
    dialog.value = false

}
const showKeyboard = ref(true);
const dialog = ref(false);
const openDialog = () => {
    value.value = ''
    content.value = ''
    dialog.value = true
}
defineExpose({
    openDialog
})
</script>
<style lang="scss" scoped>
.pay-Money {
    .header {
        position: relative;
        margin: 0.5rem 0.1rem;

        .title {
            width: 3rem;
            text-align: center;
            // position: absolute;
            // left: 50%;
            // transform: translate(-50%);
            font-weight: 600;
            font-size: 0.43rem;
            margin: 0 auto;
        }

        .left {
            font-size: 0.4rem;
            position: absolute;
            left: 0.3rem;
            top: 0;
        }

        .forget {
            position: absolute;
            right: 0.3rem;
            top: 0;
            font-size: 0.36rem;
            color: #00B955;
        }
    }

}
</style>
<style>
.pay-Money .van-password-input__item {
    background: #E9E9E9 !important;
    border-radius: 0.2rem !important;
}
</style>
