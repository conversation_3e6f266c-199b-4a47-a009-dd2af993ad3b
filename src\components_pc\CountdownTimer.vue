<template>
  <div>
    <span class="timer">{{ formattedTime.hours }}</span>
    <span class="font">:</span>
    <span class="timer">{{ formattedTime.minutes }}</span>
    <span class="font">:</span>
    <span class="timer">{{ formattedTime.seconds }}</span>
    <!-- <span class="font">后结束</span> -->
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';

export default {
  name: 'CountdownTimer',
  props: {
    endTime: {
      type: Number,
      required: true,
      default: 0
    },
  },
  setup(props) {
    const remainingTime = ref(props.endTime);
    const ts = ref(remainingTime.value - Date.now());

    const formattedTime = computed(() => {
      const totalSeconds = Math.max(0, Math.floor(ts.value / 1000)); // 确保总秒数不小于0
      const hours = String(Math.floor(totalSeconds / 3600)).padStart(2, '0');
      const minutes = String(Math.floor((totalSeconds % 3600) / 60)).padStart(2, '0');
      const seconds = String(totalSeconds % 60).padStart(2, '0');
      return { hours, minutes, seconds };
    });

    const updateTimer = () => {
      ts.value = remainingTime.value - Date.now();
      if (ts.value <= 0) {
        ts.value = 0;
        clearInterval(timerInterval); // 停止更新
      }
    };

    let timerInterval;
    onMounted(() => {
      timerInterval = setInterval(updateTimer, 1000);
    });

    onBeforeUnmount(() => {
      clearInterval(timerInterval);
    });

    return {
      formattedTime,
    };
  },
};
</script>

<style scoped>
.timer {
    font-size: 14px;
    text-align: center;
    display: inline-block;
    color: red;
    background-color: #fff;
    height: 18px;
    width: 19px;
    line-height: 18px;
    border-radius: 3px;
}
.font {
    font-size: 16px;
    color: red;
    margin:0 3px;
}
</style>