import { ElMessage } from "element-plus";
import * as Crypto  from "crypto-js"
/**
 * 
 * @param { (params: any) => Promise<any> } apiFn 
 * @param { any } params 
 * @param { string } errorText 
 * @param { string } successText
 * @param { number } successCode 
 * @returns { Promise<any> }
 */

export const sendRequest = (apiFn, params, errorText, successText, successCode) => {
	return new Promise((resolve, reject) => {
		apiFn(params).then((res) => {
			if (res.code == successCode) {
				if (successText) {
					ElMessage({ type:'success', message: successText })
				}
				resolve(res);
			} else {
				if (errorText) {
					ElMessage({ type:'error', message: errorText })
				}
				reject(res)
			}
		}).catch(err => {
			reject(err)
			ElMessage({ type:'error', message: errorText })
		})
	})
}

/**
 * 需保证链中的函数只有一个自定义的值，如需多个，则设置成对象的形式。
 * 只要满足链中的任意一个函数，执行链就会终止，且返回true，都不满足则返回false
 * @param { Array<Function> } FnList 需要执行的函数数组
 * @param { Array<any> } paramList 函数对应的参数
 * @param { Array<number> } arr 需要校验的数组
 */
export const checkChain = (FnList, paramList, arr) => {
	const runChain = (index, temp) => {
		//判断是否满足函数
		if (temp) return true;
		if (index >= FnList.length) return false;
		//获取链中函数的执行结果
		const result = FnList[index](arr, paramList[index]);
		return runChain(index + 1, result);
	}
	return runChain(0, false);
}

/**
 * 判断公差为t的等差数列，满足返回true
 * @param { Array } arr
 * @param { number } t   自定义的公差
 * @returns { boolean }
 */
export const verifyArithmeticProgression = (arr, t) => {
	/**
	 * 判断公差为c的等差数列
	 * @param { Array } arr
	 * @param { number } index   索引 
	 * @param { number } c 公差
	 * @param { boolean } temp  上次计算结果
	 * @returns { boolean }
	 */
	const func =  (arr, index, c, temp) => {
		if (!temp) return false;
		if (index == 1) {
			return (Number(arr[index]) - Number(arr[index - 1])) == c;
		}
		return func(arr, index - 1, c, Number(arr[index]) - Number(arr[index - 1]) == c)
	}
	return func(arr, arr.length - 1, t, true);
}

export const encrypto = (content) => {
	const key = Crypto.enc.Utf8.parse("FmpHxGc9no95cvd4");  //十六位十六进制数作为密钥
	const srcs = Crypto.enc.Utf8.parse(content);
	const encrypted = Crypto.AES.encrypt(srcs, key, { mode: Crypto.mode.CBC, padding: Crypto.pad.Pkcs7, iv: key });
	return encrypted.toString();
}