<template>
  <div class="after-sales-page">
    <!-- 头部导航 -->
    <div class="header">
      <div class="back-btn" @click="goBack">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M15 18L9 12L15 6" stroke="#333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="title">申请售后</div>
      <div class="history-btn" @click="goToHistory">历史售后</div>
    </div>

    <!-- 服务类型选择 -->
    <div class="service-type-section">
      <div class="section-title">请选择服务类型：</div>
      
      <!-- 发票售后 -->
      <div class="service-item" :class="{ disabled: isInvoiceDisabled }" @click="selectInvoiceService">
        <div class="service-content">
          <div class="service-name">发票售后</div>
          <div class="service-desc">无票/错票/申请专票</div>
        </div>
        <div class="arrow-right">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M6 12L10 8L6 4" stroke="#999" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div v-if="isInvoiceDisabled" class="overlay"></div>
      </div>

      <!-- 资质售后 -->
      <div class="service-item" :class="{ disabled: isQualificationDisabled }" @click="selectQualificationService">
        <div class="service-content">
          <div class="service-name">资质售后</div>
          <div class="service-desc">漏发未发资质/补发出库单等</div>
        </div>
        <div class="arrow-right">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M6 12L10 8L6 4" stroke="#999" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div v-if="isQualificationDisabled" class="overlay"></div>
      </div>
    </div>

    <!-- 商品售后 -->
    <div class="product-section">
      <div class="section-header">
        <div class="section-title">商品售后</div>
        <div class="action-btn" :class="{ 'refund-mode': isRefundMode }" @click="toggleMode">
          {{ isRefundMode ? '整单退款' : '整单售后' }}
        </div>
      </div>

      <!-- 搜索框 -->
      <div class="search-section">
        <div class="order-count">订单商品(3)</div>
        <div class="search-box">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" class="search-icon">
            <circle cx="7" cy="7" r="6" stroke="#999" stroke-width="1.5"/>
            <path d="M13 13L11.5 11.5" stroke="#999" stroke-width="1.5" stroke-linecap="round"/>
          </svg>
          <input type="text" placeholder="输入商品名称搜索" v-model="searchKeyword">
        </div>
      </div>

      <!-- 商品列表 -->
      <div class="product-list">
        <div v-for="(product, index) in filteredProducts" :key="index" class="product-item">
          <div class="product-image" :style="{ backgroundColor: product.imageColor }"></div>
          <div class="product-info">
            <div class="product-name">{{ product.name }}</div>
            <div class="product-spec">{{ product.spec }}</div>
            <div class="product-company">{{ product.company }}</div>
            <div class="product-status" v-if="product.refundCount || product.afterSaleCount">
              <span v-if="product.refundCount" class="refund-status">已退款：{{ product.refundCount }}</span>
              <span v-if="product.afterSaleCount" class="after-sale-status">售后中：{{ product.afterSaleCount }}</span>
            </div>
          </div>
          <div class="product-right">
            <div class="product-price">{{ product.price }}</div>
            <div class="product-quantity">×{{ product.quantity }}</div>
            <div class="product-total">小计：{{ product.total }}</div>
            <div class="apply-btn" @click="applyAfterSale(product, index)">申请售后</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 弹窗 -->
    <div v-if="showModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-text">
          您的订单尚未配送完成<br>
          申请售后，可前往店铺<br>
          首页/订单详情处查看相<br>
          关资料
        </div>
        <div class="modal-close" @click="closeModal">×</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AfterSalesPage',
  data() {
    return {
      searchKeyword: '',
      isRefundMode: false,
      showModal: false,
      isInvoiceDisabled: false,
      isQualificationDisabled: false,
      products: [
        {
          name: '双蚁 复方感冒灵颗粒(双蚁)',
          spec: '14g*12袋',
          company: '广西双蚁药业有限公司',
          price: '¥11.28',
          quantity: 4,
          total: '¥45.12',
          imageColor: '#4A90E2',
          refundCount: 0,
          afterSaleCount: 0
        },
        {
          name: '哈药 哈药牌钙铁锌口服液',
          spec: '10ml*10支',
          company: '哈药集团三精制药有限公司',
          price: '¥9.8',
          quantity: 10,
          total: '¥98',
          imageColor: '#7ED321',
          refundCount: 1,
          afterSaleCount: 3
        },
        {
          name: '泰康海恩 舒肤止痒剂(泰康海恩)',
          spec: '30ml',
          company: '大康海恩药业有限公司',
          price: '¥41.5',
          quantity: 20,
          total: '¥830',
          imageColor: '#F5A623',
          refundCount: 3,
          afterSaleCount: 17
        }
      ]
    }
  },
  computed: {
    filteredProducts() {
      if (!this.searchKeyword) {
        return this.products
      }
      return this.products.filter(product => 
        product.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
      )
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    goToHistory() {
      console.log('跳转到历史售后')
    },
    selectInvoiceService() {
      if (this.isInvoiceDisabled) {
        this.showModal = true
        return
      }
      console.log('选择发票售后')
    },
    selectQualificationService() {
      if (this.isQualificationDisabled) {
        this.showModal = true
        return
      }
      console.log('选择资质售后')
    },
    toggleMode() {
      this.isRefundMode = !this.isRefundMode
    },
    applyAfterSale(product, index) {
      if (this.shouldShowModal(product)) {
        this.showModal = true
        return
      }
      console.log('申请售后', product)
    },
    shouldShowModal(product) {
      // 模拟订单未配送完成的情况
      // 实际项目中这里应该根据订单状态判断
      // 比如：订单状态为"配送中"、"待发货"等状态时显示弹窗
      return product.name.includes('哈药') // 示例：哈药产品触发弹窗
    },
    closeModal() {
      this.showModal = false
    },
    checkServiceAvailability() {
      // 模拟根据订单状态判断服务是否可用
      // 实际项目中这里应该根据订单状态、配送状态等判断
      this.isInvoiceDisabled = false // 可以根据实际业务逻辑设置
      this.isQualificationDisabled = false // 可以根据实际业务逻辑设置
    }
  },
  mounted() {
    this.checkServiceAvailability()
  }
}
</script>

<style scoped>
.after-sales-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 头部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  padding: 4px;
  cursor: pointer;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.history-btn {
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

/* 服务类型选择 */
.service-type-section {
  background-color: #fff;
  margin-bottom: 10px;
}

.section-title {
  padding: 16px 16px 12px;
  font-size: 14px;
  color: #666;
  background-color: #f8f8f8;
}

.service-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  position: relative;
  transition: background-color 0.2s;
}

.service-item:hover {
  background-color: #f8f8f8;
}

.service-item.disabled {
  cursor: not-allowed;
}

.service-content {
  flex: 1;
}

.service-name {
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
}

.service-desc {
  font-size: 12px;
  color: #999;
}

.arrow-right {
  margin-left: 12px;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 1;
}

/* 商品售后 */
.product-section {
  background-color: #fff;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.action-btn {
  padding: 6px 12px;
  background-color: #00c853;
  color: white;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-btn.refund-mode {
  background-color: #ff5722;
}

/* 搜索区域 */
.search-section {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.order-count {
  font-size: 14px;
  color: #333;
  margin-bottom: 12px;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 20px;
  padding: 8px 12px;
}

.search-icon {
  margin-right: 8px;
}

.search-box input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 14px;
  color: #333;
}

.search-box input::placeholder {
  color: #999;
}

/* 商品列表 */
.product-list {
  padding: 0 16px;
}

.product-item {
  display: flex;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  margin-right: 12px;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  margin-right: 12px;
}

.product-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.4;
}

.product-spec {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.product-company {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.product-status {
  display: flex;
  gap: 8px;
}

.refund-status {
  font-size: 12px;
  color: #ff9800;
}

.after-sale-status {
  font-size: 12px;
  color: #2196f3;
}

.product-right {
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
}

.product-price {
  font-size: 14px;
  color: #333;
}

.product-quantity {
  font-size: 12px;
  color: #666;
  margin: 2px 0;
}

.product-total {
  font-size: 12px;
  color: #333;
  margin-bottom: 8px;
}

.apply-btn {
  padding: 4px 12px;
  background-color: #00c853;
  color: white;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.apply-btn:hover {
  background-color: #00a843;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: #333;
  color: white;
  padding: 20px;
  border-radius: 8px;
  margin: 0 20px;
  max-width: 280px;
  position: relative;
  text-align: center;
}

.modal-text {
  font-size: 14px;
  line-height: 1.6;
  color: #fff;
}

.modal-close {
  position: absolute;
  top: 8px;
  right: 12px;
  font-size: 20px;
  color: #fff;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .product-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .product-info {
    margin-right: 0;
    margin-bottom: 8px;
    width: 100%;
  }

  .product-right {
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}
