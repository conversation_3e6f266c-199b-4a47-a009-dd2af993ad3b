<template>
  <div class="account-info">
    <div class="my flex" style="margin-right: 5px;padding-right: 0px;">
      <div class="change-head-left">
        <span></span>
        平安商户信息
      </div>
      <div style="display: inline-block; margin-left: auto; margin-right: 5px; color: rgb(0,198,117);font-weight: 600;font-size: 14px;" @click="jumpServicePdf">《平安银行“产业结算通”会员服务协议》</div>
      <div class="change-head-right" @click="resetAccount">
        返回
      </div>
    </div>
    <div class="account-content">
      <div class="account-main">
        <div class="account-header flex">
          企业信息
        </div>
        <!-- //lwq -->
        <div class="account-list" v-if="formModel">
          <div class="account-item"><span class="item-label-name">企业名称：</span> <span class="account-item-textv2"> {{ formModel.enterpriseName || "" }}</span></div>
          <div class="account-item"><span class="item-label-name">企业类型：</span> <span class="account-item-textv2">企业</span></div>
          <div class="account-item"><span class="item-label-name">统一社会信用代码：</span> <span class="account-item-textv2">{{ formModel.enterpriseRegistrationNo }}</span></div>
          <div class="account-item"><span class="item-label-name">企业法人姓名：</span> <span class="account-item-textv2">{{ formModel.corporationName }}</span></div>
          <div class="account-item"><span class="item-label-name">企业法人身份证号：</span> <span class="account-item-textv2">{{ (formModel.corporationIdNo||"").replace(/^(.{3})(?:\d+)(.{4})$/, "$1***********$2") || "" }}</span></div>
          <div class="account-item"><span class="item-label-name">药店负责人手机号：</span> <span class="account-item-textv2">{{ formModel.corporatePhone }}</span></div>
        </div>
      </div>
      <div class="account-main">
        <div class="account-header flex" style="position: relative;">
          绑卡信息<div style="display: inline-block;" v-if="(((formModel||{}).creditCardDtoList)||[]).length">({{(((formModel||{}).creditCardDtoList)||[]).length}}张)</div>
          <el-button v-if="cardStatus==32" type="primary" size="mini" @click="changeCard((((formModel||{}).creditCardDtoList)||[]).length)" style="position: absolute;right: 20px;">绑定新卡</el-button>
        </div>
         <div class="account-item-content account-not-card" v-if="[33, 1, 2, 4, 8, 16].findIndex(item => item === cardStatus) > -1">
              <!-- <div class="account-item-img">
                  <img v-if="cardStatus === 0" src="@/assets/images/pinganMerchant/card-pgs.png" alt="">
                  <img v-if="cardStatus === 1 || cardStatus === 2 || cardStatus === 4" src="@/assets/images/pinganMerchant/card-pgs-ing.png" alt="">
                  <img v-if="cardStatus === 8 || cardStatus === 16" src="@/assets/images/pinganMerchant/card-pgs-error.png" alt="">
              </div> -->
              <div class="account-item-text" v-if="cardStatus === 33" style="text-align: center;">
                <img src="./img/noCar.png" alt="" style="width: 120px;"><br>
               <div> 您还没有绑定银行卡，点击“前往绑卡”申请绑卡</div>
              </div>
              <div class="account-item-text" v-if="cardStatus === 2">
                当前有一条绑卡申请记录【银行卡审核中】，审批周期预计5-10分钟，可点击“查看绑卡进度”查看详情
              </div>
              <div class="account-item-text" v-if="cardStatus === 8 || cardStatus === 16">
                当前有一条绑卡申请记录【银行卡审核失败】，请点击“查看绑卡进度”查看失败原因
              </div>
              <div class="account-item-text text-flex" v-if="cardStatus === 4">
                当前有一条绑卡申请记录【银行卡待验证】，请点击“查看绑卡进度”进行打款验证请在账户收到小额打款后48小时内进行打款验证，超时需重新发起绑卡申请
              </div>
              <div class="account-btn" @click="changeCard((((formModel||{}).creditCardDtoList)||[]).length)" v-if="cardStatus !== 32">
                {{ btnValue }}
              </div>
            </div>
        <div v-if="formModel&&formModel.creditCardDtoList" v-for="item in formModel.creditCardDtoList" class="account-card-item">
          <div v-if="Array.isArray(formModel.creditCardDtoList)&&formModel.creditCardDtoList.length">
            <div class="account-card-info">
              <div class="account-list">
                <div class="account-item"><span class="item-label-name">账户名：</span> <span class="account-item-textv2">{{item.accountName}}</span>  </div>
                <!-- //lwq -->
                <div class="account-item"><span class="item-label-name">银行账号：</span> <span class="account-item-textv2">{{(item.acct||"").replace(/^(.{3})(?:\d+)(.{4})$/, "$1* **** **** $2") || "" }}</span></div>
                <div class="account-item"><span class="item-label-name">开户银行：</span> <span class="account-item-textv2">{{item.bankName}}</span></div>
                <div class="account-item"><span class="item-label-name">开户支行：</span> <span class="account-item-textv2">{{item.branchBankName}}</span></div>
              </div>
              <div class="card-option" @click="unbindCard(item)">
                解绑
              </div>
            </div>
          </div>
           
        </div>
        
      </div>
    </div>
  </div>
</template>

<script setup>
  import { computed, reactive, ref } from "vue";
  import { useRouter, useRoute } from 'vue-router';
  import { queryPingAnAccountInfo,unBoundCard } from '@/http_pc/api';
  import { useStore } from "vuex";
  import { ElMessage,ElLoading,ElMessageBox } from 'element-plus';
  const router = useRouter();
  const route = useRoute();
  const store = useStore();
  const { merchantId } = route.query;
  const cardStatus = ref(2);  // 银行卡状态
  //lwq null
  const btnValue = computed(() => {
    return cardStatus.value === 33 ? '前往绑卡' : '查看绑卡进度';
  })
  let formModel = ref(null);
  const changeCard = (res) => {
    if(res>=5){
      ElMessage.warning('每个平安商户最多绑定5张对公卡，当前已达上限。请解绑已有卡后再次尝试');
      return;
    }
    router.push({
      path: '/pinganMerchant/pc/changeBankCard',
      query: { merchantId }
    });
  }
  const resetAccount = () => {
    // router.go(-1);
    router.push({
      path: '/pinganMerchant/pc/my',
      query: { merchantId }
    });
  }
  queryPingAnAccountInfo({ merchantId }).then((res) => {
    if (res.code === 1000) {
      const { data } = res;
      formModel.value = data;
      cardStatus.value = data.status;
      store.commit('app/setAccountInfo', formModel.value);
    }else{
      //lwq
    //   formModel.value={"creditCardDtoList":[
    //     {"accountName":"sit ullamco velit mollit","branchBankName":"cupidatat ex enim","branchBankCd":"aliqua","bankName":"officia eu ea commodo","acct":"mollit in Duis","mobileNo":"occaecat exercitation"},
    //     {"accountName":"sit ullamco velit mollit","branchBankName":"cupidatat ex enim","branchBankCd":"aliqua","bankName":"officia eu ea commodo","acct":"mollit in Duis","mobileNo":"occaecat exercitation"}]}
    }
  })
  // 跳转协议
const jumpServicePdf = () => {
  window.open(`https://my.orangebank.com.cn/orgLogin/hd/act/jianzb/B2BClearing.html?name=${formModel.value.accountName == null ? "" : formModel.value.accountName}`);
}
//解绑
const unbindCard = (item) => {
  ElMessageBox.alert('确认解绑当前银行卡吗？', '提示', {
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning', // 增加提示类型样式
  }).then((action) => {
    if (action === 'confirm') {
      let loadingService = ElLoading.service({
        text: "解绑中",
        size: "small"
      });

      let params = {
        merchantId,
        acct: item.acct,
        mobileNo: item.mobileNo,
        accountName: item.accountName
      };

      unBoundCard(params).then((res) => {
        if (res.code === 1000) {
          queryPingAnAccountInfo({ merchantId }).then(res=>{
            if (res.code === 1000) {
              const { data } = res;
              formModel.value = data;
              cardStatus.value = data.status;
              store.commit('app/setAccountInfo', formModel.value);
            }
          }); // ✅ 正确传递参数
          ElMessage.success("解绑成功");
        } else {
          ElMessage.error(res.msg);
        }
      }).finally(() => {
        loadingService.close(); // ✅ 直接关闭 loading，无需 setTimeout
      });
    }
  }).catch(() => {
    // 用户点击取消或其他方式关闭弹窗时的操作（可选）
    // ElMessage.info('已取消解绑');
  });

}
</script>

<style lang="scss" scoped>
#app {
  background: #fff;
}
.item-label-name{
  width: 300px;
  display: inline-block;
  text-align: right;
}
.account-item-textv2 {
  width: 360px;
  height: 32px;
  line-height: 32px;
  background: #EFF1F4;
  border: 1px solid #CDCDCD;
  border-radius: 4px;
  padding: 0px 15px;
  display: inline-block;

}
.account-info {
  background: #fff;
  width: 980px;
  margin: auto;
  padding: 30px 30px 0 30px;
  box-sizing: border-box;
  .flex {
    display: flex;
    align-items: center;
  }
  .my {
    justify-content: space-between;
    padding-right: 50px;
    box-sizing: border-box;
    .change-head-left {
      display: flex;
      align-items: center;
      span {
        width: 4px;
        height: 16px;
        background: #00B377;
        margin-right: 10px;
      }
      font-weight: 600;
      font-size: 18px;
      color: #292933;
      letter-spacing: 0;
      line-height: 20px;
    }
    .change-head-right {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 14px;
      color: #666666;
      padding: 6px 8px;
      box-sizing: border-box;
      border: 1px solid #DDDDDD;
      border-radius: 2px;
      cursor: pointer;
    }
  }
  .account-content {
    .account-list {
      
      margin-top: 12px;
      .account-item {

        color: #000000a6;
        font-size: 14px;
        font-face: PingFangSC;
        font-weight: 400;
        line-height: 14px;
        letter-spacing: 0;
        paragraph-spacing: 0;
        text-align: left;
        margin-bottom: 10px;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    .card-option {
      width: 80px;
      height: 32px;
      // background: #00B955;
      background: #FFFFFF;
      
      border-radius: 2px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 14px;
      color: red;
      border: 1px solid #E2E7E7;
      letter-spacing: 0.51px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      position: absolute;
      right: 10px;
      top: 10px;
    }
  }
  .account-main {
    padding: 20px 0;
    box-sizing: border-box;
    border-bottom: 1px solid #eee;
    &:last-child {
      border-bottom: 0;
    }
    .account-header {
      font-family: PingFangSC-Medium;
      font-weight: bold;
      font-size: 14px;
      color: #292933;
      letter-spacing: 0;
      line-height: 16px;
      span {
        width: 4px;
        height: 4px;
        background: #00B955;
        display: flex;
        border-radius: 50%;
        margin-right: 5px;
      }
    }
    .account-item-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .account-item-img {
        width: 48px;
        height: 48px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .account-item-text {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #676773;
        margin: 20px 0;
        // display: flex;
        align-items: center;
        .warn-span {
          color: #FF7B03;
        }
        .error-span {
          color: #FF2121;
        }
      }
      .text-flex {
        flex-direction: column;
        div {
          display: flex;
        }
        .account-item-tips {
          margin-top: 10px;
          color: #ff7a06;
          font-size: 14px;
          font-face: PingFangSC;
          font-weight: 400;
          line-height: 24px;
        }
      }
      .account-btn {
        width: 300px;
        height: 32px;
        background: #00B955;
        border-radius: 2px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
    }
  }
}
.account-card-item{
  border-radius: 5px;
  margin-bottom: 10px;
  background-color: #FAFAFA;
  padding: 20px 0;
  margin-top: 20px;
  position: relative;
}
</style>