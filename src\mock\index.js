import Mock from "mockjs";

const { mock } = Mock; // Mock函数

//表格数据
const curPrincipalAmountBalance = 42000.00;
let { records } = Mock.mock({
    'records|20-30':[
        {
            billCode:'@guid(10)',
            "billName|1": ['本月账单','2023年7月账单','2023年6月账单','2023年5月账单','2023年4月账单'],
            curPrincipalAmountBalance: '@float(1,50000,0,2)',
            actPrincipalAmount: '@float(1,50000,0,2)',
            actInterestAmount: '@float(1,50000,0,2)',
            actSubsidyInterestAmount: '@float(1,50000,0,2)',
            subsidyInterestDate: '@date(yyyy年MM月dd)',
            "payoffflag|1": [0,1,2,3],
            "orderNumber|1":[1,2,3]
        }
    ]
})

// mock(RegExp('/api/pinganLoan/my_loan'), "post", () => {
//     return {
//         code: 0,
//         msg: '获取列表数据',
//         data: {
//             curPrincipalAmountBalance: curPrincipalAmountBalance,
//             availableAmount: availableAmount,
//             approveAmount: approveAmount,
//             records: records,
//         }
//     }
// });


//明细数据
const billDate = "2023年7月账单"
let { recordsDetail } = Mock.mock({
    'recordsDetail|1-4':[
        {
            billDate:'@date(MM月dd)',
            'records|1-4':[
                {
                    "payoffflag|1": [0,1],
                    payDayFormat:'@date(yyyy年MM月dd)',
                    receiverName:"湖北小药药自营旗舰店",
                    tradeAmount:'@float(1,50000,0,2)',
                    businessPayNo: "111111111111111111111",
                    curRepayDate: '@date(yyyy年MM月dd)',
                    actRepayDate: '@date(yyyy年MM月dd)',
                    curPrincipalAmountBalance: '@float(1,50000,0,2)',
                    actPrincipalAmount: '@float(1,50000,0,2)',
                    actInterestAmount: '@float(1,50000,0,2)',
                    actSubsidyInterestAmount: '@float(1,50000,0,2)',
                    "refund|1":[1,0],
                }
            ]
        }
    ]
})
// mock(RegExp('/api/pinganLoan/loan_detail'), "post", () => {
//     return {
//         code: 0,
//         msg: '获取明细数据',
//         data: recordsDetail
//     }
// });

//平安ePay可用余额
const availableAmount = 45000.00;
const approveAmount = 1000000.00;

// mock(RegExp('/api/app/pinganaccount/queryPingAnCreditBalance'), "post", () => {
//     return {
//         code: 0,
//         msg: '获取平安ePay可用余额',
//         data: {
//             availableAmount: availableAmount,
//             approveAmount: approveAmount
//         }
//     }
// });