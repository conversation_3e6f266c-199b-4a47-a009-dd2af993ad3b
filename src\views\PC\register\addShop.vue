el-select<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加店铺信息"
    width="700px"
    :before-close="handleClose"
  >
    <div>
      <el-form ref="ruleFormRef" :model="shopInfo" :rules="rules" label-width="180px" style="margin: 0 auto;max-width: 650px;">
        <el-form-item label="店铺名称" prop="name">
          <el-input v-model="shopInfo.name" placeholder="请输入店铺名称" style="width: 300px" />
        </el-form-item>
        <el-form-item>
          <template #label>
            <span style="color: red;">*</span>
            <span>店铺地址</span>
          </template>
          <el-select
            v-model="searchAddressInfo.provinceId"
            placeholder="省"
            @change="selectChange($event, 'cityList')"
            style="width: 200px"
          >
            <el-option
              v-for="(item, index) in provList"
              :key="index"
              :label="item.areaName"
              :value="item.areaCode"
            />
          </el-select>
          <el-select
            v-model="searchAddressInfo.cityId"
            placeholder="市"
            @change="selectChange($event, 'areaList')"
            style="width: 200px"
          >
            <el-option
              v-for="(item, index) in cityList"
              :key="index"
              :label="item.areaName"
              :value="item.areaCode"
            />
          </el-select>
          
          <el-select
            v-model="searchAddressInfo.districtId"
            placeholder="区"
            @change="selectChange($event, 'streeList')"
            style="width: 200px"
          >
            <el-option
              v-for="(item, index) in areaList"
              :key="index"
              :label="item.areaName"
              :value="item.areaCode"
            />
          </el-select>  
          <el-select
            v-model="searchAddressInfo.streetId"
            placeholder="街道"
            @change="selectChange($event, '')"
            style="width: 200px"
          >
            <el-option
              v-for="(item, index) in streeList"
              :key="index"
              :label="item.areaName"
              :value="item.areaCode"
            />
          </el-select>
          <!-- <el-cascader
            ref="areaCategoryLevel"
            v-model="shopInfo.areaCodes"
            :props="{
              lazy: true,
              lazyLoad: lazyLoad
            }"
            style="width: 300px"
            placeholder="请选择店铺所在的省/市/区/街道"
            @change="handleAreaChange"
          /> -->
        </el-form-item>
        <el-form-item label="" prop="address">
          <el-input v-model="shopInfo.address" placeholder="请输入店铺门牌/详细地址" style="width: 300px" />
        </el-form-item>
        <el-form-item label="企业类型" prop="customerType">
          <el-select style="width: 300px" v-model="shopInfo.customerType">
            <el-option v-for="item in customerTypeArr" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="shopInfo.customerType"
          :label="`${isClinicType.includes(shopInfo.customerType) ? '医疗机构执业许可证号' : '营业执照号'}`"
          prop="licenseNo">
          <el-input v-model="shopInfo.licenseNo"
            :placeholder="`${isClinicType.includes(shopInfo.customerType) ? '请输入医疗机构执业许可证号' : '请输入营业执照号'}`"
            style="width: 300px" />
        </el-form-item>
        <el-form-item
          v-if="shopInfo.customerType"
          class="uploadBox"
        >
          <template #label>
            <span style="color: red;">*</span>
            <span>{{ notClinicType.includes(shopInfo.customerType) ? '营业执照电子版' : '医疗机构执业许可证电子版' }}</span>
          </template>
          <el-upload
            action
            list-type="picture-card"
            :http-request="toUploadImg"
            :before-upload="beforeUpload"
          >
            <div class="imgBox" v-if="imgSrc">
              <img
                :src="imgSrc"
                alt=""
                class="img"
              >
              <el-icon class="delIcon" @click.stop="delImageUrl(item)"><Delete /></el-icon>
            </div>
            <el-icon v-else><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="onSubmit(ruleFormRef)" :loading="submitLoading"
          >提交审核</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from "element-plus";
import {
  findAreaByParentId, uploadImg, getAllCustomerType, addShop
} from '@/http_pc/api';
import { Plus, Delete } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';

const props = defineProps({
  showAddShop: {
    default: false,
  },
  cancelDialog: {
    type: Function,
    default: () => {}
  },
});
const router = useRouter();
const imgUrl = import.meta.env.VITE_IMG;
const submitLoading = ref(false);
const ruleFormRef = ref(null);
const areaCategoryLevel = ref(null);
const rules = {
  name: [
    { required: true,  message: '请输入', trigger: 'blur' },
    { max: 50, message: '长度限制为50个字符', trigger: 'change' }
  ],
  // areaCodes: [
  //   { required: true,  message: '请选择', trigger: 'change' },
  // ],
  customerType: [
    { required: true,  message: '请选择', trigger: 'change' },
  ],
  licenseNo: [
    { required: true,  message: '请填写', trigger: 'change' },
    { validator: (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请填写'))
      } else if (!/^([a-zA-Z0-9]{15}|[a-zA-Z0-9]{18})$/.test(value)) {
        callback(new Error('格式错误，请填写15或18位的数字/字母'))
      } else {
        callback();
      }
    }, trigger: 'change' }
  ],
};
const dialogVisible = ref(props.showAddShop);
const shopInfo = ref({
  name: '',
  address: '',
  customerType: undefined,
  // areaCodes: [],
  licenseNo: '',
});
const provList = ref([]);
const cityList = ref([]);
const areaList = ref([]);
const streeList = ref([]);

const searchAddressInfo = ref({
  cityId: '',
  districtId: '',
  streetId: '',
  provinceId: '',
  prov: '',
  city: '',
  area: '',
  streetName: ''
});

const imgSrc = ref('');
const areaNames = ref([]);
const customerTypeArr = ref([]);
const isClinicType = ref([]); // 诊所类型
const notClinicType = ref([]); // 非诊所类型
const handleClose = () => {
  props.cancelDialog();
}

// const lazyLoad = (node, resolve) => {
//     const { level } = node;
//     const arr = [];
//     findAreaByParentId({
//       parentId: node.value || 0,
//     }).then((res) => {
//       if (res.areaList && res.areaList.length > 0) {
//         res.areaList.map((item) => {
//           let obj = {
//             ...item,
//             value: item.areaCode,
//             label: item.areaName,
//             leaf: level > 2,
//           };
//           arr.push(obj);
//         });
//         resolve(arr);
//       } else {
//         resolve(arr);
//       }
//     });
//   }

  // const handleAreaChange = () => {
  //   if ((shopInfo.value.areaCodes || []).length != 0) {       
  //     areaNames.value = areaCategoryLevel.value.getCheckedNodes()[0].pathLabels;
  //   }
  // }

  const getAddres = (code, listStr) => {
    findAreaByParentId({
      parentId: code || 0,
    }).then((res) => {
      if ((res.areaList || []).length) {
        switch (listStr) {
        case 'provList':
          provList.value = res.areaList;
          break;
        case 'cityList':
          cityList.value = res.areaList;
          break;
        case 'areaList':
          areaList.value = res.areaList;
          break;
        case 'streeList':
          streeList.value = res.areaList;
          break;
        }
      }
    });
  }

  const selectChange = (value, listStr) => {
    switch (listStr) {
      case 'cityList':
        cityList.value = [];
        areaList.value = [];
        streeList.value = [];
        searchAddressInfo.value.cityId = '';
        searchAddressInfo.value.districtId = '';
        searchAddressInfo.value.streetId = '';
        if (searchAddressInfo.value.provinceId) {
          provList.value.forEach((item) => {
            if (item.areaCode === searchAddressInfo.value.provinceId) {
              searchAddressInfo.value.prov = item.areaName;
            }
          });
        }
        break;
      case 'areaList':
        areaList.value = [];
        streeList.value = [];
        searchAddressInfo.value.districtId = '';
        searchAddressInfo.value.streetId = '';
        if (searchAddressInfo.value.cityId) {
          cityList.value.forEach((item) => {
            if (item.areaCode === searchAddressInfo.value.cityId) {
              searchAddressInfo.value.city = item.areaName;
            }
          });
        }
        break;
      case 'streeList':
        streeList.value = [];
        searchAddressInfo.value.streetId = '';
        if (searchAddressInfo.value.districtId) {
          areaList.value.forEach((item) => {
            if (item.areaCode === searchAddressInfo.value.districtId) {
              searchAddressInfo.value.area = item.areaName;
            }
          });
        }
        break;
      default:
        if (searchAddressInfo.value.streetId) {
          streeList.value.forEach((item) => {
            if (item.areaCode === searchAddressInfo.value.streetId) {
              searchAddressInfo.value.streetName = item.areaName;
            }
          });
        }
        break;
    }
    getAddres(value, listStr);
  };

  const toUploadImg = (file) => {
    uploadImg({ file, }).then((res) => {
      if (res.status === 'success') {
        imgSrc.value = `${imgUrl}${res.fileName[0]}`;
      }
    });
  }

  const delImageUrl = (src) => {
    imgSrc.value = '';
  }

  const beforeUpload = (file) => {
    const isJPG = file.type === 'image/jpg' || file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/bmp' || file.type === 'image/gif';
    if (!isJPG) {
      ElMessage.error('请上传正确格式的图片');
      return false;
    }
    const size = 2;
    const isLt2M = file.size / 1024 / 1024 < size;
    if (!isLt2M) {
      ElMessage.error(`图片大小不得超过${size}M`);
      return false;
    }
    if (isJPG && isLt2M) {
      toUploadImg(file);
    }
    return false;
  };

  const onSubmit = async(formEl) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
      if (valid) {
        if (!imgSrc.value) {
          ElMessage.error('请上传电子版许可证');
          return;
        }
        if (!searchAddressInfo.value.districtId) {
          ElMessage.error('店铺地址至少到三级区');
          return;
        }
        if (streeList.value.length && !searchAddressInfo.value.streetId) {
          ElMessage.error('此区域的店铺地址需要选到四级区');
          return;
        }
        submitLoading.value = true;
        let params = {
          customerType: shopInfo.value.customerType, // 店铺类型
          licenseNo: shopInfo.value.licenseNo, // 医疗机构执业许可证编号/营业执照号
          name: shopInfo.value.name,
          provinceCode: searchAddressInfo.value.provinceId || '',
          cityCode: searchAddressInfo.value.cityId || '',
          areaCode: searchAddressInfo.value.districtId || '',
          streetCode: searchAddressInfo.value.streetId || '',
          province: searchAddressInfo.value.prov || '',
          city: searchAddressInfo.value.city || '',
          district: searchAddressInfo.value.area || '',
          street: searchAddressInfo.value.streetName || '',
          // provinceCode: shopInfo.value.areaCodes[0] || '',
          // cityCode: shopInfo.value.areaCodes[1] || '',
          // areaCode: shopInfo.value.areaCodes[2] || '',
          // streetCode: shopInfo.value.areaCodes[3] || '',
          address: shopInfo.value.address,
          // province: areaNames.value[0] || '',
          // city: areaNames.value[1] || '',
          // district: areaNames.value[2] || '',
          // street: areaNames.value[3] || '',
          licenseImageUrl: `https:${imgSrc.value}`,
        };
        addShop(params).then((res) => {
          submitLoading.value = false;
          if (res.status === 'success') {
            ElMessage.success('添加成功');
            setTimeout(() => {
              router.push(`/register/examineShopInfo?merchantId=${res.data.merchantId}`);
            }, 800);
          } else {
            ElMessage.error(res.errorMsg || res.msg);
          }
        }).catch(() => {
          submitLoading.value = false;
        })
      } else {
        console.log('没有提交', fields);
      }
    })
  }

  // 获取企业类型
  const getAllType = () => {
    getAllCustomerType({}).then((res) => {
      customerTypeArr.value = res.list || [];
      customerTypeArr.value.map((item) => {
        if (item.id == 4 || item.id == 14 || item.id == 10 || item.id == 9 || item.id == 8 || item.id == 15 || item.id == 6 || item.id == 7 || item.id == 22 || item.id == 23) {
          isClinicType.value.push(item.id);
        } else {
          notClinicType.value.push(item.id);
        }
      })
    })
  }

  onMounted(() => {
    getAddres(0, 'provList');
    getAllType();
  })

</script>
<style lang="scss" scoped>
  .uploadBox {
    height: 170px;
    margin: 20px;
    .imgBox {
      width: 148px;
      height: 148px;
      position: relative;
      .img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
  .delIcon {
    display: none;
    position: absolute;
    top: 60px;
    left: 60px;
  }
  .imgBox:hover .delIcon {
    display: block;
  }
</style>
