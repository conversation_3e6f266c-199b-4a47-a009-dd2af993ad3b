<!-- 加减购物车按钮，使用示例在temprow.vue组件 -->
<template>
  <div class="btn-container">
    <div v-if="productData.status != 2" class="content">
      <div class="spread-add">
        <div class="reduce" @click.stop.prevent="addProductCart('min');bntClick('减',1)">-</div>
        <div class="cont">
          <input
            @click.stop.prevent
            @focus="bntClick(productValue,2)"
            class="input-val"
            :class="'input-val'+goodsId"
            type="tel"
            :value="productValue"
            @change="inputCart"
            @blur="handleCheckValue"
          />
        </div>
        <div class="plus" @click.stop.prevent="addProductCart('add');bntClick('加',3)">+</div>
      </div>
      <div class="addPurchaseOrder soudOutBtn" v-if="productData.status === 2">已售罄</div>
      <!-- <div class="addPurchaseOrder pgbyBtn" v-else @click.stop.prevent="toJumpPage()">去抢购</div> -->
      <div class="addPurchaseOrder-pgby-box" v-else>
        <div class="addPurchaseOrder-addCar" v-if="productData.canAddToCart" @click.stop.prevent="bntClick('加入购物车',4);addProductCart()">
          <img src="../assets/images/addcar.png" alt="">
        </div>
        <div class="addPurchaseOrder-pgby pgbyBtn" @click.stop.prevent="bntClick('去抢购',productData.canAddToCart?5:4);toJumpPage()">去抢购</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { putRequest, changeCart } from '@/http_pc/api';
import { actionTracking } from '@/config/eventTracking';
import { onMounted, ref, watch, getCurrentInstance, computed } from "vue";
import { ElMessage } from 'element-plus'
import { useStore } from "vuex";
import { formatDate } from '@/utils/index';
  const props = defineProps({
    productData: {
      default: {}
    },
    jgAgentInfo: {
      default: {}
    },
    keyWord: {
      default: "",
    },
    searchSortStrategyId: {
      default: ""
    },
    index: {
      default: ""
    },
    qtData: {
      default: {}
    },
    scmE:{
      default: ''
    }
  });
  const { proxy } = getCurrentInstance();
  const qtData=computed(()=>{
    return  props.qtData;
  })
  const activePrice = ref('');
  const productValue = ref('');
  const is_split = ref('');
  const goodsId = ref('');
  const med_num = ref('');

  onMounted(() => {
    const actPgby = (props.productData || {}).actPgby || {};
    goodsId.value = (props.productData || {}).id;
    productValue.value = (props.productData || {}).cartProductNum ? (props.productData || {}).cartProductNum : actPgby.skuStartNum;
    is_split.value = (props.productData || {}).isSplit;
    med_num.value = parseInt((props.productData || {}).mediumPackageNum);
    activePrice.value = actPgby.minSkuPrice;
  })

  
  const addProductCart = (type) => {
    const actPgby = (props.productData || {}).actPgby || {};
    if (type === "add") {
      productValue.value += med_num.value;
      // this.addType = 3;
    } else if (type === "min") {
      if (productValue.value > 0) {
        if(productValue.value < actPgby.skuStartNum || productValue.value == actPgby.skuStartNum) {
          return;
        }
        productValue.value = is_split.value == 1 ? productValue.value - 1 : productValue.value - med_num.value;
        // this.addType = 1;
      }
    } else {
      sessionStorage.setItem("jgInfo", JSON.stringify({
        ...props.jgAgentInfo,
        rank: props.index + 1,
        search_sort_strategy_id:props.searchSortStrategyId || null,
        operation_id: props.productData.operationId && props.productData.operationId != 'null' ? `${props.productData.operationId}` : null, 
        operation_rank: props.productData.operationId && props.productData.operationId != 'null' ? 1 : null,
        list_position_type:`${props.productData.positionType}`,
        list_position_typename:props.productData.positionTypeName,
        product_id:props.productData.id,
        product_name:props.productData.productName,
        product_first:`${props.productData.categoryFirstId}`,
        product_present_price:getPrice(props.productData),
        product_type:`${props.productData.productType}`,
        product_shop_code:props.productData.shopCode,
        product_shop_name:props.productData.shopName,
      }))
      window.AnalysysAgent.track("action_product_button_click", {
        ...props.jgAgentInfo,
        rank: props.index + 1,
        key_word: props.keyWord,
        search_sort_strategy_id: props.searchSortStrategyId && props.searchSortStrategyId != 'null' ? `${props.searchSortStrategyId}` : null,
        operation_id: props.productData.operationId && props.productData.operationId != 'null' ? `${props.productData.operationId}` : null, 
        operation_rank: props.productData.operationId && props.productData.operationId != 'null' ? 1 : null,
        list_position_type:`${props.productData.positionType}`,
        list_position_typename:`${props.productData.positionTypeName}`,
        product_id: props.productData.id,
        product_name:`${props.productData.productName}`,
        product_first:`${props.productData.categoryFirstId}`,
        product_number: productValue.value,
        product_present_price: getPrice(props.productData),
        product_type:`${props.productData.productType}`,
        product_shop_code:props.productData.shopCode,
        product_shop_name:props.productData.shopName,
        btn_name: "加入购物车",
        btn_desc: "列表页",
        direct: "1"
      });
      window.AnalysysAgent.track("add_to_cart", {
        ...props.jgAgentInfo,
        rank: props.index + 1,
        key_word: props.keyWord,
        search_sort_strategy_id: props.searchSortStrategyId && props.searchSortStrategyId != 'null' ? `${props.searchSortStrategyId}` : null,
        operation_id: props.productData.operationId && props.productData.operationId != 'null' ? `${props.productData.operationId}` : null, 
        operation_rank: props.productData.operationId && props.productData.operationId != 'null' ? 1 : null,
        list_position_type:`${props.productData.positionType}`,
        list_position_typename:`${props.productData.positionTypeName}`,
        product_id: props.productData.id,
        product_name:`${props.productData.productName}`,
        product_first:`${props.productData.categoryId}`,
        product_number: productValue.value,
        product_present_price: getPrice(props.productData),
        product_type:`${props.productData.productType}`,
        product_shop_code:props.productData.shopCode,
        product_shop_name:props.productData.shopName,
        direct: "1",
        add_cart_time: formatDate(new Date() * 1, 'yyyy-MM-dd hh:mm:ss')
      });
      window.AnalysysAgent.track("action_list_product_click", {
        ...props.jgAgentInfo,
        rank: props.index + 1,
        key_word: props.keyWord,
        search_sort_strategy_id: props.searchSortStrategyId && props.searchSortStrategyId != 'null' ? `${props.searchSortStrategyId}` : null,
        operation_id: props.productData.operationId && props.productData.operationId != 'null' ? `${props.productData.operationId}` : null, 
        operation_rank: props.productData.operationId && props.productData.operationId != 'null' ? 1 : null,
        list_position_type:`${props.productData.positionType}`,
        list_position_typename:`${props.productData.positionTypeName}`,
        product_id: props.productData.id,
        product_name:`${props.productData.productName}`,
        product_first:`${props.productData.categoryFirstId}`,
        product_present_price: getPrice(props.productData),
        product_type:`${props.productData.productType}`,
        product_shop_code:props.productData.shopCode,
        product_shop_name:props.productData.shopName,
      });
      postCartData(productValue.value);
    }
    // changePtPrice();
  }
  const getCartNum = () => {
    putRequest('get', '/merchant/center/cart/getCartNum', {
      r: Math.random(),
      num:0
    }).then((res) => {
      if(res.status === "success") {
        const numId = window.parent.document.getElementById('rigthCartNum');
        console.log('加购成功之后', numId);
        if (numId && res.num) {
          numId.className = 'topp cycle2';
          numId.innerText = res.num;
        }
      }
    })
  }
  const isPack = ref(false); // 套餐
  const store = useStore();
  const postCartData = (val) => {
    if (val > 0) {
      const config = isPack.value == true ? {
        'merchantId': proxy.$merchantId,
        'amount': val,
        "packageId": goodsId.value
      } : { 'merchantId': proxy.$merchantId, 'amount': val, "skuId": goodsId.value }
      let jgInfo = JSON.parse(sessionStorage.getItem("jgInfo")) || null;
      if (jgInfo) {
        jgInfo.direct = "1";
        config.mddata = JSON.stringify(jgInfo);
      }
      if (window.fwData) {
        config.qtdata = JSON.stringify({
          spm_cnt: window.fwData['spm_cnt'],
          scm_cnt: window.fwData['scm_cnt']
        })
        if (qtData.value.commonName == "BigSearch") {
          config.qtdata = JSON.stringify({
            spm_cnt: window.fwData['spm_cnt'],
            scm_cnt: window.fwData['scm_cnt'],
            qt_list_data:JSON.parse(window.fwData['qt_list_data']),
              qt_sku_data:JSON.parse(window.fwData['qt_sku_data']) 
          })
        }
      }
      putRequest('post', '/merchant/center/cart/changeCart.json', config).then((res) => {
        if (res.status == "success") {
          if (res.data.qty != val) {
            productValue.value = parseInt(res.data.qty);
          }
          
          if (res.dialog != null) {
            if (res.dialog.style == 20) {
              ElMessage.error(res.dialog.msg)
            } else {
              ElMessage.error(res.dialog.msg)
            }
          }
          if (res.data.message) {
            ElMessage.error(res.data.message);
          } else {
            // ElMessage.success('加购成功！')
            store.commit('app/changeprompt', { promptmsg: '加购成功！', showprompt: true })
            getCartNum();
          }

          // try {
          //   const addconfig = isPack.value == true ? {
          //     proid: goodsId.value,
          //     pronum: productValue.value,
          //     isAdd: 1,
          //     type: 1
          //   } : { proid: goodsId.value, pronum: productValue.value, isAdd: 1 }
          //   // window.webkit.messageHandlers.addPlanNumber.postMessage(addconfig);//ios
          // } catch (erro) {

          // }
          ;
        } else {
          productValue.value = 0;
          ElMessage.error(res.errorMsg || res.msg || res.dialog.msg);
        }
      }).catch((err) => {
        console.log('errrrrr', err);
        // WS.Bus.loading = false;
      })
    } else {
      const config = isPack.value == true ? { "packageIds": goodsId.value } : { "ids": goodsId.value }
      putRequest('post', `/app/batchRemoveProductFromCart`, config).then((res) => {
        if (res.status == "success") {
          productValue.value = 0;
          // try {
          //   const addconfig = isPack.value == true ? {
          //     proid: goodsId.value,
          //     pronum: 0,
          //     isAdd: 1,
          //     type: 1
          //   } : { proid: goodsId.value, pronum: 0, isAdd: 1 }
          //   window.webkit.messageHandlers.addPlanNumber.postMessage(addconfig);//ios
          // } catch (erro) {

          // }
          // ;
          // try {
          //   if (isPack.value == true) {
          //     window.hybrid.addPlanNumber(goodsId.value, 0, 1, 1); //android
          //   } else {
          //     window.hybrid.addPlanNumber(goodsId.value, 0, 1); //android
          //   }
          // } catch (erro) {

          // }
          // ;
        }
      })
    }
  }
  const inputCart = (e) => {
    let num = parseInt(e.target.value);
    num = num > 0 ? num : 0;
    productValue.value = num;
    // this.addType = 2;
  }
  const getPrice = (item) => {
    if (item.actPt) {
      if (item.actPt.stepPriceStatus == 1) {
        //阶梯\
        return item.actPt.minSkuPrice;
      } else if (item.actPt.stepPriceStatus == 2 && item.actPt.assembleStatus == 1) {
        return item.actPt.assemblePrice;
      }
      return ''
    } else if (item.actPgby) {
      return item.actPgby.assemblePrice;
    } else if (item.priceType == 2 && item.skuPriceRangeList) {
      return item.skuPriceRangeList[item.skuPriceRangeList.length -1].price;
    } else {
      return item.fob
    }
  }
  const handleCheckValue = (e) => {
    let num = parseInt(e.target.value);
    if (num < ((props.productData || {}).actPgby || {}).skuStartNum) {
      productValue.value = ((props.productData || {}).actPgby || {}).skuStartNum;
      return false;
    }
    if (is_split.value == 0) {
      const remainder = num % med_num.value;
      if (remainder > 0) {
        productValue.value = num - remainder;
      }
    }
  }
      
  const changePtPrice = () => {
    changeCart({
      skuId: (props.productData || {}).id,
      quantity: productValue.value,
      merchantId: proxy.$merchantId,
    }).then((res) => {
      console.log('加购', res);
      if (res.data.status === 'success') {
        activePrice.value = res.data.data.price;
        // proxy.$emit('change-price', (props.productData || {}).id, activePrice.value);
      } else {
        WS.Toast(res.data.errorMsg);
      }
    })
  }
  const toJumpPage = () => {
    sessionStorage.setItem("jgInfo", JSON.stringify({
      ...props.jgAgentInfo,
      rank: props.index + 1,
      search_sort_strategy_id:props.searchSortStrategyId || null,
      operation_id: props.productData.operationId && props.productData.operationId != 'null' ? `${props.productData.operationId}` : null, 
      operation_rank: props.productData.operationId && props.productData.operationId != 'null' ? 1 : null,
      list_position_type:`${props.productData.positionType}`,
      list_position_typename:props.productData.positionTypeName,
      product_id:props.productData.id,
      product_name:props.productData.productName,
      product_first:`${props.productData.categoryFirstId}`,
      product_present_price:getPrice(props.productData),
      product_type:`${props.productData.productType}`,
      product_shop_code:props.productData.shopCode,
      product_shop_name:props.productData.shopName,
      direct: "1"
    }))
    window.AnalysysAgent.track("action_product_button_click", {
      ...props.jgAgentInfo,
      rank: props.index + 1,
      key_word: props.keyWord,
      search_sort_strategy_id: props.searchSortStrategyId && props.searchSortStrategyId != 'null' ? `${props.searchSortStrategyId}` : null,
      operation_id: props.productData.operationId && props.productData.operationId != 'null' ? `${props.productData.operationId}` : null, 
      operation_rank: props.productData.operationId && props.productData.operationId != 'null' ? 1 : null,
      list_position_type:`${props.productData.positionType}`,
      list_position_typename:`${props.productData.positionTypeName}`,
      product_id: props.productData.id,
      product_name:`${props.productData.productName}`,
      product_first:`${props.productData.categoryFirstId}`,
      product_number: productValue.value,
      product_present_price: getPrice(props.productData),
      product_type:`${props.productData.productType}`,
      product_shop_code:props.productData.shopCode,
      product_shop_name:props.productData.shopName,
      btn_name: "去抢购",
      btn_desc: "列表页",
      direct: "1"
    });
    window.AnalysysAgent.track("action_list_product_click", {
      ...props.jgAgentInfo,
      rank: props.index + 1,
      key_word: props.keyWord,
      search_sort_strategy_id: props.searchSortStrategyId && props.searchSortStrategyId != 'null' ? `${props.searchSortStrategyId}` : null,
      operation_id: props.productData.operationId && props.productData.operationId != 'null' ? `${props.productData.operationId}` : null, 
      operation_rank: props.productData.operationId && props.productData.operationId != 'null' ? 1 : null,
      list_position_type:`${props.productData.positionType}`,
      list_position_typename:`${props.productData.positionTypeName}`,
      product_id: props.productData.id,
      product_name:`${props.productData.productName}`,
      product_first:`${props.productData.categoryFirstId}`,
      product_present_price: getPrice(props.productData),
      product_type:`${props.productData.productType}`,
      product_shop_code:props.productData.shopCode,
      product_shop_name:props.productData.shopName,
    });
    let url = `/merchant/center/order/group/settle.htm?productNum=${productValue.value}&skuId=${(props.productData || {}).id}&bizSource=10&shopCodes=${(props.productData || {}).shopCode}&isSupportOldSxp=${((props.productData || {}).actPgby || {}).supportSuiXinPin}&isThirdCompany=${(props.productData || {}).isThirdCompany}`
    // if (window.fwData) {
    //   url = url + `&spm_cnt=${window.fwData['spm_cnt']}&scm_cnt=${window.fwData['scm_cnt']}`
    //   if (qtData.value.commonName == "BigSearch") {
    //     url+=`&qt_list_data=${encodeURIComponent(window.fwData['qt_list_data']) }&qt_sku_data=${ encodeURIComponent(window.fwData['qt_sku_data']) }`
    //   }
    // }
    window.open(url)
     //运营位埋点
    //  if(props.productData.operationId){
    //   actionTracking('pc_action_Product_Click', {
    //       commodityId: props.productData.id,
    //       commodityName:  props.productData.commonName,
    //       commodityCode:  props.productData.barcode,
    //       commodityCategory:  props.productData.categoryId,
    //       spid: props.trackInfo.spid,
    //       sid: props.trackInfo.sid,
    //       sptype: props.trackInfo.sptype,
    //       real: 2,
    //       search_sort_strategy_id: props.searchSortStrategyId,
    //       active_type: 0,
    //       click_item: 0,
    //       goods_groupid:  props.productData.operationExhibitionId,
    //       active_id:  props.productData.operationId,
    //       active_index: props.index
    //     })
    // }
  }
 let scmEShopDetail=''
const bntClick = (text, index) => {
  //所有商品按钮点击qt埋点
  //商品按钮点击+商品点击(商品详情页)
  if (qtData.value.commonName == "productDetail" && qtData.value.isShopDetail && window.parent.getSpmE) {
    scmEShopDetail = props.scmE + proxy.scmEActive(6)
    window.fwData = {
      "spm_cnt": `1_4.productDetail_${qtData.value.id}-<EMAIL>@${qtData.value.index + 1}_btn@${index}.${window.parent.getSpmE()}`,
      "scm_cnt": `productBusiness.${props.searchSortStrategyId||'0'}.all_0.prod-${(props.productData || {}).id}_text-${text}.${scmEShopDetail}`,//yz 商品组和人群scme14位
      // "result_cnt": qtData.value.result_cnt,
      "product_id": (props.productData || {}).id,
      "product_name": (props.productData || {}).showName
    }
    aplus_queue.push({
      'action': 'aplus.record',
      'arguments': ['action_product_button_click', 'CLK', window.fwData]
    });
    aplus_queue.push({
      'action': 'aplus.record',
      'arguments': ['action_list_product_click', 'CLK', {
        "spm_cnt": `1_4.productDetail_${qtData.value.id}-<EMAIL>@${qtData.value.index+1}.${window.parent.getSpmE()}`,
        "scm_cnt": `productBusiness.${props.searchSortStrategyId||'0'}.all_0.prod-${(props.productData || {}).id}.${scmEShopDetail}`,//yz 商品组和人群scme14位
        // "result_cnt": qtData.value.result_cnt,
        "product_id": (props.productData || {}).id,
        "product_name": (props.productData || {}).showName
      }]
    });
  }
  if (qtData.value.commonName == "BigSearch" && window.parent.getSpmE ) {//&& window.parent.getSpmE
    scmEShopDetail = props.scmE + proxy.scmEActive(6)
   if(qtData.value.isOperation){
    try {
      let qtSkuData=JSON.parse((props.productData || {}).qtSkuData)
            const getStr = (str) => {
            return str.replace(/[^a-zA-Z0-9]/g, "")
          }
          window.fwData ={
            "spm_cnt":`<EMAIL>@${qtSkuData.rank}_prod@${qtSkuData.rank}-${qtSkuData.operation_rank}_btn@${index}.${window.parent.getSpmE()}`,
            "scm_cnt":`search.${props.searchSortStrategyId||'0'}.${props.productData.operationCustomerGroupId||"all"}_${getStr(props.productData.operationExhibitionId)}.prod-${(props.productData || {}).id}_text-${text}.${scmEShopDetail}`,
            "product_id": (props.productData || {}).id,
            "product_name":(props.productData || {}).showName,
            "qt_list_data": props.qtData.qtListData,
            'qt_sku_data': (props.productData || {}).qtSkuData
          }
          console.log(window.fwData,"window.fwData")
          aplus_queue.push({
                    'action': 'aplus.record',
                    'arguments': ['action_product_button_click', 'EXP',window.fwData]
              });
          aplus_queue.push({
                    'action': 'aplus.record',
                    'arguments': ['action_list_product_click', 'CLK', {
                      "spm_cnt":`<EMAIL>@${qtSkuData.rank}_prod@${qtSkuData.rank}-${qtSkuData.operation_rank}.${window.parent.getSpmE()}`,
                      "scm_cnt":`search.${props.searchSortStrategyId||'0'}.${props.productData.operationCustomerGroupId||"all"}_${getStr(props.productData.operationExhibitionId)}.prod-${(props.productData || {}).id}.${scmEShopDetail}`,
                      "product_id": (props.productData || {}).id,
                      "product_name":(props.productData || {}).showName,
                      "qt_list_data": props.qtData.qtListData,
                      'qt_sku_data': (props.productData || {}).qtSkuData
                    }]
        });
       } catch (error) {
        
       }
   }else{
    window.fwData = {
      "spm_cnt": `1_4.searchResult_0-0_0.${props.qtData.goodsType == 3 ? 'recommendList' : 'searchList'}@5.prod@${qtData.value.index + 1}_btn@${index}.${window.parent.getSpmE()}`,
      "scm_cnt": `${props.qtData.goodsType == 3 ? 'recommend' : 'search'}.${props.searchSortStrategyId||'0'}.all_0.prod-${(props.productData || {}).id}_text-${text}.${scmEShopDetail}`,
      // "result_cnt": qtData.value.result_cnt,
      "product_id": (props.productData || {}).id,
      "product_name": (props.productData || {}).showName,
      "qt_list_data": props.qtData.qtListData,
      'qt_sku_data': (props.productData || {}).qtSkuData

    }
    aplus_queue.push({
      'action': 'aplus.record',
      'arguments': ['action_product_button_click', 'CLK', window.fwData]
    });
    aplus_queue.push({
      'action': 'aplus.record',
      'arguments': ['action_list_product_click', 'CLK', {
        "spm_cnt": `1_4.searchResult_0-0_0.${props.qtData.goodsType == 3 ? 'recommendList' : 'searchList'}@5.prod@${qtData.value.index+1}.${window.parent.getSpmE()}`,
        "scm_cnt": `${props.qtData.goodsType == 3 ? 'recommend' : 'search'}.${props.searchSortStrategyId||'0'}.all_0.prod-${(props.productData || {}).id}.${scmEShopDetail}`,
        // "result_cnt": qtData.value.result_cnt,
        "product_id": (props.productData || {}).id,
        "product_name": (props.productData || {}).showName,
        "qt_list_data": props.qtData.qtListData,
        'qt_sku_data': (props.productData || {}).qtSkuData
      }]
    });
   }
  }
} 

</script>

<style lang="scss" scoped>
div {
  box-sizing: border-box;
}

.btn-container {
  .addPurchaseOrder {
    cursor: pointer;
    width: 100px;
    height: 42px;
    margin-left: 10px;
    background-image: linear-gradient(90deg, #FEA527 0%, #FE5427 100%);
    border-radius: 2px;
    font-size: 16px;
    font-family: MicrosoftYaHei;
    text-align: center;
    color: #ffffff;
    line-height: 42px;
    flex-grow: 1;
  }
  .addPurchaseOrder-pgby-box {
    flex-grow: 1;
    display: flex;
    .addPurchaseOrder-addCar {
      width: 40px;
      height: 42px;
      background: #FFFFFF;
      border-radius: 6px;
      margin-left: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #e9e9e9;
      img {
        width: 24px;
        height: 24px;
      }
    }
    .addPurchaseOrder-pgby {
      flex-grow: 1;
      flex-shrink: 0;
      cursor: pointer;
      height: 42px;
      margin-left: 8px;
      background-color:#FF6204;
      border-radius: 6px;
      font-size: 16px;
      font-family: MicrosoftYaHei;
      text-align: center;
      color: #ffffff;
      line-height: 42px;
      padding: 0 7px;
      box-sizing: border-box;
      
    }
  }
  .pgbyBtn {
    background-color:#FF6204;
  }
  .soudOutBtn {
    background: #838A93;
  }
}

.content {
  display: flex;
  align-items: center;
}

.spread-add {
  display: flex;
  display: -webkit-flex;
  flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #d7d7d7;
  overflow: hidden;
  .plus,
  .reduce {
    // width: 32px;
    width: 22px;
    height: 42px;
    line-height: 42px;
    color: #757575;
    text-align: center;
    background: #EFEFEF;
    cursor: pointer;
    font-size: 20px;
  }

  .plus {
    border-left: 1px solid #d7d7d7;
    // border-radius: 0px 2px 2px 0px;
  }

  .reduce {
    border-right: 1px solid #d7d7d7;
    // border-radius: 2px 0px 0px 2px;
  }

  .cont {
    width: 40px;
    .input-val {
      box-sizing: border-box;
      padding: 0;
      width: 100%;
      text-align: center;
      color: #292933;
      border: none;
      outline: none;
      font-size: 16px;
      font-family: MicrosoftYaHei;
      text-align: left;
      color: #333333;
      line-height: 42px;
      text-align: center;
    }
  }
}
</style>
