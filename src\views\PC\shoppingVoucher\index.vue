<template>
<div class="my">
    <div class="myShoppingGlodTitle">
        <div style="display: flex;align-items: center;font-weight: 600;">
            <span></span>
            我的购物金
        </div>
        <div>
            <div class="useDirection" @click="openExplainDialog">
                <el-icon><QuestionFilled /></el-icon>
                使用说明
            </div>
        </div>
    </div>
    <div class="myMerchant-content">
        <div class="balance">
            <span style="font-size: 14px;">账户余额:</span>
            <h4 class="amountStyle" style="display: flex;">
                <div>
                    <p><span>¥</span>{{ balance }}</p>
                </div>
            </h4>
        </div>
        <div style="width: 235px;text-align: right;">
            <div class="recharge" @click="handleGoRecharge" style="display: inline-block;">
              在线充值
            </div>
            <div class="rechargeTitle" v-if="redPacketAmount">
              <div class="triangle-up"></div>
              <div class="promotion-tip">
                <i class="promotion-icon"></i>
                <span class="promotion-text">{{ rechargeDiscount }}<span style="color: #F70015">{{ redPacketAmount }}</span></span>
              </div>
            </div> 
        </div>
    </div>
    <shoppingVoucherDetails></shoppingVoucherDetails>
</div>
<shoppingMoneyExplain v-model="showExplainDialog"/>
</template>

<script setup>
import { ref,defineComponent,getCurrentInstance } from 'vue'
import shoppingMoneyExplain from './components/shoppingMoneyExplain.vue';
import shoppingVoucherDetails from './components/shoppingVoucherDetails.vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { getGoldBalance,getRechargeDiscount } from '@/http_pc/shoppingGold/index.js';
const router = useRouter();
defineComponent({
    shoppingMoneyExplain,
    shoppingVoucherDetails 
})
// 余额
const balance = ref(0);
// 使用说明
const showExplainDialog = ref(false);
// 购物金充值文案
const rechargeDiscount = ref('')
// 反红包金额
const redPacketAmount = ref('')
const { proxy } = getCurrentInstance();
const openExplainDialog = () => {
    showExplainDialog.value = true; 
}
const handleGoRecharge = () => {
    router.push({
        path: '/shoppingVoucher/onlineRecharge',
    });
}
// 获取余额
const getBalance = () => {
    getGoldBalance().then((res) => {
        if (res.code === 1000) {
            balance.value = res.data.availableVirtualGold;
        }else {
            ElMessage.error(res.errorMsg || res.msg || "获取余额失败")
        }
    }).catch((err) => {
        ElMessage.error("获取余额失败")
    })
}
getBalance();
const merchantId = proxy.$merchantId;
const initRechargeDiscount = () => {
    getRechargeDiscount({channel: 0}).then(res=>{
        if(res.code === 1000) {
            rechargeDiscount.value = res.data.data.highLevelRedPacketMsgUpperHalf;
            redPacketAmount.value = res.data.data.highLevelRedPacketMsgLowerHalf;
        }
    })
}
initRechargeDiscount()
</script>

<style lang='scss' scoped>
.my {
 height: 800px;
  background: #fff;
  width: 980px;
  margin: auto;
  padding: 20px 20px 0 20px;
  box-sizing: border-box;
  .myShoppingGlodTitle {
        span {
            width: 4px;
            height: 16px;
            display: inline-block;
            background: #00B377;
            margin-right: 10px;
        }
        font-weight: 600;
        font-size: 18px;
        color: #292933;
        letter-spacing: 0;
        line-height: 20px;
        display: flex;
        justify-content: space-between;
        .useDirection {
            cursor: pointer;
            color: #333333;
            font-weight: 400;
            font-size: 14px;
            display: flex;
            align-items: center;
            .el-icon {
                margin-right: 4px;
            }
        }
  }
  .myMerchant-content {
    margin-top: 20px;
    width: 100%;
    border: 1px solid #EEEEEE;
    border-radius: 2px;
    display: flex;
    justify-content: space-between;
    align-items: start;
    padding: 18px 20px;
    box-sizing: border-box;
    .balance {
        text-align: left;
        font-size: 12px;
        color: #292933;
        letter-spacing: 0;
        line-height: 16px;
        h4 {
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            padding-top: 4px;
        }
        .amountStyle {
            box-sizing: border-box;
            justify-content: space-between;
            p {
                font-size: 32px;
                font-face: MiSans;
                font-weight: 600;
                line-height: 30px;
                letter-spacing: 0;
                paragraph-spacing: 0;
                span {
                    font-size: 14px;
                }
            }
        }
    }
    .recharge, .account {
        box-sizing: border-box;;
        width: 108px;
        height: 36px;
        background: #00B955;
        border-radius: 5px;
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 36px;
        text-align: center;
        cursor: pointer;
        margin-top: 10px;
    }
    .rechargeTitle {
        margin-top: 10px;
        max-width: 235px;
        display: inline-block;
        text-align: left;
        position: relative;
        
        .triangle-up {
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-bottom: 6px solid #FFF2F2;
            position: absolute;
            top: -6px;
            left: 133px; /* 调整位置，使其对准"转出到购物金"按钮 */
        }
        
        .promotion-tip {
            padding: 4px 10px;
            background-color: #FFEEEF;
            border-radius: 4px;
            display: flex;
            align-items: center;
        
            .promotion-icon {
                display: inline-block;
                width: 14px;
                height: 14px;
                background: url('@/assets/images/red-packet.png') no-repeat;
                background-size: contain;
                margin-right: 6px;
            }
            
            .promotion-text {
                font-size: 14px;
                line-height: 1.5;
                white-space: nowrap;
            }
        }
    }
  }
}
</style>