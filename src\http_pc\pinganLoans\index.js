import http from '../index';

// 我的平安货
export const getLoans = (params) => {
	return http.post('/pc/pinganLoan/my_loan', params);
}
// 我的平安货明细
export const getLoansDetail = (params) => {
	return http.post('/pc/pinganLoan/loan_detail', params);
}
//查询平安ePay可用余额
export const getPingAnCreditBalance = (params) => {
	return http.post('/app/pinganaccount/queryPingAnCreditBalance?merchantId='+params.merchantId, params);
}
//下载对账单
export const downLoadLoan = (params) => {
	return http.post('/pc/pinganLoan/export?billCode='+params.billCode+'&merchantId='+params.merchantId, {}, { responseType: 'blob' }, 'exportExcel');
}
//获取订单id
export const getOrderId = (params) => {
	return http.post('/merchant/center/order/queryid?orderNo='+params.ybm, {});
}
// 获取退款单id
export const getRefundId = (params) => {
	return http.post('/merchant/center/order/queryRefundId?refundNo='+params.ybm, params);
}