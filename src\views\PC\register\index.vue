<template>
  <div class="registerWrapper">
    <RegisterHeader title="注册" :isLogin="false" />
    <div class="registerBox">
      <div class="formBox">
        <el-form ref="ruleFormRef" :model="registerInfo" :rules="rules" label-width="120px" style="margin: 0 auto;max-width: 650px;">
          <el-form-item label="联系人姓名" prop="contactName">
            <el-input v-model.trim="registerInfo.contactName" placeholder="请输入联系人姓名，字数在8个汉字以内" style="width: 300px" />
          </el-form-item>
          <el-form-item label="手机号码" prop="mobile">
            <el-input v-model="registerInfo.mobile" placeholder="请输入您的手机号码" style="width: 300px" />
          </el-form-item>
          <el-form-item label="验证码" prop="photoCode">
            <el-input v-model="registerInfo.photoCode" placeholder="请输入您的验证码" style="width: 300px" />
            <div @click="refreshRandom()">
              <img alt="" :src="photoCodeSrc" class="photoCodeImg" />
            </div>
            <span style="color: #1713C0;" @click="refreshRandom()">看不清楚？换张图片</span>
          </el-form-item>
          <el-form-item label="短信验证码" prop="code">
            <el-input v-model="registerInfo.code" placeholder="请输入短信验证码" style="width: 300px">
              <template #append>
                <el-button v-if="showCount">重新发送({{ countdownSeconds }})</el-button>
                <el-button v-else @click="showCountdown">获取短信验证码</el-button>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="设置密码" prop="password">
            <el-input v-model="registerInfo.password" placeholder="请设置密码" style="width: 300px" type="password" :maxlength="16" />
          </el-form-item>
          <div style="color: #999;">密码必须8-16个字符，至少含数字、字母、符号两种组合，首位只能为字母！</div>
          <el-form-item label="" style="marginTop: 200px;">
            <el-checkbox v-model="isAgree" label="" @click="agreement"/> 
            同意<span class="activeText" @click="toCheckAgreement(1)">《用户服务协议》</span>和<span class="activeText" @click="toCheckAgreement(2)">《隐私政策》</span>
          </el-form-item>
        </el-form>
        <el-button type="primary" class="registerBtn" @click="toRegister(ruleFormRef)">注册</el-button>
      </div>
    </div>
    <RegisterFooter />
  </div>
</template>
<script setup>
  import { onMounted, ref } from "vue";
  import { useRouter } from 'vue-router';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { checkPhoCode, sendRegisterVerificationCode, register, logger, getAgreement } from '@/http_pc/api';
  import RegisterFooter from './components/registerFooter.vue';
  import RegisterHeader from './components/registerHeader.vue';
  import { actionTracking } from '@/config/eventTracking';

  const ruleFormRef = ref(null);
  const router = useRouter();
  const isAgree = ref(false);
  const showCount = ref(false);
  const countdownSeconds = ref(60);
  const timer = ref(null);
  const photoCodeSrc = ref('/login/getCode.htm?merchantId=0');
  const registerInfo = ref({
    contactName:'',
    mobile: '',
    password: '',
    code: '',
    photoCode: '',
  })
  const agreementInfo = ref({
    agreements: [],
    mobile: '',
    checkTime: '',
    operateType: 1
  })
  const agreementUrl = ref({
    userService: '/login/agreement.htm',
    private: '/helpCenter/privacy.htm'
  })
  const rules = {
    mobile: [
      { required: true,  message: '请填写', trigger: 'blur' },
      { validator: (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请填写'))
        } else if (!/^1[345789]\d{9}$/g.test(value)) {
          callback(new Error('请输入正确的手机号'))
        } else {
          callback();
        }
      }, trigger: 'blur' }
    ],
    password: [
      { required: true,  message: '请填写', trigger: 'blur' },
      { min: 8, max: 16, message: '长度限制为8至16个字符', trigger: 'change' },
      // { validator: (rule, value, callback) => {
      //   if (value === '') {
      //     callback(new Error('请填写'))
      //   // } else if (!/^[a-zA-Z][a-zA-Z0-9$,@%_]{8,16}$/g.test(value)) {
      //   } else if (!/^[a-zA-Z]\w{7,15}$/g.test(value)) {
      //     callback(new Error('不符合规则，请检查'))
      //   } else {
      //     callback();
      //   }
      // }, trigger: 'blur' }
    ],
    photoCode: [
      { required: true,  message: '请填写', trigger: 'blur' },
    ],
    code: [
      { required: true,  message: '请填写', trigger: 'blur' },
    ],
    contactName: [
      { required: true,  message: '请填写', trigger: 'blur' },
      {
        pattern: /^[\u4e00-\u9fa5]{2,8}$/,
        message: '姓名不能包含特殊符号，且字数在2至8个汉字之间',
      },
    ]
  }

  getAgreement().then(res => {
    if (res.status === 'success') {
      agreementInfo.value.agreements.push({
        agreementId: res.data.userServiceAgreement.id,
        agreementVersion: res.data.userServiceAgreement.version,
      })
      agreementInfo.value.agreements.push({
        agreementId: res.data.privacyPolicy.id,
        agreementVersion: res.data.privacyPolicy.version,
      })
      agreementUrl.value.userService = res.data.userServiceAgreement.url ? res.data.userServiceAgreement.url : agreementUrl.value.userService;
      agreementUrl.value.private = res.data.privacyPolicy.url ? res.data.privacyPolicy.url : agreementUrl.value.private;
    }
  })
  const toCheckAgreement = (type) => {
    window.open({1: agreementUrl.value.userService, 2: agreementUrl.value.private}[type])
  }
  const agreement = () => {
    if (!isAgree.value) {
      agreementInfo.value.checkTime = Date.now();
    }
  }

  // 刷新验证码
  const refreshRandom = () => {
    photoCodeSrc.value = `/login/getCode.htm?merchantId=${registerInfo.value.mobile}&serial=${(new Date()).getTime()}`
  }

  // 获取验证码，展示倒计时
  const showCountdown = () => {
    if (!registerInfo.value.mobile) {
      ElMessage.error('请先填写手机号码');
      return;
    }
    checkPhoCode({
      photoCode: registerInfo.value.photoCode,
      mobile: registerInfo.value.mobile,
    }).then((res) => {
      if(res.status) {
        // 调用发送验证码接口
        sendRegisterVerificationCode({
          mobileNumber: registerInfo.value.mobile,
          code: registerInfo.value.photoCode,
        }).then((data) => {
          if (data.status === "success") {
            showCount.value = true;
            timer.value = setInterval(() => {
              if (countdownSeconds.value > 0) {
                countdownSeconds.value--;
              } else {
                clearInterval(timer.value);
                showCount.value = false;
                countdownSeconds.value = 60;
              }
            }, 1000);
            ElMessage.success(data.msg);
          } else {
            ElMessage.error(data.errorMsg);
          }
        })
      } else {
        ElMessage.error(res.msg || '验证码有误');
      }
    })
  }

  const toRegister = async(formEl) => {
    actionTracking('pc_action_register_registerBtn_click', { 
      phone: registerInfo.value.mobile,
    });
    if (!formEl) return
    console.log(registerInfo.contactName);
    debugger
    if(!/^[\u4e00-\u9fa5]{2,8}$/.test(registerInfo.value.contactName)){
      return ElMessage.error("姓名不能包含特殊符号，且字数在2至8个汉字之间");
    }
    await formEl.validate((valid, fields) => {
      if (valid) {
        if (!isAgree.value) {
          ElMessageBox.alert('请阅读并同意服务条款', '注意', {
            confirmButtonText: '我知道了',
            type: 'warning'
          })
        } else {
          register({
            contactName:registerInfo.value.contactName,
            mobile: registerInfo.value.mobile,
            password: registerInfo.value.password,
            code: registerInfo.value.code,
            photoCode: registerInfo.value.photoCode,
          }).then((res) => {
            if (res.status === 'success') {
              agreementInfo.value.mobile = registerInfo.value.mobile;
              logger(agreementInfo.value);
              router.push('/register/connectPharmacy');
            } else {
              ElMessage.error(res.errorMsg);
            }
          })
        }
      }
    })
  }

</script>
<style lang="scss" scoped>
.registerWrapper {
  background: #F8F8F8;
  width: 100%;
  min-height: 100vh;
  .activeText {
    color: #00B377;
  }
  .registerBox {
    margin: 0 auto;
    width: 1200px;
    // height: 615px;
    background: #fff;
    padding: 70px 0 100px 0;
    .registerBtn {
      width: 300px;
      margin-left: 385px;
    }
    .photoCodeImg {
      width: 100px;
      height: 30px;
    }
  }
}
.el-checkbox {
  margin-right: 10px !important;
}
.el-form-item {
  margin-bottom: 28px;
}
</style>