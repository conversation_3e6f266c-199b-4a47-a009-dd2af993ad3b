<template>
    <div class="quality-notice-detail">
        <div class="quality-detail-title">
            <p>{{ detailInfo.noticeTitle }}</p>
            <span>{{ formatDate(detailInfo.updateTime, 'yyyy-MM-dd') }}</span>
        </div>
        <div class="demo-image__preview">
          <el-image-viewer hide-on-click-modal @close="closePreview" v-if="showViewer" :url-list="previewList" />
        </div>
        <div @click="getImg($event)">
            <div v-html="detailInfo.noticeText"></div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, reactive, ref } from "vue";
import { useRoute } from 'vue-router';
import { getQualityNoticeDetail } from '@/http/qualityNotice';
import { formatDate } from '@/utils/index';

const route = useRoute();
const queryId = route?.query?.id;

let detailInfo = ref({});
const getQualityDetail = () => {
    getQualityNoticeDetail({ noticeId: queryId }).then((res) => {
        if (res.code == 1000) {
            detailInfo.value = res.data;
        }
    })
};


const showViewer = ref(false);
const previewList = ref([]);
const getImg = ($event) => {
    console.log($event, '???')    //当前点击的图片的路径    
    if (!$event.target.children[0].currentSrc) {
        return
    }
    previewList.value = [$event.target.children[0].currentSrc];
    showViewer.value = true;
    stop();
}
const closePreview = () => {
    showViewer.value = false;
    move();
}


// 记录页面滚动位置
  const pageLocation = ref('');
 
// 禁止滚动-在显示遮罩层的时候调用
  function stop(e) {
    let scrollTop = window.scrollY;//滚动的高度；
    pageLocation.value = scrollTop;
    document.body.style.position = 'fixed';
    document.body.style.top = '-' + scrollTop + 'px';
  };
 
  // 取消滑动限制-在关闭遮罩层的时候调用
  function move() {
    document.body.style.position = 'static';
    window.scrollTo(0, pageLocation.value);
  }


onMounted(function (){
    queryId && getQualityDetail();
});

</script>

<style lang="scss" scoped>
.quality-notice-detail {
    background: #fff;
    padding: 10px 30px;
    box-sizing: border-box;
    .quality-detail-title {
        display: flex;
        flex-direction: column;
        p {
            width: 100%;
            font-size: 16px;
            font-weight: bold;
            line-height: 26px;
            text-align: center;
        }
        span {
            font-size: 12px;
            margin: 20px 0;
            color: #ccc;
            text-align: right;
        }
    }
    
}   
</style>

<style lang="scss">
.quality-notice-detail {
    .el-image-viewer__mask {
        opacity: .7;
    }
}
</style>