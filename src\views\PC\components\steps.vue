<template>
    <div class="steps">
        <div class="steps-item" :class="{'step-active': step === index + 1}" v-for="(item, index) in stepData" :key="index">
            <div class="step-img">
                <!-- <img v-if="(step === index + 1 && step !== 2) || (step === 2 && index === 1 && stepStatus)" src="@/assets/images/pinganMerchant/step-know.png" alt=""> -->
                <div class="now" v-if="(step === index + 1 && step !== 2) || (step === 2 && index === 1 && stepStatus)">{{index + 1}}</div>
                <img style="width: 40px;height: 40px;" v-else-if="step > index + 1" src="@/assets/images/pinganMerchant/step-successv2.png" alt="">
                <img style="width: 40px;height: 40px;" v-else-if="step == 2 && index === 1 && !stepStatus" src="@/assets/images/pinganMerchant/step-errorv2.png" alt="">
                <div class="willnow " v-else>{{index + 1}}</div>
                <!-- <img v-else src="@/assets/images/pinganMerchant/step.png" alt=""> -->
            </div>
            <span v-if="step == 2 && index === 1 && !stepStatus">银行卡审核失败</span>
            <span v-else>{{item.title}}</span>
        </div>
        <div class="step-line"></div>
    </div>
</template>

<script setup>
import { reactive } from "vue";

const props = defineProps({
    step: {
      type: Number,
      default: 1
    },
    stepStatus: {
      type: Boolean,
      default: true
    }
});
let stepData = reactive([
  {title: '提交申请'},
  {title: '银行审批'},
  {title: '打款验证'},
])
console.log(props.step, '??')
</script>

<style lang="scss" scoped>
.steps {
    .willnow{
        width: 40px;
        height: 40px;
        background: #FFFFFF;
        border: 2px solid #E6E6E6;
        text-align: center;
        line-height: 40px;
        color: #666666;
        border-radius: 50%;
        font-size: 16px;
    }
    .now{
        width: 40px;
        height: 40px;
        text-align: center;
        line-height: 40px;
        border-radius: 50%;
        background: #00B955;
        color: white;
        font-size: 16px;
    }
    display: flex;
    // padding: 20px;
    box-sizing: border-box;
    position: relative;
    justify-content: center;
    background: #FFFFFF;
    .step-line {
        width: 62%;
        height: 0px;
        border:1px dashed #E6E6E6;
        // background: #E6E6E6;
        position: absolute;
        top: 35%;
        z-index: 1;
    }
    .steps-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 2;
        .step-img {
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            img {
                width: 44px;
                height: 44px;
            }
        }
        span {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #9494A6;
        }
    }
    .step-active {
        .step-img {
            width: 44px;
            height: 44px;
            img {
                width: 44px;
                height: 44px;
            }
        }
        span {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #292933;
        }
    }
}
</style>