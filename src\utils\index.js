import Bridge from "@/config/Bridge";
import ShareIcon from "@/assets/images/logo.png";
import { actionTracking } from '@/config/eventTracking';

// 防抖
export function debounce(fn, wait) {
  let timeout = null;
  return function() {
    if(timeout !== null) clearTimeout(timeout);
    timeout = setTimeout(fn, wait);
  }
}
// 取消月份前导0
export function removeLeadingZeroes(month) {
  if (month[0] == "0") {
    return (month = month.slice(1));
  } else {
    return month;
  }
}
// 只适用于顶部导航栏的分享
export function sharePage(params) {
  const local_href = (params || {}).local_href || window.location.href;
  const shareTitle = (params || {}).shareTitle || '超值优惠 限时抢购';
  const shareDesc = (params || {}).shareDesc || "刚刚在【药帮忙】看到一些不错的商品，赶快来看看吧！";
  const shareIcon = (params || {}).shareIcon || ShareIcon;
  const obj = {
    shareTitle,
    shareDesc,
    shareUrl: `https://app-v4.ybm100.com/static/xyyvue/dist/#/launchYbmApp?jumpurl=${encodeURIComponent(
      local_href
    )}`,
    shareImageUrl: shareIcon.replace(/^\/\//, 'https://'),
  }
  Bridge.setToolBar("liveShare",JSON.stringify(obj));
}

export function getUrlParam(name) {
  const urlArr = window.location.href.split('?');
  if (urlArr.length < 2) {
    return '';
  }
  const tempArr = urlArr[1].split('&');
  for (let i = 0; i < tempArr.length; i++) {
    const item = tempArr[i].split('=');
    if (item[0].trim() === name) {
      let res = decodeURI(item[1]);
      if (res.includes('#/')) {
        res = res.substr(0, res.indexOf('#/'));
      }
      return res;
    }
  }
  const BASEURL_PC = import.meta.env.VITE_BASE_URL_PC;
  if (BASEURL_PC.indexOf(window.location.host) > -1 && document.getElementById(name)) {
    return document.getElementById(name).value
  }
  return '';
}

// 格式化时间
export function formatDate(date, fmt) {
  if(!date) return '';
  date = typeof date === "string" ? date.replace(/-/g,'/') : date;
  date = new Date(date);
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substring(4 - RegExp.$1.length));
  }
  let o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds()
  };
  for (let k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      let str = o[k] + '';
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : padLeftZero(str));
    }
  }
  return fmt;
};

function padLeftZero(str) {
  return ('00' + str).substring(str.length);
};

export function numFilterInt(value) {
  // 截取当前数据的整数部分
  let realVal = parseInt(value);
  return realVal;
};
export function numFilterFloat(value) {
  // 截取当前数据到小数点后两位
  let realValnew = value.toString().split(".")[1];
  if (!realValnew) {
    realValnew = 0;
  }
  return realValnew;
};

//获取cookie值    方法 
export function getCookieValue(name){   
  var name = escape(name);   
  //读cookie属性，这将返回文档的所有cookie   
  var allcookies = document.cookie;          
  //查找名为name的cookie的开始位置   
  name += "=";   
  var pos = allcookies.indexOf(name);       
  //如果找到了具有该名字的cookie，那么提取并使用它的值   
  if (pos != -1){                                             //如果pos值为-1则说明搜索"version="失败   
      var start = pos + name.length;                  //cookie值开始的位置   
      var end = allcookies.indexOf(";",start);        //从cookie值开始的位置起搜索第一个";"的位置,即cookie值结尾的位置   
      if (end == -1) end = allcookies.length;        //如果end值为-1说明cookie列表里只有一个cookie   
      var value = allcookies.substring(start,end);  //提取cookie的值   
      return unescape(value);                           //对它解码         
      }      
  else return "";                                             //搜索失败，返回空字符串   
}  

// 导出文件
export function exportExcel(res, ecxelName) {
  const blob = new Blob([res]); // 接受文档流
  if ('msSaveOrOpenBlob' in navigator) {
    // IE下的导出
    // eslint-disable-next-line no-useless-concat
    window.navigator.msSaveOrOpenBlob(blob, ecxelName || '导出文件' + '.xlsx'); // 设置导出的文件名
  } else {
    // 非ie下的导出
    const a = document.createElement('a');
    const url = window.URL.createObjectURL(blob);
    // eslint-disable-next-line no-useless-concat
    const filename = ecxelName || '导出文件' + '.xlsx'; // 设置导出的文件名
    console.log(filename);
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }
}

function isElementInViewport(el) {
  var rect = el.getBoundingClientRect();
  var width_st = rect.width,
    height_st = rect.height;
  var innerHeight = window.innerHeight,
    innerWidth = window.innerWidth;
  if ( rect.top <=0 && rect.height > innerHeight 
    || rect.left <= 0 && rect.width > innerWidth
  ) {
    return rect.left * rect.right <= 0
      || rect.top * rect.bottom <= 0
  }
  return (
      rect.height > 0 
    && rect.width > 0 
    && ( ( rect.top >= 0 && rect.top <= innerHeight - height_st )
      || ( rect.bottom >= height_st && rect.bottom <= innerHeight ) )
    && ( ( rect.left >= 0 && rect.left <= innerWidth - width_st )
      || ( rect.right >= width_st && rect.right <= innerWidth ) )
  );
}

// 漏出曝光埋点
export function exposureView(){
  let eles = document.querySelectorAll("[data-exposure]");
  eles = [...eles];
  eles.forEach((item) => {
    const attrs = item.getAttribute('data-exposure');
    if (attrs) {
      // hastrack-是否已上报
      const hastrack = item.getAttribute('hastrack');
      if (isElementInViewport(item)) {
        // 在可见区域内没有上报的才会上报
        if (hastrack === 'false' || !hastrack) {
          const child = eval("("+attrs+")");
          if (child.eventName == 'pc_page_ListPage_Exposure'||child.eventName =='pc_search_Active_Exposure' ) {
            actionTracking(child.eventName, {
              user_id: child.merchantId,
              commodityId: child.productId,
              commodityName: child.commonName,
              sptype: child.sptype || 4,
              spid: child.spid || `${String(new Date().getFullYear()).substring(2)}-${this.core.page_id}-${this.core.page_title}`,
              sid: child.sid,
              position: child.position || null,
              cate_position: child.cate_position || -1,
              cate_text: child.cate_text || '',
              search_sort_strategy_id:child.search_sort_strategy_id||''
            })
          }
          item.setAttribute('hastrack', true);
        }
      } else {
        item.setAttribute('hastrack', false);
      }
    }
  }) 
}


// 漏出曝光埋点
export function exposureAnalysysAgentView(){
  let eles = document.querySelectorAll("[data-analysysagent-exposure]");
  eles = [...eles];
  eles.forEach((item) => {
    const attrs = item.getAttribute('data-analysysagent-exposure');
    if (attrs) {
      // hastrack-是否已上报
      const hastrack = item.getAttribute('jg-hastrack') || false;
      if (isElementInViewport(item)) {
      // 在可见区域内没有上报的才会上报
        if (hastrack === 'false' || !hastrack) {
          const child = eval("("+attrs+")");
          if (child.eventName == 'page_list_product_exposure') {
            if (child.operation_id && child.operation_id != 'null') {
              child.operation_id = String(child.operation_id);
            }
            if (child.search_sort_strategy_id && child.search_sort_strategy_id == 'null') {
              child.search_sort_strategy_id = null;
            }
            delete child.eventName;
            if(!child.jgspid) {
              child.jgspid = sessionStorage.getItem("jgspid");
            }
            window.AnalysysAgent.track('page_list_product_exposure', child)
          }
          item.setAttribute('jg-hastrack', true);
        }
      } else {
        if (!hastrack) {
          item.setAttribute('jg-hastrack', false);
        }
      }
    }
  }) 
}