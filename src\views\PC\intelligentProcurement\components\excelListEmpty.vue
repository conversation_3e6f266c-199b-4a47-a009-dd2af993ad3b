<template>
  <div class="excel-list-empty">
    <div class="excel-list-list">
      <div class="excel-list-list-header">
        <div class="excel-list-list-header-item right-border">商品信息</div>
        <div class="excel-list-list-header-item right-border">规格</div>
        <div class="excel-list-list-header-item">生产厂家</div>
      </div>
      <div class="excel-list-list-content">
        <div
          v-for="(item, index) in props.excelList"
          :key="index + 1000"
          class="excel-list-list-row"
        >
          <!-- 通用名 -->
          <div
            class="excel-list-list-row-item right-border"
            :style="getItemStyle(item, 'excelCommonName', index)"
            @click="needEdit(item, 'excelCommonName', index)"
          >
            <div
              v-if="
                editRowIndex.key !== 'excelCommonName' ||
                editRowIndex.index !== index
              "
              class="excel-list-empty-list-text"
              :style="{
                color: dataList[index].excelCommonName ? '#696969' : '#FF5B5B',
              }"
            >
              {{
                dataList && dataList[index].excelCommonName
                  ? dataList[index].excelCommonName
                  : "请填写通用名"
              }}
            </div>
            <div v-else>
              <el-input
                v-model="dataList[index].excelCommonName"
                style="width: 250px"
                @blur="inputBlur"
              ></el-input>
            </div>
          </div>
          <!-- 规格 -->
          <div
            class="excel-list-list-row-item right-border"
            :style="getItemStyle(item, 'excelSpec', index)"
            @click="needEdit(item, 'excelSpec', index)"
          >
            <div
              v-if="
                editRowIndex.key !== 'excelSpec' || editRowIndex.index !== index
              "
              class="excel-list-empty-list-text"
              :style="{
                color: dataList[index].excelSpec ? '#696969' : '#FF5B5B',
              }"
            >
              {{
                dataList[index].excelSpec
                  ? dataList[index].excelSpec
                  : "请填写规格"
              }}
            </div>
            <div v-else>
              <el-input
                v-model="dataList[index].excelSpec"
                style="width: 250px"
                @blur="inputBlur"
              ></el-input>
            </div>
          </div>

          <!-- 生产厂家 -->
          <div
            class="excel-list-list-row-item"
            :style="getItemStyle(item, 'excelManufacturer', index)"
            @click="needEdit(item, 'excelManufacturer', index)"
          >
            <div
              v-if="
                editRowIndex.key !== 'excelManufacturer' ||
                editRowIndex.index !== index
              "
              class="excel-list-empty-list-text"
              :style="{
                color: dataList[index].excelManufacturer
                  ? '#696969'
                  : '#FF5B5B',
              }"
            >
              {{
                dataList[index].excelManufacturer
                  ? dataList[index].excelManufacturer
                  : "请填写生产厂家"
              }}
            </div>
            <div v-else>
              <el-input
                v-model="dataList[index].excelManufacturer"
                style="width: 250px"
                @blur="inputBlur"
              ></el-input>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="excel-list-bottom">
      <el-button @click="cancelFunction()">全部忽略</el-button>
      <el-button type="primary" @click="saveFunction()"
        >保存并重新开始匹价</el-button
      >
    </div>
    <el-dialog v-model="colDialog" :title="'温馨提示'" width="600px">
      <div>还有商品缺少关键字信息，是否继续匹配？</div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="colDialog = false">继续填写</el-button>
          <el-button type="primary" @click="autoFunction()">匹配</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import { cloneDeep } from "lodash";
import { ElMessage, ElMessageBox } from "element-plus";
import { matchPricedDtailUpdate } from "@/http_pc/api";

const props = defineProps({
  excelList: {
    default: [],
  },
});
const colDialog = ref(false);
const saveType = ref("");
const emit = defineEmits(["refresh"]);

const editRowIndex = ref({ key: "", index: "" });

const dataList = ref(cloneDeep(props.excelList));

watch(
  () => props.excelList,
  (val) => {
    if (val) {
      dataList.value = cloneDeep(props.excelList);
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

const needEdit = (val, key, index) => {
  if (!val[key]) {
    editRowIndex.value = { key: key, index: index };
  }
};

const inputBlur = () => {
  editRowIndex.value = { key: "", index: "" };
};

const autoFunction = () => {
  if (saveType.value === "1") {
    apimatchPricedDtailUpdate();
    saveType.value = "";
    return;
  }
  emit("refresh");
};

const getItemStyle = (val, key, index) => {
  let style = "";
  if (val[key] !== undefined) {
    style += "background: #FFFFFF;";
  } else {
    style += "background: #FFFCF0;";
  }

  if (editRowIndex.value.key === key && editRowIndex.value.index === index) {
    style += "border: 1px solid #00B955;";
  } else {
    style += "border-bottom: 1px solid #EEEEEE;";
  }
  return style;
};

const cancelFunction = () => {
  colDialog.value = true;
};

const saveFunction = () => {
  let findItem = dataList.value.find((item) => {
    if (!item.excelCommonName || !item.excelSpec || !item.excelManufacturer) {
      return true;
    }
  });

  if (findItem) {
    colDialog.value = true;
    saveType.value = "1";
    return;
  }

  apimatchPricedDtailUpdate();
};

const apimatchPricedDtailUpdate = () => {
  matchPricedDtailUpdate(dataList.value).then((res) => {
    if (res.code == 0) {
      emit("refresh");
    } else {
      ElMessage.error(res.msg);
    }
  });
};
</script>

<style scoped>
.excel-list-empty {
  background-color: #ffffff;
  padding: 20px;
  margin-top: 10px;
  border: 1px solid #e0e0e0;
}

.excel-list-list {
  border: 1px solid #e0e0e0;
}

.excel-list-list-header {
  display: flex;
  flex-direction: row;
}

.excel-list-list-content {
  height: 480px;
  overflow-y: auto;
  /* border: 1px solid #eeeeee; */
}

.excel-list-list-header-item {
  flex: 1;
  height: 38px;
  border-bottom: 1px solid #eeeeee;
  background: #f9f9f9;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 10px;
  font-weight: 400;
  font-size: 12px;
  color: #666666;
}

.empty-row {
  background: #fffcf0;
  border: 1px solid #00b955;
}

.excel-list-empty-list-text {
  font-weight: 400;
  font-size: 14px;
  color: #696969;
}

.excel-list-list-row {
  display: flex;
  flex-direction: row;
}

.excel-list-list-row-item {
  flex: 1;
  height: 46px;
  background: #ffffff;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 10px;
  font-weight: 400;
  font-size: 14px;
  color: #696969;
}

.excel-list-bottom {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  height: 40px;
}

.right-border {
  border-right: 1px solid #eeeeee;
}
</style>