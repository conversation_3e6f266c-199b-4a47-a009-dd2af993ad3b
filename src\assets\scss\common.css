/* No CSS *//*# sourceMappingURL=common.css.map */
a, button, input, textarea, div {
  -webkit-tap-highlight-color: transparent;
}

div, section {
  /* -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; */
}

body {
  margin-left: auto;
  margin-right: auto;
}

a {
  text-decoration: none;
}

.loaing {
  font-size: 14px;
  line-height: 1.2rem;
  color: #666;
  text-align: center;
}

.bottom-tip {
  width: 100%;
  height: 1.06667rem;
  line-height: 1.06667rem;
  text-align: center;
  color: #777;
  background: #F7F7F8;
}

/*- scrollbar -*/
::-webkit-scrollbar {
  width: 0.06667rem;
  height: 0.06667rem;
}

::-webkit-scrollbar-thumb {
  background-color: #999;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:vertical:hover {
  background-color: #666;
}

::-webkit-scrollbar-thumb:vertical:active {
  background-color: #333;
}

::-webkit-scrollbar-button {
  display: none;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

html {
  overflow-x: hidden;
  font-size: 16px;
}

/*reset*/
html,body,div,ul,li,ol,dl,dd,dt,p,h1,h2,h3,h4,h5,h6,legend,img,input,a,span,b,strong,i,em{
	margin:0;
  padding:0;
  font-family: 'PingFang SC','Arial','Heiti SC','Microsoft YaHei UI';
	-webkit-font-smoothing: antialiased;
	-webkit-tap-highlight-color: rgba(255, 255, 255, 0);
  -webkit-touch-callout: none;
  -webkit-text-size-adjust: none;
  -moz-text-size-adjust: none;
  -ms-text-size-adjust: none;
  text-size-adjust: none;
  -webkit-appearance:none;
  font-weight:normal;
  -webkit-overflow-scrolling: touch;
}
img{
    width:100%;
    pointer-events:none;
}
a,u{
    text-decoration:none;
    -webkit-tap-highlight-color:transparent
}
ul,ol,li{
    list-style:none;
}
em,i{
    font-style:normal;
}
input{    
    background: -webkit-gradient(linear, 0 0, 0 100%, from(#fff), to(#fff));
    -webkit-appearance: none;
    outline: none;
    border-radius: 0;
}

.clearfixed { 
    clear: both; 
}
.clearfixed:after { 
    display: block; 
    clear: both; 
    visibility: hidden; 
    height: 0; 
    overflow: hidden; 
    content: "."; 
}
.textellipsis{
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
.textellipsis2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  /* autoprefixer: off */
  -webkit-box-orient: vertical;
  /* autoprefixer: on */
  -webkit-line-clamp: 2;
  word-break: break-all;
  word-wrap: break-word;
}
.posmiddle{
    position:absolute;
    left:0;
    top:0;
    right:0;
    bottom:0;
    margin:auto
}

.el-button--primary {
  /* background-color: #00B377 !important; */
}

/*# sourceMappingURL=common.css.map */