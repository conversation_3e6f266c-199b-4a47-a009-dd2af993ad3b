<template>
  <el-dialog
    v-model="showPostageVis"
    title="起送包邮金额提醒"
    width="500px"
		:lock-scroll="false"
    :before-close="handleClose"
  >
		<div v-if="unsatisfiedStartPriceList.length">
			<div style="color:#F39800">以下店铺加购商品不够起送门槛，不能提交订单</div>
			<div v-for="item in unsatisfiedStartPriceList" :key="item.name" class="shopBox">
				<div class="shopTop">
					<div>
						<span class="tag" v-if="item.isSelfCompany === 1">自营</span>
						<span class="name">{{ item.companyName }}</span>
					</div>
					<div class="btn" @click="handleClose">去凑单 <img src="../../../../assets/images/intelligentProcurement/jt.png" alt=""> </div>
				</div>
				<div class="shopBottom">
					{{ item.freightTips }}
					<span class="payMoney" v-if="item.freightPriceTag">{{ item.freightPriceTag }}</span>
				</div>
			</div>
		</div>
		<div v-if="unsatisfiedFreeShippingList.length" style="margin-top: 20px;">
			<div style="color:#F39800">以下店铺加购商品不够包邮门槛，需另付运费</div>
			<div v-for="item in unsatisfiedFreeShippingList" :key="item.name" class="shopBox">
				<div class="shopTop">
					<div>
						<span class="tag" v-if="item.isSelfCompany === 1">自营</span>
						<span class="name">{{ item.companyName }}</span>
					</div>
					<div class="btn" @click="handleClose">去凑单 <img src="../../../../assets/images/intelligentProcurement/jt.png" alt=""> </div>
				</div>
				<div class="shopBottom">
					{{ item.freightTips }}
					<span class="payMoney" v-if="item.freightPriceTag">{{ item.freightPriceTag }}</span>
				</div>
			</div>
		</div>
    <template #footer>
			<span class="dialog-footer">
        <el-button @click="handleClose">继续加购</el-button>
        <el-button v-if="unsatisfiedFreeShippingList.length" type="primary" @click="handleOk">暂不凑单，去结算</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
	showPostageVis: {
		default: false
	},
	unsatisfiedStartPriceList: {
		default: [],
	},
	unsatisfiedFreeShippingList: {
		default: [],
	},
	cancelDialog: {
		type: Function,
		default: () => {}
	},
	confirmSubmit: {
		type: Function,
		default: () => {}
	}
});
const showPostageVis = computed({
	get: () => props.showPostageVis,
	set: (value) => emit("cancelDialog", value),
})
// const showList = props.unsatisfiedStartPriceList.length ? ref(props.unsatisfiedStartPriceList) : ref(props.unsatisfiedFreeShippingList);

const handleClose = () => {
  props.cancelDialog();
}
const handleOk = () => {
	props.confirmSubmit();
}
</script>
<style scoped lang="scss">
.dialog-footer button:first-child {
  margin-right: 10px;
}
.shopBox {
	background: #F5F5F5;
	border-radius: 2px;
	padding: 10px;
	margin-top: 12px;
	.shopTop {
		display: flex;
		justify-content: space-between;
		align-items: center;
		.tag {
			padding: 2px 4px;
			text-align: center;
			background: #00B377;
			border-radius: 2px;
			color: #fff;
			font-size: 12px;
			margin-right: 6px;
		}
		.name {
			color: #222222;
			font-size: 16px;
			font-weight: bold;
		}
		.btn {
			color: #00B377;
			font-size: 12px;
			img {
				width: 5px;
				height: 8px;
				margin-left: 2px;
			}
		}
	}
	.shopBottom {
		color: #666666;
		margin-top: 8px;
		.redText {
			color: #E62E2E;
		}
		.payMoney {
			background: #FFF7F2;
			border: 1px solid #FF5B00;
			border-radius: 2px;
			padding: 2px 4px;
			color: #FF5B00;
			font-size: 12px;
			margin-left: 10px;
		}
	}
}
</style>
