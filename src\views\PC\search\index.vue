<template>
  <div class="wrapperBox">
    <div class="topBar">
      <el-cascader
        ref="categoryLevelRef"
        :options="categoryList"
        :props="props"
        v-model="selctedCategory"
        placeholder="全部分类"
        clearable
        style="width: 260px"
        @change="getCheckedNodes"
      />
      <div v-if="selectedCJ.length" class="selectedBox">
        <span class="triangle"></span>
        <span class="selectedItemBox">
          厂家：
          <el-tooltip
            :content="selectedCJ.join()"
            placement="bottom"
            :show-after="1000"
          >
            <span class="textellipsis selectedItem">{{
              selectedCJ.join()
            }}</span>
          </el-tooltip>
          <el-icon @click="clearCondition('selectedCJ')"><Close /></el-icon>
        </span>
      </div>
      <div v-if="selectedSJ.length" class="selectedBox">
        <span class="triangle"></span>
        <span class="selectedItemBox">
          商家：
          <el-tooltip
            :content="selectedSJ.join()"
            placement="bottom"
            :show-after="1000"
          >
            <span class="textellipsis selectedItem">{{
              selectedSJ.join()
            }}</span>
          </el-tooltip>
          <el-icon @click="clearCondition('selectedSJ')"><Close /></el-icon>
        </span>
      </div>
      <div v-if="selectedGG.length" class="selectedBox">
        <span class="triangle"></span>
        <span class="selectedItemBox">
          规格：
          <el-tooltip
            :content="selectedGG.join()"
            placement="bottom"
            :show-after="1000"
          >
            <span class="textellipsis selectedItem">{{
              selectedGG.join()
            }}</span>
          </el-tooltip>
          <el-icon @click="clearCondition('selectedGG')"><Close /></el-icon>
        </span>
      </div>
      <div v-if="selectedJYLX.length" class="selectedBox">
        <span class="triangle"></span>
        <span class="selectedItemBox">
          经营类型：
          <el-tooltip
            :content="selectedJYLX.join()"
            placement="bottom"
            :show-after="1000"
          >
            <span class="textellipsis selectedItem">{{
              selectedJYLX.join()
            }}</span>
          </el-tooltip>
          <el-icon @click="clearCondition('selectedJYLX')"><Close /></el-icon>
        </span>
      </div>
      <span class="triangle"></span>
      <span>关键词：{{ keyword }}</span>
      <span
        v-if="
          selectedCJ.length ||
          selectedSJ.length ||
          selectedGG.length ||
          selectedJYLX.length
        "
        class="clearBtn"
        @click="clearCondition('all')"
      >
        <el-icon><Delete /></el-icon>&nbsp;清空
      </span>
    </div>
    <div class="searchRow">
      <div class="flexBox" :style="{ height: showMoreObj.CJ ? '' : '40px' }">
        <div class="searchTitle">厂家：</div>
        <div class="searchContent">
          <div class="searchItem" v-if="showMoreObj.CJ">
            <el-input
              v-model="keywordCJ"
              :prefix-icon="Search"
              style="width: 200px"
              placeholder="搜索厂家名称"
              clearable
              @input="searchName('CJ', $event)"
            />
            <div class="singleBtn" v-if="!isSingleObj.CJ">
              <el-button size="small" @click="clearCheckbox('CJ')"
                >取消</el-button
              >
              <el-button
                type="primary"
                size="small"
                @click="confirmCheckbox('CJ')"
                :disabled="checkListCJ.length === 0"
                >确定</el-button
              >
            </div>
          </div>
          <div v-if="manufacturerArr.length">
            <div
              class="itemBox"
              v-if="isSingleObj.CJ"
              :style="{ overflow: showMoreObj.CJ ? 'scroll' : 'hidden' }"
            >
              <div
                v-for="item in manufacturerArr"
                :key="item.key"
                @click="changeCondition('selectedCJ', item, item.key)"
              >
                <el-tooltip
                  :content="item.key"
                  placement="bottom"
                  :show-after="1000"
                >
                  <span
                    class="itemText textellipsis"
                    :class="{ activeText: selectedCJ.includes(item.key) }"
                    >{{ item.key }}</span
                  >
                </el-tooltip>
              </div>
            </div>
            <div class="itemBox" v-else>
              <el-checkbox-group v-model="checkListCJ">
                <el-checkbox
                  v-for="item in manufacturerArr"
                  :key="item.key"
                  :label="item.key"
                >
                  <el-tooltip
                    :content="item.key"
                    placement="bottom"
                    :show-after="1000"
                    >{{ item.key }}</el-tooltip
                  >
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div v-else class="itemBox">
            <div>抱歉，未找到对应厂家</div>
          </div>
        </div>
        <div class="searchBtn">
          <el-button @click="showMore('CJ')" size="small">
            <span v-if="showMoreObj.CJ">
              收起 <el-icon><ArrowUp /></el-icon>
            </span>
            <span v-else>
              展开 <el-icon><ArrowDown /></el-icon>
            </span>
          </el-button>
          <el-button @click="changSingle('CJ')" size="small">
            <span v-if="!isSingleObj.CJ">
              <el-icon><Minus /></el-icon>&nbsp;单选
            </span>
            <span v-else>
              <el-icon><Plus /></el-icon>&nbsp;多选
            </span>
          </el-button>
        </div>
      </div>
    </div>
    <div class="searchRow" v-if="fromPage === 'searchIndex'">
      <div class="flexBox" :style="{ height: showMoreObj.SJ ? '' : '40px' }">
        <div class="searchTitle">商家：</div>
        <div class="searchContent">
          <div class="searchItem" v-if="showMoreObj.SJ">
            <el-input
              v-model="keywordSJ"
              :prefix-icon="Search"
              style="width: 200px"
              placeholder="搜索商家名称"
              clearable
              @input="searchName('SJ', $event)"
            />
            <div class="singleBtn" v-if="!isSingleObj.SJ">
              <el-button size="small" @click="clearCheckbox('SJ')"
                >取消</el-button
              >
              <el-button
                type="primary"
                size="small"
                @click="confirmCheckbox('SJ')"
                :disabled="checkListSJ.length === 0"
                >确定</el-button
              >
            </div>
          </div>
          <div v-if="shopArr.length">
            <div
              class="itemBox"
              v-if="isSingleObj.SJ"
              :style="{ overflow: showMoreObj.SJ ? 'scroll' : 'hidden' }"
            >
              <div
                v-for="item in shopArr"
                :key="item.key"
                @click="
                  changeCondition('selectedSJ', item, item.showName, item.key)
                "
              >
                <el-tooltip
                  :content="item.showName"
                  placement="bottom"
                  :show-after="1000"
                >
                  <span
                    class="itemText textellipsis"
                    :class="{ activeText: selectedSJ.includes(item.showName) }"
                    >{{ item.showName }}</span
                  >
                </el-tooltip>
              </div>
            </div>
            <div class="itemBox" v-else>
              <el-checkbox-group v-model="checkListSJ">
                <el-checkbox
                  v-for="item in shopArr"
                  :key="item.showName"
                  :label="item.key"
                >
                  <el-tooltip
                    :content="item.showName"
                    placement="bottom"
                    :show-after="1000"
                    >{{ item.showName }}</el-tooltip
                  >
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div v-else class="itemBox">
            <div>抱歉，未找到对应商家</div>
          </div>
        </div>
        <div class="searchBtn">
          <el-button @click="showMore('SJ')" size="small">
            <span v-if="showMoreObj.SJ">
              收起 <el-icon><ArrowUp /></el-icon>
            </span>
            <span v-else>
              展开 <el-icon><ArrowDown /></el-icon>
            </span>
          </el-button>
          <el-button @click="changSingle('SJ')" size="small">
            <span v-if="!isSingleObj.SJ">
              <el-icon><Minus /></el-icon>&nbsp;单选
            </span>
            <span v-else>
              <el-icon><Plus /></el-icon>&nbsp;多选
            </span>
          </el-button>
        </div>
      </div>
    </div>
    <div class="searchRow">
      <div class="flexBox" :style="{ height: showMoreObj.GG ? '' : '40px' }">
        <div class="searchTitle">规格：</div>
        <div class="searchContent">
          <div class="searchItem" v-if="showMoreObj.GG">
            <el-input
              v-model="keywordCJ"
              style="width: 200px; visibility: hidden"
            />
            <div class="singleBtn" v-if="!isSingleObj.GG">
              <el-button size="small" @click="clearCheckbox('GG')"
                >取消</el-button
              >
              <el-button
                type="primary"
                size="small"
                @click="confirmCheckbox('GG')"
                :disabled="checkListGG.length === 0"
                >确定</el-button
              >
            </div>
          </div>
          <div v-if="specStats.length">
            <div
              class="itemBox"
              v-if="isSingleObj.GG"
              :style="{ overflow: showMoreObj.GG ? 'scroll' : 'hidden' }"
            >
              <div
                v-for="item in specStats"
                :key="item.key"
                @click="changeCondition('selectedGG', item, item.key)"
              >
                <el-tooltip
                  :content="item.key"
                  placement="bottom"
                  :show-after="1000"
                >
                  <span
                    class="itemText textellipsis"
                    :class="{ activeText: selectedGG.includes(item.key) }"
                    >{{ item.key }}</span
                  >
                </el-tooltip>
              </div>
            </div>
            <el-checkbox-group v-else v-model="checkListGG">
              <el-checkbox
                v-for="item in specStats"
                :key="item.key"
                :label="item.key"
              >
                <el-tooltip
                  :content="item.key"
                  placement="bottom"
                  :show-after="1000"
                  >{{ item.key }}</el-tooltip
                >
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <div v-else class="itemBox">
            <div>抱歉，未找到对应规格</div>
          </div>
        </div>
        <div class="searchBtn">
          <el-button @click="showMore('GG')" size="small">
            <span v-if="showMoreObj.GG">
              收起 <el-icon><ArrowUp /></el-icon>
            </span>
            <span v-else>
              展开 <el-icon><ArrowDown /></el-icon>
            </span>
          </el-button>
          <el-button @click="changSingle('GG')" size="small">
            <span v-if="!isSingleObj.GG">
              <el-icon><Minus /></el-icon>&nbsp;单选
            </span>
            <span v-else>
              <el-icon><Plus /></el-icon>&nbsp;多选
            </span>
          </el-button>
        </div>
      </div>
    </div>
    <div class="searchRow" v-if="fromPage === 'searchIndex'">
      <div class="flexBox">
        <div class="searchTitle">经营类型：</div>
        <div class="searchContent">
          <div class="itemBox">
            <div
              v-for="item in drugClassification"
              :key="item.key"
              @click="
                changeCondition('selectedJYLX', item, item.label, item.key)
              "
            >
              <el-tooltip
                :content="item.label"
                placement="bottom"
                :show-after="1000"
              >
                <span
                  class="itemText textellipsis"
                  :class="{ activeText: selectedJYLX.includes(item.label) }"
                  >{{ item.label }}</span
                >
              </el-tooltip>
            </div>
          </div>
        </div>
        <div class="searchBtn"></div>
      </div>
    </div>
    <div class="tableTop">
      <div class="tableTop-left">
        <el-button-group class="ml-4 search-rows">
          <el-button
            :type="property === 'smsr.sale_num' ? 'primary' : 'default'"
            @click="changeProperty('smsr.sale_num', 'comprehensive')"
          >
            综合
          </el-button>
          <el-button
            :type="property === 'spa.sale_num' ? 'primary' : 'default'"
            @click="changeProperty('spa.sale_num', 'sale')"
          >
            销量
            <el-icon><Bottom /></el-icon>
          </el-button>
          <el-button
            :type="property === 'fob' ? 'primary' : 'default'"
            @click="changeProperty('fob', 'price')"
          >
            价格
            <el-icon><Top /></el-icon>
          </el-button>
        </el-button-group>
      </div>
      <div class="checkboxWrapper">
        <div class="row-line r-left"></div>
        <el-checkbox-group
          v-model="checkBoxStatus"
          @change="changeCheckBoxStatus"
          class="search-rows"
        >
          <el-checkbox
            v-for="item in checkBoxStatusArr"
            :key="item.key"
            :label="item.key"
            >{{ item.value }}</el-checkbox
          >
        </el-checkbox-group>
        <div class="row-line r-right"></div>
      </div>
      <div class="priceBox">
        <span style="width: 60px">价格区间</span>
        <div class="priceInput">
          <el-input
            size="small"
            v-model="minPrice"
            style="width: 50px; margin: 0 5px"
          />
          <span>—</span>
          <el-input
            size="small"
            v-model="maxPrice"
            style="width: 50px; margin: 0 5px"
          />
        </div>
        <div class="priceBtn">
          <el-button
            size="small"
            style="margin: 0 0 4px 4px"
            @click="clearPrice"
            >清空</el-button
          >
          <el-button
            type="primary"
            size="small"
            style="margin: 0 4px 4px 0"
            @click="confirmPrice"
            >确认</el-button
          >
        </div>
      </div>
      <div class="totalBox">
        <!-- 共&nbsp;<span style="color: #00B955;">{{ total }}</span>&nbsp;件商品 -->
        <div class="leftIcon pageIcon" @click="changePage('last')">
          <el-icon><ArrowLeft /></el-icon>
        </div>
        <span style="white-space: nowrap">{{ pageNum }} / {{ pages }}</span>
        <div class="rightIcon pageIcon" @click="changePage('next')">
          <el-icon><ArrowRight /></el-icon>
        </div>
      </div>
    </div>
    <div class="douDiText" v-if="goodsType === 3">抱歉，没有找到商品</div>
    <div v-if="list.length">
      <tempMixedRow
        :goods-data="list"
        :license-status="licenseStatus"
        :spid="trackInfo.spid"
        :track-info="trackInfo"
      />
    </div>
    <div v-if="!list.length&&!firstLoad" class="tableContent">
      <div class="noGoods">
        <img src="../../../assets/images/search-nodata.png" alt="" />
      </div>
    </div>
    <div class="pagination-container">
      <el-pagination
        v-model:currentPage="pageNum"
        :page-size="20"
        :background="true"
        layout="prev, pager, next, jumper"
        :total="total"
        @current-change="pagerChangePage"
      />
    </div>
  </div>
</template>

<script setup>
import { actionTracking } from "@/config/eventTracking";
import { onMounted, ref, getCurrentInstance, onUnmounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { getAllCategory, getAggs, getProductList } from "@/http_pc/api";
import tempMixedRow from "../../../components_pc/tempMixedRow.vue";
import { getUrlParam, exposureView } from "@/utils/index";
import { useStore } from "vuex";

const { proxy } = getCurrentInstance();
const store = useStore();
const merchantId = proxy.$merchantId;
const trackInfo = ref({});
const licenseStatus = ref("");
const total = ref(0);
const pageNum = ref(1);
const pages = ref(0); // 总页数
const goodsType = ref(1); // 1-正常搜索, 2-截断召回搜索, 3-兜底推荐
const categoryLevelRef = ref(null);
const selctedCategory = ref([]);
// const pageSize = ref(20);
const categoryList = ref([]);
const property = ref("smsr.sale_num");
const specStats = ref([]);
const shopStats = ref([]); // 接口返回的商家数据
const shopArr = ref([]); // 前端实现搜索后的数据
const manufacturerStats = ref([]); // 接口返回的厂家数据
const manufacturerArr = ref([]); // 前端实现搜索后的数据
const list = ref([]);
const checkListCJ = ref([]); // 多选的厂家
const checkListSJ = ref([]); // 多选的商家
const checkListGG = ref([]); // 多选的规格
const showMoreObj = ref({
  CJ: false, // 厂家
  SJ: false, // 商家
  GG: false, // 规格
});
// 单选/多选
const isSingleObj = ref({
  CJ: true, // 厂家
  SJ: true, // 商家
  GG: true, // 规格
});

const selectedSJKeys = ref([]); // shopCode
const selectedSJ = ref([]); // 所选商家条件
const selectedCJ = ref([]); // 所选厂家条件
const selectedGG = ref([]); // 所选规格条件
const oldSelectedSJ = ref([]); // 旧所选商家条件
const oldSelectedCJ = ref([]); // 旧所选厂家条件
const oldSelectedGG = ref([]); // 旧所选规格条件
const selectedJYLX = ref([]); // 所选经营类型条件
const selectedJYLXKeys = ref([]); // 所选经营类型key
const minPrice = ref("");
const maxPrice = ref("");
const keyword = ref(getUrlParam("keywordSearch"));
const fromPage = ref(getUrlParam("fromPage")); // searchIndex-大搜；shop-店铺；selfShop-自营店铺
const keywordCJ = ref("");
const keywordSJ = ref("");
const categoryIds = ref({
  categoryFirstId: getUrlParam("categoryFirstId"),
  categorySecondId: getUrlParam("categorySecondId"),
  categoryThirdId: getUrlParam("categoryThirdId"),
}); // 所选分类id
const checkBoxStatus = ref([]);
const checkBoxStatusArr = ref([]);
const drugClassification = ref([
  {
    key: "1",
    label: "甲类OTC",
  },
  {
    key: "2",
    label: "乙类OTC",
  },
  {
    key: "3",
    label: "处方药RX",
  },
  {
    key: "0",
    label: "其他",
  },
]);
const props = ref({
  checkStrictly: true,
  value: "key",
  label: "showName",
});
const getCheckBoxStatusArr = () => {
  if (fromPage.value === "shop" || fromPage.value === "selfShop") {
    checkBoxStatusArr.value = [
      { value: "拼团", key: "isPintuan" },
      { value: "优选", key: "highGross" },
      { value: "有货", key: "hasStock" },
      { value: "可用券", key: "isAvailableCoupons" },
      { value: "单品包邮", key: "isPgby" },
    ];
  } else {
    checkBoxStatusArr.value = [
      { value: "拼团", key: "isPintuan" },
      { value: "优选", key: "highGross" },
      { value: "京东", key: "isJD" },
      { value: "顺丰", key: "isSF" },
      { value: "自营", key: "isZiYing" },
      { value: "有货", key: "hasStock" },
      { value: "可用券", key: "isAvailableCoupons" },
      { value: "单品包邮", key: "isPgby" },
    ];
  }
};
// 获取各种厂家商家规格
const getSearchCategory = () => {
  let params = getParams();
  getAggs(params).then((res) => {
    if (res.status === "success") {
      specStats.value = ((res.data || {}).aggregations || {}).specStats || [];
      shopStats.value = ((res.data || {}).aggregations || {}).shopStats || [];
      categoryList.value = ((res.data || {}).aggregations || {}).catStats || [];
      shopArr.value = shopStats.value;
      manufacturerStats.value =
        ((res.data || {}).aggregations || {}).manufacturerStats || [];
      manufacturerArr.value = manufacturerStats.value;
    }
  });
};
// 更新分类
const changeCategoryList = (type) => {
  let params = getParams();
  getAggs(params).then((res) => {
    if (res.status === "success") {
      categoryList.value = ((res.data || {}).aggregations || {}).catStats || [];
      singleRubishServerFilter(res, type);
    }
  });
};

function singleRubishServerFilter(res, type) {
  if (res.data && res.data.aggregations) {
    let oldSeltSJ = findItem(
      "showName",
      oldSelectedSJ,
      res.data.aggregations.shopStats
    );
    let oldSelGG = findItem(
      "key",
      oldSelectedGG,
      res.data.aggregations.specStats
    );
    let oldSelCJ = findItem(
      "key",
      oldSelectedCJ,
      res.data.aggregations.manufacturerStats
    );

    if (type !== "selectedSJ") {
      if (oldSeltSJ.length > 0) {
        shopStats.value = (res.data.aggregations.shopStats || []).concat(
          oldSeltSJ
        );
      } else {
        shopStats.value = ((res.data || {}).aggregations || {}).shopStats || [];
      }
      shopArr.value = shopStats.value;
    }

    if (type !== "selectedGG") {
      if (oldSelGG.length > 0) {
        specStats.value = (
          ((res.data || {}).aggregations || {}).specStats || []
        ).concat(oldSelGG);
      } else {
        specStats.value = ((res.data || {}).aggregations || {}).specStats || [];
      }
    }

    if (type !== "selectedCJ") {
      if (oldSelCJ.length > 0) {
        manufacturerStats.value = (
          ((res.data || {}).aggregations || {}).manufacturerStats || []
        ).concat(oldSelCJ);
      } else {
        manufacturerStats.value =
          ((res.data || {}).aggregations || {}).manufacturerStats || [];
      }
      manufacturerArr.value = manufacturerStats.value;
    }
  }
}

//查询出来是否有相应的数据
function findItem(key, selecteds, arr) {
  let arritems = [];
  selecteds.value.forEach((item) => {
    let finds = arr.find((row) => {
      return row[key] === item[key];
    });
    if (!finds) {
      arritems.push(item);
    }
  });

  return arritems;
}

// 获取所有节点
const getCheckedNodes = (node) => {
  if ((node || []).length) {
    categoryIds.value = {
      categoryFirstId: node[0] || "",
      categorySecondId: node[1] || "",
      categoryThirdId: node[2] || "",
    };
    // 埋点
    let categoryNames = [];
    categoryNames = categoryLevelRef.value.getCheckedNodes()[0].pathLabels;
    const fromPage = getUrlParam("fromPage");
    if (node[2]) {
      fromPage === "searchIndex"
        ? actionTracking("search_third_classification", {
            classification: categoryNames[2],
          })
        : fromPage === "shop" || fromPage === "selfShop"
        ? actionTracking("shop_search_third_classification", {
            classification: categoryNames[2],
          })
        : "";
    } else if (node[1]) {
      fromPage === "searchIndex"
        ? actionTracking("search_second_classification", {
            classification: categoryNames[1],
          })
        : fromPage === "shop" || fromPage === "selfShop"
        ? actionTracking("shop_search_second_classification", {
            classification: categoryNames[1],
          })
        : "";
    } else if (node[0]) {
      fromPage === "searchIndex"
        ? actionTracking("search_first_classification", {
            classification: categoryNames[0],
          })
        : fromPage === "shop" || fromPage === "selfShop"
        ? actionTracking("shop_search_first_classification", {
            classification: categoryNames[0],
          })
        : "";
    }
  } else {
    categoryIds.value = {
      categoryFirstId: "",
      categorySecondId: "",
      categoryThirdId: "",
    };
  }
  pageNum.value = 1;
  changeCategoryList();
  loadData();
};
const changeCheckBoxStatus = (e) => {
  pageNum.value = 1;
  changeCategoryList();
  loadData();

  let arr = [];
  e.includes("isJD") ? arr.push("JD") : null;
  e.includes("isSF") ? arr.push("SF") : null;
  e.includes("isPintuan") ? arr.push("dough") : null;
  e.includes("highGross") ? arr.push("high_gross") : null;
  e.includes("isZiYing") ? arr.push("self") : null;
  e.includes("hasStock") ? arr.push("in_stock") : null;
  e.includes("isPgby") ? arr.push("Pgby") : null;
  const fromPage = getUrlParam("fromPage");
  fromPage === "searchIndex"
    ? actionTracking("search_quick_filter", { item: arr.join() })
    : fromPage === "shop" || fromPage === "selfShop"
    ? actionTracking("shop_search_quick_filter", { item: arr.join() })
    : "";
};
const getParams = (from) => {
  let tagList = [];
  checkBoxStatus.value.includes("isJD")
    ? tagList.push("YBM_ST_SERV_LOG_JD")
    : "";
  checkBoxStatus.value.includes("isSF")
    ? tagList.push("YBM_ST_SERV_LOG_SF")
    : "";
  return {
    keyword: keyword.value, // 关键词
    ...categoryIds.value, // 分类id
    categoryId:
      getUrlParam("categoryFirstId") ||
      getUrlParam("categorySecondId") ||
      getUrlParam("categoryThirdId"), // 不区分123级的分类id
    manufacturerList: selectedCJ.value.join(), // 厂家
    shopCodes: getUrlParam("shopCode") || selectedSJKeys.value.join(), // 商家
    spec: selectedGG.value.join(), // 规格
    drugClassificationStr: selectedJYLXKeys.value.join(), // 经营类型
    property: property.value, // 排序
    productTypes: checkBoxStatus.value.includes("isPintuan") ? "3" : "", // 拼团
    highGross: checkBoxStatus.value.includes("highGross") ? 1 : "", // 高毛
    tagList, // 京东/顺丰
    isThirdCompany: checkBoxStatus.value.includes("isZiYing") ? "0" : "", // 自营
    hasStock: checkBoxStatus.value.includes("hasStock") ? "1" : "", // 有货
    isAvailableCoupons: checkBoxStatus.value.includes("isAvailableCoupons")
      ? "1"
      : "", // 是否可用券
    isWholesale: checkBoxStatus.value.includes("isPgby")
      ? "1"
      : "",
    minPrice: minPrice.value, // 最低价格
    maxPrice: maxPrice.value, // 最高价格
    pageNum: pageNum.value,
    pageSize: 20,
    spFrom: getUrlParam("fromPage") === "searchIndex" ? 1 : 2, // 搜索页面来源：1-大搜；2-店铺内搜索；3-专区搜索；
    requestType: 1, // 请求方式：0-feed流，1-分页，默认=0
    direction: property.value === "fob" ? "asc" : "desc", // desc - 降序，asc-升序
    type: from === "changePage" ? goodsType.value : 1, // 1-正常搜索, 2-截断召回搜索, 3-兜底推荐
  };
};
const firstLoad= ref(true);
// 执行搜索接口
const loadData = (from) => {
  let params = getParams(from);
  store.commit("app/showLoading", true);
  getProductList(params).then((res) => {
    firstLoad.value = false;
    store.commit("app/showLoading", false);
    if (res.status === "success") {
      list.value = (res.data || {}).rows || [];
      total.value = (res.data || {}).total || 0;
      pages.value = (res.data || {}).pages || 0;
      goodsType.value = (res.data || {}).type || 1;
      licenseStatus.value = (res.data || {}).licenseStatus;
      trackInfo.value = {
        spid: (res.data || {}).spid,
        sid: (res.data || {}).sid,
        sptype: (res.data || {}).sptype,
      };
      nextTick(() => {
        postHeight();
        exposureView();
      });
      actionTracking("pc_page_Commoditylist", {
        spid: trackInfo.value.spid,
        sid: trackInfo.value.sid,
        sptype: trackInfo.value.sptype,
        user_id: merchantId,
      });
    } else if (res.code == 5000) {
      ElMessage.error(res.msg);
    }
  });
};
// 切换排序
const changeProperty = (type, name) => {
  property.value = type;
  pageNum.value = 1;
  changeCategoryList();
  loadData();
  // 埋点
  const fromPage = getUrlParam("fromPage");
  fromPage === "searchIndex"
    ? actionTracking("search_quick_filter", { item: name })
    : fromPage === "shop" || fromPage === "selfShop"
    ? actionTracking("shop_search_quick_filter", { item: name })
    : "";
};
// 展开/收起
const showMore = (type) => {
  showMoreObj.value[type] = !showMoreObj.value[type];
};
// 单选/多选
const changSingle = (type) => {
  isSingleObj.value[type] = !isSingleObj.value[type];
  // 多选时自动展开
  if (!isSingleObj.value[type]) {
    showMoreObj.value[type] = true;
    // 默认勾选已选的数据
    switch (type) {
      case "CJ":
        checkListCJ.value = selectedCJ.value;
        break;
      case "SJ":
        checkListSJ.value = selectedSJKeys.value;
        break;
      case "GG":
        checkListGG.value = selectedGG.value;
        break;
    }
  }
};
// 点击条件项
const changeCondition = (type, item, val, key) => {
  switch (type) {
    case "selectedCJ":
      isSingleObj.value.CJ
        ? changeSelectedData(
            selectedCJ,
            oldSelectedCJ,
            undefined,
            item,
            val,
            undefined
          )
        : "";
      break;
    case "selectedSJ":
      isSingleObj.value.SJ
        ? changeSelectedData(
            selectedSJ,
            oldSelectedSJ,
            selectedSJKeys,
            item,
            val,
            key
          )
        : "";
      break;
    case "selectedGG":
      isSingleObj.value.GG
        ? changeSelectedData(
            selectedGG,
            oldSelectedGG,
            undefined,
            item,
            val,
            undefined
          )
        : "";
      break;
    case "selectedJYLX":
      changeSelectedData(
        selectedJYLX,
        undefined,
        selectedJYLXKeys,
        item,
        val,
        key
      );
      break;
  }
  pageNum.value = 1;
  changeCategoryList(type);
  loadData();
};

function changeSelectedData(
  selecteds,
  oldSelected,
  selectedKeys,
  item,
  val,
  key
) {
  const index = selecteds.value.findIndex((item) => item === val);
  if (index === -1 || selecteds.value.length > 1) {
    selecteds.value = [val];
    if (oldSelected) {
      oldSelected.value = [item];
    }
  } else {
    selecteds.value.splice(index, 1);
    if (oldSelected) {
      oldSelected.value.splice(index, 1);
    }
  }
  if (key) {
    const keyIndex = selectedKeys.value.findIndex((item) => item === key);
    if (keyIndex === -1 || selectedKeys.value.length > 1) {
      selectedKeys.value = [key];
    } else {
      selectedKeys.value.splice(keyIndex, 1);
    }
  }
}

// 单个清空已选查询条件
const clearCondition = (type) => {
  switch (type) {
    case "selectedCJ":
      selectedCJ.value = [];
      oldSelectedCJ.value = [];
      break;
    case "selectedSJ":
      selectedSJ.value = [];
      selectedSJKeys.value = [];
      oldSelectedSJ.value = [];
      break;
    case "selectedGG":
      selectedGG.value = [];
      oldSelectedGG.value = [];
      break;
    case "selectedJYLX":
      selectedJYLX.value = [];
      selectedJYLXKeys.value = [];
      break;
    case "all":
      selectedCJ.value = [];
      selectedSJ.value = [];
      selectedSJKeys.value = [];
      selectedGG.value = [];
      selectedJYLX.value = [];
      selectedJYLXKeys.value = [];
      checkListSJ.value = [];
      checkListCJ.value = [];
      checkListGG.value = [];
      oldSelectedGG.value = [];
      oldSelectedSJ.value = [];
      oldSelectedCJ.value = [];
      break;
  }
  pageNum.value = 1;
  changeCategoryList();
  loadData();
};
// 确定多选
const confirmCheckbox = (type) => {
  const fromPage = getUrlParam("fromPage");
  switch (type) {
    case "CJ":
      selectedCJ.value = checkListCJ.value;
      oldSelectedCJ.value = manufacturerArr.value.filter((item) => {
        return checkListCJ.value.includes(item.key);
      });
      showMoreObj.value.CJ = false;
      isSingleObj.value.CJ = true;
      // 埋点
      fromPage === "searchIndex"
        ? actionTracking("search_multi_choice_confirmation", {
            item: "manufactor",
          })
        : fromPage === "shop" || fromPage === "selfShop"
        ? actionTracking("shop_search_multi_choice_confirmation", {
            item: "manufactor",
          })
        : "";
      break;
    case "SJ":
      selectedSJ.value = [];
      checkListSJ.value.forEach((item) => {
        shopArr.value.forEach((item2) => {
          if (item === item2.key) {
            selectedSJ.value.push(item2.showName);
          }
        });
      });
      oldSelectedCJ.value = shopArr.value.filter((item) => {
        return selectedSJ.value.includes(item.showName);
      });
      // selectedSJ.value = checkListSJ.value;
      selectedSJKeys.value = [...new Set(checkListSJ.value)];
      showMoreObj.value.SJ = false;
      isSingleObj.value.SJ = true;
      // 埋点
      fromPage === "searchIndex"
        ? actionTracking("search_multi_choice_confirmation", { item: "shop" })
        : fromPage === "shop" || fromPage === "selfShop"
        ? actionTracking("shop_search_multi_choice_confirmation", {
            item: "shop",
          })
        : "";
      break;
    case "GG":
      selectedGG.value = checkListGG.value;
      showMoreObj.value.GG = false;
      isSingleObj.value.GG = true;
      oldSelectedGG.value = specStats.value.filter((item) => {
        return selectedGG.value.includes(item.key);
      });

      // 埋点
      fromPage === "searchIndex"
        ? actionTracking("search_multi_choice_confirmation", { item: "desc" })
        : fromPage === "shop" || fromPage === "selfShop"
        ? actionTracking("shop_search_multi_choice_confirmation", {
            item: "desc",
          })
        : "";
      break;
  }
  pageNum.value = 1;
  changeCategoryList("selected" + type);
  loadData();
};
// 取消多选
const clearCheckbox = (type) => {
  switch (type) {
    case "CJ":
      checkListCJ.value = [];
      showMoreObj.value.CJ = false;
      isSingleObj.value.CJ = true;
      break;
    case "SJ":
      checkListSJ.value = [];
      showMoreObj.value.SJ = false;
      isSingleObj.value.SJ = true;
      break;
    case "GG":
      checkListGG.value = [];
      showMoreObj.value.GG = false;
      isSingleObj.value.GG = true;
      break;
  }
};
// 确认选择价格区间
const confirmPrice = () => {
  let maxMoney = eval(
    `Math.max(${[minPrice.value, maxPrice.value].toString()})`
  );
  let minMoney = eval(
    `Math.min(${[minPrice.value, maxPrice.value].toString()})`
  );
  minPrice.value = minMoney;
  maxPrice.value = maxMoney;
  pageNum.value = 1;
  changeCategoryList();
  loadData();

  // 埋点
  const fromPage = getUrlParam("fromPage");
  fromPage === "searchIndex"
    ? actionTracking("search_quick_filter", { item: "price_range" })
    : fromPage === "shop" || fromPage === "selfShop"
    ? actionTracking("shop_search_quick_filter", { item: "price_range" })
    : "";
};
// 清空价格区间
const clearPrice = () => {
  minPrice.value = "";
  maxPrice.value = "";
  pageNum.value = 1;
  changeCategoryList();
  loadData();
};

// 前端实现搜索框筛选
const searchName = (type, val) => {
  if (type === "CJ") {
    if (val) {
      manufacturerArr.value = manufacturerStats.value.filter(
        (i) => String(i.key).indexOf(val) > -1
      );
    } else {
      manufacturerArr.value = manufacturerStats.value;
    }
  } else if (type === "SJ") {
    if (val) {
      shopArr.value = shopStats.value.filter(
        (i) => String(i.showName).indexOf(val) > -1
      );
    } else {
      shopArr.value = shopStats.value;
    }
  }
};

const pagerChangePage = (page) => {
  pageNum.value = page;
  // changeCategoryList();
  loadData("changePage");
};
const changePage = (type) => {
  if (type === "last") {
    if (pageNum.value > 1) {
      pagerChangePage(pageNum.value - 1);
    }
  } else if (type === "next") {
    if (pageNum.value < pages.value) {
      pagerChangePage(pageNum.value + 1);
    }
  }
};

const scrollTimer = ref(null);
const scrollTimeStart = ref(0);
const scrollTimeEnd = ref(0);

const handleScrollStart = () => {
  clearTimeout(scrollTimer.value);
  scrollTimer.value = setTimeout(handleScrollEnd, 100);
  scrollTimeStart.value = document.body.scrollTop;
  postHeight();
};
const handleScrollEnd = () => {
  scrollTimeEnd.value = document.body.scrollTop;
  if (scrollTimeEnd.value === scrollTimeStart.value) {
    clearTimeout(scrollTimer.value);
    // 露出曝光，反复曝光
    exposureView();
  }
};

const calcPageHeight = (doc) => {
  const cHeight = Math.max(
    doc.body.clientHeight,
    doc.documentElement.clientHeight
  );
  const sHeight = Math.max(
    doc.body.scrollHeight,
    doc.documentElement.scrollHeight
  );
  const height = Math.max(cHeight, sHeight);
  return height;
};

// 动态设置iframe高度
const postHeight = () => {
  let height = calcPageHeight(document);
  window.parent.postMessage(
    {
      message: "getSearchHeight",
      height,
    },
    "*"
  );
};

onMounted(() => {
  getSearchCategory();
  loadData();
  getCheckBoxStatusArr();
  window.addEventListener("scroll", handleScrollStart);
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScrollStart);
});
</script>
<style lang="scss" scoped>
.wrapperBox {
  width: 1200px;
  background: #fff;
  margin: 0 auto;
  padding-bottom: 30px;
  padding-top: 10px;
  font-size: 12px;
  .triangle {
    margin-left: 10px;
    width: 0;
    height: 0;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent transparent #999999;
    display: inline-block;
  }
  .topBar {
    // margin-top: 10px;
    border: 1px solid #eeeeee;
    background: #f9f9f9;
    line-height: 40px;
    padding-left: 20px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    font-size: 12px;
    .selectedBox {
      display: flex;
      align-items: center;
      .selectedItemBox {
        display: flex;
        align-items: center;
        padding: 0 10px;
        background: #ffffff;
        border: 1px solid #d6d6d6;
        border-radius: 2px;
        height: 24px;
        .el-icon:hover {
          color: #00b955;
        }
      }
      .selectedItem {
        display: inline-block;
        max-width: 180px;
        margin-right: 4px;
      }
    }
    .clearBtn {
      color: #666666;
      margin-left: 10px;
      display: flex;
      align-items: center;
    }
    .clearBtn:hover {
      color: #00b955;
    }
  }
  .searchRow {
    background: #f9f9f9;
    border: 1px solid #eeeeee;
    border-top: none;
    .flexBox {
      display: flex;
      overflow: hidden;
      .searchTitle {
        background: #f9f9f9;
        width: 83px;
        border-right: 1px solid #eeeeee;
        min-height: 40px;
        line-height: 40px;
        text-align: center;
        font-size: 12px;
      }
      .searchContent {
        background: #fff;
        width: 946px;
        padding-left: 10px;
        .searchItem {
          min-height: 32px;
          margin-top: 8px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-bottom: 10px;
          border-bottom: 1px solid #eee;
          .singleBtn {
            margin: -8px 20px 0 0;
          }
        }
        .itemBox {
          display: flex;
          flex-wrap: wrap;
          max-height: 142px;
          overflow-y: scroll;
          div {
            padding-top: 12px;
            font-size: 14px;
          }
          .el-checkbox {
            height: 24px;
            margin: 0px 20px 5px 0;
          }
          .itemText {
            display: inline-block;
            margin-right: 12px;
            color: #333333;
            max-width: 168px;
            cursor: pointer;
          }
          .itemText:hover {
            color: #00b955;
          }
          .activeText {
            background: #00b955;
            color: #fff;
            border-radius: 4px;
            padding: 1px 4px;
          }
          .activeText:hover {
            color: #fff;
          }
        }
      }
      .searchBtn {
        background: #fff;
        flex-grow: 1;
        display: flex;
        padding-top: 8px;
        div {
          display: flex;
          align-items: center;
          margin-right: 10px;
          border: 1px solid #e2e2e2;
          padding: 6px 10px;
          border-radius: 2px;
        }
      }
    }
  }
  .search-rows {
    display: flex;
    flex-direction: row;
  }

  .tableTop {
    margin-top: 10px;
    // padding-left: 10px;
    padding: 5px 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #f9f9f9;
    border: 1px solid #eeeeee;
    height: auto;
    font-size: 12px;
    justify-content: space-between;
    .tableTop-left {
      .el-button-group {
        display: flex;
      }
    }
    .checkboxWrapper {
      padding: 0 6px 0 20px;
      margin: 0 20px;
      // height: 14px;
      display: flex;
      align-items: center;
      // border-left: 1px solid #bbbbbb;
      // border-right: 1px solid #bbbbbb;
      position: relative;
      .row-line {
        height: 14px;
        width: 1px;
        background: #bbbbbb;
        position: absolute;
      }
      .r-left {
        left: 0;
      } 
      .r-right {
        right: 0;
      }
      .el-checkbox {
        margin-right: 14px;
      }
      .el-checkbox-group {
        flex-wrap: wrap;
      }
    }
    .priceBox {
      display: flex;
      align-items: center;
      position: relative;
      z-index: 9;
      .priceInput {
        display: flex;
        align-items: center;
        position: relative;
        z-index: 9;
      }
      .priceBtn {
        display: none;
      }
    }
    .priceBox:hover .priceBtn {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      position: absolute;
      box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.08);
      border-radius: 2px;
      height: 65px;
      width: 140px;
      left: 55px;
      top: -6px;
      background: #fff;
      z-index: 0;
      padding: 0 6px;
      box-sizing: border-box;
    }
    .totalBox {
      display: flex;
      align-items: center;
      // margin-left: 90px;
      .pageIcon {
        width: 36px;
        height: 26px;
        border: 1px solid #e0e0e0;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .leftIcon {
        margin: 0 6px 0 10px;
      }
      .rightIcon {
        margin-left: 6px;
      }
    }
  }
  .douDiText {
    text-align: center;
    margin: 30px 0;
  }
  .tableContent {
    border: 1px solid #e0e0e0;
    border-top: none;
    .noGoods {
      position: relative;
      height: 140px;
      padding: 30px 0;
      img {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        width: 636px;
        height: 140px;
      }
    }
  }
  .pagination-container {
    margin: 20px 0;
    width: 758px;
    display: flex;
    justify-content: flex-end;
  }

  .itemBox::-webkit-scrollbar {
    display: none;
  }
}
</style>
