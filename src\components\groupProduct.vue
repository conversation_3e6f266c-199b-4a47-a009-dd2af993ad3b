<template>
  <div class="itemInfo">
    <div class="info">
      <!-- 标题 -->
      <!-- <a -->
        <!-- :href="`${detailUrl}product_id=${groupData.id}&nsid=${groupData.nsid}&sdata=${groupData.sdata || ''}&sid=${buriedPoint.sid}&spid=${buriedPoint.spid}&sptype=${buriedPoint.sptype}`" -->
        <!-- v-tracking="{eventName:'ActPage_CategoryCommodity_Click',params:{pageName: pageTitle, csu_id: groupData.id, sid: buriedPoint.sid, spid: buriedPoint.spid, sptype: buriedPoint.sptype, title: groupData.showName}}" -->
      <!-- > -->
      <div>
        <div class="commonName">
          <span
            v-for="(item, index) in ((groupData || {}).tags || {}).titleTags"
            class="titleTag"
            :key="index"
            :style="{marginLeft: index > 0 ? '0.12rem' : ''}"
          >
            <span v-if="item.text && item.uiStyle == 1" :style="{color: parseRGBA(item.textColor), background: parseRGBA(item.bgColor)}">{{item.text}}</span>
          </span>
          <!-- 药名 -->
          <span class="name">
            {{groupData.showName.trim()}}
          </span>
        </div>
        <div v-if="groupData.controlTitle && groupData.controlType != 5" class="controlTitleText">{{groupData.controlTitle}}</div>
        <div class="price-go" v-else>
          <div class="cur-price">
            拼团价
            <span class="priceDec">￥</span>
            <span v-if="(groupData.actPt || {}).assembleStatus === 1">
              <!-- 多阶梯拼团 -->
              <span v-if="(groupData.actPt || {}).stepPriceStatus === 1">
                <span class="priceInt">{{String((groupData.actPt || {}).minSkuPrice.toFixed(2)).split('.')[0]}}. </span>
                <span class="priceDec">{{String((groupData.actPt || {}).minSkuPrice.toFixed(2)).split('.')[1] || '00'}}</span>
                <span class="priceDec">起</span>
              </span>
              <!-- 单阶梯拼团 -->
              <span v-if="(groupData.actPt || {}).stepPriceStatus === 2">
                <span class="priceInt">{{String(((groupData.actPt || {}).assemblePrice || '').toFixed(2)).split('.')[0]}}. </span>
                <span class="priceDec">{{String(((groupData.actPt || {}).assemblePrice || '').toFixed(2)).split('.')[1] || '00'}}</span>
              </span>
            </span>
            <span class="priceNone" v-if="(groupData.actPt || {}).assembleStatus === 0">？</span>
          </div>
          <!-- <div class="original-price" v-if="(groupData.actPt || {}).stepPriceStatus === 2">
            <span>￥{{(groupData.retailPrice || '').toFixed(2)}}</span>
          </div> -->
        </div>
        <div class="actionArea" :class="groupData.status === 2 ? 'nullActionArea' : (groupData.actPt || {}).assembleStatus === 1 ? 'inActionArea' : (groupData.actPt || {}).assembleStatus === 0 ? 'beforeActionArea' : ''">
          <div>
            <div class="pin-count">
              已拼{{(groupData.actPt || {}).orderNum}}{{(groupData.actPt || {}).productUnit || groupData.productUnit}}/{{(groupData.actPt || {}).skuStartNum}}{{(groupData.actPt || {}).productUnit || groupData.productUnit}}起拼
            </div>
            <!-- 进度 -->
            <div class="progress-container">
              <div class="progress">
                <div class="progressBox">
                  <div class="progressContnet" :style="{width: `${(groupData.actPt || {}).percentage*1 > 100 ? 100 : (groupData.actPt || {}).percentage*1}%`}"></div>
                </div>
                <span class="percentText" :class="groupData.status === 2 ? 'nullPercentText' : (groupData.actPt || {}).assembleStatus === 1 ? 'inPercentText' : (groupData.actPt || {}).assembleStatus === 2 ? 'beforePercentText' : ''">
                  {{(groupData.actPt || {}).percentage*1}}%
                </span>
              </div>
            </div>
          </div>
          <!-- 拼团按钮 -->
          <div class="btnsBox">
            <div v-if="groupData.status === 2" class="go-mails nullBtn">已抢光</div>
            <div v-else-if="(groupData.actPt || {}).assembleStatus === 1" class="go-mails inBtn">
              立即参团
            </div>
            <div v-else-if="(groupData.actPt || {}).assembleStatus === 2" class="go-mails beforeBtn">
              即将开团
            </div>
          </div>
        </div>
        <!-- 活动时间 -->
        <div class="active-time">
          <div v-if="(groupData.actPt || {}).assembleStatus === 2" class="beforeTitle">
            <img src="../assets/images/green.png" />
            <span>{{dateFilter((groupData.actPt || {}).assembleStartTime)}}开抢</span>
          </div>
          <div v-if="(groupData.actPt || {}).assembleStatus === 1" class="inTitle">
            <img src="../assets/images/red.png" />
            <span>
              距离结束仅剩
              <LimitTime :endTime="(groupData.actPt || {}).assembleEndTime" timeColor="#FE5427" />
            </span>
          </div>
        </div>
        <div class="shopBox" v-if="groupData.shopUrl" @click.stop="setPreviewUrl(groupData.shopUrl)">
          <div class="shopName">{{groupData.shopName}}</div>
          <div class="shopEntry">进店
            <img src="../assets/images/toRight.png" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { useStore } from "vuex";
  import LimitTime from "./limit_time.vue";
  import { actionTracking } from "@/config/eventTracking";

  const props = defineProps({
    groupData: {
      default: {}
    },
    categoryName :{
       default : '',
    },
    // 埋点数据
    buriedPoint: {
      default: {
        spid: '',
        sid: '',
        sptype: ''
      },
    }
  });

  const store = useStore();
  const detailUrl = computed(() => store.state.app.detailBaseUrl);
  const pageTitle = window.document.title;
  
  function parseRGBA(val) {
    val = val.trim().toLowerCase();  //去掉前后空格
    if (val.length > 8) {
      let color = {};
      try {
        let argb = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(val);
        color.r = parseInt(argb[2], 16);
        color.g = parseInt(argb[3], 16);
        color.b = parseInt(argb[4], 16);
        color.a = parseInt(argb[1], 16) / 255;
      } catch (e) {
        console.log(e)
      }
      return 'rgba(' + color.r + ',' + color.g + ',' + color.b + ',' + parseFloat(color.a).toFixed(1) + ')';
    } else {
      return val;
    }
  }

  onMounted(() => {
    actionTracking("page_Chart_productList_Exposure", { 
      categoryName : props.categoryName,
      standardProduct_id : props.groupData.masterStandardProductId,
      showName : props.groupData.showName,
      ...props.buriedPoint
    })
  })
  function setPreviewUrl(shopurl){
    window.location.href = shopurl;
  }
  function dateFilter(date) {
    function formatDate(date) {
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let hour = date.getHours();
      let minute = date.getMinutes();
      return (String(month).length > 1 ? month : '0' + month) + '月' +
        (String(day).length > 1 ? day : '0' + day) + '日' + (String(hour).length > 1 ? hour : '0' + hour) + ':' + (String(minute).length > 1 ? minute : '0' + minute)
    }
    if (date) {
      return formatDate(new Date(date))
    } else {
      return ""
    }
  }

</script>
<style scoped lang="scss">
  .itemInfo {
    display: flex;
    margin-top: rem(10);
    margin-left: rem(20);
  }
  .info {
    padding-left: rem(10);
    width: rem(450);
    padding-right: rem(15);
    .commonName {
      position: relative;
      margin-bottom: rem(12);
      font-weight: bold;
      font-size: rem(30);
      color: #292933;
      line-height: rem(40);
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      /* autoprefixer: off */
      -webkit-box-orient: vertical;
      /* autoprefixer: on */
      -webkit-line-clamp: 2;
      word-break: break-all;
      word-wrap: break-word;
      .titleTag {
        border-radius: rem(2);
        font-size: rem(20);
        span {
          padding: 0 rem(8);
        }
      }
    }
    .actionArea {
      margin: rem(9) 0;
      border-radius: 0 rem(4) rem(4) 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .pin-count {
      font-size: rem(16);
      padding: rem(8) rem(10);
    }
    .nullActionArea {
      background: rgba(219,219,219,0.5);
      .pin-count {
        color: #676773;
      }
    }
    .inActionArea {
      background: linear-gradient(133deg, rgba(253,219,20,0.5) 12%, rgba(255,225,134,0.5) 88%);
      .pin-count {
        color: #ff5729;
      }
    }
    .beforeActionArea {
      background: rgba(201,255,237,0.5);
      .pin-count {
        color: #676773;
      }
    }
    .progress-container {
      padding: rem(0) rem(10);
      position: relative;
      width: 100%;
      display: flex;
      height: rem(20);
      .progress {
        .progressBox {
          width: rem(200);
          height: rem(12);
          background: #fff;
          border-radius: rem(6);
          margin-right: rem(10);
          .progressContnet {
            height: rem(12);
            background-image: linear-gradient(90deg, #FC6C41 0%, #F62526 100%);
            border-radius: rem(6);
          }
        }
      }
      .percentText {
        position: absolute;
        top: rem(-8);
        left: rem(220);
        font-size: rem(14);
      }
      .nullPercentText {
        color: #787c85;
      }
      .inPercentText {
        color: #ff5729;
      }
      .beforePercentText {
        color: #676773;
      }
    }
    .controlTitleText {
      font-size: rem(32);
      color: #f49926;
      font-weight: 600;
    }
    .price-go {
      position: relative;
      display: flex;
      align-items: flex-end;
      margin-top: rem(14);
      .cur-price {
        font-size: rem(22);
        color: #FF2121;
        font-weight: bold;
        .priceDec {
          font-weight: bold;
          font-size: rem(20);
        }
        .priceInt {
          font-weight: bold;
          font-size: rem(30);
          margin: 0 rem(-6);
        }
        .priceNone {
          font-weight: bold;
          font-size: rem(30);
        }
      }
      .original-price {
        font-size: rem(22);
        color: #9494A6;
        text-decoration: line-through;
        margin-left: rem(10);
      }
      .no-mails {
        background-color: #A9AEB7;
      }
    }
    .btnsBox {
      .go-mails {
        min-width: rem(112);
        border-radius: 0 rem(6) rem(6) 0;
        text-align: center;
        padding: rem(12) rem(10) rem(12) rem(30);
        font-size: rem(28);
        color: #ffffff;
      }
      .nullBtn {
        background: url('../assets/images/qg.png') no-repeat;
        background-size: 100%;
      }
      .inBtn {
        background: url('../assets/images/ct.png') no-repeat;
        background-size: 100%;
      }
      .beforeBtn {
        background: url('../assets/images/ck.png') no-repeat;
        background-size: 100%;
      }
    }
    .active-time {
      font-size: rem(24);
      width: rem(400);
      .beforeTitle {
        color: #00B377;
        display: flex;
        align-items: center;
        img {
          width: rem(24);
          height: rem(24);
          margin-right: rem(10);
        }
      }
      .inTitle {
        color: #FE5427;
        display: flex;
        align-items: center;
        img {
          width: rem(24);
          height: rem(24);
          margin-right: rem(10);
        }
      }
    }
    .shopBox {
      color: #676773;
      font-size: rem(24);
      padding: rem(12) 0 rem(26);
      display: flex;
      align-items: center;
      .shopName {
        max-width: rem(289);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .shopEntry {
        color: #292933;
        display: flex;
        align-items: center;
        margin-left: rem(10);
        img {
          width: rem(24);
          height: rem(24);
        }
      }
    }
  }
</style>