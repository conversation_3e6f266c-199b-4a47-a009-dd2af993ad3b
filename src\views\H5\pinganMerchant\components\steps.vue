<template>
    <div class="steps">
        <div class="steps-item" :class="{'step-active': step === index + 1}" v-for="(item, index) in stepData" :key="index">
            <div class="step-img">
                <div class="now" v-if="(step === index + 1 && step !== 2) || (step === 2 && index === 1 && stepStatus)">{{index + 1}}</div>
                <img style="width: 20px;height: 20px;" v-else-if="step > index + 1" src="@/assets/images/pinganMerchant/step-successv2.png" alt="">
                <img style="width: 20px;height: 20px;" v-else-if="step == 2 && index === 1 && !stepStatus" src="@/assets/images/pinganMerchant/step-errorv2.png" alt="">
                <div class="willnow " v-else>{{index + 1}}</div>
            </div>
            <span v-if="step == 2 && index === 1 && !stepStatus" style="font-size: 17px;">银行卡审核失败</span>
            <span v-else style="font-size: 17px;">{{item.title}}</span>
        </div>
        <div class="step-line"></div>
    </div>
</template>

<script setup>
import { reactive } from "vue";

const props = defineProps({
    step: {
      type: Number,
      default: 1
    },
    stepStatus: {
      type: Boolean,
      default: true
    }
});
let stepData = reactive([
  {title: '提交申请'},
  {title: '银行审批'},
  {title: '打款验证'},
])
console.log(props.step, '??')
</script>

<style lang="scss" scoped>
.steps {
    .willnow{
        width: 20px;
        height: 20px;
        background: #FFFFFF;
        border: 2px solid #E6E6E6;
        text-align: center;
        line-height: 20px;
        color: #666666;
        border-radius: 50%;
        font-size: 16px;
    }
    .now{
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        border-radius: 50%;
        background: #00B955;
        color: white;
        font-size: 16px;
    }
    display: flex;
    padding: rem(20);
    box-sizing: border-box;
    position: relative;
    justify-content: center;
    background: #FFFFFF;
    .step-line {
        width: 62%;
        // height: 1px;
        height: 0px;
        border:1px dashed #E6E6E6;
        // background: #E6E6E6;
        position: absolute;
        top: 40%;
        z-index: 1;
    }
    .steps-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 2;
        .step-img {
            width: rem(74);
            height: rem(74);
            display: flex;
            align-items: center;
            justify-content: center;
            img {
                width: rem(60);
                height: rem(60);
            }
        }
        span {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: rem(24);
            color: #9494A6;
        }
    }
    .step-active {
        .step-img {
            width: rem(74);
            height: rem(74);
            img {
                width: rem(60);
                height: rem(60);
            }
        }
        span {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: rem(24);
            color: #292933;
        }
    }
}
</style>