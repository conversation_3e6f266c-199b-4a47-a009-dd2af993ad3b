{"name": "static", "version": "0.0.0", "scripts": {"dev": "vite", "start": "vite", "build:prod": "vite build --mode=production", "build:staging": "vite build --mode=stage", "build:test": "vite build --mode=test", "serve": "vite preview", "lint": "eslint src --fix --ext .js,.vue"}, "dependencies": {"@element-plus/icons-vue": "^2.0.9", "@vueuse/core": "^4.10.0", "@xyy-ec/eslint-config-ecfe-base": "^1.0.5", "@xyy-ec/eslint-config-ecfe-vue": "^1.0.6", "axios": "^0.21.1", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "echarts": "^5.1.2", "element-plus": "^2.7.3", "eslint": "^7.11.0", "lodash": "^4.17.21", "mitt": "^3.0.0", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "throttle-debounce": "^3.0.1", "vant": "^4.6.8", "vant-green": "^1.0.44", "vconsole": "^3.12.0", "vue": "^3.2.32", "vue-router": "^4.0.0-beta.13", "vue3-lazy": "^1.0.0-alpha.1", "vuex": "^4.0.0-beta.4"}, "devDependencies": {"@babel/core": "^7.20.5", "@babel/preset-env": "^7.20.2", "@rollup/plugin-babel": "^6.0.3", "@types/node": "^15.0.3", "@vitejs/plugin-legacy": "^1.8.2", "@vitejs/plugin-vue": "^1.2.2", "@vue/compiler-sfc": "^3.0.5", "core-js": "^3.26.1", "eslint-plugin-import": "^2.25.2", "eslint-plugin-vue": "^7.20.0", "husky": "^7.0.4", "lint-staged": "^11.2.4", "mockjs": "^1.1.0", "sass": "^1.32.12", "vite": "2.3.7", "vite-plugin-mock": "2.8.0", "vue-tsc": "^0.0.24"}, "husky": {"hooks": {"pre-commit": "lint-staged", "post-merge": "yarn", "post-checkout": "yarn"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint 'src/**/*.{js,vue}' --fix"]}}