<template>
  <div class="jdPay_result">
    <img v-if="status === 'fail'" class="result_icon" src="@/assets/images/jdPay/error.png" />
    <div v-if="status === 'padding'" class="result_padding">
      <span></span>
      <span></span>
      <span></span>
    </div>
    <p>{{ { 'fail': '绑卡失败', 'padding': '绑卡中' }[status] }}</p>
    <div v-if="status === 'fail'" class="result_btn" @click="sendResultToApp">
      完成
    </div>
  </div>
</template>

<script setup>
  import { ref } from "vue";
  import { useRoute } from 'vue-router';
  import { queryContractSignResult } from '@/http/jdPay';
  import Bridge from "@/config/Bridge";
  
  const route = useRoute();
  const maxCount = 5;
  let currentCount = 0;
  let canPostReq = true;
  const status = ref('padding');
  const sendMsg = ref('');
  const interval = setInterval(() => {
    currentCount += 1;
    if (currentCount === maxCount) {
      canPostReq = false;
      clearInterval(interval);
    }
  }, 1000);
  
  const getResult = () => {
    if (!canPostReq) {
      status.value = 'fail';
      sendMsg.value = '绑卡失败';
      return false;
    }
    queryContractSignResult({
      reqNo: route.params.id
    }).then((res) => {
      if (res.code === 1000) {
        const { data } = res;
        const { cardBindState, msg } = data;
        if (cardBindState === 1) {
          getResult();
          return false;
        }
        sendMsg.value = msg;
        if (cardBindState === 4) {
          status.value = 'fail';
        } else {
          status.value = 'success';
          sendResultToApp();
        }
        clearInterval(interval);
      }
    });
  };
  getResult();

  const sendResultToApp = () => {
    Bridge.callJDBindBankCardResult({'fail':  4, 'success': 2}[status.value], sendMsg.value);
  };
  
</script>
<style lang="scss" scoped>
.jdPay_result {
  padding: rem(200) 0 0 0;
  .result_icon {
    width: rem(88);
    height: rem(88);
    margin: 0 auto;
    display: block;
  }
  .result_padding {
    width: rem(88);
    height: rem(88);
    margin: 0 auto;
    display: block;
    background: #FFAD00;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    @keyframes dotBounce {
      0%,
      80%,
      100% {
        transform: scale(0);
        background-color: #fff;
      }
      40% {
        transform: scale(1);
        background-color: #fff;
      }
    };
    span {
      display: inline-block;
      width: rem(12);
      height: rem(12);
      border-radius: 50%;
      margin-right: rem(9);
      background: #ffffff;
      animation: dotBounce 2s infinite ease-in-out both;
    }
    span:nth-child(1) {
      animation-delay: 0.2s;
    }
    span:nth-child(2) {
      animation-delay: 0.4s;
    }
    span:nth-child(3) {
      animation-delay: 0.6s;
      margin-right: rem(0);
    }
  }
  p {
    margin: rem(32) 0 0 0;
    font-weight: 400;
    font-size: rem(32);
    color: #292933;
    text-align: center;
  }
  .result_btn{
    width: rem(345);
    height: rem(80);
    background: #FFFFFF;
    border: rem(1) solid #00B377;
    border-radius: rem(4);
    font-size: rem(28);
    color: #00B377;
    text-align: center;
    line-height: rem(80);
    position: fixed;
    bottom: rem(120);
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>