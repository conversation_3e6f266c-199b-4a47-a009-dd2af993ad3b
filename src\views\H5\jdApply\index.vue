<template>
    <div>
        <div class="jdApply">
            <el-form label-position="top" :model="dataForm" label-width="auto" :rules="rules" ref="formEl">
                <div class="topPadding">客户名称：{{ dataForm.merchantName }}</div>
                <el-form-item label="法人姓名" prop="name">
                    <el-input v-model="dataForm.name" maxlength="10"></el-input>
                </el-form-item>
                <el-form-item label="法人身份证号" prop="idCard">
                    <el-input v-model="dataForm.idCard" maxlength="18"></el-input>
                </el-form-item>
                <el-form-item label="法人实名手机号" prop="mobile">
                    <el-input v-model="dataForm.mobile" maxlength="11"></el-input>
                </el-form-item>
                <el-form-item label="上传法人身份证" prop="card" class="is-required">
                    <div style="display: flex;justify-content: space-between;width: 100%;">
                        <van-uploader :after-read="portrait" image-fit="fill" v-model="dataForm.cardFrontUrl"
                            :preview-size="['45vw', '30vw']">
                            <div class="uploadImg" v-if="dataForm.cardFrontUrl.length == 0">
                                <div class="content">
                                    <img src="./img/pic.png" alt="">
                                    <div class="text">上传人像面</div>
                                </div>

                            </div>
                        </van-uploader>
                        <van-uploader class="uploadImg-box" :after-read="cardBackUrl" image-fit="fill"
                            :preview-size="['45vw', '30vw']" v-model="dataForm.cardBackUrl">
                            <div class="uploadImg" v-if="dataForm.cardBackUrl.length == 0">
                                <div class="content">
                                    <img src="./img/pic.png" alt="">
                                    <div class="text">上传国徽面</div>
                                </div>
                            </div>

                        </van-uploader>
                    </div>
                </el-form-item>
                <el-form-item label="统一社会信用代码" prop="uscc">
                    <el-input v-model="dataForm.uscc" maxlength="30"></el-input>
                </el-form-item>
                <el-form-item label="上传营业执照" prop="companyLicenseUrl" class="is-required">
                    <div style="display: flex;justify-content: space-between;width: 100%;">
                        <van-uploader :after-read="companyLicenseUrl" image-fit="fill"
                            v-model="dataForm.companyLicenseUrl" :preview-size="['45vw', '30vw']">
                            <div class="uploadImg" v-if="dataForm.companyLicenseUrl.length == 0">
                                <div class="content">
                                    <img src="./img/pic.png" alt="">
                                    <div class="text">上传营业执照原件或加盖公章的复印件照片</div>
                                </div>

                            </div>
                        </van-uploader>

                    </div>
                </el-form-item>
                <el-form-item label="药品经营许可证编号" prop="drugLicenseCode" @change="drugLicenseCodeChange">
                    <el-input v-model="dataForm.drugLicenseCode" maxlength="30"></el-input>
                </el-form-item>
                <el-form-item label="药品经营许可证类型" prop="drugLicenseType">
                    <el-select v-model="dataForm.drugLicenseType" placeholder="请选择">
                        <!-- <el-option label="请选择" :value="''"></el-option> -->
                        <el-option label="批发" :value="0"></el-option>
                        <el-option label="零售" :value="1"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="药品经营许可证有效期" prop="drugLicenseEnd">
                    <div class="select-trigger" @click="openCalendar">{{ dataForm.drugLicenseEnd || '请选择' }}</div>
                    <template v-if="showCalendar">
                        <van-calendar v-model:show="showCalendar" :default-date="dataForm.drugLicenseEndDate"
                            @confirm="datEonConfirm" />
                    </template>

                </el-form-item>
                <el-form-item label="药品经营许可证" prop="drugLicenseUrl" class="is-required">
                    <div style="display: flex;justify-content: space-between;width: 100%;">
                        <van-uploader :after-read="drugLicenseUrl" image-fit="fill" v-model="dataForm.drugLicenseUrl"
                            :preview-size="['45vw', '30vw']">
                            <div class="uploadImg" v-if="dataForm.drugLicenseUrl.length == 0">
                                <div class="content">
                                    <img src="./img/pic.png" alt="">
                                    <div class="text">上传药品经营许可证原件或加盖公章的复印件照片</div>
                                </div>

                            </div>
                        </van-uploader>

                    </div>
                </el-form-item>
            </el-form>

        </div>
        <div class="bottom-btn" v-if="!show">
            <el-button type="primary" @click="submitForm"  :class="{ comon: true, noCommit: !isHasSubmit }">提交预申请</el-button>
        </div>
    </div>
</template>
<script setup>
import { reactive, ref ,watch} from 'vue'
import { uploadFile, queryCreditMerchant ,saveCreditMerchant ,queryPreApply} from "@/http/jdApply"
import { useRoute , useRouter } from 'vue-router'
import { ElLoading,ElMessage} from 'element-plus'
import { showToast, showDialog } from 'vant';
import Bridge from "@/config/Bridge";
const route = useRoute()
const router = useRouter()
let merchantId=route.query.merchantId
const dataForm = ref({
    merchantName: route.query.merchantName,
    name: "",
    idCard: "",
    mobile: "",
    cardFrontUrl: [],
    cardBackUrl: [],
    uscc: "",
    companyLicenseUrl: [],
    drugLicenseCode: "",
    drugLicenseType: "",
    drugLicenseEnd: "",
    drugLicenseEndDate: new Date(),
    drugLicenseUrl: [],
    merchantId: merchantId,
})
const isHasSubmit = ref(false)
watch(dataForm,()=>{ 
  console.log(checkData())
  isHasSubmit.value=checkData()
},{
    deep:true
})
const rules=reactive({
    name: [
    { required: true, message: '请输入法人姓名', trigger: 'blur' },
  ],
  idCard: [
    { required: true, message: '请输入身份证', trigger: 'blur' },
  ],
  mobile: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
  ],
  uscc:[
    { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
  ],
  drugLicenseCode:[
    { required: true, message: '请选择药品经营许可证编号', trigger: 'blur' },
  ],
  drugLicenseType:[
    { required: true, message: '请选择药品经营许可证类型', trigger: 'change' },
  ],
  drugLicenseEndDate:[
    { required: true, message: '请选择药品经营许可证有效期', trigger: 'change' },
  ],
})
//初始化数据
queryCreditMerchant({ merchantId: route.query.merchantId, creditChannel: 5 }).then((res) => {
  if(res.code == 1000){
    Object.keys(res.data).forEach((key) => {
        if( ['cardFrontUrl','cardBackUrl','companyLicenseUrl','drugLicenseUrl'].includes(key)){
            dataForm.value[key]=[{url:getUrl(res.data[key])}]
        } else if(key=='drugLicenseEnd'){
            dataForm.value[key] = dateFormat(new Date(Number(res.data[key])))
            dataForm.value['drugLicenseEndDate'] = new Date(Number(res.data[key]))
        }else{
            dataForm.value[key] = res.data[key] || ''
        }
    })
  }
})
//没有携带https的手动添加
const getUrl = (url) => {
    if(url.indexOf('http') == -1){
        return 'http://oss-ec.ybm100.com/'+url
    }else{
        return url
    }
}
const show = ref(false)
const showCalendar = ref(false)
//上传
// 法人身份证正面
const portrait = async (file) => {
    dataForm.value.cardFrontUrl=[]
    let url=await uploadFileImg('1', file)
    if(!url){
        return
    }
    dataForm.value.cardFrontUrl = [{url}]
}
const cardBackUrl = async(file) => {
    dataForm.value.cardBackUrl=[]
    let url=await uploadFileImg('2', file)
    if(!url){
        return
    }
    dataForm.value.cardBackUrl = [{url}]
}
//营业执照
const companyLicenseUrl =async (file) => {
    dataForm.value.companyLicenseUrl=[]
    let url=await uploadFileImg('3', file)
    if(!url){
        return
    }
    dataForm.value.companyLicenseUrl = [{url}]
   
}
//药品经营许可证
const drugLicenseUrl =async (file) => {
    dataForm.value.drugLicenseUrl=[]
    let url= await uploadFileImg('4', file)
    if(!url){
        return
    }
    dataForm.value.drugLicenseUrl =  [{url}]
}
const drugLicenseCodeChange =  (value) => {
    if(dataForm.value.drugLicenseCode.indexOf('BA')!=-1 && dataForm.value.drugLicenseType === ""){
        dataForm.value.drugLicenseType = 1
    }
}
// 文件业务类型
// 01 法人身份证正面
// 02 法人身份证反面
// 03 营业执照
// 04 药品经营许可证
// 05 经办人身份证正面
// 06 经办人身份证反面
// 07 物流凭证影像资料
// 08 发票影像资料

function imgUrlV2(url, catalogue) {
  // catalogue->存放的目录，默认/ybm/product

  return (window.evn !== 'development') 
  ? `https://oss-ec.ybm100.com/${catalogue || ''}${url}` 
  : `http://oss-ec.ybm100.com/${catalogue || ''}${url}`;
}
const uploadFileImg =async (type, file) => {
    // if (file.file.size > 1024 * 1024 * 30) {
    //     ElMessage.error('文件大小不能超过30M')
    //     return
    // }
    let formData = new FormData()
    formData.append('file', file.file)
    formData.append('creditChannel', 5)
    let loadingService=ElLoading.service({
        text:"上传中"
    })
    let url=""

  await  uploadFile(formData).then(res => {
        // console.log(imgUrlV2(res.data.filePath))
        if(res.code == 1000){
            url= imgUrlV2(res.data.filePath)
        }else{
            ElMessage.error(res.msg)
        }
        
    }).catch(err => {
        console.log(err)
    }).finally(() => {
        loadingService.close()
    })
    return url
}
const dateFormat = (dateValue) => {
    const year = dateValue.getFullYear();
    const month = (dateValue.getMonth() + 1).toString().padStart(2, '0');
    const day = dateValue.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
}
//日期选择
const datEonConfirm = (date) => {
    show.value = false
    showCalendar.value = false
    console.log(dateFormat(date))
    dataForm.value.drugLicenseEndDate = date
    dataForm.value.drugLicenseEnd = dateFormat(date)
}
//打开日期弹窗·
const openCalendar = () => {
    show.value = true
    showCalendar.value = true
    console.log(show.value);
}
//校验数据是否完整
const checkData = () => {
     let checkResult=true

    // let result= Object.keys(dataForm.value).every((key) => {
    //   return (dataForm.value[key]||(Array.isArray(dataForm.value[key])&&dataForm.value[key].length))
    // })
    let result= Object.keys(dataForm.value).forEach((key) => {
      if(dataForm.value[key]===''||dataForm.value[key]==undefined||(Array.isArray(dataForm.value[key])&&dataForm.value[key].length==0)){
        checkResult=false
      }
    })
    return checkResult;
}
//提交申请
let formEl= ref()
const getParams = ()=>{
    let data= JSON.parse(JSON.stringify(dataForm.value))
    data.cardFrontUrl= data.cardFrontUrl[0].url;
    data.cardBackUrl= data.cardBackUrl[0].url;
    data.companyLicenseUrl= data.companyLicenseUrl[0].url;
    data.drugLicenseUrl= data.drugLicenseUrl[0].url;
    data.drugLicenseEnd=new Date(data.drugLicenseEnd+" 00:00:00").getTime()
    data.creditChannel=5
    return data;
}
let loadingService;

const submitForm =async ()=>{
    await formEl.value.validate((valid, fields) => {
        if (valid&&checkData()) {
            let params =getParams()
            loadingService=ElLoading.service({
                        text:"申请中"
            })
            saveCreditMerchant(params).then((res) => {
                if (res.code === 1000) {
                    reqCount=1
                    setTimeout(()=>{
                      getResult(res.data.thirdApplyNo);
                    },1000)                   
                } else {
                    loadingService.close()
                    ElMessage.error(res.msg);
                }
            }).catch(err=>{
                loadingService.close()
            })
        } else {
            console.log('error submit!', fields)
        }
    })

}
//获取提交结果
let reqCount=1
const getResult =async (thirdApplyNo) => { 
   await queryPreApply({
    thirdApplyNo,
    reqCount
   }).then((res) => {
        if(res.code==1000){
            if (res.data.msg) {
                loadingService.close()
                if (res.data.code == '3') {
                    showDialog({
                        title: '提示',
                        width: "80%",
                        confirmButtonText: "确定",
                        confirmButtonColor: "#00B377",
                        message: res.data.msg,
                    }).then(() => {
                        // Bridge.closeWebView()
                    });
                }else if(res.data.code == "1"){
                    window.location.href=res.data.h5Url
                } else {
                        router.push({
                        path: '/jdApplyResult',
                        query: {
                            msg: encodeURIComponent(res.data.msg),
                            h5Url: res.data.h5Url,
                            code: res.data.code
                        }
                    });
                }              
            } else {
                setTimeout(() => {
                    reqCount++
                    getResult(thirdApplyNo)
                }, 1000)
            }
        }else{
            loadingService.close()
            showDialog({
                title: '提示',
                width: "80%",
                confirmButtonText: "确定",
                confirmButtonColor: "#00B377",
                message: res.msg,
            }).then(() => {
                // Bridge.closeWebView()
            });
            // ElMessage.error(res.msg)
        }

    }).catch(err=>{
        loadingService.close()
    })
}

</script>
<style lang="scss" scoped>
::v-deep .van-uploader__preview-image {
    background: #F7F7F8 !important;
    border: 1px solid #C6C6D7 !important;
    border-radius: 8px;
}

::v-deep .van-button--primary {
    background: #00B955 !important;
    border: none !important;

}

::v-deep .van-calendar__selected-day {
    background: #00B955 !important;
}

.custom-select {
    position: relative;
    width: 100%;
    font-family: Arial, sans-serif;
}

.select-trigger {
    border: 1px solid #C6C6D7;
    padding: 0px 12px;
    border-radius: 4px;
    background-color: #fff;
    cursor: default;
    /* 表示不可交互 */
    width: 100%;
    color: #606266;
    font-size: 14px;
}

.select-trigger::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%) rotate(45deg);
    width: 6px;
    height: 6px;
    border: solid #999;
    border-width: 0 1px 1px 0;
}

::v-deep .van-uploader__preview-delete--shadow {
    width: 20px !important;
    height: 20px !important;
    z-index: 99;
    border-radius: 12px !important;
    background-color: #707070 !important;
}

::v-deep .van-uploader__preview-delete-icon {
    top: 4px;
    right: 4px;
}

::v-deep .van-uploader__preview-delete {
    top: -8px !important;
    right: -7px !important;
}

.jdApply {
    background-color: white;
    font-size: 14px;
    padding: 15px 12px;
    // height: calc(100vh - 85px);
    // flex: 1;
    margin-bottom: 75px;
    overflow-y: scroll;

    .topPadding {
        padding-bottom: 15px;
        color: black;
        font-size: 16px;

    }
}

.uploadImg {
    width: 43vw !important;
    height: 30vw !important;
    background: #F7F7F8;
    border: 1px dashed #C6C6D7;
    border-radius: 8px;
    text-align: center;

    .content {
        position: relative;
        top: 50%;
        transform: translateY(-50%);
        line-height: 20px;
        font-size: 12px;
        padding: 0 6px;
    }

    img {
        width: 10vw;
    }
}

.comon {
    width: 90%;
    height: 38px;
    line-height: 38px;
    text-align: center;
}

.noCommit {

    background: #7FDCAA;
    border-radius: 4px;
    border: none;

}

.bottom-btn {
    text-align: center;
    position: fixed;
    bottom: 0;
    height: 35px;
    flex: 1;
    width: 100%;
    background-color: #F0F2F5;
    padding: 20px 0;
    z-index: 100;
}

// .uploadImg-box{
//     img{
//         // width: 45vw !important;
//         // height: 30vw !important;
//         background: #F7F7F8 !important;
//         border: 1px dashed #C6C6D7 !important;
//         border-radius: 8px;
//     }

// }</style>