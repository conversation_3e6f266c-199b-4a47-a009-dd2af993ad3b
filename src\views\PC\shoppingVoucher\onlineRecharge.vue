<template>
<div class="onlineRecharge">
    <div class="myShoppingGlodTitle">
        <div style="display: flex;align-items: center;">
            <span></span>
            我的购物金-在线充值
        </div>
        <div>
            <el-button @click="goBack">返回</el-button>
        </div>
    </div>
    <div class="notice" v-if="noticeText">
        <div class="notice-icon"><i class="promotion-icon"></i></div>
        <div class="notice-text" ref="textContainer" :class="{ scroll: shouldScroll }">
            <div class="text-inner">{{ noticeText }}</div>
        </div>
    </div>
    <div class="recharge-container">
        <div class="selectMoney">
            <div class="title fixd">选择充值金额</div>
            <div class="money-list">
                <div 
                    v-for="(item, index) in selectMoneyList" 
                    :key="index"
                    class="money-item"
                    :class="{ 'money-item-active': item.selected }"
                    @click="toggleSelectMoney(index)"
                >
                    <div class="money-item-img" v-if="item.recommendStr">{{ item.recommendStr }}</div>
                    <div class="money-item-content">
                        <span style="font-size: 16px;margin-right: 4px;font-weight: 600;">¥</span>{{ item.rechargeAmount }}
                    </div>
                    <div class="money-item-title" v-if="item.discountStr">{{ item.discountStr }}</div>
                </div>
            </div>
        </div>
        <div class="otherMoney">
            <div class="title otherTitle">其他金额</div>
            <div style="width: 100%;">
                <el-input
                    v-model="otherMoneyAmount"
                    placeholder="请输入充值金额"
                    style="width: 90%;"
                    clearable
                    size="large"
                    @input="validateIntegerInput"
                    @blur="formatIntegerInput"
                    @focus="handleInputFocus"
                >
                    <template #prefix>
                        <span style="color: #606266;font-size: 20px;">¥</span>
                    </template>
                </el-input>
                <p class="otherMoney-tip">提示：最小充值金额1元，最大充值金额100000元</p>
            </div>
        </div>
        <div class="submitBtn">
            <el-button type="primary" @click="submitRecharge">立即充值</el-button>
        </div>
    </div>
</div>
</template>

<script setup>
import { onMounted, ref,onUpdated, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus'
import { getRechargeDiscount,onlinePay } from '@/http_pc/shoppingGold/index.js'
const router = useRouter();
const noticeText = ref('');
const textContainer = ref(null)
const shouldScroll = ref(false)
const selectMoneyIndex  = ref(-1)
const selectMoneyList = ref([])
const otherMoneyAmount = ref('')
// 检测文本是否需要滚动
const checkOverflow = () => {
    if (!textContainer.value) return
    const container = textContainer.value
    const content = container.querySelector('.text-inner')
    shouldScroll.value = content.scrollWidth > container.offsetWidth
}
// 处理窗口变化
const handleResize = () => {
    checkOverflow()
}
// 获取购物金活动充值信息
const getRechargeInfo = () => {
    getRechargeDiscount({channel: 0}).then((res) => {
        const stairsList = res.data.data.stairsList || [];
        // 给每个都加上selected标识
        stairsList.forEach((item, index) => {
            item.selected = false;
        })
        selectMoneyList.value = stairsList;
        noticeText.value = res.data.data.topUpStr;
    })
}
// 生命周期钩子
onMounted(() => {
    getRechargeInfo();
    checkOverflow()
    window.addEventListener('resize', handleResize)
})

onUpdated(() => {
    checkOverflow()
})

onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize)
})
const goBack = () => {
    router.go(-1);
}
const toggleSelectMoney = (index) => {
    if (selectMoneyList.value[index].selected) {
        selectMoneyList.value[index].selected = false;
        selectMoneyIndex.value = -1;
    } else {
        selectMoneyList.value.forEach(item => {
            item.selected = false;
        });
        selectMoneyList.value[index].selected = true;
        selectMoneyIndex.value = index;
        otherMoneyAmount.value = ""
    }
}
const detailUrl = import.meta.env.VITE_BASE_URL_PC;
const submitRecharge = () => {
    const params = {}
    if(selectMoneyIndex.value !== -1) {
        params.amount = selectMoneyList.value[selectMoneyIndex.value].rechargeAmount;
    }else {
        if(otherMoneyAmount.value === '') {
            return ElMessage.warning('请选择充值金额');
        }
        if(otherMoneyAmount.value > 100000 || otherMoneyAmount.value < 1) {
            const title = otherMoneyAmount.value > 100000 ? '充值金额需小于等于100000元' : '充值金额需大于等于1元';
            return ElMessage.warning(title);
        }
        params.amount = otherMoneyAmount.value;
    }
    params.rechargeType = 2; // 充值类型，1：订单支付，2：购物金在线充值
    params.reqScene = 'pccashier'; // 结算页： pcSettle （走提单页） 收银台：pcCashier （购物金直接充值）
    window.open(`${detailUrl}merchant/center/order/queryConfirmOrder.htm?rechargeAmount=${params.amount}&rechargeType=${params.rechargeType}&reqScene=${params.reqScene}`)
}
// 添加整数验证函数
const validateIntegerInput = (value) => {
    // 只允许输入整数
    const regex = /^[1-9]\d*$/;
    if (value === '') {
        otherMoneyAmount.value = '';
    } else if (!regex.test(value)) {
        // 如果输入的不是整数，则移除非法字符
        otherMoneyAmount.value = value.replace(/[^\d]/g, '');
        // 移除前导零
        otherMoneyAmount.value = otherMoneyAmount.value.replace(/^0+/, '');
        // 如果结果为空（例如只输入了0或非数字），则设为空字符串
        if (otherMoneyAmount.value === '') {
            otherMoneyAmount.value = '';
        }
    }
}

// 失去焦点时格式化
const formatIntegerInput = () => {
    if (otherMoneyAmount.value === '') return;
    
    // 确保是整数
    const num = parseInt(otherMoneyAmount.value);
    if (isNaN(num)) {
        otherMoneyAmount.value = '';
    } else {
        otherMoneyAmount.value = num.toString();
    }
}
// 处理输入框获得焦点
const handleInputFocus = () => {
    // 取消所有选中的金额选项
    selectMoneyList.value.forEach(item => {
        item.selected = false;
    });
    // 重置选中索引
    selectMoneyIndex.value = -1;
}
</script>

<style lang='scss' scoped>
.onlineRecharge{
  height: 800px;
  background: #fff;
  width: 980px;
  margin: auto;
  padding: 20px 20px 0 20px;
  box-sizing: border-box;
  .myShoppingGlodTitle {
        span {
            width: 4px;
            height: 16px;
            display: inline-block;
            background: #00B377;
            margin-right: 10px;
        }
        font-weight: 600;
        font-size: 18px;
        color: #292933;
        letter-spacing: 0;
        line-height: 20px;
        display: flex;
        justify-content: space-between;
        padding: 5px 0;
  }
  .notice {
    display: flex;
    align-items: center;
    height: 30px;
    width: 100%;
    background-color: #FFF8E6;
    border-radius: 4px;
    margin: 20px 0;
    .notice-icon {
        padding: 0 8px;
        display: flex;
        align-items: center;
        .promotion-icon {
            display: inline-block;
            width: 14px;
            height: 14px;
            background: url('@/assets/images/notice.png') no-repeat;
            background-size: contain;
            margin-right: 6px;
        }
    }
    .notice-text {
        flex-grow: 1;
        overflow: hidden;
        position: relative;
    }
  }
  .recharge-container {
    .title {
        width: 110px;
        height: 16px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        line-height: 16px;
        margin-right: 10px;
        text-align: right;
    }
    .selectMoney {
        display: flex;
        max-height: 460px;
        overflow: auto;
        .fixd {
            margin-top: 3px;
            position: sticky;
            top: 3px;
        }
        .money-list {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            flex: 1;
            padding-top: 3px;
            .money-item {
                width: 180px;
                height: 98px;
                background: #F6F6F9;
                border-radius: 6px;
                cursor: pointer;
                position: relative;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                border: 2px solid #F6F6F9;
                .money-item-img {
                    position: absolute;
                    top: -5px;  /* 上移1px */
                    left: -5px;  /* 左移1px */
                    background: #FFF3F3;
                    color: #FF223B;
                    padding: 2px 4px;
                    border-radius: 3px 0 3px 0;
                    z-index: 10; 
                }
                .money-item-content {
                    font-size: 28px;
                    font-weight: bold;
                    margin-bottom: 4px;
                }
                .money-item-title {
                    padding: 0 5px;
                    height: 20px;
                    background: #FC443D;
                    border-radius: 4px;
                    color: #FFFFFF;
                    line-height: 20px;
                    text-align: center;
                }
            }
            .money-item-active {
                background: #F9FFFD;
                border: 2px solid #00B955;
                border-radius: 6px;
                &::after {
                    content: "";
                    position: absolute;
                    width: 16px;  /* 图标尺寸 */
                    height: 16px;
                    right: -1px;  /* 对齐边框外侧 */
                    bottom: -1px;
                    background: url('@/assets/images/checkBoxMoney.png');
                    background-size: contain;
                }
            }
        }
    }
    .otherMoney {
        margin-top: 20px;
        display: flex;
        align-items: flex-start;
        .otherTitle {
            text-align: right;
            padding-top: 10px;
            width: 128px;
        }
        .otherMoney-tip {
            margin-top: 10px;
            font-size: 14px;
        }
    }
    .submitBtn {
        margin-top: 30px;
        text-align: center;
        .el-button {
            width: 160px;
            height: 48px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 18px;
            color: #FFFFFF;
            background: #00B955;
            text-align: center;
            line-height: 18px;
        }
    }
  }   
}
.text-inner {
  display: inline-block;
  font-size: 14px;
  white-space: nowrap;
  transition: 0.3s transform;
}

.notice-text.scroll .text-inner {
  animation: scroll 40s linear infinite;
  padding-left: 100%;
}

.notice-text.scroll:hover .text-inner {
  animation-play-state: paused;
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}
</style>
