<template>
  <div id="app">
    <router-view></router-view>
    <!-- loading图 -->
    <Loading v-if="showloading"></Loading>
    <!-- 提示 -->
    <Prompt v-if="promptData.showprompt" :prompt="promptData.promptmsg"></Prompt>
    <!-- 确认提示 -->
    <SureDialog v-if="sureDialog.showsureDialog" :suredialog='sureDialog.dialogmsg'></SureDialog>
    <!-- 安卓加入购物车弹框 -->
    <AndroidAddCart v-if="android_addCart.showandoridedit"
      :goods-id="android_addCart.id"
      :data-val="android_addCart.val"
      :is-split="android_addCart.split"
      :med-pack="android_addCart.medpack"
      :package="android_addCart.package">
    </AndroidAddCart>
  </div>
</template>

<script setup>
  import { computed, onMounted  } from 'vue';
  import { useStore } from 'vuex';
  import SureDialog from '@/components/suredialog.vue';
  import Prompt from '@/components/prompt.vue';
  import AndroidAddCart from '@/components/AndroidAddCart.vue';
  import Loading from '@/components/loading.vue';
  import { useRouter } from 'vue-router';

  const store = useStore();
  const sureDialog = computed(() => store.state.app.sureDialogdata || {});
  const showloading = computed(() => store.state.app.loading);
  const promptData = computed(() => store.state.app.promptData || {});
  const android_addCart = computed(() => store.state.app.android_addCart || {});

  const router = useRouter();
  const BASEURL_PC = import.meta.env.VITE_BASE_URL_PC;
  if (BASEURL_PC.indexOf(window.location.host) > -1 && router.currentRoute.value.path === '/') {
    if (document.getElementById('toPage')) {
      const toPage = document.getElementById('toPage').value
      if (toPage) {
        router.push({
          path: toPage
        });
      }
    }
  }

  onMounted(() => {
    document.body.style.setProperty('--el-color-primary', '#00B377');
    document.body.style.setProperty('--el-color-primary-light-9', '#F5FBF0');
    document.body.style.setProperty('--el-color-primary-light-5', '#00B377');
    document.body.style.setProperty('--el-color-primary-light-3', '#1EC78F');
    document.body.style.setProperty('--el-color-primary-dark-2', '#00B377');
  })

</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  width: 100%;
  height: auto;
  background: #F7F7F8;
}
</style>
