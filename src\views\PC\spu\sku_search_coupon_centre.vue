<template>
  <div style="background: #fff">
    <div class="wrapperBox" v-if="list.length">
      <tempMixedRow :goods-data="list" />
    </div>
  </div>
</template>

<script setup>
// import { actionTracking } from "@/config/eventTracking";
import { onMounted, ref, getCurrentInstance, onUnmounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
import tempMixedRow from "./skuSearchComponents/tempMixedRow.vue";
import { useStore } from "vuex";
import { useRoute } from "vue-router";

const route = useRoute();
const list = ref([]);
const store = useStore();


onMounted(() => {
    window.addEventListener('message', function(event) {
		console.log(event, "eventttt");
		if (event.data.action === 'getData') {
			// 检查消息来源是否安全（可选，但推荐）
			if (event.origin !== window.location.origin) return;
      list.value = event.data.arr;
		}
	});
});

</script>
<style lang="scss" scoped>
.wrapperBox {
}
</style>
