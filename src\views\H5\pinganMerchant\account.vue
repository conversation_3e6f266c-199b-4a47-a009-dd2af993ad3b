<template>
    <div class="account-page">
        <div style="height: 10px;"></div>
        <div class="account-item" style="margin-top: 0;">
            <div class="account-item-title" style="border: none;color: #292933;font-size: 17px;">
                企业信息
            </div>
            <div class="account-item-content" v-if="formModal">
                <div class="account-item-list">
                    <div class="account-span">企业名称</div>
                    <div class="account-p">{{ formModal.enterpriseName || ""}}</div>
                </div>
                <div class="account-item-list">
                    <div class="account-span">企业类型</div>
                    <div class="account-p">企业</div>
                </div>
                <div class="account-item-list">
                    <div class="account-span">统一社会信用代码</div>
                    <div class="account-p">{{ formModal.enterpriseRegistrationNo }}</div>
                </div>
                <div class="account-item-list">
                    <div class="account-span">企业法人姓名</div>
                    <div class="account-p">{{ formModal.corporationName }}</div>
                </div>
                <div class="account-item-list">
                    <div class="account-span">企业法人身份证号</div>
                    <div class="account-p">{{ (formModal.corporationIdNo||"").replace(/^(.{3})(?:\d+)(.{4})$/,
                        "$1***********$2") || "" }}</div>
                </div>
                <div class="account-item-list">
                    <div class="account-span">药店负责人手机号</div>
                    <div class="account-p">{{ formModal.corporatePhone }}</div>
                </div>
            </div>
        </div>
        <div class="account-item" style="overflow: hidden;">
            <div class="account-item-title" style="color: #292933;font-size: 17px;border: none;">绑定银行卡&nbsp;&nbsp;<span style="color: #292933;font-size: 17px;" v-if="(((formModal||{}).creditCardDtoList)||[]).length">({{(((formModal||{}).creditCardDtoList)||[]).length}})</span></div>
            <div class="account-item-content account-not-card" style="margin-bottom: 10px;"
                v-if="[33, 1, 2, 4, 8, 16].findIndex(item => item === cardStatus) > -1">
                <div style="width:100%;background: white;box-sizing: border-box;border-radius: 10px;">
             
            <!-- <div class="info-form-img">
              <img v-if="cardStatus === 0" src="@/assets/images/pinganMerchant/card-pgs.png" alt="">
              <img v-if="cardStatus === 1 || cardStatus === 2 || cardStatus === 4" src="@/assets/images/pinganMerchant/card-pgs-ing.png" alt="">
              <img v-if="cardStatus === 8 || cardStatus === 16" src="@/assets/images/pinganMerchant/card-pgs-error.png" alt="">
            </div> -->
            <div v-if="cardStatus === 33" style="text-align: center;">
              <img src="./img/noCar.png" alt="" style="width: 120px;"><br>
              <div> 还没有绑定银行卡，可点击“绑定新卡”进行申请</div>
            </div>
            <!-- <div class="account-item-text" v-if="cardStatus === 0">您还没有绑定银行卡，点击“前往绑卡”申请绑卡</div> -->
            <div class="account-item-text" v-if="cardStatus === 2">
              <div style="margin-bottom: 10px;color: #292933;font-size: 16px;" >绑卡申请记录</div>
              <span  @click="handleGoChangeBankCard" style="position: absolute;color: #00B377;right: 15px;top: 12px;"> 查看进度</span>
              银行卡申请中，审批周期预计5-10分钟，可点击“查看绑卡进度”查看详情
            </div>
            <div class="account-item-text" v-if="cardStatus === 8 || cardStatus === 16">
                <div style="margin-bottom: 10px;font-size: 16px;color: #292933;" >绑卡申请记录</div>
              <span  @click="handleGoChangeBankCard" style="position: absolute;color: #00B377;right: 15px;top: 12px;"> 查看进度</span>
              <span style="color: red;">银行卡审核失败，请点击“查看绑卡进度”查看失败原因</span>
            </div>
            <div class="account-item-text" v-if="cardStatus === 4">
                <div style="margin-bottom: 10px;font-size: 16px;color: #292933;" >绑卡申请记录</div>
              <span  @click="handleGoChangeBankCard" style="position: absolute;color: #00B377;right: 15px;top: 12px;">立即验证</span>
              <span style="color: red;">
                银行卡待验证，请在账户收到小额打款后48小时内进行打款验证，超时需要重新发起绑卡申请
              </span>
            </div>
         </div>
            </div>
            <div class="info-form" v-if="formModal && formModal.creditCardDtoList"
                style="background: white;padding:0 10px 10px 10px;">
                
                <div v-for="(item, index) in formModal.creditCardDtoList" class="account-item-content"
                    style="margin-top: 10px;padding: 10px 10px;background: #F7F7F8;border-radius: 10px;">
                    <div style="color: #292933;font-size: 16px;position: relative;">{{ item.bankName }} <span style="color: red;position: absolute;right: 4px;" @click="unBoundCardClick(item)">解绑</span></div>
                    <div class="account-item-list" style="margin-top: 15px;">
                        <div class="account-span">账户名</div>
                        <div class="account-p">{{ item.accountName }}</div>
                    </div>
                    <div class="account-item-list">
                        <div class="account-span">银行账号</div>
                        <div class="account-p">{{ (item.acct||"").replace(/^(.{3})(?:\d+)(.{4})$/, "$1* **** **** $2") || ""
                            }}</div>
                    </div>
                    <div class="account-item-list">
                        <div class="account-span">开户支行</div>
                        <div class="account-p">{{ item.branchBankName }}</div>
                    </div>
                </div>
            </div>
            <!-- <div class="account-item-content" v-if="cardStatus === 32">
                <div class="account-item-list">
                    <div class="account-span">账户名</div>
                    <div class="account-p">{{ formModal.accountName }}</div>
                </div>
                <div class="account-item-list">
                    <div class="account-span">银行账号</div>
                    <div class="account-p">{{ formModal.acct.replace(/^(.{3})(?:\d+)(.{4})$/, "$1* **** **** $2") || ""  }}</div>
                </div>
                <div class="account-item-list">
                    <div class="account-span">开户银行</div>
                    <div class="account-p">{{ formModal.bankName }}</div>
                </div>
                <div class="account-item-list">
                    <div class="account-span">开户支行</div>
                    <div class="account-p">{{ formModal.branchBankName }}</div>
                </div>
            </div> -->
        </div>
        <div style="color: rgb(0,198,117); margin: 20px 0 10px 10px;;cursor: pointer;" @click="jumpServicePdf">
            点击查看《平安银行“产业结算通”会员服务协议》</div>
        <div class="account-footer">
            <div class="account-btn" v-if="cardStatus === 33 || cardStatus==32" @click="handleGoChangeBankCard((((formModal||{}).creditCardDtoList)||[]).length)">绑定新卡</div>
        </div>
    </div>
</template>

<script setup>
import { queryPingAnAccountInfo } from '@/http/pinganMerchant';
import { unBoundCard } from '@/http/api';
import { ref, reactive, computed } from "vue";
import { useRouter, useRoute } from 'vue-router';
import {ElLoading,ElMessage } from 'element-plus';
import { showToast ,showDialog } from 'vant';
const router = useRouter();
const route = useRoute();
const cardStatus = ref(null);  // 银行卡信息 0未绑卡  1审核中  2失败 3待验证 4已绑定
//lwq
const { merchantId } = route.query;

const btnValue = computed(() => {
    return cardStatus.value === 32 ? '换绑银行卡' : '查看绑卡进度';
})
let formModal = ref(null);
const handleGoChangeBankCard = (res) => {
    if(res>=5){
        showToast('每个平安商户最多绑定5张对公卡，当前已达上限。请解绑已有卡后再次尝试');
    return
  }
    router.push({
        path: '/pinganMerchant/changeBankCard',
        query: { merchantId,from:"account" }
    });
};
queryPingAnAccountInfo({ merchantId }).then((res) => {
    if (res.code === 1000) {
        const { data } = res;
        formModal.value = data;
        cardStatus.value = data.status;
    }else{
        //lwq
    //     formModal.value={
    //     "backAccountName": "平安银行电子商务交易资金待清算专户(药帮忙）",
    //     "backAccountNo": "**************",
    //     "backName": "平安银行昆明分行营业部",
    //     "backNo": "************",
    //     "corporationIdNo":"**************",
    //     "creditCardDtoList":[
    //     {"accountName":"洛阳市康乡医药有限责任公司","branchBankName":"中原银行股份有限公司洛阳春都路支行","branchBankCd":"aliqua","bankName":"建设银行","acct":"***************","mobileNo":"occaecat exercitation"},
    //     {"accountName":"洛阳市康乡医药有限责任公司","branchBankName":"中原银行股份有限公司洛阳春都路支行","branchBankCd":"aliqua","bankName":"建设银行","acct":"***************","mobileNo":"occaecat exercitation"},
    //     {"accountName":"洛阳市康乡医药有限责任公司","branchBankName":"中原银行股份有限公司洛阳春都路支行","branchBankCd":"aliqua","bankName":"建设银行","acct":"***************","mobileNo":"occaecat exercitation"},
    //     {"accountName":"洛阳市康乡医药有限责任公司","branchBankName":"中原银行股份有限公司洛阳春都路支行","branchBankCd":"aliqua","bankName":"建设银行","acct":"***************","mobileNo":"occaecat exercitation"},
    //     {"accountName":"洛阳市康乡医药有限责任公司","branchBankName":"中原银行股份有限公司洛阳春都路支行","branchBankCd":"aliqua","bankName":"建设银行","acct":"***************","mobileNo":"occaecat exercitation"},
    // ]}
    //     cardStatus.value = 32
    }
})
// 跳转协议
const jumpServicePdf = () => {
    window.location.href = `https://my.orangebank.com.cn/orgLogin/hd/act/jianzb/B2BClearing.html?name=${formModal.value.accountName == null ? "" : formModal.value.accountName}`;
}
//解绑
const unBoundCardClick = (item) => {
    showDialog({
        title: '提示',
        message: '确认解绑当前银行卡吗？',
        showCancelButton:true,
        confirmButtonColor:"#00B955"
    }).then(() => {
        let loadingService = ElLoading.service({
            text: "解绑中",
            size: "small"
        })
        let params = {
            merchantId,
            acct: item.acct,
            mobileNo: item.mobileNo,
            accountName: item.accountName
        }
        unBoundCard(params).then((res) => {
            if (res.code === 1000) {
                queryPingAnAccountInfo({merchantId}).then(res=>{
                    if (res.code === 1000) {
                        const { data } = res;
                        formModal.value = data;
                        cardStatus.value = data.status;
                    }
                });
                setTimeout(() => {
                    showToast("解绑成功");
                }, 500)
            } else {
                showToast(res.msg);
            }
        }).finally(() => {
            setTimeout(() => {
                loadingService.close();
            }, 500)
        })
    });

}
</script>

<style lang="scss" scoped>
.account-page {
    height: 100vh;
    width: 100%;
    background: #F7F7F8;
    position: relative;
    .account-item {
        border-radius: 10px;
        margin: 0 10px;
        margin-top: 10px;
        margin-top: 10px;
        background: #FFFFFF;

        display: flex;
        flex-direction: column;
        .account-item-title {
            height: rem(88);
            font-family: PingFangSC-Medium;
            font-weight: 500;
            font-size: rem(30);
            color: #292933;
            padding: 0 10px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #eee;
        }
        .account-item-content:nth-child(1){
           margin-top: 0px !important;
        }
        .account-item-content {
            display: flex;
            flex-direction: column;
            padding: rem(0) rem(20);
            padding-bottom: 0;
            box-sizing: border-box;
            .account-item-list {
                display: flex;
                margin-bottom: rem(30);
                font-family: PingFangSC-Regular;
                font-weight: 400;
                font-size: rem(26);
                .account-span {
                    width: 36%;
                    display: flex;
                    white-space: nowrap;
                    color: #676773;
                }
                .account-p {
                    flex: 1;
                    color: #292933;
                    // white-space: nowrap;
                }
            }
        }
        .account-not-card {
            display: flex;
            align-items: center;
            justify-content: center;
            // padding: rem(40) 0 rem(100) 0;
            box-sizing: border-box;
            .account-item-img {
                width: rem(120);
                height: rem(120);
                img {
                    width: 100%;
                    height: 100%;
                }
            }
            .account-item-text {
                position: relative;
                background: #F7F7F8;
                padding:12px 10px;
                border-radius: 10px;
                margin-bottom: 0;
                line-height: 18px;
                box-sizing: border-box;
                font-family: PingFangSC-Regular;
                font-weight: 400;
                font-size: rem(24);
                color: #676773;
                // margin-top: rem(60);
                text-align: left;
                .warn-span {
                    color: #FF7B03;
                }
                .error-span {
                    color: #FF2121;
                }
                > p {
                    margin-top: rem(8);
                }
                .account-item-tips {
                    padding: 0 rem(20);
                    box-sizing: border-box;
                    margin-top: rem(30);
                    .account-item-tips-content {
                        background: #FFF7EF;
                        border-radius: rem(4);
                        padding: rem(20);
                        box-sizing: border-box;
                        font-family: PingFangSC-Regular;
                        font-weight: 400;
                        font-size: rem(24);
                        color: #99664D;
                        text-align: left;
                        line-height: rem(35);
                    }
                }
            }
        }
    }
    .account-footer {
        width: 100%;
        background: #F7F7F8;
       
        padding: rem(20) rem(40);
        box-sizing: border-box;
        .account-btn {
            height: rem(88);
            background: #00B955;
            border-radius: 4px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            font-size: rem(32);
            color: #FFFFFF;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}
</style>