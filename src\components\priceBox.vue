<template>
    <div class="priceBox">
        <span v-if="uniformPrice">
            控销价¥{{uniformPrice.toFixed(2)}}
        </span>
        <span v-if="suggestPrice">
            零售价¥{{ suggestPrice.toFixed(2)}}
        </span>
        <span class="gross-margin" v-if="grossMargin&&!(status == 2)">
            (毛利率{{grossMargin}})
        </span>
    </div>
</template>

<script setup>

    const props = defineProps({
        uniformPrice: {
            default: ''
        },
        suggestPrice: {
            default: ''
        },
        grossMargin: {
            default: ''
        },
        status: {
            default: ''
        },
        
    });
</script>

<style lang="scss" scoped>
    .priceBox {
        width: 100%;
        color: rgba(148, 148, 166, 1);
        font-size: rem(24);
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }

</style>
