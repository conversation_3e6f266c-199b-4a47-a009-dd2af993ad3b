<template>
  <div style="background: #fafafa">
    <div class="wrapperBox" v-if="list.length">
      <tempMixedRow :scmE="scmE" :goods-data="list" :qtData="qtData" />
      <!-- <tempMixedRow
        :goods-data="list"
        :license-status="licenseStatus"
        :searchSortStrategyId="searchSortStrategyId"
        :spid="trackInfo.spid"
        :track-info="trackInfo"
      /> -->
    </div>
  </div>
</template>

<script setup>
import { findSpuAggregatSku } from "@/http_pc/productSpu/index";
// import { actionTracking } from "@/config/eventTracking";
import { onMounted, ref, getCurrentInstance, onUnmounted, nextTick ,reactive} from "vue";
import { ElMessage } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { getAllCategory, getV2Aggs, getProductLists } from "@/http_pc/api";
import { getUrlParam, exposureView } from "@/utils/index";
import tempMixedRow from "./components/myMixedRow.vue";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
import  install from "@/utils/qt"
install()
const { proxy } = getCurrentInstance();
const route = useRoute();
const list = ref([]);
const store = useStore();
let scmE=ref('')
const loadData = (from) => {
  // let params = getParams(from);
  store.commit("app/showLoading", true);
  // findSpuAggregatSku({ id: 7503, merchantId: 1500124955 });
  console.log(route.query, window.parent.origin, window.location.origin);
  function removeIframe() {
    console.log("移除iframe");
    window.parent.postMessage(
      {
        action: "removeIframe",
      },
      `${window.location.origin}/search/skuDetail/${route.query.id}.htm`
      // `https:${import.meta.env.VITE_BASE_URL_PC}search/skuDetail/${route.query.id}.htm`
      // `https:${"//new-www.test.ybm100.com/"}search/skuDetail/62565142.htm`
    ); // 通知父网页移除iframe
  }
  function breackCur(){
    store.commit("app/showLoading", false);
    removeIframe();
  }
  if(route.query.isVirtual=="virtual"){
    breackCur();
    return;
  }
  setTimeout(() => {
    if(list.value.length == 0) breackCur();
  }, 10000); // 10秒后还没拿到数据就移除iframe

  findSpuAggregatSku({ id: route.query.id, merchantId: route.query.merchantId })
    .then((res) => {
      // getProductLists(params).then((res) => {
      if (res.status == "success") {
        scmE.value=res.data.scmE
        // alert( scmE.value)
        list.value = (res.data || {}).rows || [];
        if (list.value.length > 0) return;
      } else if (res.code == 5000) {
        // removeIframe()
        ElMessage.error(res.msg);
      }
      removeIframe();
    })
    .catch((err) => {
      removeIframe();
    })
    .finally(() => {
      store.commit("app/showLoading", false);
    });
};
onMounted(() => {
  loadData();
  // console.log(import.meta.env.VITE_BASE_URL_PC);
  // window.addEventListener("scroll", handleScrollStart);
});

onUnmounted(() => {
  // window.removeEventListener("scroll", handleScrollStart);
});
let qtData=reactive({
  commonName:"productDetail",
  isShopDetail:route.query.isShopDetail,
  id:route.query.id,
})
</script>
<style lang="scss" scoped>
.wrapperBox {
  width: 1200px;
  // height: 355px;
  background: #fff;
  margin: 0 auto;
  // padding-bottom: 30px;
  // padding-top: 10px;
  font-size: 12px;
}
</style>
