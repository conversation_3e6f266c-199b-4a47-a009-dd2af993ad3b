import http from '../index';

// https://yapi.int.ybm100.com/project/65/interface/api/cat_8716 平安数字贷

//创建平安账户
export function createPingAnAccount(params){
	return http.post('/app/pinganaccount/createPingAnAccount', params)
}

export function createPingAnAccountNew(params){
	return http.post('/app/pinganaccount/boundCard', params)
}

//查询开户信息
//	return http.get(`/app/pinganaccount/queryPingAnAccountInfo?t=${new Date().getTime()}`, params)
export function queryPingAnAccountInfo(params){
	return http.get('/app/pinganaccount/queryPingAnAccountInfo', params)
}

//查询账户余额
export function queryPingAnAccountBalance(params){
	return http.get('/app/pinganaccount/queryPingAnAccountBalance', params)
}

//查询资金待清算银行信息
export function queryTopUpBankCardInfo(params){
	return http.get('/app/pinganaccount/queryTopUpBankCardInfo', params)
}

//查询银行列表
export function queryBankList(params){
	return http.get('/app/pinganaccount/queryBankList', params)
}

//查询平安开户银行支行列表
export function querySubBankList(params){
	return http.get('/app/pinganaccount/querySubBankList', params)
}

//验证平安账户
export function paymentAuth(params){
	return http.post('/app/pinganaccount/paymentAuth', params)
}

//查询企业信息
export function queryCompanyInfo(params){
	return http.get('/app/pinganaccount/queryCompanyInfo', params)
}

//发送验证码
export function sendVerificationCode(params){
	return http.get('/app/pinganaccount/sendVerificationCode', params)
}
//校验支付密码
export const payPwdQueryState = (params) => {
	return http.post('/app/payPwd/queryState', params);
}

//购物金转入
export const goldTransferIn = (params) => {
	return http.postFormData('/app/virtual/gold/transferIn', params);
}
//购物金查询
export const virtualGold = (params) => {
	return http.post('/app/virtualGold/query', params);
}
//购物金转出
export const goldTransferOut = (params) => {
	return http.postFormData('/app/virtual/gold/transferOut', params);
}
//app查询充值结果
export const getResultByTranNo = (params) => {
	return http.postFormData('/app/virtual/gold/getResultByTranNo', params);
}
// 流水号
export const getTranNo = (params) => {
	return http.post('/app/virtual/gold/getTranNo', params);
}
// 平台代收款说明
export const getPaymentDesc = (params) => {
	return http.post('/app/agreement/instructions', params);
}
// 购物金充值活动信息
export const getRechargeDiscount = (params) => {
	return http.post('/app/virtual/gold/getRechargeDiscount', params);
}
// App-平安商户明细
export const getPingAnMerchantDetail = (params) => {
	return http.post('/app/pinganaccount/queryPayRecordsForApp', params);
}
