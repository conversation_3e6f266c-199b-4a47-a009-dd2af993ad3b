<!-- 加减购物车按钮，使用示例在temprow.vue组件 -->
<template>
  <div class="btn-container">
    <div class="content">
      <div class="spread-add">
        <div class="reduce" @click.stop.prevent="addProductCart('min')">-</div>
        <div class="cont">
          <input
            @click.stop.prevent
            class="input-val"
            :class="'input-val'+goodsId"
            type="tel"
            :value="productValue"
            @change="inputCart"
            @blur="handleCheckValue"
            :disabled="productData.status === 2"
          />
        </div>
        <div class="plus" @click.stop.prevent="addProductCart('add')">+</div>
      </div>
      <div class="addPurchaseOrder soudOutBtn" v-if="productData.status === 2">已售罄</div>
      <div class="addPurchaseOrder skBtn" v-else-if="goodsType === 'actSk'" @click.stop.prevent="addProductCart()">立即抢购</div>
      <div class="addPurchaseOrder pgbyBtn" v-else-if="goodsType === 'actPgby'" @click.stop.prevent="toDetail(productData.id)">去抢购</div>
      <div class="addPurchaseOrder" v-else @click.stop.prevent="addProductCart()">加入采购单</div>
    </div>
  </div>
</template>

<script setup>
import { putRequest } from "@/http_pc/api";
import { ElMessage } from 'element-plus';
import { actionTracking } from '@/config/eventTracking';
import { onMounted, ref, watch, getCurrentInstance } from "vue";
import { useStore } from 'vuex';
import { formatDate } from '@/utils/index';
  const props = defineProps({
    productData: {
      default: {}
    },
    trackInfo: {
      default: {}
    },
    goodsType: {
      default: ''
    } ,
    jgAgentInfo: {
      default: {}
    },
    keyWord: {
      default: "",
    },
    searchSortStrategyId: {
      default: ""
    },
    index: {
      default: ''
    },
  });
  const { proxy } = getCurrentInstance();
  const store = useStore();
  const productValue = ref('');
  const is_split = ref('');
  const goodsId = ref('');
  const med_num = ref('');
  const isPack = ref(false); // 套餐
  onMounted(() => {
    goodsId.value = (props.productData || {}).id;
    productValue.value = (props.productData || {}).cartProductNum ? (props.productData || {}).cartProductNum : 0;
    is_split.value = (props.productData || {}).isSplit;
    med_num.value = parseInt((props.productData || {}).mediumPackageNum);
  })

  const postCartData = (val) => {
      // WS.Bus.loading = true;
      console.log('proxy.$merchantId', proxy.$merchantId);
      if (val > 0) {
        const config = isPack.value == true ? {
          'merchantId': proxy.$merchantId,
          'amount': val,
          "packageId": goodsId.value
        } : { 'merchantId': proxy.$merchantId, 'amount': val, "skuId": goodsId.value }
        // let jgInfo = JSON.parse(sessionStorage.getItem("jgInfo")) || null;
        // if (jgInfo) {
        //   jgInfo.direct = "1";
        //   config.mddata = JSON.stringify(jgInfo);
        // }
        putRequest('post', '/merchant/center/cart/changeCart.json', config).then((res) => {
          // WS.Bus.loading = false;
          if (res.status == "success") {
            if (res.data.qty != val) {
              productValue.value = parseInt(res.data.qty);
            }
            
            if (res.dialog != null) {
              if (res.dialog.style == 20) {
                ElMessage.error(res.dialog.msg)
              } else {
                ElMessage.error(res.dialog.msg)
              }
            }
            if (res.data.message) {
              ElMessage.error(res.data.message);
            } else {
              // ElMessage.success('加购成功！')
              store.commit('app/changeprompt', { promptmsg: '加购成功！', showprompt: true })
              window.parent.postMessage({changeSubTotal: true}, '*');
              getCartNum();
            }

            // try {
            //   const addconfig = isPack.value == true ? {
            //     proid: goodsId.value,
            //     pronum: productValue.value,
            //     isAdd: 1,
            //     type: 1
            //   } : { proid: goodsId.value, pronum: productValue.value, isAdd: 1 }
            //   // window.webkit.messageHandlers.addPlanNumber.postMessage(addconfig);//ios
            // } catch (erro) {

            // }
            ;
          } else {
            productValue.value = 0;
            ElMessage.error(res.errorMsg || res.msg || res.dialog.msg);
          }
        }).catch((err) => {
          console.log('errrrrr', err);
          // WS.Bus.loading = false;
        })
      } else {
        const config = isPack.value == true ? { "packageIds": goodsId.value } : { "ids": goodsId.value }
        putRequest('post', `/app/batchRemoveProductFromCart`, config).then((res) => {
          if (res.status == "success") {
            productValue.value = 0;
            // try {
            //   const addconfig = isPack.value == true ? {
            //     proid: goodsId.value,
            //     pronum: 0,
            //     isAdd: 1,
            //     type: 1
            //   } : { proid: goodsId.value, pronum: 0, isAdd: 1 }
            //   window.webkit.messageHandlers.addPlanNumber.postMessage(addconfig);//ios
            // } catch (erro) {

            // }
            // ;
            // try {
            //   if (isPack.value == true) {
            //     window.hybrid.addPlanNumber(goodsId.value, 0, 1, 1); //android
            //   } else {
            //     window.hybrid.addPlanNumber(goodsId.value, 0, 1); //android
            //   }
            // } catch (erro) {

            // }
            // ;
          }
        })
      }
    }

  const getCartNum = () => {
    putRequest('get', '/merchant/center/cart/getCartNum', {
      r: Math.random(),
      num:0
    }).then((res) => {
      if(res.status === "success") {
        const numId = window.parent.document.getElementById('rigthCartNum');
        console.log('加购成功之后', numId);
        if (numId && res.num) {
          numId.className = 'topp cycle2';
          numId.innerText = res.num;
        }
      }
    })
  }
  const detailUrl = import.meta.env.VITE_BASE_URL_PC;
  const toDetail = (id) => {
    // actionTracking('pc_page_CommodityDetails', {
    //   spid: props.trackInfo.spid,
    //   sid: props.trackInfo.sid,
    //   sptype: props.trackInfo.sptype,
    //   user_id: merchantId,
    //   commodityId: id,
    // })
    postCartData(productValue.value);
    window.open(`${detailUrl}search/skuDetail/${id}.htm`);
  }
  
  const addProductCart = (type) => {
    if (type === "add") {
      productValue.value += med_num.value;
      return false;
      // this.addType = 3;
    } else if (type === "min") {
      if (productValue.value > 0) {
        productValue.value = is_split.value == 1 ? productValue.value - 1 : productValue.value - med_num.value;
        // this.addType = 1;
      }
      return false;
    }
    if (productValue.value == 0) {
      ElMessage.error('请输入购买数量！');
      return false;
    }
    actionTracking('加入采购单', {
      spid: props.trackInfo.spid,
      sid: props.trackInfo.sid,
      sptype: props.trackInfo.sptype,
      user_id: proxy.$merchantId,
      commodityId: goodsId.value,
      search_sort_strategy_id: props.trackInfo.searchSortStrategyId || null,
    })
    
    postCartData(productValue.value);
  }
  const inputCart = (e) => {
    let num = parseInt(e.target.value);
    num = num > 0 ? num : 0;
    productValue.value = num;
    // this.addType = 2;
  }
  const getPrice = (item) => {
    if (item.actPt) {
      if (item.actPt.stepPriceStatus == 1) {
        //阶梯\
        return item.actPt.minSkuPrice;
      } else if (item.actPt.stepPriceStatus == 2 && item.actPt.assembleStatus == 1) {
        return item.actPt.assemblePrice;
      }
      return ''
    } else if (item.actPgby) {
      return item.actPgby.assemblePrice;
    } else if (item.priceType == 2 && item.skuPriceRangeList) {
      return item.skuPriceRangeList[item.skuPriceRangeList.length -1].price;
    } else {
      return item.fob
    }
  }
  const handleCheckValue = (e) => {
    let num = parseInt(e.target.value);
    if (is_split.value == 0) {
      const remainder = num % med_num.value;
      if (remainder > 0) {
        productValue.value = num - remainder;
      }
    }
  }
  
   

</script>

<style lang="scss" scoped>
div {
  box-sizing: border-box;
}

.btn-container {
  margin: 20px 0 10px 0;
  .addPurchaseOrder {
    z-index: 9;
    cursor: pointer;
    width: 100px;
    height: 42px;
    margin-left: 10px;
    background: #00B955;
    border-radius: 2px;
    font-size: 16px;
    font-family: MicrosoftYaHei;
    text-align: center;
    color: #ffffff;
    line-height: 42px;
  }
  .skBtn {
    background-image: linear-gradient(90deg, #FEA527 0%, #FE5427 100%);
  }
  .pgbyBtn {
    background-image: linear-gradient(-64deg, #FF223B 0%, #FF834A 100%, #FF834A 100%, #FF834A 100%, #FF834A 100%);
  }
  .soudOutBtn {
    background: #838A93;
  }
}

.content {
  height: 28px;
  display: flex;
  align-items: center;
}

.spread-add {
  display: flex;
  display: -webkit-flex;
  flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  box-sizing: border-box;
  z-index: 9;

  .plus,
  .reduce {
    width: 32px;
    height: 42px;
    line-height: 42px;
    color: #757575;
    text-align: center;
    background: #EFEFEF;
    cursor: pointer;
    font-size: 20px;
  }

  .plus {
    border-left: 1px solid #d7d7d7;
    border-radius: 0px 2px 2px 0px;
  }

  .reduce {
    border-right: 1px solid #d7d7d7;
    border-radius: 2px 0px 0px 2px;
  }

  .cont {
    width: 42px;
    .input-val {
      box-sizing: border-box;
      padding: 0;
      width: 100%;
      text-align: center;
      color: #292933;
      border: none;
      outline: none;
      font-size: 16px;
      font-family: MicrosoftYaHei;
      text-align: left;
      color: #333333;
      line-height: 42px;
      text-align: center;
    }
  }
}
</style>
