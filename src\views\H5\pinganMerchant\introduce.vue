<template>
  <div class="introduce">
    <div class="title flex">
      什么是平安商户?
      <img class="logo" src="@/assets/images/pinganMerchant/logo.png" />
    </div>
    <p class="text">
      平安商户是药帮忙联合平安银行推出的药品流通行业<br>
      资金支付解决方案，帮助药店一站式解决<br>
      支付、税务、金融等问题
    </p>
    <div class="content">
      <img src="@/assets/images/pinganMerchant/cont.png" />
    </div>
    <div class="content pt20">
      <img src="@/assets/images/pinganMerchant/cont2.png" />
    </div>
    <div v-if="!hide" class="openBtn">
      <div class="open" @click="handleOpen">立即开通</div>
    </div>
  </div>
</template>

<script setup>
  import { ref } from "vue";
  import { useRouter, useRoute } from 'vue-router';
//   import Bridge from "@/config/Bridge";
// Bridge.setAppRightMenu("0")
  const router = useRouter();
  const route = useRoute();
  const hide = ref(false);
  const { from, merchantId } = route.query;
  if (from) {
    hide.value = true;
  }
  
  const handleOpen = () => {
    router.push({
      path: '/pinganMerchant/open',
      query: { merchantId }
    });
  };
</script>
<style lang="scss" scoped>
#app {
  background: #fff;
}
.introduce {
  background: url("@/assets/images/pinganMerchant/introduce-bg.png") no-repeat;
  background-color: #F7F7F8;
  min-height: 100%;
  background-size: 100%;
  padding: rem(47) 0 rem(128) rem(34);
  .flex {
    display: flex;
    align-items: center;
  }
  .title {
    font-family: AlimamaShuHeiTi-Bold;
    font-weight: 700;
    font-size: rem(40);
    color: #FFFFFF;
    line-height: rem(40);
    .logo {
      width: rem(180);
      height: rem(32);
      margin-left: rem(20);
    }
  }
  .text {
    font-size: rem(24);
    color: #FFFFFF;
    line-height: rem(36);
    padding-top: rem(23);
  }
  .content {
    padding: rem(40) rem(30) 0 0;
  }
  .pt20 {
    padding-top: rem(20);
  }
  .openBtn {
    width: 100%;
    background: #FFFFFF;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: rem(20) rem(40);
    box-sizing: border-box;
    .open {
      background: #FF3000;
      border-radius: rem(44);
      height: rem(88);
      line-height: rem(88);
      font-weight: 500;
      font-size: rem(32);
      color: #FFFFFF;
      text-align: center;
    }
  }
}
</style>