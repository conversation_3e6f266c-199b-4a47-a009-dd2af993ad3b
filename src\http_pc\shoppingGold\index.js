import http from '../index';

export const getRechargeDiscount = (params) => {
	return http.post('/pc/virtual/gold/getRechargeDiscount',params)
}
// 获取购物金余额
export const getGoldBalance = () => {
	return http.post('/pc/virtual/gold/queryMyVirtualGold')
}
//我的购物金流水
export const getGoldFlow = (params) => {
	return http.post('https://yapi.int.ybm100.com/mock/65/pc/virtual/gold/queryMyRecord', params)
}
// PC-在线支付
export const onlinePay = (params) => {
	return http.post('/shop/payment', params)
}