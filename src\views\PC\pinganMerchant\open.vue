<template>
  <div class="open">
    <div class="my flex">
      <span></span>
      我的平安商户
      <div style="display: inline-block; margin-left: auto; color: rgb(0,198,117);font-weight: 600;font-size: 16px;" @click="jumpServicePdf">《平安银行“产业结算通”会员服务协议》</div>
    </div>
    <div class="status">
      <div class="top flex">
        开户状态：
        <p :style="{color: statusObj.color || '#7A2809'}" class="flex1">{{ statusObj.name }}</p>
      </div>
      <div class="bottom flex">
        {{ statusObj.text }}
        <div v-if="type === 16" class="rejectionReason flex" @click="handleCheckAuditFailReason">
          查看驳回原因
        </div>
      </div>
    </div>
    <el-form class="form" ref="formModalRef" :model="formModal" :rules="rules">
      <el-form-item label="统一社会信用代码:" prop="enterpriseRegistrationNo" class="item" label-width="140px">
        <!-- <p>统一社会信用代码</p> -->
        <el-input placeholder="请输入企业营业执照编号" maxlength="20" v-model.trim="formModal.enterpriseRegistrationNo" style="width: 100%" :disabled="disabled" />
      </el-form-item>
      <el-form-item label="公账户名:" class="item" label-width="140px">
        <!-- <p>公账户名</p> -->
        <el-input placeholder="" v-model.trim="formModal.accountName" style="width: 100%" readonly />
      </el-form-item>
      <el-form-item label="公账账户:" prop="acct" class="item" label-width="140px">
        <!-- <p>公账账户</p> -->
        <el-input placeholder="请输入企业对公账户号" maxlength="30" v-model.trim="formModal.acct" style="width: 100%" :disabled="disabled && failDisabled"/>
      </el-form-item>
      <el-form-item label="开户银行:" prop="bankName" class="item" label-width="140px">
        <!-- <p>开户银行</p> -->
        <el-select
          v-model="formModal.bankName"
          placeholder="请选择公账账户开户银行，支持模糊搜索"
          style="width: 100%"
          :filterable="true"
          :filter-method="getBankList"
          @change="bankChange"
          :key="formModal.bankName"
          :disabled="disabled && failDisabled"
        >
          <el-option
            v-for="item in bankList"
            :key="item.bankName"
            :label="item.bankName"
            :value="item.bankName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="开户支行:" prop="branchBankName" class="item" label-width="140px">
        <!-- <p>开户支行</p> -->
        <el-select
          v-model="formModal.branchBankName"
          placeholder="请选择公账账户开户银行，支持模糊搜索"
          style="width: 100%"
          :filterable="true"
          :filter-method="searchSubBank"
          @change="subBankChange"
          :key="formModal.branchBankName"
          :disabled="disabled && failDisabled"
        >
          <el-option
            v-for="item in subBankList"
            :key="item.bankName"
            :label="item.bankName"
            :value="item.bankName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="企业法人姓名:" prop="corporationName" class="item" label-width="140px">
        <!-- <p>企业法人姓名</p> -->
        <el-input placeholder="请输入企业法人姓名" maxlength="10" v-model.trim="formModal.corporationName" style="width: 100%" :disabled="disabled && failDisabled" />
      </el-form-item>
      <el-form-item label="企业法人身份证号:" prop="corporationIdNo" class="item" label-width="140px">
        <!-- <p>企业法人身份证号</p> -->
        <el-input placeholder="请输入企业法人身份证号" maxlength="30" v-model.trim="formModal.corporationIdNo" style="width: 100%" :disabled="disabled && failDisabled" />
      </el-form-item>
      <el-form-item label="药店负责人手机号:" prop="corporatePhone" class="item" label-width="140px">
        <!-- <p>药店负责人手机号</p> -->
        <el-input placeholder="请输入企业负责人手机号" maxlength="11" v-model.trim="formModal.corporatePhone" style="width: 100%" :disabled="disabled && failDisabled" />
      </el-form-item>
      <el-form-item label="门店数" prop="storeQty" v-if="isKa" class="item" label-width="140px">
        <!-- <p>药店负责人手机号</p> -->
        <el-input placeholder="请输入门店数" maxlength="11" oninput="value=value.replace(/[^0-9]/g, '')" v-model.trim="formModal.storeQty" style="width: 100%" :readonly="disabled && failDisabled" />
      </el-form-item>
      <el-form-item label="公帐收到的金额:" prop="publicPrice" class="item" v-if="type === 4" label-width="140px">
        <!-- <p>公帐收到的金额</p> -->
        <el-input placeholder="请输入企业对公账户收到的金额" maxlength="10" v-model.trim="formModal.publicPrice" style="width: 100%" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g,'$1')" />
      </el-form-item>
      <el-form-item label="鉴权序号:" prop="signNo" class="item" v-if="type === 4" label-width="140px">
        <!-- <p>鉴权序号</p> -->
        <el-input :placeholder="`请输入发送至${formModal.corporatePhone.replace( /^(\d{3})\d{4}(\d{4})$/, '$1****$2')}的短信鉴权序号`" v-model.trim="formModal.signNo" style="width: 100%" maxlength="6" />
      </el-form-item>
      <!-- v-if="type === 4" -->
      <div v-if="type === 4" class="item" style="margin-bottom: 18px" >
        <el-checkbox
					v-model="checkAgreement"
          style="height: auto"
				/>
        <div class="agreement">我已阅读并同意<span @click="handleCheckAgreement('https://my.orangebank.com.cn/orgLogin/hd/act//jianzb/jzbxym.html')">《平安银行电子商务“见证宝”商户服务协议》</span>和<span @click="handleCheckAgreement('https://auth.orangebank.com.cn/#/m/cDealOne?showNavBar=1')">《平安银行数字口袋协议》</span></div>
      </div>
      <el-form-item label="短信验证码:" prop="activeCode"  class="item" v-if="type === 0 || type === 16" label-width="140px">
        <!-- <p>短信验证码</p> -->
        <div class="flex">
          <el-input style="width: 209px;" class="flex1" placeholder="请输入短信验证码" v-model.trim="formModal.activeCode" />
          <el-button class="wid80" type="primary" @click="sendActiveCode" :disabled="sendDisabled">获取验证码{{ sendDisabled ? `(${sendCountDown})` : '' }}</el-button>
        </div>
      </el-form-item>
    </el-form>
    <div class="submit">
      <el-button v-if="type !== 2 && type !== 4 && type !== 32" style="width: 100%" type="primary" @click="create" >提交申请</el-button>
      <el-button v-if="type === 4" style="width: 100%" type="primary" @click="verify">提交验证</el-button>
    </div>
  </div>
</template>

<script setup>
  import { reactive, ref, computed, onMounted } from "vue";
  import { queryPingAnAccountInfo, queryBankList, querySubBankList, createPingAnAccount, paymentAuth, queryCompanyInfo, sendVerificationCode } from '@/http_pc/api';
  import { useRouter, useRoute } from 'vue-router';
  import { ElMessage, ElMessageBox } from 'element-plus';

  const router = useRouter();
  const route = useRoute();
  const { merchantId } = route.query;
  const formModalRef = ref(null);
  const type = ref(-1);
  const sendDisabled = ref(false);
  const checkAgreement = ref(false);
  const sendCountDown = ref(60);
  const disabled = ref(true);
  const failDisabled = ref(true);
  const auditFailReason = ref('');
  const formModal = reactive({
    enterpriseRegistrationNo: '',
    accountName: '',
    acct: '',
    bankName: '',
    branchBankCd: '',
    branchBankName: '',
    corporationName: '',
    corporationIdNo: '',
    corporatePhone: '',
    activeCode: '',
    publicPrice: '',
    signNo: '',
    storeQty: ''
  });
  const rules = {
    enterpriseRegistrationNo: [
      { required: true, message: '请输入企业营业执照编号', trigger: 'blur' },
    ],
    acct: [
      { required: true, message: '请输入企业对公账户号', trigger: 'blur' },
    ],
    bankName: [
      { required: true, message: '请输入企业开户银行', trigger: 'blur' },
    ],
    branchBankName: [
      { required: true, message: '请输入企业开户支行', trigger: 'blur' },
    ],
    corporationName: [
      { required: true, message: '请输入企业法人姓名', trigger: 'blur' },
    ],
    corporationIdNo: [
      { required: true, message: '请输入企业法人身份证号', trigger: 'blur' },
    ],
    corporatePhone: [
      { required: true, message: '请输入企业负责人手机号', trigger: 'blur' },
    ],
    storeQty: [
      { required: true, message: '请输入门店数', trigger: 'blur' },
    ],
    publicPrice: [
      { required: true, message: '请输入企业对公账户收到的金额', trigger: 'blur' },
    ],
    signNo: [
      { required: true, message: `请输入发送至${formModal.corporatePhone.replace( /^(\d{3})\d{4}(\d{4})$/, '$1****$2')}的短信鉴权序号`, trigger: 'blur' },
    ],
    activeCode: [
      { required: true, message: '请输入短信验证码', trigger: 'blur' },
    ]
  }

  const bankList = ref([]);
  const subBankList = ref([]);
  const isKa = ref(0);

  const queryAccountInfo = () => {
    queryPingAnAccountInfo({ merchantId }).then((res) => {
      if (res.code === 1000) {
        const { data } = res;
        if (data.status === 8) {
          data.status = 16
        }
        type.value = data.status;
        isKa.value = data.isKa || 0;
        if (type.value === 0) {
          disabled.value = false;
          failDisabled.value = false;
          queryCompanyInfo({ merchantId }).then((res) => {
            if (res.code === 1000) {
              formModal.accountName = res.data.corporateAccountName;
            }
          });
        }
        if (type.value === 2 || type.value === 4 || type.value === 32) {
          disabled.value = true;
          failDisabled.value = true;
        }
        if (type.value === 16) {
          failDisabled.value = false;
          auditFailReason.value = data.message;
        }
        if (data.status && data.status !== 0) {
          Object.keys(formModal).forEach((key) => {
            formModal[key] = data[key];
          });
        }
      }
    })
  };

  if (merchantId) {
    queryAccountInfo();
  }

  // CREATE(1, "创建中"),
  // AUDITING(2, "审核中"),
  // PASS(4, "待验证"),
  // REJECT(8, "审核驳回"),
  // FAIL(16, "失败"),
  // COMPLETE(32, "完成"),
  // 0 未开户
  const statusObj = computed(() => {
    const obj = {};
    if (type.value === 0) {
      obj.name = '未开户';
      obj.text = '开通平安商户，需绑定结算银行卡信息，用于商户余额充值、提现';
    }
    if (type.value === 2) {
      obj.name = '银行卡审核中';
      obj.text = '您的开户申请已提交至银行进行人工审批，审批周期预计5-10分钟，请耐心等待!';
    }
    if (type.value === 16) {
      obj.name = '银行卡审核失败！';
      obj.color = '#FE2021';
      obj.text = '您的开户信息银行审核未通过或超时未做打款验证，请参考驳回原因编辑信息重新提交';
    }
    if (type.value === 4) {
      obj.name = '银行卡待验证';
      obj.text = '您的开户申请银行已审批通过银行会向您的公账账户小额打款，打款可能存在一定延时，请您在户收到打款后，48小时内进入此页面进行打款验证';
    }
    if (type.value === 32) {
      obj.name = '开户成功';
      obj.text = '';
    }
    return obj;
  });

  const getBankList = (val) => {
    queryBankList({ bankName: val || ''}).then((res) => {
      if (res.code === 1000) {
        bankList.value = res.data;
      }
    })
  };
  getBankList();

  const searchSubBank = async(val) => {
    querySubBankList({bankName: formModal.bankName, subBankName: val}).then((res) => {
      if (res.code === 1000) {
        subBankList.value = res.data;
      }
    })
    //const res = await querySubBankList({bankName: this.accountVo.bankName, subBankName: val})
  };
  const bankChange = () => {
    formModal.branchBankName = '';
    searchSubBank();
  };

  const subBankChange = (val) => {
    if (val) {
      const value = subBankList.value.find((item) => {
        return item.bankName === val;
      })
      if (value) {
        formModal.branchBankCd = value.bankCode;
      }
    } else {
      formModal.branchBankCd = '';
    }
  };

  const sendActiveCode = async () => {
    if (formModal.corporatePhone) {
      sendDisabled.value = true;
      timeOutSend();
      const res = await sendVerificationCode({ mobileNumber: formModal.corporatePhone })
      if (res && res.code === 1000) {
        ElMessage.success('验证码发送成功')
      } else {
        sendDisabled.value = false;
        sendCountDown.value = 60;
        ElMessage.error(res.msg || res.errorMsg)
      }
    }
  };
  const timeOutSend = () => {
    setTimeout(() => {
      if (sendCountDown.value > 0) {
        sendCountDown.value --;
        timeOutSend();
      } else {
        sendDisabled.value = false;
        sendCountDown.value = 60;
      }
    }, 1000);
  };
  const handleCheckAgreement = (url) => {
    location.href = url;
  };

  const create = () => {
    formModalRef.value.validate((valid) => {
      if (valid) {
        const params = { ...formModal };
        params.merchantId = merchantId;
        createPingAnAccount(params).then((res) => {
          if (res.code === 1000) {
            ElMessage.success(res.msg);
            queryAccountInfo();
          } else {
            ElMessage.error(res.msg);
          }
        });
      }
    })
  };

  const verify = () => {
    formModalRef.value.validate((valid) => {
      if (!checkAgreement.value) {
        ElMessage.warning('请阅读并同意《平安银行电子商务“见证宝”商户服务协议》和《平安银行数字口袋协议》');
        return false;
      }
      if (valid) {
        const params = { ...formModal };
        params.merchantId = merchantId;
        paymentAuth(params).then((res) => {
          if (res.code === 1000) {
            // ElMessage.success(res.msg);
            setTimeout(() => {
              router.push({
                path: '/pinganMerchant/pc/my',
                query: { merchantId }
              });
            }, 100)
          } else {
            let str = '';
            if (res.code === 1001) {
              str = `银行卡验证失败。${res.msg}`;
            }
            if (res.code === 1002) {
              str = '银行卡验证失效，请重新提交开户申请';
            }
            ElMessageBox.alert(str, '提示', {
              confirmButtonText: '确定',
              callback: () => {
                queryAccountInfo();
              },
            })
          }
        })
      }
    })
  }

  const handleCheckAuditFailReason = () => {
    ElMessageBox.alert(auditFailReason.value, '提示', {
      confirmButtonText: '确定',
      callback: () => {},
    })
  }
// 跳转协议
const jumpServicePdf = () => {
  window.open(`https://my.orangebank.com.cn/orgLogin/hd/act/jianzb/B2BClearing.html?name=${formModal.accountName == null ? '' : formModal.accountName}`);
}
</script>
<style lang="scss" scoped>
#app {
  background: #fff;
}
.open {
  background: #fff;
  width: 980px;
  margin: auto;
  box-sizing: border-box;
  padding: 30px 30px 0 30px;
  .flex {
    display: flex;
    align-items: center;
  }
  .my {
    span {
      width: 4px;
      height: 16px;
      background: #00B377;
      margin-right: 10px;
    }
    font-weight: 600;
    font-size: 18px;
    color: #292933;
    letter-spacing: 0;
    line-height: 20px;
  }
  .flex1 {
    flex: 1;
  }
  .status {
    // background: #FFF7EF;
    padding: 24px 0 0 0;
  }
  .top {
    font-size: 14px;
    color: #292933;
    line-height: 16px;
    font-weight: 500;
  }
  .bottom {
    color: #595959;
    letter-spacing: 0;
    font-size: 12px;
    line-height: 14px;
    padding-top: 10px;
    .rejectionReason {
      color: #00C675;
      text-decoration: underline;
      margin-left: 14px;
      cursor: pointer;
    }
  }
  .form {
    padding: 33px 0 0 0;
    .item {
      // width: 440px;
      // margin: 0 0 rem(40) 0;
      // p {
      //   font-weight: 500;
      //   font-size: rem(28);
      //   color: #676773;
      //   line-height: rem(30);
      //   margin: 0 0 rem(20) 0;
      // }
      .agreement {
        line-height: 14px;
        // line-height: rem(33);
        margin-left: 10px;
        display: inline;
        vertical-align: top;
        span {
          color: #00B377;
        }
      }
    }
    .el-checkbox__label {
      display: inline-grid;
      white-space: pre-line;
      word-wrap: break-word;
      overflow: hidden;
      line-height: 20px;
    }
  }
  .wid80 {
    min-width: 85px;
    margin-left: 10px;
  }
  .submit {
    padding: 0 0 0 167px;
    width: 100px;
    height: 32px;
  }
  .el-input,.el-select {
    width: 300px !important;
  }
}
</style>
