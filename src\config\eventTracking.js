import { computed } from 'vue'
import { putRequest } from "@/http/api";
import { useStore } from "vuex";

const store = useStore();
// const trackingDatas = computed(() => store.state.app.trackingDatas);
// const detailBaseUrl = computed(() => store.state.app.detailBaseUrl);
// const trackingParams = computed(() => store.state.app.trackingParams);
// const productDatas = computed(() => store.state.app.productDatas);

/**
 * 埋点主方法
 * @param {事件名称} eventName
 * @param {埋点参数} params
 * @param {启用状态} status
 * @param {回调函数} callback
 */
const tracking = function(
  eventName,
  params = {},
  status = true,
  callback = () => {}
) {
  if (status) {
    try {
      window.parent.webSdk.track(eventName, params, callback);
    } catch (error) {
      window.webSdk.track(eventName, params, callback);
    }
  }
};
/**
 * 设置用户id
 * @param {用户id} id
 */
export const trackingIdentify = id => window.webSdk.identify(id);
/**
 * h5 页面入口 埋点
 */
export const entryTracking = (eventName, params, status = true, callback) => {
  // 模拟接口
  if (status) {
    putRequest("get", "/app/sku/getSid", { merchantId: localStorage.getItem('merchantId') }).then(res => {
      if (res.status === "success") {
        params.sid = res.data.sid;
        tracking(eventName, params, status, callback);
      }
    });
  }
};
/**
 * h5 行为触发 埋点
 */
export const actionTracking = (eventName, params, status = true, callback) => {
  // console.log(eventName, params);
  // const store = useStore();
  const trackingDatas = computed(() => store.state.app.trackingDatas);
  const trackingParams = computed(() => store.state.app.trackingParams);
  params.url = location.href;
  if (status) {
    if (["h5_page_CommodityDetails_o"].includes(eventName)) {
      params = {
        ...trackingParams.value,
        ...params
      };
      if (params.sid) {
        // 上报数据
        tracking(eventName, params, status, callback);
      } else {
        // 暂存数据
        const arr = JSON.parse(JSON.stringify(trackingDatas.value));
        arr.push({ eventName, params, status, callback });
        store.commit('app/setTrackingDatas', arr);
      }
    } else {
      tracking(eventName, params, status, callback);
    }
  }
};

/**
 * 商品列表曝光埋点
 */
export const exposureTracking = (
  eventName,
  params,
  key,
  status = true,
  callback
) => {
  const store = useStore();
  const trackingDatas = computed(() => store.state.app.trackingDatas);
  const trackingParams = computed(() => store.state.app.trackingParams);
  const productDatas = computed(() => store.state.app.productDatas);
  if (status) {
    if (params.list) {
      const list = JSON.parse(JSON.stringify(params.list));
      const ids = Array.from(productDatas.value).map(el => el.id); // 已上报队列
      const arr = [];
      // 待上报列表中存在 已上报队列中没有的数据
      if (list.some(el => ids.indexOf(key ? el[key].id : el.id) < 0)) {
        list.forEach(el => {
          const _el = key ? el[key] : el;
          // 当前商品为未上报数据 添加到待处理数据中
          if (ids.indexOf(_el.id) < 0) {
            arr.push(_el);
            if (eventName === "h5_page_ListPage_Exposure") {
              // 单个商品上报
              if (trackingParams.value.sid) {
                tracking(
                  eventName,
                  {
                    ...trackingParams.value,
                    commodityId: _el.id,
                    commodityName: _el.commonName
                  },
                  status,
                  callback
                );
              } else {
                const datas = JSON.parse(JSON.stringify(trackingDatas.value));
                datas.push({
                  eventName,
                  params: {
                    commodityId: _el.id,
                    commodityName: _el.commonName
                  },
                  status,
                  callback
                });
                store.commit('app/setTrackingDatas', datas);
              }
            }
          }
        });
        store.commit('app/setProductDatas', productDatas.value.concat(arr));
      }
    }
  }
};
