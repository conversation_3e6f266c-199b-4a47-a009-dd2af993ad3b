<template>
  <!-- 此组件样式为普通商品流和拼团商品流混排，使用见品类商品流组件 -->
    <div>
      <ul class="templist test1">
        <li
          class="templist-item"
          v-for="(item, index) in datalist"
          :key="item.id"
          @mouseenter.self="mouseOver(item.id)"
          @mouseleave.self="mouseLeave(item.id)"
          :data-exposure="`{
            eventName: 'pc_page_ListPage_Exposure',
            commonName: '${item.showName}',
            spid: '${trackInfo.spid}',
            sid: '${trackInfo.sid}',
            sptype: '${trackInfo.sptype}',
            merchantId: '${merchantId}',
            productId: '${item.id}',
            position: '${index}',
          }`"
        >
          <div class="images">
            <a @click="toDetail(item.id)">
              <div v-if="item.status === 2" class="sold-out">已售罄</div>
              <div>
                <img
                  class="pic"
                  v-lazy="imgUrl + '/ybm/product/min/' + item.imageUrl"
                  :key="item.imageUrl"
                  :alt="item.showName"
                />
                <!-- 不带文案的标签 -->
                <img
                  v-if="item.markerUrl && !item.reducePrice"
                  :src="imgUrl + item.markerUrl"
                  class="activity-token"
                  alt
                />
                <!-- 带文案的标签 -->
                <div
                  class="mark-text"
                  v-if="
                    item.markerUrl &&
                    item.reducePrice &&
                    (item.isControl != 1 ||
                      (item.isPurchase == true && item.isControl == 1))
                  "
                >
                  <img :src="imgUrl + item.markerUrl" alt="药采节价" />
                  <h4>药采节价：{{ fixedtwo(item.reducePrice) }}</h4>
                </div>
                <!-- 促销图标 -->
                <div
                  class="promotionSkuPrice"
                  v-if="
                    item.promotionSkuImageUrl &&
                    (item.isControl != 1 ||
                      (item.isPurchase == true && item.isControl == 1))
                  "
                >
                  <img :src="imgUrl + item.promotionSkuImageUrl" alt />
                  <span>￥{{ fixedtwo(item.promotionSkuPrice) }}</span>
                </div>
                <div
                  class="active-tags"
                  v-if="(item.tags || {}).activityTag && (item.tags || {}).activityTag.tagNoteBackGroupUrl"
                >
                  <img :src="imgUrl + item.tags.activityTag.tagNoteBackGroupUrl" alt />
                  <div class="timeStr" v-if="item.tags.activityTag.timeStr">
                    <span>{{ item.tags.activityTag.timeStr }}</span>
                  </div>
                  <div class="temtejia806">
                    <span
                      class="price806"
                      v-for="(item3, index) in item.tags.activityTag.skuTagNotes"
                      :style="{ color: '#' + item3.textColor }"
                      :key="index"
                      >{{ item3.text }}</span
                    >
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div
            v-if="item.nearEffect || (item.tags.purchaseTags || []).filter(i=>i.type==4).length"
            class="nearEffectBox"
            :class="{leftText: (item.tags.purchaseTags || []).filter(i=>i.type==4).length}"
          >
            <span v-if="item.nearEffect">有效期 {{ item.nearEffect }}</span>
            <span class="countTag" v-if="(item.tags.purchaseTags || []).filter(i=>i.type==4).length">{{ ((item.tags.purchaseTags || []).filter(i=>i.type==4)[0] || {}).text }}</span>
          </div>
          <div v-if="item.actPt">
            <groupProduct :group-data="item" :track-info="trackInfo" :licenseStatus="licenseStatus" />
          </div>
          <div v-else-if="item.actPgby">
            <groupPgbyProduct :group-data="item" :track-info="trackInfo" :licenseStatus="licenseStatus" />
          </div>
          <div class="info" v-else>
            <!-- 价格  -->
            <div class="price-add-wrap">
              <div class="price-wrap">
                <div class="price-container">
                  <div class="controlTitleText" v-if="item.controlTitle && item.controlType != 5">
                    {{ item.controlTitle }}
                  </div>
                  <!-- <div class="qualifications" v-if="licenseStatus === 1 || licenseStatus === 5">
                    认证资质可见
                  </div>
                  <div class="nobuy" v-else-if="item.isPurchase != true && item.isControl == 1">
                    暂无购买权限
                  </div> -->
                  <div v-else class="price-numer">
                    <!-- <span class="price-permission" v-if="!merchantId">
                      价格登录可见
                    </span>
                    <span class="price-permission" v-else-if="(item.isOEM == 'true' && item.signStatus == 0) || item.showAgree == 0">
                      签署协议可见
                    </span> -->
                    <i class="price-numer-i" v-if="item.levelPriceDTO">
                      {{item.levelPriceDTO.rangePriceShowText}}
                    </i>
                    <i v-else-if="item.priceType == 2 && item.skuPriceRangeList">
                      ￥{{ fixedtwo(item.skuPriceRangeList[0].price) }}~{{
                        fixedtwo(item.skuPriceRangeList[item.skuPriceRangeList.length - 1].price)
                      }}
                    </i>
                    <div v-else class="pricewapper">
                      <div class="price-box clearfixed">
                        <div class="price-two">
                          <p>
                            <span class="priceDec">￥</span>
                            <span class="priceInt">{{String(item.fob.toFixed(2)).split('.')[0]}}</span>
                            <span class="priceFloat">.{{String(item.fob.toFixed(2)).split('.')[1] || '00'}}</span>
                            <!-- <span>￥{{ fixedtwo(item.fob) }}</span> -->
                            <span class="zheHouPrice" v-if="item.zheHouPrice && Number(item.zheHouPrice.split('￥')[1]) < item.fob">
                              <span class="zheHouText">折后约</span>
                              ￥{{ (item.zheHouPrice).split('￥')[1] || '' }}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- <div style="minheight: 43px;"> -->
                  <!-- <div class="label-box" v-if="item.tagList && item.tagList.length > 0">
                    <div class="labels">
                      <div class="labels-span">
                        <span
                          v-for="(item2, index2) in item.tagList.slice(0, 3)"
                          :key="index2"
                          :class="`span${item2.uiType}`"
                          :style="{
                            background: ['临期', '近效期'].indexOf(item2.name) != -1
                              ? '#FF8E00'
                              : '#fe0800',
                          }"
                        >
                          {{ item2.name }}
                        </span>
                      </div>
                    </div>
                  </div> -->
                  <!-- 零售价毛利 -->
                  <!-- <div class="control-hid" v-if="merchantId">
                    <div class="control-box" v-if="!(licenseStatus === 1 || licenseStatus === 5)">
                      <priceBox
                        v-if="
                          (item.isOEM != 'true' &&
                            (item.isControl != 1 ||
                              (item.isPurchase == true &&
                                item.isControl == 1))) ||
                          (item.isOEM == 'true' && item.signStatus == 1)
                        "
                        :uniformPrice="item.uniformPrice"
                        :suggestPrice="item.suggestPrice"
                        :grossMargin="item.grossMargin"
                      />
                    </div>
                  </div> -->
                <!-- </div> -->
              </div>
            </div>
            <a @click="toDetail(item.id)">
              <div class="commonName">
                <!-- 药名和规格 -->
                <span class="name">{{ item.showName.trim() }}{{ item.spec }}</span>
              </div>
              <div class="tagBox">
                <span v-for="tag in item.tags.productTags" :key="tag.text">
                  <span
                    class="tagItem"
                    v-if="tag.text && tag.uiStyle == 1"
                    :style="{ color: parseRGBA(tag.textColor), background: parseRGBA(tag.bgColor), border: `1px solid ${parseRGBA(tag.borderColor)}` }"
                  >{{ tag.text }}</span>
                </span>
                <span v-for="tag in item.tags.dataTags" :key="tag.text">
                  <span
                    class="tagItem"
                    v-if="tag.text && tag.uiStyle == 1"
                    :style="{ color: parseRGBA(tag.textColor), background: parseRGBA(tag.bgColor), border: `1px solid ${parseRGBA(tag.borderColor)}` }"
                  >{{ tag.text }}</span>
                </span>
              </div>
              <div class="manufacturer textellipsis">{{ item.manufacturer }}</div>
              <div class="collageBox">
                <div class="skText" v-if="item.actSk">
                  距结束&nbsp;
                  <groupLimitTime :endTime="(item.actSk || {}).endTime" timeColor="#F43000" />
                </div>
                <div v-else class="actBox">
                  <div class="couponBg" v-if="((item.tags || {}).couponTags || []).length">
                    <span>{{ ((item.tags || {}).couponTags[0] || {}).text }}</span>
                  </div>
                  <div class="tagBox" v-if="((item.tags || {}).activityTags || []).length">
                    <span v-for="tag in item.tags.activityTags" :key="tag.text">
                      <span
                        class="tagItem tagItemBig"
                        :class="tag.promoType == 8 ? 'tejia' : tag.promoType == 20 ? 'zhijiang' : ''"
                        v-if="tag.text && (tag.promoType == 8 || tag.promoType == 20)"
                      >{{ tag.text }}</span>
                      <span
                        v-else-if="tag.text"
                        class="tagItem tagItemBig"
                        :style="{ color: parseRGBA(tag.textColor), background: parseRGBA(tag.bgColor), border: `1px solid ${parseRGBA(tag.borderColor)}` }"
                      >{{ tag.text }}</span>
                    </span>
                  </div>
                </div>
              </div>
            </a>

            <div class="self-support">
              <span class="shrink0" v-if="(item.tags || {}).titleTags && (item.tags || {}).titleTags.length">
                {{ ((item.tags || {}).titleTags[0] || {}).text }}
              </span>
              <!-- <span
                v-else-if="item.titleTagList && item.titleTagList.length"
              >
                {{ (item.titleTagList[0] || {}).text }}
              </span> -->
              <span class="shopName" :class="{maxWidthName: (item.tags.purchaseTags || []).filter(i=>i.type==9).length && (item.tags || {}).titleTags && (item.tags || {}).titleTags.length}">{{ item.shopName }}</span>
              <span class="purchaseTags" v-if="(item.tags.purchaseTags || []).filter(i=>i.type==9).length">{{ ((item.tags.purchaseTags || []).filter(i=>i.type==9)[0] || {}).text }}</span>
            </div>
          </div>
          <a
            @click="toDetail(item.id)"
            class="float-carButton"
            v-show="showCarBtnIndex == item.id"
          >
            <!-- <div class="collection" @click.stop.prevent="collection(item, index)">
              <div class="zone-1">
                <div v-if="item.favoriteStatus == 2" class="top top-1">
                  <span class="w-icon-normal icon-normal-collectEpt"></span>
                </div>
                <div v-if="item.favoriteStatus == 1" class="top top-2">
                  <span class="w-icon-normal icon-normal-collectFull"></span>
                </div>
              </div>
              <div class="zone-2">
                <div v-if="item.favoriteStatus == 2" class="bottom bottom-1">
                  <p class="textOne">收藏</p>
                </div>
                <div v-if="item.favoriteStatus == 1" class="bottom bottom-2">
                  <p class="textOne">已收藏</p>
                </div>
              </div>
            </div> -->
            <div v-if="item.actPt">
              <div class="toStore" @click.stop.prevent="toStore(item.shopUrl)">
                <div class="storeIcon">
                  <img src="@/assets/images/icon-store.png" />
                </div>
                <div class="toStoreText">进店</div>
              </div>
              <!-- stepPriceStatus=2为单阶梯 -->
              <div v-if="item.actPt.stepPriceStatus == 2">
                <div class="hoverBox">
                  <div class="circleBg"></div>
                  <div class="price-wrap">
                    <div class="price-container">
                      <span class="priceDec">￥</span>
                      <span v-if="getActivityStatus(item.actPt) === 'inProgress'">
                        <!-- 多阶梯价格 -->
                        <span v-if="(item.actPt || {}).stepPriceStatus == 1">
                          <span class="priceInt">{{String((item.actPt || {}).minSkuPrice.toFixed(2)).split('.')[0]}}</span>
                          <span class="priceFloat">.{{String((item.actPt || {}).minSkuPrice.toFixed(2)).split('.')[1] || '00'}}</span>
                          -
                          <span class="priceInt">{{String((item.actPt || {}).maxSkuPrice.toFixed(2)).split('.')[0]}}</span>
                          <span class="priceFloat">.{{String((item.actPt || {}).maxSkuPrice.toFixed(2)).split('.')[1] || '00'}}</span>
                        </span>
                        <!-- 单阶梯价格 -->
                        <span v-if="(item.actPt || {}).stepPriceStatus == 2">
                          <span class="priceInt" v-if="getActivityStatus(item.actPt) === 'inProgress'">{{String((item.actPt || {}).assemblePrice.toFixed(2)).split('.')[0]}}</span>
                          <span class="priceFloat" v-if="getActivityStatus(item.actPt) === 'inProgress'">.{{String((item.actPt || {}).assemblePrice.toFixed(2)).split('.')[1] || '00'}}</span>
                        </span>
                      </span>
                      <span class="priceInt" v-if="getActivityStatus(item.actPt) === 'notStart'">??</span>
                      <span class="priceFloat" v-if="getActivityStatus(item.actPt) === 'notStart'">.??</span>
                    </div>
                    <!-- <div class="retailPrice" v-if="(item.actPt || {}).stepPriceStatus == 2">￥{{item.retailPrice.toFixed(2)}}</div> -->
                  </div>
                  <div class="ptShowName">{{item.showName}}/{{item.spec}}</div>
                  <div class="pCount">
                    <span class="ypCount">已拼{{(item.actPt || {}).orderNum}}{{item.productUnit}}</span>
                    <span class="qpCount">/{{(item.actPt || {}).skuStartNum}}{{item.productUnit}}起拼</span>
                  </div>
                  <div class="progressBox">
                    <div class="progressContnet" :style="{width: `${(item.actPt || {}).percentage*1 > 100 ? 100 : (item.actPt || {}).percentage*1}%`}"></div>
                    <div class="progressSpot" :style="{left: `${(item.actPt || {}).percentage*1 > 100 ? 100 : (item.actPt || {}).percentage*1}%`}"></div>
                  </div>
                  <span class="ptPercentage">{{((item.actPt || {}).percentage*1).toFixed(0)}}%</span>
                  <cartBtnPt :product-data="item" @change-price="changePrice" />
                </div>
              </div>
              <!-- stepPriceStatus=1为多阶梯 -->
              <div v-else-if="item.actPt.stepPriceStatus == 1" class="ptHoverBox">
                <div class="circleBg"></div>
                <!-- <div class="ptPrice">{{ item.activePrice || `￥${item.actPt.maxSkuPrice.toFixed(2)}` }}</div> -->
                <div class="price-wrap">
                  <div class="price-container">
                    <span class="priceDec">￥</span>
                    <span v-if="getActivityStatus(item.actPt) === 'inProgress'">
                      <!-- 多阶梯价格 -->
                      <span v-if="(item.actPt || {}).stepPriceStatus == 1">
                        <span class="priceInt">{{String((item.actPt || {}).minSkuPrice.toFixed(2)).split('.')[0]}}</span>
                        <span class="priceFloat">.{{String((item.actPt || {}).minSkuPrice.toFixed(2)).split('.')[1] || '00'}}</span>
                        -
                        <span class="priceInt">{{String((item.actPt || {}).maxSkuPrice.toFixed(2)).split('.')[0]}}</span>
                        <span class="priceFloat">.{{String((item.actPt || {}).maxSkuPrice.toFixed(2)).split('.')[1] || '00'}}</span>
                      </span>
                      <!-- 单阶梯价格 -->
                      <span v-if="(item.actPt || {}).stepPriceStatus == 2">
                        <span class="priceInt" v-if="getActivityStatus(item.actPt) === 'inProgress'">{{String((item.actPt || {}).assemblePrice.toFixed(2)).split('.')[0]}}</span>
                        <span class="priceFloat" v-if="getActivityStatus(item.actPt) === 'inProgress'">.{{String((item.actPt || {}).assemblePrice.toFixed(2)).split('.')[1] || '00'}}</span>
                      </span>
                    </span>
                    <span class="priceInt" v-if="getActivityStatus(item.actPt) === 'notStart'">??</span>
                    <span class="priceFloat" v-if="getActivityStatus(item.actPt) === 'notStart'">.??</span>
                  </div>
                  <!-- <div class="retailPrice" v-if="(item.actPt || {}).stepPriceStatus == 2">￥{{item.retailPrice.toFixed(2)}}</div> -->
                </div>
                <div class="ptShowName">{{item.showName}}/{{item.spec}}</div>
                <div class="priceDesc"  v-if="!item.controlTitle">
                  <div v-for="text in (item.actPt || {}).stepPriceShowTexts" :key="text">{{ text }}</div>
                </div>
                <div class="pCount">
                  <span class="ypCount">已拼{{(item.actPt || {}).orderNum}}{{item.productUnit}}</span>
                  <span class="qpCount">/{{(item.actPt || {}).skuStartNum}}{{item.productUnit}}起拼</span>
                </div>
                <div class="progressBox">
                  <div class="progressContnet" :style="{width: `${(item.actPt || {}).percentage*1 > 100 ? 100 : (item.actPt || {}).percentage*1}%`}"></div>
                  <div class="progressSpot" :style="{left: `${(item.actPt || {}).percentage*1 > 100 ? 100 : (item.actPt || {}).percentage*1}%`}"></div>
                </div>
                <span class="ptPercentage">{{((item.actPt || {}).percentage*1).toFixed(0)}}%</span>
                <div>
                  <cartBtnPt :product-data="item" @change-price="changePrice" />
                </div>
              </div>
            </div>
            <div v-else-if="item.actSk">
              <div class="toStore" @click.stop.prevent="toStore(item.shopUrl)">
                <div class="storeIcon">
                  <img src="@/assets/images/icon-store.png" />
                </div>
                <div class="toStoreText">进店</div>
              </div>
              <div class="ptHoverBox">
                <div class="circleBg"></div>
                <div class="ptShowName">{{item.showName}}/{{item.spec}}</div>
                <div class="progressBox">
                  <div class="progressContnet" :style="{width: `${(item.actSk || {}).percentage*1 > 100 ? 100 : (item.actSk || {}).percentage*1}%`}"></div>
                  <div class="progressSpot" :style="{left: `${(item.actSk || {}).percentage*1 > 100 ? 100 : (item.actSk || {}).percentage*1}%`}"></div>
                </div>
                <span class="ptPercentage">{{((item.actSk || {}).percentage*1).toFixed(0)}}%</span>
                <div>
                  <cartBtn goodsType="actSk" :product-data="item" />
                </div>
              </div>
            </div>
            <div v-else-if="item.actPgby">
              <!-- <div class="toStore" @click.stop.prevent="toStore(item.shopUrl)">
                <div class="storeIcon">
                  <img src="@/assets/images/icon-store.png" />
                </div>
                <div class="toStoreText">进店</div>
              </div> -->
              <div class="ptHoverBox">
                <div class="circleBg"></div>
                <div class="price-wrap">
                  <div class="price-container">
                    <span class="priceDec">￥</span>
                    <span>
                      <span>
                        <span class="priceInt">{{String((item.actPgby || {}).assemblePrice.toFixed(2)).split('.')[0]}}</span>
                        <span class="priceFloat">.{{String((item.actPgby || {}).assemblePrice.toFixed(2)).split('.')[1] || '00'}}</span>
                      </span>
                    </span>
                  </div>
                  <!-- <div class="retailPrice" v-if="(item.actPt || {}).stepPriceStatus == 2">￥{{item.retailPrice.toFixed(2)}}</div> -->
                </div>
                <div class="ptShowName">{{item.showName}}/{{item.spec}}</div>
                <!-- <div class="progressBox">
                  <div class="progressContnet" :style="{width: `${(item.actSk || {}).percentage*1 > 100 ? 100 : (item.actSk || {}).percentage*1}%`}"></div>
                  <div class="progressSpot" :style="{left: `${(item.actSk || {}).percentage*1 > 100 ? 100 : (item.actSk || {}).percentage*1}%`}"></div>
                </div> -->
                <!-- <span class="ptPercentage">{{((item.actSk || {}).percentage*1).toFixed(0)}}%</span> -->
                <div>
                  <!-- <cartBtn goodsType="actPgby" :product-data="item" /> -->
                  <cartBtnPgby :product-data="item" @change-price="changePrice" />
                </div>
              </div>
            </div>
            <div v-else>
              <div class="hoverBox levelHoverBox">
                <div class="circleBg"></div>
                <!-- <div class="ptPrice">{{ item.activePrice || `￥${item.actPt.maxSkuPrice.toFixed(2)}` }}</div> -->
                <div class="price-wrap" v-if="item.levelPriceDTO">
                  <div class="price-container-box">
                    <span>{{ item.levelPriceDTO.rangePriceShowText }}</span>
                  </div>
                  <!-- <div class="retailPrice" v-if="(item.actPt || {}).stepPriceStatus == 2">￥{{item.retailPrice.toFixed(2)}}</div> -->
                </div>
                <div class="ptShowName">{{item.showName}}/{{item.spec}}</div>
                <div class="priceDesc"  v-if="!item.controlTitle && item.levelPriceDTO">
                  <div v-for="text in (item.levelPriceDTO || {}).stepPriceShowTexts" :key="text">{{ text }}</div>
                </div>
                <div>
                  <cartBtn :product-data="item" :track-info="trackInfo" />
                </div>
              </div>
              <!-- <div class="inputNumber" v-if="merchantId">
                <div
                  v-if="
                    (item.isOEM == 'true' && item.signStatus == 0) ||
                    item.showAgree == 0
                  "
                ></div>
                <div v-else style="height: 100%;">
                  <cartBtn
                    v-if="
                      (item.isControl != 1 ||
                        (item.isPurchase == true && item.isControl == 1)) &&
                      !(licenseStatus === 1 || licenseStatus === 5)
                    "
                    :is-pack="false"
                    :data-id="item.id"
                    :is-split="item.isSplit"
                    :product-num="item.cartProductNum"
                    :med-num="item.mediumPackageNum"
                  ></cartBtn>
                </div>
              </div> -->
            </div>
          </a>
        </li>
      </ul>
    </div>
</template>
  
<script setup>
  import cartBtn from "./addCartBtn.vue";
  import cartBtnPt from "./addCartBtnPt.vue";
  import cartBtnPgby from "./addCartBtnPgby.vue";
  import priceBox from "../../../../components_pc/priceBox.vue";
  import { putRequest, handleDiscount } from "@/http_pc/api";
  import groupProduct from '../../../../components_pc/groupProduct.vue';
  import groupPgbyProduct from '../../../../components_pc/groupPgbyProduct.vue';
  import groupLimitTime from '../../../../components_pc/groupLimitTime.vue';
  import { onMounted, ref, watch, getCurrentInstance } from "vue";
  import { actionTracking } from '@/config/eventTracking';
  
  const props = defineProps({
    goodsData: {
      default: []
    },
    trackInfo: {
      default: {}
    },
    licenseStatus: {
      default: 0,
    },
    body_color: {
      default: '#fafafa'
    }
  });

  const detailUrl = import.meta.env.VITE_BASE_URL_PC;
  const showCarBtnIndex = ref(null);
  const datalist = ref(props.goodsData);
  const imgUrl = import.meta.env.VITE_IMG;
  const length = ref(10);
  const { proxy } = getCurrentInstance();
  const merchantId = proxy.$merchantId;
  //datalist.value.forEach(item => {
  //  if (item.productInfo.tags.dataTags) {
  //    let arr = [];
  //    item.productInfo.tags.productTags.forEach(k => {
  //      if (item.productInfo.tags.dataTags.findIndex(f => f.text == k.text) == -1) {
  //        arr.push(k);
  //      }
  //    })
  //    item.productInfo.tags.productTags = arr;
  //  }
  //})
  onMounted(() => {
    // datalist.value = props.goodsData;
    addDiscount(props.goodsData);
    // WS.Bus.$on("cart_isExtend", (isExtend, id) => {
    //   let original = document.querySelectorAll(`i[data-original-id]`);
    //   for (let i = 0; i < original.length; i++) {
    //     if (original[i].getAttribute("data-original-id") == id) {
    //       if (isExtend) {
    //         original[i].style.display = "none";
    //       } else {
    //         original[i].style.display = "inline-block";
    //       }
    //     }
    //   }
    //   let pageage = document.querySelectorAll(`div[data-pageage-id]`);
    //   for (let i = 0; i < pageage.length; i++) {
    //     if (pageage[i].getAttribute("data-pageage-id") == id) {
    //       if (isExtend) {
    //         pageage[i].style.display = "block";
    //         pageage[i].previousElementSibling.style.width = "40%";
    //       } else {
    //         pageage[i].previousElementSibling.style.width = "90%";
    //         pageage[i].style.display = "none";
    //       }
    //     }
    //   }
    // });
  })

  const fixedtwo = (val) => {
    if (val === 0) return "0.00";
    if (!val) return "";
    return parseFloat(val).toFixed(2);
  }

  function parseRGBA(val) {
    val = val.trim().toLowerCase();  //去掉前后空格
    if (val.length > 8) {
      let color = {};
      try {
        let argb = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(val);
        color.r = parseInt(argb[2], 16);
        color.g = parseInt(argb[3], 16);
        color.b = parseInt(argb[4], 16);
        color.a = parseInt(argb[1], 16) / 255;
      } catch (e) {
        console.log(e)
      }
      return 'rgba(' + color.r + ',' + color.g + ',' + color.b + ',' + parseFloat(color.a).toFixed(1) + ')';
    } else {
      return val;
    }
  }

  watch(
    () => props.goodsData,
    (new_val) => {
      if (new_val) {
        addDiscount(new_val);
      }
    },
    {
      immediate: false,
      deep: true,
    },
  )
  const collection = (item, index) => {
    const url =
      item.favoriteStatus == 2
        ? "/merchant/center/collection/attention.json"
        : "/merchant/center/collection/cancelAttention.json";
    putRequest("get", url, { skuId: item.id })
      .then((res) => {
        if (res.status == "success") {
          if (item.favoriteStatus == 2) {
            item.favoriteStatus = 1;
            datalist.value.splice(index, 1, item);
          } else {
            item.favoriteStatus = 2;
            datalist.value.splice(index, 1, item);
          }
        } else {
          alert(res.errorMsg);
        }
      })
      .catch((err) => {
        console.log('收藏错误', err);
      });
  }
  const mouseOver = (id) => {
    showCarBtnIndex.value = id;
  }
  const mouseLeave = (id) => {
    showCarBtnIndex.value = null;
  }
  // 添加折后价
  const addDiscount = (data) => {
    if (data && data.length > 0) {
      const idList = data.map(item => { return item.id });
      const params = {
        skuIds: idList.join(','),
        merchantId,
      }
      handleDiscount(params)
        .then((res) => {
          if (res.status == "success") {
            const priceArr = res.data;
            priceArr.map((item, index) => {
              data.map((item2,index2) => {
                if (item.skuId == item2.id) {
                  // this.$set(item2, 'zheHouPrice', item.price)
				  const zheHouPrice = Number(item.price.split('￥')[1]);
				  if (!isNaN(zheHouPrice)) {
					item2.zheHouPrice = item.price;
				  }
                }
              })
            })
            
          }
        })
        .catch((err) => {
          console.log(err, "请求失败了");
        });
    }
    datalist.value = data;
  }
  const toStore = (url) => {
    // const a = document.createElement("a"); // 创建a标签
    // a.href = url;
    // a.click();
    window.open(url);
  }
  const toDetail = (id) => {
    actionTracking('pc_page_CommodityDetails', {
      spid: props.trackInfo.spid,
      sid: props.trackInfo.sid,
      sptype: props.trackInfo.sptype,
      user_id: merchantId,
      commodityId: id,
    })
    window.open(`${detailUrl}search/skuDetail/${id}.htm`);
  }
  const changePrice = (id, price) => {
    datalist.value.forEach((item) => {
      if (item.id == id) {
        item.activePrice = price;
        // this.$set(item, 'activePrice', price);
      }
    })
  }

  const getActivityStatus = (item) => {
    return item.assembleStatus === 0 ? 'notStart' : item.assembleStatus === 1 ? 'inProgress' : '';
  }
   
</script>
  
<style lang="scss" scoped>
.templist {
  display: flex;
  display: -webkit-flex;
  flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  margin-top: 10px;
  .templist-item {
    background: #fff;
    position: relative;
    // overflow: hidden;
    width: 240px;
    box-sizing: border-box;
    // padding: 20px 17px 0 17px;
    padding: 0 17px;
    height: 396px;
    // border: 1px solid #f1f1f1;
    .float-carButton {
      position: absolute;
      top: 0;
      left: 0;
      width: 240px;
      height: 396px;
      box-shadow: 2px 2px 10px 0 rgba(0,0,0,0.20);
      border: 1px solid #E0E0E0;
      box-sizing: border-box;
      display: block;
      .price-wrap {
        display: flex;
        align-items: flex-end;
        font-weight: Medium;
        .price-container {
          color: #FF2121;
          span {
            font-weight: bold;
          }
          .priceDec {
            font-size: 14px;
          }
          .priceInt {
            font-size: 22px;
          }
          .priceFloat {
            font-size: 18px;
          }
        }
        .retailPrice {
          font-size: 12px;
          color: #666666;
          text-decoration: line-through;
          margin-left: 8px;
        }
      }
      .collection {
        position: absolute;
        top: 5px;
        right: 0px;
        z-index: 100;
        display: block;
        height: 49px;
        width: 40px;
        border: 0px solid #ccc;
        overflow: hidden;
        vertical-align: middle;
        text-align: center;
        cursor: pointer;
        .zone-1 {
          height: 24px;
          overflow: hidden;
          position: relative;
          .top {
            position: relative;
            height: 24px;
            width: 40px;
          }
          .top-1 {
            animation: sliderDown 0.8s;
            animation-fill-mode: forwards;
          }
          @keyframes sliderDown {
            from {
              -webkit-transform: translateY(-24px);
              transform: translateY(-24px);
            }
            to {
              -webkit-transform: translateY(0);
              transform: translateY(0);
            }
          }
          .top-2 {
            -webkit-animation: sliderUp 0.8s;
            animation: sliderUp 0.8s;
            -webkit-animation-fill-mode: forwards;
            animation-fill-mode: forwards;
          }
          @keyframes sliderUp {
            from {
              -webkit-transform: translateY(24px);
              transform: translateY(24px);
            }
            to {
              -webkit-transform: translateY(0);
              transform: translateY(0);
            }
          }
          .w-icon-normal {
            margin-top: 6px;
          }
          .icon-normal-collectEpt {
            width: 16px;
            height: 16px;
            background: url(@/assets/images/search/shoucang.png) no-repeat;
            display: inline-block;
          }
          .icon-normal-collectFull {
            width: 16px;
            height: 16px;
            background: url(@/assets/images/search/shoucang2.png) no-repeat;
            display: inline-block;
          }
        }
        .zone-2 {
          height: 23px;
          overflow: hidden;
          position: relative;
          .bottom {
            height: 23px;
            font-size: 13px;
            line-height: 23px;
            color: #999;
            p {
              font-size: 12px;
              color: #adadad;
            }
          }
        }
      }
      .toStore {
        position: absolute;
        top: 5px;
        right: 7px;
        font-size: 12px;
        text-align: center;
        cursor: pointer;
        .storeIcon {
          width: 16px;
          height: 16px;
          margin: 4px auto;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .toStoreText {
          font-size: 12px;
          color: #adadad;
        }
      }
      .hoverBox {
        // width: 100%;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        padding:0 10px 10px;
        background: #222C38;
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #ffffff;
        .circleBg {
          position: absolute;
          top: -9px;
          left: 0;
          right: 0;
          background: url('@/assets/images/search/yh.png') no-repeat;
          background-size: 100%;
          height: 10px;
        }
      }
      .levelHoverBox {
        .price-wrap {
          display: flex;
          align-items: baseline;
          font-weight: Medium;
          margin: 4px 0 6px 0;
          .price-container {
            display: flex;
            align-items: baseline;
            color: #FF2121;
            .priceDec {
              font-size: 14px;
            }
            .priceInt {
              font-size: 22px;
            }
            .priceFloat {
              font-size: 18px;
            }
          }
          .price-container-box {
            color: #FF2121;
            font-size: 20px;
          }
          .retailPrice {
            font-size: 12px;
            color: #666666;
            text-decoration: line-through;
            margin-left: 8px;
          }
        }
        .priceDesc {
          div {
            color: #FFCD86;
            font-size: 12px;
            margin-top: 6px;
          }
          margin: 10px 0;
        }
      }
      .progressBox {
        width: 148px;
        height: 4px;
        background: #F5F5F5;
        border-radius: 2px;
        position: relative;
        display: inline-block;
        margin-right: 10px;
        .progressContnet {
          height: 4px;
          background-image: linear-gradient(90deg, #FC6C41 0%, #F62526 100%);
          border-radius: 2px;
        }
        .progressSpot {
          position: absolute;
          top: -3px;
          width: 6px;
          height: 6px;
          border: 1.5px solid #fff;
          border-radius: 50%;
          background: #FF2121;
        }
      }
      .ptPercentage {
        color: #FF6F48;
      }
      .pCount {
        font-size: 12px;
        margin-top: 4px;
        .ypCount {
          color: #FF6F48;
        }
        .qpCount {
          color: #B4B8BC;
        }
      }
      .ptHoverBox {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: #222C38;
        padding: 10px;
        // border-radius: 10px 10px 0 0;
        .circleBg {
          position: absolute;
          top: -9px;
          left: 0;
          right: 0;
          background: url('@/assets/images/search/yh.png') no-repeat;
          background-size: 100%;
          height: 10px;
        }
        .ptPrice {
          color: #FF5B5B;
          font-size: 16px;
        }
        .priceDesc {
          div {
            color: #FFCD86;
            font-size: 12px;
            margin-top: 6px;
          }
          margin: 10px 0;
        }
        .progressBox {
          width: 148px;
          margin-top: 10px;
        }
      }
      .ptShowName {
        color: #fff;
        font-size: 14px;
        margin-top: 10px;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        /* autoprefixer: off */
        -webkit-box-orient: vertical;
        /* autoprefixer: on */
        -webkit-line-clamp: 3;
      }
      .inputNumber {
        width: 100%;
        height: 48px;
        position: absolute;
        right: 0;
        bottom: 0;
        background: #2f3846;
        .no-goods {
          img {
            position: absolute;
            top: rem(5);
            right: rem(10);
            width: rem(40);
            height: rem(40);
          }
        }
      }
    }
    a {
      display: block;
    }
    .images {
      // width: 146px;
      // height: 99px;
      width: 185px;
      height: 180px;
      box-sizing: content-box;
      margin: auto;
      padding-right: 17px;
      position: relative;
      .sold-out {
        position: absolute;
        left: 50%;
        top: 50%;
        margin: -30px 0 0 -30px;
        width: 60px;
        height: 60px;
        background-color: rgba(0, 0, 0, 0.4);
        text-align: center;
        font-size: 14px;
        color: #fff;
        border-radius: 50%;
        line-height: 60px;
      }
      .pic {
        // max-width: 146px;
        // max-height: 99px;
        max-width: 185px;
        max-height: 180px;
        display: block;
        margin: auto;
        object-fit:contain;
      }
      .activity-token {
        position: absolute;
        width: 185px;
        height: auto;
        max-width: 100%;
        margin-top: -6px;
        top: 2px;
        left: 0px;
        z-index: 3;
      }
      .mark-text {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        z-index: 5;
        img {
          width: 100%;
          height: 100%;
        }
        h4 {
          position: absolute;
          left: 0;
          bottom: 0;
          font-size: rem(20);
          color: #fff;
          width: 100%;
          text-align: center;
        }
      }
      .promotionSkuPrice {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 5;
        span {
          position: absolute;
          right: 0;
          bottom: 0;
          font-size: 14px;
          color: #ff6201;
          display: flex;
          display: -webkit-flex;
          justify-content: center;
          -webkit-justify-content: center;
          align-items: center;
          -webkit-align-items: center;
          width: rem(140);
          height: rem(40);
          text-shadow: 1px 1px 1px #ffd9b4;
          font-weight: 600;
        }
      }
      .active-tags {
        position: absolute;
        left: 50%;
        top: 16px;
        z-index: 5;
        margin-left: -92.5px;
        img {
          width: 185px;
          height: auto;
        }
        .timeStr {
          left: 0;
          position: absolute;
          bottom: 47px;
          text-align: center;
          width: 174px;
          height: 10px;
          line-height: 10px;
          span {
            color: #ffffff;
            display: block;
            font-size: 12px;
            text-align: center;
            transform: scale(0.65);
          }
        }
        .temtejia806 {
          font-size: 12px;
          text-shadow: none;
          width: auto;
          font-weight: 400;
          text-align: center;
          color: #fff;
          position: absolute;
          bottom: 25px;
          left: 0;
          width: 174px;
          overflow: hidden;
          height: 18px;
          span {
            display: inline-block;
          }
        }
      }
    }
    .nearEffectBox {
      background: rgba(0, 0, 0, 0.6);
      font-size: 12px;
      color: #FFFFFF;
      height: 22px;
      line-height: 22px;
      position: relative;
      padding-left: 10px;
      .countTag {
        text-align: right;
        padding-right: 10px;
        position: absolute;
        top: 0;
        right: 0;
        height: 22px;
        width: 60px;
        background: url("@/assets/images/search/tag.png") no-repeat;
        background-size: 100% 100%;
      }
    }
    .leftText {
      padding-left: 8px;
      text-align: left;
    }

    .info {
      padding: 4px 0 0 0;
      overflow: hidden;
      .commonName {
        font-size: 16px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: left;
        color: #333333;
        line-height: 22px;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        /* autoprefixer: off */
        -webkit-box-orient: vertical;
        /* autoprefixer: on */
        -webkit-line-clamp: 2;
      }
      .actBox {
        white-space: nowrap;
      }
      .tagBox {
        margin: 6px;
        margin-left: 0;
        white-space: nowrap;
        display: inline-block;
        .tagItem {
          margin-right: 8px;
          padding: 1px 4px;
          font-size: 12px;
          border-radius: 4px;
        }
        .tagItemBig {
          padding: 4px;
          display: inline-block;
        }
        .tejia {
          color: #fff;
          background-image: linear-gradient(270deg, #FF0000 1%, #F96401 100%);
          border: 1px solid #FF9AA3;
          border-radius: 4px;
          padding: 3px 5px;
        }
        .zhijiang {
          color: #fff;
          background-image: linear-gradient(270deg, #8001EE 1%, #6A01F9 100%);
          border: 1px solid #E9D3FF;
          border-radius: 4px;
          padding: 3px 5px;
        }
      }
      .manufacturer {
        background: none;
        font-size: 12px;
        color: #999999;
        // margin: 2px 0 8px;
      }
      .collageBox {
        display: flex;
        align-items: center;
        font-size: 14px;
        .skText {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          width: 200px;
          height: 24px;
          background: url('@/assets/images/search/msTime.png') no-repeat;
          background-size: 100%;
          padding: 0 30px;
          color: #222222;
        }
        .couponBg {
          // width: 200px;
          display: inline-block;
          font-size: 12px;
          color: #F1081C;
          height: 24px;
          line-height: 24px;
          background: url('@/assets/images/search/coupon.png') no-repeat;
          background-size: 100% 100%;
          padding: 0 4px;
          margin-right: 4px;
        }
      }
      .price-add-wrap {
        display: flex;
        display: -webkit-flex;
        flex-wrap: nowrap;
        -webkit-flex-wrap: nowrap;
        justify-content: space-between;
        position: relative;
        padding-top: 4px;
        .price-wrap {
          display: flex;
          display: -webkit-flex;
          flex-wrap: nowrap;
          -webkit-flex-wrap: nowrap;
          flex-direction: column;
          overflow: hidden;
          .price-container {
            display: flex;
            display: -webkit-flex;
            flex-wrap: nowrap;
            -webkit-flex-wrap: nowrap;
            justify-content: space-between;
            .controlTitleText {
              color: #f49926;
              font-weight: 500;
              font-size: 18px;
            }
            .qualifications {
              font-size: 18px;
              font-family: PingFangSC, PingFangSC-Medium;
              font-weight: 500;
              text-align: center;
              color: #e3293a;
              line-height: 25px;
            }
            .nobuy {
              font-size: 18px;
              font-family: PingFangSC, PingFangSC-Medium;
              font-weight: 500;
              text-align: center;
              color: #e3293a;
              line-height: 25px;
            }
            .price-numer {
              font-size: 18px;
              font-family: PingFangSC, PingFangSC-Medium;
              font-weight: 500;
              text-align: center;
              color: #e3293a;
              line-height: 25px;
              .price-permission {
                color: #f39800;
              }
              .price-numer-i {
                color: #ff2121;
                font-size: 20px;
                font-family: PingFangSC;
                font-weight: bold;
              }
              .pricewapper {
                .price-box {
                  overflow: hidden;
                  color: #FF2121;
                  .price-two {
                    p {
                      color: #FF2121;
                    }
                    
                    letter-spacing: 0;
                    span {
                      font-weight: bold;
                    }
                    .priceDec {
                      font-size: 14px;
                    }
                    .priceInt {
                      font-size: 22px;
                    }
                    .priceFloat {
                      font-size: 18px;
                    }
                    // p {
                    //   span {
                    //     color: #ff2121;
                    //   }
                      .zheHouPrice {
                        color: #ff2121;
                        .zheHouText {
                          font-weight: normal;
                          color: #666;
                        }
                        
                        font-size: 12px;
                        font-weight: 400;
                        margin-left: 6px;
                      }
                    // }
                  }
                  .midPack {
                    line-height: rem(40);
                    text-align: right;
                    width: 35%;
                    font-size: rem(20);
                    color: #999;
                  }
                }
              }
            }
          }
          .control-hid {
            .control-box {
              margin-top: 5px;
            }
          }
        }
      }
      .label-box {
        margin: 2px 0 0 0;
        .labels {
          height: 18px;
          line-height: 18px;
          position: relative;
          font-size: 0;
          display: flex;
          display: -webkit-flex;
          flex-wrap: nowrap;
          -webkit-flex-wrap: nowrap;
          justify-content: space-between;
          .labels-span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-left: 0;
            span {
              margin-right: 5px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              height: 18px;
              line-height: 18px;
              background: #fe0800;
              border-radius: 2px;
              color: #ffffff;
              font-size: 12px;
              padding: 0 5px;
              display: inline-block;
              width: auto;
              margin-left: 0;
              border: 0;
            }
            .fullXReductionY {
              position: relative;
              background: rgba(255, 147, 143, 1);
              color: rgba(255, 255, 255, 1);
              letter-spacing: 1px;
            }
            .fullXReductionY:before {
              content: "";
              display: inline-block;
              background-color: #fff;
              width: rem(7);
              height: rem(14);
              border-radius: 0 rem(14) rem(14) 0;
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
            }
            .fullXReductionY:after {
              content: "";
              display: inline-block;
              background-color: #fff;
              width: rem(7);
              height: rem(14);
              border-radius: rem(14) 0 0 rem(14);
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
            }
          }
          /*标签样式*/
          @import "@/assets/style/tagStyle";
          .more {
            max-width: rem(54);
            background: url("../../app/assets/images/dot.png") no-repeat center;
            background-size: 100%;
            width: rem(54);
          }
        }
      }
      .self-support {
        margin-top: 1px;
        height: 21px;
        display: flex;
        align-items: center;
        white-space: nowrap;
        .shrink0 {
          flex-shrink: 0;
        }
    
        span {
          background-color: #00B955;
          border-radius: 2px;
          color: #ffffff;
          font-size: 12px;
          padding: 2px;
          display: inline-block;
        }
        .shopName {
          flex-wrap: nowrap;
          background: none;
          font-size: 12px;
          color: #999999;
          display: inline-block;
          flex-grow: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .maxWidthName {
          max-width: 70px;
        }
        .purchaseTags {
          background: none;
          color: #009D48;
        }
      }
    }
  }
  .templist-item:hover {
    cursor: pointer;
  }
  // .templist-item:nth-child(4n + 0) {
  //   border-right: 0;
  // }
  // .templist-item:last-child(4n + 0) {
  //   border-right: 0;
  // }
}
</style>
  