<template>
  <!-- 此组件样式为普通商品流和拼团商品流混排，使用见品类商品流组件 -->
  <div class="spuBox">
    <!-- showScrollBtn -->
    <div v-if="showScrollBtn">
      <el-icon id="scrollToLeft">
        <ArrowRight style="font-size: 35px; margin-left: 30px" />
      </el-icon>
      <el-icon id="scrollToRight">
        <ArrowLeft style="font-size: 35px; margin-right: 30px" />
      </el-icon>
    </div>
    <div class="spu-p"><span style="font-weight: bold">同品其他规格</span></div>
    <ul class="spulist">
      <template v-for="(item, index) in datalist" :key="item.id">
        <li
        @click="shopClick({name:item.productInfo.showName, index, id:item.productInfo.id})"
         v-in-viewport="{fn:exposureView,data:{index,id:item.productInfo.id,name:item.productInfo.showName}}"
          v-if="item.cardType == 1"
          class="spulist-item"
          @mouseenter.self="mouseOver(item.productInfo.id)"
          @mouseleave.self="mouseLeave(item.productInfo.id)"
          :data-exposure="`{
                eventName: 'pc_page_ListPage_Exposure',
                commonName: '${item.productInfo.showName}',
                merchantId: '${merchantId}',
                productId: '${item.productInfo.id}',
                }`"
        >
          <div class="images">
            <a @click="toDetail(item.productInfo.id)">
              <div v-if="item.productInfo.status === 2" class="sold-out">已售罄</div>
              <div>
                <img
                  class="pic"
                  v-lazy="imgUrl + '/ybm/product/min/' + item.productInfo.imageUrl"
                  :key="item.productInfo.imageUrl"
                  :alt="item.productInfo.showName"
                />
                <!-- 不带文案的标签 -->
                <div
                  class="mark-text"
                  v-if="
                    item.productInfo.markerUrl &&
                    item.productInfo.reducePrice &&
                    (item.productInfo.isControl != 1 ||
                      (item.productInfo.isPurchase == true && item.productInfo.isControl == 1))
                  "
                >
                  <img :src="imgUrl + item.productInfo.markerUrl" alt="药采节价" />
                  <h4>药采节价：{{ fixedtwo(item.productInfo.reducePrice) }}</h4>
                </div>
                <!-- 促销图标 -->
                <div
                  class="promotionSkuPrice"
                  v-if="
                    item.productInfo.promotionSkuImageUrl &&
                    (item.productInfo.isControl != 1 ||
                      (item.productInfo.isPurchase == true && item.productInfo.isControl == 1))
                  "
                >
                  <img :src="imgUrl + item.productInfo.promotionSkuImageUrl" alt />
                  <span>￥{{ fixedtwo(item.productInfo.promotionSkuPrice) }}</span>
                </div>
                <div
                  class="active-tags"
                  v-if="
                    (item.productInfo.tags || {}).activityTag &&
                    (item.productInfo.tags || {}).activityTag.tagNoteBackGroupUrl
                  "
                >
                  <img :src="imgUrl + item.productInfo.tags.activityTag.tagNoteBackGroupUrl" alt />
                  <div class="timeStr" v-if="item.productInfo.tags.activityTag.timeStr">
                    <span>{{ item.productInfo.tags.activityTag.timeStr }}</span>
                  </div>
                  <div class="temtejia806">
                    <span
                      class="price806"
                      v-for="(item3, index) in item.productInfo.tags.activityTag.skuTagNotes"
                      :style="{ color: '#' + item3.textColor }"
                      :key="index"
                      >{{ item3.text }}</span
                    >
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div v-if="item.productInfo.actPt">
            <groupProduct
              :group-data="item.productInfo"
              :track-info="trackInfo"
              :licenseStatus="licenseStatus"
            />
          </div>
          <div v-else-if="item.productInfo.actPgby">
            <groupPgbyProduct
              :group-data="item.productInfo"
              :track-info="trackInfo"
              :licenseStatus="licenseStatus"
            />
          </div>
          <div class="info" v-else>
            <!-- 价格  -->
            <div class="price-add-wrap">
              <div class="price-wrap">
                <div class="price-container">
                  <div
                    class="controlTitleText"
                    v-if="item.productInfo.controlTitle && item.productInfo.controlType != 5"
                  >
                    {{ item.productInfo.controlTitle }}
                  </div>
                  <!-- <div class="qualifications" v-if="licenseStatus === 1 || licenseStatus === 5">
                    认证资质可见
                  </div>
                  <div class="nobuy" v-else-if="item.productInfo.isPurchase != true && item.productInfo.isControl == 1">
                    暂无购买权限
                  </div> -->
                  <div v-else class="price-numer">
                    <!-- <span class="price-permission" v-if="!merchantId">
                      价格登录可见
                    </span>
                    <span class="price-permission" v-else-if="(item.productInfo.isOEM == 'true' && item.productInfo.signStatus == 0) || item.productInfo.showAgree == 0">
                      签署协议可见
                    </span> -->
                    <i class="price-numer-i" v-if="item.productInfo.levelPriceDTO">
                      {{ item.productInfo.levelPriceDTO.rangePriceShowText }}
                    </i>
                    <i
                      v-else-if="
                        item.productInfo.priceType == 2 && item.productInfo.skuPriceRangeList
                      "
                    >
                      ￥{{ fixedtwo(item.productInfo.skuPriceRangeList[0].price) }}~{{
                        fixedtwo(
                          item.productInfo.skuPriceRangeList[
                            item.productInfo.skuPriceRangeList.length - 1
                          ].price
                        )
                      }}
                    </i>
                    <div v-else class="pricewapper">
                      <div class="price-box clearfixed">
                        <div class="price-two">
                          <p>
                            <span class="priceDec">￥</span>
                            <span class="priceInt">{{
                              String(item.productInfo.fob.toFixed(2)).split(".")[0]
                            }}</span>
                            <span class="priceFloat"
                              >.{{
                                String(item.productInfo.fob.toFixed(2)).split(".")[1] || "00"
                              }}</span
                            >
                            <!-- <span>￥{{ fixedtwo(item.productInfo.fob) }}</span> -->
                            <span class="zheHouPrice" v-if="item.productInfo.zheHouPrice">
                              <span class="zheHouText">折后约</span>
                              ￥{{ item.productInfo.zheHouPrice.split("￥")[1] || "" }}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- <div style="minheight: 43px;"> -->
                <!-- <div class="label-box" v-if="item.productInfo.tagList && item.productInfo.tagList.length > 0">
                    <div class="labels">
                      <div class="labels-span">
                        <span
                          v-for="(item2, index2) in item.productInfo.tagList.slice(0, 3)"
                          :key="index2"
                          :class="`span${item2.uiType}`"
                          :style="{
                            background: ['临期', '近效期'].indexOf(item2.name) != -1
                              ? '#FF8E00'
                              : '#fe0800',
                          }"
                        >
                          {{ item2.name }}
                        </span>
                      </div>
                    </div>
                  </div> -->
                <!-- 零售价毛利 -->
                <!-- <div class="control-hid" v-if="merchantId">
                    <div class="control-box" v-if="!(licenseStatus === 1 || licenseStatus === 5)">
                      <priceBox
                        v-if="
                          (item.productInfo.isOEM != 'true' &&
                            (item.productInfo.isControl != 1 ||
                              (item.productInfo.isPurchase == true &&
                                item.productInfo.isControl == 1))) ||
                          (item.productInfo.isOEM == 'true' && item.productInfo.signStatus == 1)
                        "
                        :uniformPrice="item.productInfo.uniformPrice"
                        :suggestPrice="item.productInfo.suggestPrice"
                        :grossMargin="item.productInfo.grossMargin"
                      />
                    </div>
                  </div> -->
                <!-- </div> -->
              </div>
            </div>
            <a @click="toDetail(item.productInfo.id)">
              <div class="commonName">
                <!-- 药名和规格 -->
                <span class="name"
                  >{{ item.productInfo.showName.trim() }}{{ item.productInfo.spec }}</span
                >
              </div>
              <div class="collageBox">
                <div class="skText" v-if="item.productInfo.actSk">
                  距结束&nbsp;
                  <groupLimitTime
                    :endTime="(item.productInfo.actSk || {}).endTime"
                    timeColor="#F43000"
                  />
                </div>
              </div>
            </a>
          </div>
          <a
            @click="toDetail(item.productInfo.id)"
            class="float-carButton"
            v-show="showCarBtnIndex == item.productInfo.id"
          >
            <!-- <div class="collection" @click.stop.prevent="collection(item, index)">
              <div class="zone-1">
                <div v-if="item.productInfo.favoriteStatus == 2" class="top top-1">
                  <span class="w-icon-normal icon-normal-collectEpt"></span>
                </div>
                <div v-if="item.productInfo.favoriteStatus == 1" class="top top-2">
                  <span class="w-icon-normal icon-normal-collectFull"></span>
                </div>
              </div>
              <div class="zone-2">
                <div v-if="item.productInfo.favoriteStatus == 2" class="bottom bottom-1">
                  <p class="textOne">收藏</p>
                </div>
                <div v-if="item.productInfo.favoriteStatus == 1" class="bottom bottom-2">
                  <p class="textOne">已收藏</p>
                </div>
              </div>
            </div> -->
            <div v-if="item.productInfo.actPt">
              <!-- stepPriceStatus=2为单阶梯 -->
              <div v-if="item.productInfo.actPt.stepPriceStatus == 2">
                <div class="hoverBox">
                  <div class="circleBg"></div>
                  <div class="price-wrap">
                    <div class="price-container">
                      <span class="priceDec">￥</span>
                      <span v-if="getActivityStatus(item.productInfo.actPt) === 'inProgress'">
                        <!-- 多阶梯价格 -->
                        <span v-if="(item.productInfo.actPt || {}).stepPriceStatus == 1">
                          <span class="priceInt">{{
                            String((item.productInfo.actPt || {}).minSkuPrice.toFixed(2)).split(
                              "."
                            )[0]
                          }}</span>
                          <span class="priceFloat"
                            >.{{
                              String((item.productInfo.actPt || {}).minSkuPrice.toFixed(2)).split(
                                "."
                              )[1] || "00"
                            }}</span
                          >
                          -
                          <span class="priceInt">{{
                            String((item.productInfo.actPt || {}).maxSkuPrice.toFixed(2)).split(
                              "."
                            )[0]
                          }}</span>
                          <span class="priceFloat"
                            >.{{
                              String((item.productInfo.actPt || {}).maxSkuPrice.toFixed(2)).split(
                                "."
                              )[1] || "00"
                            }}</span
                          >
                        </span>
                        <!-- 单阶梯价格 -->
                        <span v-if="(item.productInfo.actPt || {}).stepPriceStatus == 2">
                          <span
                            class="priceInt"
                            v-if="getActivityStatus(item.productInfo.actPt) === 'inProgress'"
                            >{{
                              String((item.productInfo.actPt || {}).assemblePrice.toFixed(2)).split(
                                "."
                              )[0]
                            }}</span
                          >
                          <span
                            class="priceFloat"
                            v-if="getActivityStatus(item.productInfo.actPt) === 'inProgress'"
                            >.{{
                              String((item.productInfo.actPt || {}).assemblePrice.toFixed(2)).split(
                                "."
                              )[1] || "00"
                            }}</span
                          >
                        </span>
                      </span>
                      <span
                        class="priceInt"
                        v-if="getActivityStatus(item.productInfo.actPt) === 'notStart'"
                        >??</span
                      >
                      <span
                        class="priceFloat"
                        v-if="getActivityStatus(item.productInfo.actPt) === 'notStart'"
                        >.??</span
                      >
                    </div>
                    <!-- <div class="retailPrice" v-if="(item.productInfo.actPt || {}).stepPriceStatus == 2">￥{{item.productInfo.retailPrice.toFixed(2)}}</div> -->
                  </div>
                  <div class="ptShowName">
                    {{ item.productInfo.showName }}/{{ item.productInfo.spec }}
                  </div>
                  <div class="pCount">
                    <span class="ypCount"
                      >已拼{{ (item.productInfo.actPt || {}).orderNum
                      }}{{ item.productInfo.productUnit }}</span
                    >
                    <span class="qpCount"
                      >/{{ (item.productInfo.actPt || {}).skuStartNum
                      }}{{ item.productInfo.productUnit }}起拼</span
                    >
                  </div>
                  <div class="progressBox">
                    <div
                      class="progressContnet"
                      :style="{
                        width: `${
                          (item.productInfo.actPt || {}).percentage * 1 > 100
                            ? 100
                            : (item.productInfo.actPt || {}).percentage * 1
                        }%`,
                      }"
                    ></div>
                    <div
                      class="progressSpot"
                      :style="{
                        left: `${
                          (item.productInfo.actPt || {}).percentage * 1 > 100
                            ? 100
                            : (item.productInfo.actPt || {}).percentage * 1
                        }%`,
                      }"
                    ></div>
                  </div>
                  <span class="ptPercentage"
                    >{{ ((item.productInfo.actPt || {}).percentage * 1).toFixed(0) }}%</span
                  >
                  <cartBtnPt :scmE="scmE" :qtData="{...qtData,index,result_cnt:datalist.length}" :product-data="item.productInfo" @change-price="changePrice" />
                </div>
              </div>
              <!-- stepPriceStatus=1为多阶梯 -->
              <div v-else-if="item.productInfo.actPt.stepPriceStatus == 1" class="ptHoverBox">
                <div class="circleBg"></div>
                <!-- <div class="ptPrice">{{ item.productInfo.activePrice || `￥${item.productInfo.actPt.maxSkuPrice.toFixed(2)}` }}</div> -->
                <div class="price-wrap">
                  <div class="price-container">
                    <span class="priceDec">￥</span>
                    <span v-if="getActivityStatus(item.productInfo.actPt) === 'inProgress'">
                      <!-- 多阶梯价格 -->
                      <span v-if="(item.productInfo.actPt || {}).stepPriceStatus == 1">
                        <span class="priceInt">{{
                          String((item.productInfo.actPt || {}).minSkuPrice.toFixed(2)).split(
                            "."
                          )[0]
                        }}</span>
                        <span class="priceFloat"
                          >.{{
                            String((item.productInfo.actPt || {}).minSkuPrice.toFixed(2)).split(
                              "."
                            )[1] || "00"
                          }}</span
                        >
                        -
                        <span class="priceInt">{{
                          String((item.productInfo.actPt || {}).maxSkuPrice.toFixed(2)).split(
                            "."
                          )[0]
                        }}</span>
                        <span class="priceFloat"
                          >.{{
                            String((item.productInfo.actPt || {}).maxSkuPrice.toFixed(2)).split(
                              "."
                            )[1] || "00"
                          }}</span
                        >
                      </span>
                      <!-- 单阶梯价格 -->
                      <span v-if="(item.productInfo.actPt || {}).stepPriceStatus == 2">
                        <span
                          class="priceInt"
                          v-if="getActivityStatus(item.productInfo.actPt) === 'inProgress'"
                          >{{
                            String((item.productInfo.actPt || {}).assemblePrice.toFixed(2)).split(
                              "."
                            )[0]
                          }}</span
                        >
                        <span
                          class="priceFloat"
                          v-if="getActivityStatus(item.productInfo.actPt) === 'inProgress'"
                          >.{{
                            String((item.productInfo.actPt || {}).assemblePrice.toFixed(2)).split(
                              "."
                            )[1] || "00"
                          }}</span
                        >
                      </span>
                    </span>
                    <span
                      class="priceInt"
                      v-if="getActivityStatus(item.productInfo.actPt) === 'notStart'"
                      >??</span
                    >
                    <span
                      class="priceFloat"
                      v-if="getActivityStatus(item.productInfo.actPt) === 'notStart'"
                      >.??</span
                    >
                  </div>
                  <!-- <div class="retailPrice" v-if="(item.productInfo.actPt || {}).stepPriceStatus == 2">￥{{item.productInfo.retailPrice.toFixed(2)}}</div> -->
                </div>
                <div class="ptShowName">
                  {{ item.productInfo.showName }}/{{ item.productInfo.spec }}
                </div>
                <div
                  class="priceDesc"
                  v-if="!item.productInfo.controlTitle || item.productInfo.controlType == 5"
                >
                  <div
                    v-for="text in (item.productInfo.actPt || {}).stepPriceShowTexts"
                    :key="text"
                  >
                    {{ text }}
                  </div>
                </div>
                <div class="pCount">
                  <span class="ypCount"
                    >已拼{{ (item.productInfo.actPt || {}).orderNum
                    }}{{ item.productInfo.productUnit }}</span
                  >
                  <span class="qpCount"
                    >/{{ (item.productInfo.actPt || {}).skuStartNum
                    }}{{ item.productInfo.productUnit }}起拼</span
                  >
                </div>
                <div class="progressBox">
                  <div
                    class="progressContnet"
                    :style="{
                      width: `${
                        (item.productInfo.actPt || {}).percentage * 1 > 100
                          ? 100
                          : (item.productInfo.actPt || {}).percentage * 1
                      }%`,
                    }"
                  ></div>
                  <div
                    class="progressSpot"
                    :style="{
                      left: `${
                        (item.productInfo.actPt || {}).percentage * 1 > 100
                          ? 100
                          : (item.productInfo.actPt || {}).percentage * 1
                      }%`,
                    }"
                  ></div>
                </div>
                <span class="ptPercentage"
                  >{{ ((item.productInfo.actPt || {}).percentage * 1).toFixed(0) }}%</span
                >
                <div>
                  <cartBtnPt  :scmE="scmE" :qtData="{...qtData,index,result_cnt:datalist.length}" :product-data="item.productInfo" @change-price="changePrice" />
                </div>
              </div>
            </div>
            <div v-else-if="item.productInfo.actSk">
              <div class="ptHoverBox">
                <div class="circleBg"></div>
                <div class="ptShowName">
                  {{ item.productInfo.showName }}/{{ item.productInfo.spec }}
                </div>
                <div class="progressBox">
                  <div
                    class="progressContnet"
                    :style="{
                      width: `${
                        (item.productInfo.actSk || {}).percentage * 1 > 100
                          ? 100
                          : (item.productInfo.actSk || {}).percentage * 1
                      }%`,
                    }"
                  ></div>
                  <div
                    class="progressSpot"
                    :style="{
                      left: `${
                        (item.productInfo.actSk || {}).percentage * 1 > 100
                          ? 100
                          : (item.productInfo.actSk || {}).percentage * 1
                      }%`,
                    }"
                  ></div>
                </div>
                <span class="ptPercentage"
                  >{{ ((item.productInfo.actSk || {}).percentage * 1).toFixed(0) }}%</span
                >
                <div>
                  <cartBtn :scmE="scmE" :qtData="{...qtData,index,result_cnt:datalist.length}" goodsType="actSk" :product-data="item.productInfo" />
                </div>
              </div>
            </div>
            <div v-else-if="item.productInfo.actPgby">
              <!-- <div class="toStore" @click.stop.prevent="toStore(item.productInfo.shopUrl)">
                <div class="storeIcon">
                  <img src="@/assets/images/icon-store.png" />
                </div>
                <div class="toStoreText">进店</div>
              </div> -->
              <div class="ptHoverBox">
                <div class="circleBg"></div>
                <div class="price-wrap">
                  <div class="price-container">
                    <span class="priceDec">￥</span>
                    <span>
                      <span>
                        <span class="priceInt">{{
                          String((item.productInfo.actPgby || {}).assemblePrice.toFixed(2)).split(
                            "."
                          )[0]
                        }}</span>
                        <span class="priceFloat"
                          >.{{
                            String((item.productInfo.actPgby || {}).assemblePrice.toFixed(2)).split(
                              "."
                            )[1] || "00"
                          }}</span
                        >
                      </span>
                    </span>
                  </div>
                  <!-- <div class="retailPrice" v-if="(item.productInfo.actPt || {}).stepPriceStatus == 2">￥{{item.productInfo.retailPrice.toFixed(2)}}</div> -->
                </div>
                <div class="ptShowName">
                  {{ item.productInfo.showName }}/{{ item.productInfo.spec }}
                </div>
                <!-- <div class="progressBox">
                  <div class="progressContnet" :style="{width: `${(item.productInfo.actSk || {}).percentage*1 > 100 ? 100 : (item.productInfo.actSk || {}).percentage*1}%`}"></div>
                  <div class="progressSpot" :style="{left: `${(item.productInfo.actSk || {}).percentage*1 > 100 ? 100 : (item.productInfo.actSk || {}).percentage*1}%`}"></div>
                </div> -->
                <!-- <span class="ptPercentage">{{((item.productInfo.actSk || {}).percentage*1).toFixed(0)}}%</span> -->
                <div>
                  <!-- <cartBtn goodsType="actPgby" :product-data="item" /> -->
                  <cartBtnPgby  :scmE="scmE" :qtData="{...qtData,index,result_cnt:datalist.length}" :product-data="item.productInfo" @change-price="changePrice" />
                </div>
              </div>
            </div>
            <div v-else>
              <div class="hoverBox levelHoverBox">
                <div class="circleBg"></div>
                <div class="price-wrap" v-if="item.productInfo.levelPriceDTO">
                  <div class="price-container-box">
                    <span>{{ item.productInfo.levelPriceDTO.rangePriceShowText }}</span>
                  </div>
                  <!-- <div class="retailPrice" v-if="(item.actPt || {}).stepPriceStatus == 2">￥{{item.retailPrice.toFixed(2)}}</div> -->
                </div>
                <div class="ptShowName">
                  {{ item.productInfo.showName }}/{{ item.productInfo.spec }}
                </div>
                <div
                  class="priceDesc"
                  v-if="
                    (!item.productInfo.controlTitle || item.productInfo.controlType == 5) &&
                    item.productInfo.levelPriceDTO
                  "
                >
                  <div
                    v-for="text in (item.productInfo.levelPriceDTO || {}).stepPriceShowTexts"
                    :key="text"
                  >
                    {{ text }}
                  </div>
                </div>
                <div>
                  <cartBtn  :scmE="scmE" :qtData="{...qtData,index,result_cnt:datalist.length}" :product-data="item.productInfo" :track-info="trackInfo" />
                </div>
              </div>
              <!-- <div class="inputNumber" v-if="merchantId">
                <div
                  v-if="
                    (item.productInfo.isOEM == 'true' && item.productInfo.signStatus == 0) ||
                    item.productInfo.showAgree == 0
                  "
                ></div>
                <div v-else style="height: 100%;">
                  <cartBtn
                    v-if="
                      (item.productInfo.isControl != 1 ||
                        (item.productInfo.isPurchase == true && item.productInfo.isControl == 1)) &&
                      !(licenseStatus === 1 || licenseStatus === 5)
                    "
                    :is-pack="false"
                    :data-id="item.productInfo.id"
                    :is-split="item.productInfo.isSplit"
                    :product-num="item.productInfo.cartProductNum"
                    :med-num="item.productInfo.mediumPackageNum"
                  ></cartBtn>
                </div>
              </div> -->
            </div>
          </a>
        </li>
      </template>
    </ul>
  </div>
</template>

<script setup>
import cartBtn from "@/components_pc/addCartBtn.vue";
import cartBtnPt from "@/components_pc/addCartBtnPt.vue";
import cartBtnPgby from "@/components_pc/addCartBtnPgby.vue";
import priceBox from "@/components_pc/priceBox.vue";
import { putRequest, handleDiscount } from "@/http_pc/api";
import { findSpuAggregatSku } from "@/http_pc/productSpu/index";

import groupProduct from "./groupProduct.vue";
import groupPgbyProduct from "./groupPgbyProduct.vue";
import groupPgbyProductRun from "./groupPgbyProductRun.vue";
import groupProductRun from "./groupProductRun.vue";

import groupLimitTime from "@/components_pc/groupLimitTime.vue";
import { onMounted, ref, watch, getCurrentInstance, nextTick } from "vue";
import { actionTracking } from "@/config/eventTracking";

const props = defineProps({
  goodsData: {
    default: [],
  },
  trackInfo: {
    default: {},
  },
  licenseStatus: {
    default: 0,
  },
  body_color: {
    default: "#fafafa",
  },
  searchSortStrategyId: {
    default: "",
  },
  qtData: {
    default: {},
  },
  scmE:{
    default: '',
  }
});
let qtData=props.qtData
const detailUrl = import.meta.env.VITE_BASE_URL_PC;
const showCarBtnIndex = ref(null);
const datalist = ref(props.goodsData);
const showScrollBtn = ref(false);

//datalist.value.forEach(item => {
//  if (item.productInfo.tags.dataTags) {
//    let arr = [];
//    item.productInfo.tags.productTags.forEach(k => {
//      if (item.productInfo.tags.dataTags.findIndex(f => f.text == k.text) == -1) {
//        arr.push(k);
//      }
//    })
//    item.productInfo.tags.productTags = arr;
//  }
//})

const imgUrl = import.meta.env.VITE_IMG;
const length = ref(10);
const { proxy } = getCurrentInstance();
const merchantId = proxy.$merchantId; //proxy.$merchantId

onMounted(() => {
  // findSpuAggregatSku({ id: 7503, merchantId: ********** });
  // datalist.value = props.goodsData;
  addDiscount(props.goodsData);
  nextTick(() => {
    clickScroll();
  });

  // WS.Bus.$on("cart_isExtend", (isExtend, id) => {
  //   let original = document.querySelectorAll(`i[data-original-id]`);
  //   for (let i = 0; i < original.length; i++) {
  //     if (original[i].getAttribute("data-original-id") == id) {
  //       if (isExtend) {
  //         original[i].style.display = "none";
  //       } else {
  //         original[i].style.display = "inline-block";
  //       }
  //     }
  //   }
  //   let pageage = document.querySelectorAll(`div[data-pageage-id]`);
  //   for (let i = 0; i < pageage.length; i++) {
  //     if (pageage[i].getAttribute("data-pageage-id") == id) {
  //       if (isExtend) {
  //         pageage[i].style.display = "block";
  //         pageage[i].previousElementSibling.style.width = "40%";
  //       } else {
  //         pageage[i].previousElementSibling.style.width = "90%";
  //         pageage[i].style.display = "none";
  //       }
  //     }
  //   }
  // });
});

function clickScroll() {
  const scrollContainer = document.querySelector(".spulist");
  const maxScroll = scrollContainer.scrollWidth - scrollContainer.clientWidth;
  console.log(scrollContainer.scrollWidth, scrollContainer.clientWidth);
  if (maxScroll <= 0) return;
  showScrollBtn.value = true;
  nextTick(() => {
    document.getElementById("scrollToLeft").addEventListener("click", function () {
      scrollContainer.scrollLeft -= 240; // 向左滚动100px
      if (scrollContainer.scrollLeft < 240) {
        scrollContainer.scrollLeft = 0; // 防止滚动到左侧边界之外
      }
    });

    document.getElementById("scrollToRight").addEventListener("click", function () {
      scrollContainer.scrollLeft += 240; // 向右滚动100px
      if (scrollContainer.scrollLeft > maxScroll - 240) {
        scrollContainer.scrollLeft = maxScroll; // 防止滚动到右侧边界之外
      }
    });
  });
}
const go = (e, operationInfo) => {
  e.stopPropagation();
  // actionTracking("pc_search_Active_Click", {
  //   active_url: operationInfo.jumpUrl,
  //   active_title: operationInfo.title,
  //   active_type: operationInfo.showType,
  // });
  window.open(operationInfo.jumpUrl);
};
const fixedtwo = (val) => {
  if (val === 0) return "0.00";
  if (!val) return "";
  return parseFloat(val).toFixed(2);
};

function parseRGBA(val) {
  val = val.trim().toLowerCase(); //去掉前后空格
  if (val.length > 8) {
    let color = {};
    try {
      let argb = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(val);
      color.r = parseInt(argb[2], 16);
      color.g = parseInt(argb[3], 16);
      color.b = parseInt(argb[4], 16);
      color.a = parseInt(argb[1], 16) / 255;
    } catch (e) {
      console.log(e);
    }
    return (
      "rgba(" + color.r + "," + color.g + "," + color.b + "," + parseFloat(color.a).toFixed(1) + ")"
    );
  } else {
    return val;
  }
}

watch(
  () => props.goodsData,
  (new_val) => {
    if (new_val) {
      addDiscount(new_val);
    }
  },
  {
    immediate: false,
    deep: true,
  }
);
const collection = (item, index) => {
  const url =
    item.favoriteStatus == 2
      ? "/merchant/center/collection/attention.json"
      : "/merchant/center/collection/cancelAttention.json";
  putRequest("get", url, { skuId: item.id })
    .then((res) => {
      if (res.status == "success") {
        if (item.favoriteStatus == 2) {
          item.favoriteStatus = 1;
          datalist.value.splice(index, 1, item);
        } else {
          item.favoriteStatus = 2;
          datalist.value.splice(index, 1, item);
        }
      } else {
        alert(res.errorMsg);
      }
    })
    .catch((err) => {
      console.log("收藏错误", err);
    });
};

const mouseOver = (id) => {
  showCarBtnIndex.value = id;
};
const mouseLeave = (id) => {
  showCarBtnIndex.value = null;
};
// 添加折后价
const addDiscount = (data) => {
  if (data && data.length > 0) {
    const idList = data.map((item) => {
      return item.id;
    });
    const params = {
      skuIds: idList.join(","),
      merchantId,
    };
    handleDiscount(params)
      .then((res) => {
        if (res.status == "success") {
          const priceArr = res.data;
          priceArr.map((item, index) => {
            data.map((item2, index2) => {
              //   if (item.skuId == item2.id) {
              //     // this.$set(item2, 'zheHouPrice', item.price)
              //     const zheHouPrice = Number(item.price.split('￥')[1]);
              //     if (!isNaN(zheHouPrice) && zheHouPrice < item2.fob) {
              //       item2.zheHouPrice = item.price;
              //     }
              //   }
              if (item2.cardType == 1) {
                //普通

                if (item.skuId == item2.productInfo.id) {
                  // this.$set(item2, 'zheHouPrice', item.price)
                  const zheHouPrice = Number(item.price.split("￥")[1]);
                  if (!isNaN(zheHouPrice) && zheHouPrice < item2.productInfo.fob) {
                    item2.productInfo.zheHouPrice = item.price;
                  }
                }
              } else {
                //运营

                if (item.skuId == item2.operationInfo.products[0].id) {
                  // this.$set(item2, 'zheHouPrice', item.price)
                  const zheHouPrice = Number(item.price.split("￥")[1]);
                  if (!isNaN(zheHouPrice) && zheHouPrice < item2.operationInfo.products[0].fob) {
                    item2.operationInfo.products[0].zheHouPrice = item.price;
                  }
                }
              }
            });
          });
        }
      })
      .catch((err) => {
        console.log(err, "请求失败了");
      });
  }
  datalist.value = data;
};
const toStore = (url, operationInfo) => {
  // const a = document.createElement("a"); // 创建a标签
  // a.href = url;
  // a.click();
  if (operationInfo) {
    // actionTracking("pc_search_Store_Click", {
    //   shop_name: operationInfo.products[0].shopName,
    //   shop_code: operationInfo.products[0].shopCode,
    // });
  }
  window.open(url);
};
const toDetail = (id, operationInfo, index) => {
  if (operationInfo) {
    //   actionTracking("pc_action_Product_Click", {
    //     commodityId: id,
    //     commodityName: operationInfo.products[0].commonName,
    //     commodityCode: operationInfo.products[0].barcode,
    //     commodityCategory: operationInfo.products[0].categoryId,
    //     spid: props.trackInfo.spid,
    //     sid: props.trackInfo.sid,
    //     sptype: props.trackInfo.sptype,
    //     real: 2,
    //     search_sort_strategy_id: props.searchSortStrategyId,
    //     active_type: 0,
    //     click_item: 3,
    //     goods_groupid: operationInfo.products[0].operationExhibitionId,
    //     active_id: operationInfo.products[0].operationId,
    //     active_index: index,
    //   });
    // } else {
    //   actionTracking("pc_page_CommodityDetails", {
    //     commodityId: id,
    //     spid: props.trackInfo.spid,
    //     sid: props.trackInfo.sid,
    //     sptype: props.trackInfo.sptype,
    //     user_id: merchantId,
    //     search_sort_strategy_id: props.searchSortStrategyId,
    //   });
  }

  window.open(`${detailUrl}search/skuDetail/${id}.htm`);
};
const changePrice = (id, price) => {
  datalist.value.forEach((item) => {
    if (item.id == id) {
      item.activePrice = price;
      // this.$set(item, 'activePrice', price);
    }
  });
};

const getActivityStatus = (item) => {
  return item.assembleStatus === 0 ? "notStart" : item.assembleStatus === 1 ? "inProgress" : "";
};
//PC商品详情页-热销商品 曝光
const exposureView=(el,data)=>{
  // console.log(qtData.commonName,"lwq")
  if(qtData.commonName=="productDetail"&&qtData.isShopDetail&&window.parent.getSpmE){
    let params={
      "spm_cnt":`1_4.productDetail_${qtData.id}-<EMAIL>@${data.index+1}.${window.parent.getSpmE()}`,
            "scm_cnt":`productBusiness.${props.searchSortStrategyId||'0'}.all_0.prod-${data.id}.${props.scmE}`,
            // "result_cnt":datalist.value.length,
            "product_id": data.id,
            "product_name":data.name
    }

      aplus_queue.push({
            'action': 'aplus.record',
            'arguments': ['page_list_product_exposure', 'EXP',params]
    });
  }
}
//商品点击
const shopClick=(data)=>{
  if(qtData.commonName=="productDetail"&&qtData.isShopDetail&&window.parent.getSpmE){
      aplus_queue.push({
            'action': 'aplus.record',
            'arguments': ['action_list_product_click', 'EXP', {
            "spm_cnt":`1_4.productDetail_${qtData.id}-<EMAIL>@${data.index+1}.${window.parent.getSpmE()}`,
            "scm_cnt":`productBusiness.${props.searchSortStrategyId||'0'}.all_0.prod-${data.id}.${props.scmE+proxy.scmEActive(6)}`,
            // "result_cnt":datalist.value.length,
            "product_id": data.id,
            "product_name":data.name
          }]
    });
  }
}
</script>

<style lang="scss" scoped>
.spuBox {
  position: relative;
  padding: 22px 18px 0 18px;
  // border: 1px solid red;
  overflow: hidden;
  background-color: #fff;
}
.spu-p {
  font-size: 20px;
  font-weight: 500 !important;
  margin: 5px 0;
}
.spulist {
  display: flex;
  display: -webkit-flex;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  margin-bottom: -5px;
  .spulist-item {
    background: #fff;
    position: relative;
    // overflow: hidden;
    width: 240px;
    box-sizing: border-box;
    // padding: 20px 17px 0 17px;
    padding: 0 17px;
    height: 310px;
    // border: 1px solid #f1f1f1;
    .float-carButton {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 240px;
      height: 396px;
      // box-shadow: 2px 2px 10px 0 rgba(0, 0, 0, 0.2);
      // border: 1px solid #e0e0e0;
      box-sizing: border-box;
      display: block;
      .price-wrap {
        display: flex;
        align-items: flex-end;
        font-weight: Medium;
        .price-container {
          color: #ff2121;
          span {
            font-weight: bold;
          }
          .priceDec {
            font-size: 14px;
          }
          .priceInt {
            font-size: 22px;
          }
          .priceFloat {
            font-size: 18px;
          }
        }
        .retailPrice {
          font-size: 12px;
          color: #666666;
          text-decoration: line-through;
          margin-left: 8px;
        }
      }
      .collection {
        position: absolute;
        top: 5px;
        right: 0px;
        z-index: 100;
        display: block;
        height: 49px;
        width: 40px;
        border: 0px solid #ccc;
        overflow: hidden;
        vertical-align: middle;
        text-align: center;
        cursor: pointer;
        .zone-1 {
          height: 24px;
          overflow: hidden;
          position: relative;
          .top {
            position: relative;
            height: 24px;
            width: 40px;
          }
          .top-1 {
            animation: sliderDown 0.8s;
            animation-fill-mode: forwards;
          }
          @keyframes sliderDown {
            from {
              -webkit-transform: translateY(-24px);
              transform: translateY(-24px);
            }
            to {
              -webkit-transform: translateY(0);
              transform: translateY(0);
            }
          }
          .top-2 {
            -webkit-animation: sliderUp 0.8s;
            animation: sliderUp 0.8s;
            -webkit-animation-fill-mode: forwards;
            animation-fill-mode: forwards;
          }
          @keyframes sliderUp {
            from {
              -webkit-transform: translateY(24px);
              transform: translateY(24px);
            }
            to {
              -webkit-transform: translateY(0);
              transform: translateY(0);
            }
          }
          .w-icon-normal {
            margin-top: 6px;
          }
          .icon-normal-collectEpt {
            width: 16px;
            height: 16px;
            background: url(@/assets/images/search/shoucang.png) no-repeat;
            display: inline-block;
          }
          .icon-normal-collectFull {
            width: 16px;
            height: 16px;
            background: url(@/assets/images/search/shoucang2.png) no-repeat;
            display: inline-block;
          }
        }
        .zone-2 {
          height: 23px;
          overflow: hidden;
          position: relative;
          .bottom {
            height: 23px;
            font-size: 13px;
            line-height: 23px;
            color: #999;
            p {
              font-size: 12px;
              color: #adadad;
            }
          }
        }
      }
      .toStore {
        position: absolute;
        top: 5px;
        right: 7px;
        font-size: 12px;
        text-align: center;
        cursor: pointer;
        .storeIcon {
          width: 16px;
          height: 16px;
          margin: 4px auto;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .toStoreText {
          font-size: 12px;
          color: #adadad;
        }
      }
      .hoverBox {
        // width: 100%;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 0 10px 10px;
        background: #222c38;
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #ffffff;
        .circleBg {
          position: absolute;
          top: -9px;
          left: 0;
          right: 0;
          background: url("@/assets/images/search/yh.png") no-repeat;
          background-size: 100%;
          height: 10px;
        }
      }
      .levelHoverBox {
        .price-wrap {
          display: flex;
          align-items: baseline;
          font-weight: Medium;
          margin: 4px 0 6px 0;
          .price-container {
            display: flex;
            align-items: baseline;
            color: #ff2121;
            .priceDec {
              font-size: 14px;
            }
            .priceInt {
              font-size: 22px;
            }
            .priceFloat {
              font-size: 18px;
            }
          }
          .price-container-box {
            color: #ff2121;
            font-size: 20px;
          }
          .retailPrice {
            font-size: 12px;
            color: #666666;
            text-decoration: line-through;
            margin-left: 8px;
          }
        }
        .priceDesc {
          div {
            color: #ffcd86;
            font-size: 12px;
            margin-top: 6px;
          }
          margin: 10px 0;
        }
      }
      .progressBox {
        width: 148px;
        height: 4px;
        background: #f5f5f5;
        border-radius: 2px;
        position: relative;
        display: inline-block;
        margin-right: 10px;
        .progressContnet {
          height: 4px;
          background-image: linear-gradient(90deg, #fc6c41 0%, #f62526 100%);
          border-radius: 2px;
        }
        .progressSpot {
          position: absolute;
          top: -3px;
          width: 6px;
          height: 6px;
          border: 1.5px solid #fff;
          border-radius: 50%;
          background: #ff2121;
        }
      }
      .ptPercentage {
        color: #ff6f48;
      }
      .pCount {
        font-size: 12px;
        margin-top: 4px;
        .ypCount {
          color: #ff6f48;
        }
        .qpCount {
          color: #b4b8bc;
        }
      }
      .ptHoverBox {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: #222c38;
        padding: 10px;
        // border-radius: 10px 10px 0 0;
        .circleBg {
          position: absolute;
          top: -9px;
          left: 0;
          right: 0;
          background: url("@/assets/images/search/yh.png") no-repeat;
          background-size: 100%;
          height: 10px;
        }
        .ptPrice {
          color: #ff5b5b;
          font-size: 16px;
        }
        .priceDesc {
          div {
            color: #ffcd86;
            font-size: 12px;
            margin-top: 6px;
          }
          margin: 10px 0;
        }
        .progressBox {
          width: 148px;
          margin-top: 10px;
        }
      }
      .ptShowName {
        color: #fff;
        font-size: 14px;
        margin-top: 10px;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        /* autoprefixer: off */
        -webkit-box-orient: vertical;
        /* autoprefixer: on */
        -webkit-line-clamp: 3;
      }
      .inputNumber {
        width: 100%;
        height: 48px;
        position: absolute;
        right: 0;
        bottom: 0;
        background: #2f3846;
        .no-goods {
          img {
            position: absolute;
            top: rem(5);
            right: rem(10);
            width: rem(40);
            height: rem(40);
          }
        }
      }
    }
    a {
      display: block;
    }
    .images {
      // width: 146px;
      // height: 99px;
      width: 185px;
      height: 180px;
      box-sizing: content-box;
      margin: auto;
      padding: 0 9px;
      position: relative;
      .sold-out {
        position: absolute;
        left: 50%;
        top: 50%;
        margin: -30px 0 0 -30px;
        width: 60px;
        height: 60px;
        background-color: rgba(0, 0, 0, 0.4);
        text-align: center;
        font-size: 14px;
        color: #fff;
        border-radius: 50%;
        line-height: 60px;
      }
      .pic {
        // max-width: 146px;
        // max-height: 99px;
        max-width: 185px;
        max-height: 180px;
        display: block;
        margin: auto;
        object-fit: contain;
      }
      .activity-token {
        position: absolute;
        width: 185px;
        height: auto;
        max-width: 100%;
        margin-top: -6px;
        top: 2px;
        left: 0px;
        z-index: 3;
      }
      .mark-text {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        z-index: 5;
        img {
          width: 100%;
          height: 100%;
        }
        h4 {
          position: absolute;
          left: 0;
          bottom: 0;
          font-size: rem(20);
          color: #fff;
          width: 100%;
          text-align: center;
        }
      }
      .promotionSkuPrice {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 5;
        span {
          position: absolute;
          right: 0;
          bottom: 0;
          font-size: 14px;
          color: #ff6201;
          display: flex;
          display: -webkit-flex;
          justify-content: center;
          -webkit-justify-content: center;
          align-items: center;
          -webkit-align-items: center;
          width: rem(140);
          height: rem(40);
          text-shadow: 1px 1px 1px #ffd9b4;
          font-weight: 600;
        }
      }
      .active-tags {
        position: absolute;
        left: 50%;
        top: 16px;
        z-index: 5;
        margin-left: -92.5px;
        img {
          width: 185px;
          height: auto;
        }
        .timeStr {
          left: 0;
          position: absolute;
          bottom: 47px;
          text-align: center;
          width: 174px;
          height: 10px;
          line-height: 10px;
          span {
            color: #ffffff;
            display: block;
            font-size: 12px;
            text-align: center;
            transform: scale(0.65);
          }
        }
        .temtejia806 {
          font-size: 12px;
          text-shadow: none;
          width: auto;
          font-weight: 400;
          text-align: center;
          color: #fff;
          position: absolute;
          bottom: 25px;
          left: 0;
          width: 174px;
          overflow: hidden;
          height: 18px;
          span {
            display: inline-block;
          }
        }
      }
    }
    .nearEffectBox {
      background: rgba(0, 0, 0, 0.6);
      font-size: 12px;
      color: #ffffff;
      height: 22px;
      line-height: 22px;
      position: relative;
      padding-left: 10px;
      .countTag {
        text-align: right;
        padding-right: 10px;
        position: absolute;
        top: 0;
        right: 0;
        height: 22px;
        width: 60px;
        background: url("@/assets/images/search/tag.png") no-repeat;
        background-size: 100% 100%;
      }
    }
    .leftText {
      padding-left: 8px;
      text-align: left;
    }

    .info {
      padding: 4px 0 0 0;
      overflow: hidden;
      .commonName {
        margin-bottom: 5px;
        font-size: 16px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: left;
        color: #333333;
        line-height: 22px;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        /* autoprefixer: off */
        -webkit-box-orient: vertical;
        /* autoprefixer: on */
        -webkit-line-clamp: 2;
      }
      .actBox {
        white-space: nowrap;
      }
      .tagBox {
        margin: 6px;
        margin-left: 0;
        white-space: nowrap;
        display: inline-block;
        .tagItem {
          margin-right: 8px;
          padding: 1px 4px;
          font-size: 12px;
          border-radius: 4px;
        }
        .tagItemBig {
          padding: 4px;
          display: inline-block;
        }
        .tejia {
          color: #fff;
          background-image: linear-gradient(270deg, #ff0000 1%, #f96401 100%);
          border: 1px solid #ff9aa3;
          border-radius: 4px;
          padding: 3px 5px;
        }
        .zhijiang {
          color: #fff;
          background-image: linear-gradient(270deg, #8001ee 1%, #6a01f9 100%);
          border: 1px solid #e9d3ff;
          border-radius: 4px;
          padding: 3px 5px;
        }
      }
      .manufacturer {
        background: none;
        font-size: 12px;
        color: #999999;
        // margin: 2px 0 8px;
      }
      .collageBox {
        display: flex;
        align-items: center;
        font-size: 14px;
        .skText {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          width: 200px;
          height: 24px;
          background: url("@/assets/images/search/msTime.png") no-repeat;
          background-size: 100%;
          padding: 0 30px;
          color: #222222;
        }
        .couponBg {
          // width: 200px;
          display: inline-block;
          font-size: 12px;
          color: #f1081c;
          height: 24px;
          line-height: 24px;
          background: url("@/assets/images/search/coupon.png") no-repeat;
          background-size: 100% 100%;
          padding: 0 4px;
          margin-right: 4px;
        }
      }
      .price-add-wrap {
        display: flex;
        display: -webkit-flex;
        flex-wrap: nowrap;
        -webkit-flex-wrap: nowrap;
        justify-content: space-between;
        position: relative;
        padding-top: 4px;
        .price-wrap {
          display: flex;
          display: -webkit-flex;
          flex-wrap: nowrap;
          -webkit-flex-wrap: nowrap;
          flex-direction: column;
          overflow: hidden;
          .price-container {
            display: flex;
            display: -webkit-flex;
            flex-wrap: nowrap;
            -webkit-flex-wrap: nowrap;
            justify-content: space-between;
            .controlTitleText {
              color: #f49926;
              font-weight: 500;
              font-size: 18px;
            }
            .qualifications {
              font-size: 18px;
              font-family: PingFangSC, PingFangSC-Medium;
              font-weight: 500;
              text-align: center;
              color: #e3293a;
              line-height: 25px;
            }
            .nobuy {
              font-size: 18px;
              font-family: PingFangSC, PingFangSC-Medium;
              font-weight: 500;
              text-align: center;
              color: #e3293a;
              line-height: 25px;
            }
            .price-numer {
              font-size: 18px;
              font-family: PingFangSC, PingFangSC-Medium;
              font-weight: 500;
              text-align: center;
              color: #e3293a;
              line-height: 25px;
              .price-permission {
                color: #f39800;
              }
              .price-numer-i {
                color: #ff2121;
                font-size: 20px;
                font-family: PingFangSC;
                font-weight: bold;
              }
              .pricewapper {
                .price-box {
                  overflow: hidden;
                  color: #ff2121;
                  .price-two {
                    p {
                      color: #ff2121;
                    }

                    letter-spacing: 0;
                    span {
                      font-weight: bold;
                    }
                    .priceDec {
                      font-size: 14px;
                    }
                    .priceInt {
                      font-size: 22px;
                    }
                    .priceFloat {
                      font-size: 18px;
                    }
                    // p {
                    //   span {
                    //     color: #ff2121;
                    //   }
                    .zheHouPrice {
                      color: #ff2121;
                      .zheHouText {
                        font-weight: normal;
                        color: #666;
                      }

                      font-size: 12px;
                      font-weight: 400;
                      margin-left: 6px;
                    }
                    // }
                  }
                  .midPack {
                    line-height: rem(40);
                    text-align: right;
                    width: 35%;
                    font-size: rem(20);
                    color: #999;
                  }
                }
              }
            }
          }
          .control-hid {
            .control-box {
              margin-top: 5px;
            }
          }
        }
      }
      .label-box {
        margin: 2px 0 0 0;
        .labels {
          height: 18px;
          line-height: 18px;
          position: relative;
          font-size: 0;
          display: flex;
          display: -webkit-flex;
          flex-wrap: nowrap;
          -webkit-flex-wrap: nowrap;
          justify-content: space-between;
          .labels-span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-left: 0;
            span {
              margin-right: 5px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              height: 18px;
              line-height: 18px;
              background: #fe0800;
              border-radius: 2px;
              color: #ffffff;
              font-size: 12px;
              padding: 0 5px;
              display: inline-block;
              width: auto;
              margin-left: 0;
              border: 0;
            }
            .fullXReductionY {
              position: relative;
              background: rgba(255, 147, 143, 1);
              color: rgba(255, 255, 255, 1);
              letter-spacing: 1px;
            }
            .fullXReductionY:before {
              content: "";
              display: inline-block;
              background-color: #fff;
              width: rem(7);
              height: rem(14);
              border-radius: 0 rem(14) rem(14) 0;
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
            }
            .fullXReductionY:after {
              content: "";
              display: inline-block;
              background-color: #fff;
              width: rem(7);
              height: rem(14);
              border-radius: rem(14) 0 0 rem(14);
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
            }
          }
          /*标签样式*/
          @import "@/assets/style/tagStyle";
          .more {
            max-width: rem(54);
            background: url("../../app/assets/images/dot.png") no-repeat center;
            background-size: 100%;
            width: rem(54);
          }
        }
      }
      .self-support {
        margin-top: 1px;
        height: 21px;
        display: flex;
        align-items: center;
        white-space: nowrap;
        .shrink0 {
          flex-shrink: 0;
        }

        span {
          background-color: #00b955;
          border-radius: 2px;
          color: #ffffff;
          font-size: 12px;
          padding: 2px;
          display: inline-block;
        }
        .shopName {
          flex-wrap: nowrap;
          background: none;
          font-size: 12px;
          color: #999999;
          display: inline-block;
          flex-grow: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .maxWidthName {
          max-width: 70px;
        }
        .purchaseTags {
          background: none;
          color: #009d48;
        }
      }
    }
  }
  .spulist-item:hover {
    cursor: pointer;
  }

  // .spulist-item:nth-child(4n + 0) {
  //   border-right: 0;
  // }
  // .spulist-item:last-child(4n + 0) {
  //   border-right: 0;
  // }
}
#scrollToLeft {
  cursor: pointer;
  font-size: 22px;
  color: #fff;
  border-radius: 100%;
  position: absolute;
  z-index: 999;
  left: 0;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 70px;
  height: 70px;
  line-height: 70px;
  background: #00000033;
}
#scrollToRight {
  cursor: pointer;
  font-size: 22px;
  color: #fff;
  border-radius: 100%;
  position: absolute;
  z-index: 999;
  right: 0;
  top: 50%;
  transform: translate(50%, -50%);
  width: 70px;
  height: 70px;
  line-height: 70px;
  background: #00000033;
}
</style>
