<template>
  <div class="recharge">
    <div class="explain" style="color: #666666;">
      请使用您当前绑定的银行卡，向平台的待清算专户打款。打款成功后，您的平安商户余额会实时增加。<br>
      请特别注意以下事项：<br>
      <span style="color: red;">
        1. 户名：平安银行电子商务交易资金待清算专户(药帮忙)，后面的括号是英文括号！！如果输入中文括号会打款失败<br>
        2. 只能使用已绑定的银行卡打款！！使用其他银行卡会打款失败，钱会自动退回到打款银行卡
      </span>
    </div>
    <div class="info" style="background: #FFFFFF;background: #F7F7F8;">
      <div style="border-radius: 10px;padding:10px;background: white;box-sizing: content-box;">
        <h4 style="color: #292933;font-size: 17px;">药帮忙交易资金待清算专户</h4>
        <div class="item flex">
          <span class="text">开户行号</span>
          <span class="num">{{ formModal.backNo }}</span>
          <span class="line"></span>
          <div class="copy" :data-clipboard-text="formModal.backNo" @click="copy($event)">复制</div>
        </div>
        <div class="item flex">
          <span class="text">开户行名称</span>
          <span class="num">{{ formModal.backName }}</span>
          <span class="line"></span>
          <div class="copy" :data-clipboard-text="formModal.backName" @click="copy($event)">复制</div>
        </div>
        <div class="item flex">
          <span class="text">账户名</span>
          <span class="num">{{ formModal.backAccountName }}</span>
          <span class="line"></span>
          <div class="copy" :data-clipboard-text="formModal.backAccountName" @click="copy($event)">复制</div>
        </div>
        <div class="item flex">
          <span class="text">账户</span>
          <span class="num">{{ formModal.backAccountNo }}</span>
          <span class="line"></span>
          <div class="copy" :data-clipboard-text="formModal.backAccountNo" @click="copy($event)">复制</div>
        </div>
      </div>
    </div>
    <div class="info mt22" style="background: #F7F7F8;">
      <div style="background: white;padding-bottom: 20px;border-radius: 10px; ">
        <h4 style="color: #292933;border: none;padding: 12px;padding-bottom: 0;margin-bottom: 0;font-size: 17px;">绑定银行卡&nbsp;&nbsp;<span
            v-if="(((formModal || {}).creditCardDtoList) || []).length" style="color: #292933;font-size: 17px;">({{ (((formModal || {}).creditCardDtoList) || []).length }})</span>
        </h4>
        <div class="info-form not-card" v-if="[33, 1, 2, 4, 8, 16].findIndex(item => item === cardStatus) > -1">
          <div style="width:100%;padding: 10px;padding-bottom: 0;background: white;box-sizing: border-box;border-radius: 10px;">
            <!-- <div class="info-form-img">
                <img v-if="cardStatus === 0" src="@/assets/images/pinganMerchant/card-pgs.png" alt="">
                <img v-if="cardStatus === 1 || cardStatus === 2 || cardStatus === 4" src="@/assets/images/pinganMerchant/card-pgs-ing.png" alt="">
                <img v-if="cardStatus === 8 || cardStatus === 16" src="@/assets/images/pinganMerchant/card-pgs-error.png" alt="">
              </div> -->
            <div v-if="cardStatus === 33" style="text-align: center;">
              <img src="./img/noCar.png" alt="" style="width: 120px;"><br>
              <div>暂无绑定银行卡，可点击“绑定新卡”进行申请</div>
            </div>
            <!-- <div class="account-item-text" v-if="cardStatus === 0">您还没有绑定银行卡，点击“前往绑卡”申请绑卡</div> -->
            <div class="account-item-text" v-if="cardStatus === 2">
              <div style="margin-bottom: 10px;color: #292933;font-size: 16px;">绑卡申请记录</div>
              <span @click="handleGoChangeBankCard" style="font-size: 15px;position: absolute;color: #00B377;right: 15px;top: 12px;">
                查看进度</span>
              银行卡申请中，审批周期预计5-10分钟，可点击“查看绑卡进度”查看详情
            </div>
            <div class="account-item-text" v-if="cardStatus === 8 || cardStatus === 16">
              <div style="margin-bottom: 10px;color: #292933;font-size: 16px;">绑卡申请记录</div>
              <span @click="handleGoChangeBankCard" style="position: absolute;color: #00B377;right: 15px;top: 12px;">
                查看进度</span>
              <span style="color: red;">银行卡审核失败，请点击“查看绑卡进度”查看失败原因</span>
            </div>
            <div class="account-item-text" v-if="cardStatus === 4">
              <div style="margin-bottom: 10px;color: #292933;font-size: 16px;">绑卡申请记录</div>
              <span @click="handleGoChangeBankCard"
                style="position: absolute;color: #00B377;right: 15px;top: 12px;">立即验证</span>
              <span style="color: red;">
                银行卡待验证，请在账户收到小额打款后48小时内进行打款验证，超时需要重新发起绑卡申请
              </span>
            </div>
          </div>

        </div>

        <div class="info-form" v-if="formModal && formModal.creditCardDtoList"
          style="background: white;padding: 10px;padding-top: 0;">

          <div v-for="(item, index) in formModal.creditCardDtoList"
            style="margin-top: 10px;padding: 5px 10px;background: #F7F7F8;border-radius: 10px;">
            <div style="color: #292933;font-size: 16px;">{{ item.bankName }}</div>
            <div class="item flex">
              <span class="text">账户名</span>
              <span class="num">{{ item.accountName }}</span>
            </div>
            <div class="item flex">
              <span class="text">银行账号</span>
              <span class="num">{{ item.acct }}</span>
            </div>
            <!-- <div class="item flex">
            <span class="text">开户银行</span>
            <span class="num">{{ item.bankName }}</span>
          </div> -->
            <div class="item flex">
              <span class="text">开户支行</span>
              <span class="num">{{ item.branchBankName }}</span>
            </div>
          </div>


        </div>
      </div>
      <div class="info-form-btn" v-if="cardStatus === 32 || cardStatus == 33" @click="handleGoChangeBankCard((((formModal || {}).creditCardDtoList) || []).length)">绑定新卡</div>
      <div style="height: 5px;"></div>
      <!-- <div class="item">
        <p>仅支持使用平安商户绑定的银行卡向交易资金待清算专户转账充值，使用未绑定的银行卡转账，资金将被退回</p>
      </div> -->
    </div>
  </div>
</template>

<script setup>
import Clipboard from 'clipboard';
import { reactive, ref, computed } from "vue";
import { ElMessage, ElMessageBox } from 'element-plus';
import { queryPingAnAccountInfo, queryTopUpBankCardInfo } from '@/http/pinganMerchant';
import { useRoute, useRouter } from 'vue-router';

const router = useRouter();
const route = useRoute();
const { merchantId } = route.query;
const haveBankCard = ref(false);
const cardStatus = ref(2);
//lwq 2
const btnValue = computed(() => {
  return (cardStatus.value === 32 || cardStatus.value === 33) ? '绑定新卡' : '查看绑卡进度';
})
const formModal = reactive({
  backNo: '',
  backName: '',
  backAccountName: '',
  backAccountNo: '',
  accountName: '',
  acct: '',
  bankName: '',
  branchBankName: '',
  creditCardDtoList:[]
});

ElMessageBox.alert('平安商户余额，可以用作“平安贷”还款，也可转至您的购物金账户，购物金账户余额可用于支付货款', '提示', {
  confirmButtonText: '确定',
})

const getInfos = () => {
  queryPingAnAccountInfo({ merchantId }).then((res) => {
    if (res.code === 1000) {
      const { data } = res;
      formModal.accountName = data.accountName;
      formModal.acct = data.acct;
      formModal.bankName = data.bankName;
      formModal.branchBankName = data.branchBankName;
      formModal.creditCardDtoList = data.creditCardDtoList;
      cardStatus.value = data.status;
    } else {
      //lwq
      // formModal.creditCardDtoList = [
      //   { "accountName": "sit ullamco velit mollit", "branchBankName": "cupidatat ex enim", "branchBankCd": "aliqua", "bankName": "officia eu ea commodo", "acct": "mollit in Duis", "mobileNo": "occaecat exercitation" }, { "accountName": "sit ullamco velit mollit", "branchBankName": "cupidatat ex enim", "branchBankCd": "aliqua", "bankName": "officia eu ea commodo", "acct": "mollit in Duis", "mobileNo": "occaecat exercitation" }]
      // console.log(formModal);
    }
  });
  queryTopUpBankCardInfo({ merchantId }).then((res) => {
    if (res.code === 1000) {
      const { data } = res;
      formModal.backNo = data.backNo;
      formModal.backName = data.backName;
      formModal.backAccountName = data.backAccountName;
      formModal.backAccountNo = data.backAccountNo;
    }
  });
};
const handleGoChangeBankCard = (res) => {
  if(res>=5){
    showToast.warning('每个平安商户最多绑定5张对公卡，当前已达上限。请解绑已有卡后再次尝试');
    return
  }
  router.push({
    path: '/pinganMerchant/changeBankCard',
    query: { merchantId }
  });
};
const copy = (e) => {
  const clipboard = new Clipboard(e.target);
  clipboard.on('success', e => {
    ElMessage.success('复制成功');
    clipboard.off('error');
    clipboard.off('success');
    clipboard.destroy();
  });
  clipboard.on('error', e => {
    clipboard.off('error');
    clipboard.off('success');
    clipboard.destroy();
  });
  clipboard.onClick(e);
};
getInfos();
</script>

<style lang="scss" scoped>
html,
body {
  height: 100%;
  background: #F7F7F8;
}

#app {
  background: #fff;
}

.recharge {
  background-color: #F7F7F8;
  height: 100vh;

  .flex {
    display: flex;
    align-items: center;
  }

  .explain {
    padding: rem(18) rem(38);
    font-size:12px ;
    line-height: rem(36);
    color: #7A2809;
    background: #FFF7EF;
  }

  .mt22 {
    // margin-top: rem(22);
  }

  .info {
    padding: 10px 10px 0 10px;
    background: #fff;

    h4 {
      color: #292933;
      font-size: rem(30);
      line-height: rem(46);
      margin-bottom: rem(30)
    }

    .item {
      align-items: normal !important;
      padding: rem(12) rem(8);
      // border-top: rem(1) solid #F5F5F5;
      font-size: rem(28);
      line-height: rem(30);

      p {
        background: #FFF7EF;
        padding: rem(20);
        font-size: rem(24);
        color: #99664D;
        line-height: rem(33);
      }

      .text {
        line-height: 22px;
        color: #9494A5;
        flex-shrink: 0;
        display: inline-block;
        width: rem(150);
        padding-right: rem(10);
      }

      .num {
        flex: 1;
        color: #292933;
        text-align: left;
        line-height: 22px;
        word-break: break-all;
      }

      .line {
        // height: rem(50);
        // width: rem(1);
        // background: #D8D8D8;
        // display: block;
        // margin: 0 rem(20);
        // margin-right: 0;
      }

      .copy {
        padding-left: rem(22);
        color: #999999;

        img {
          width: rem(28);
          height: rem(28);
        }
      }
    }
  }

  .not-card {
    display: flex;
    flex-direction: column;
    display: flex;
    align-items: center;

    h4 {
      width: 100%;
      border-bottom: 1px solid #F5F5F5;
      padding-bottom: 10px;
      box-sizing: border-box;
    }

    .info-form-img {
      width: rem(120);
      height: rem(120);

      img {
        width: 100%;
        height: 100%;
      }
    }

    .account-item-text {
      position: relative;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: rem(24);
      color: #676773;
      width: 100%;
      background: #F7F7F8;
      padding: 12px 10px;
      border-radius: 10px;
      line-height: 18px;
      box-sizing: border-box;

      // margin-top: rem(60);
      // text-align: center;
      .warn-span {
        color: #FF7B03;
      }

      .error-span {
        color: #FF2121;
      }

      >p {
        margin-top: rem(8);
      }

      .account-item-tips {
        padding: 0 rem(20);
        box-sizing: border-box;
        margin-top: rem(30);

        .account-item-tips-content {
          background: #FFF7EF;
          border-radius: rem(4);
          padding: rem(20);
          box-sizing: border-box;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: rem(24);
          color: #99664D;
          text-align: left;
          line-height: rem(35);
        }
      }
    }

  }

  .info-form-btn {
    width: 100%;
    height: rem(90);

    background: #00B955;
    border-radius: 4px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: rem(28);
    color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    margin-bottom: 30px;

  }
}
</style>