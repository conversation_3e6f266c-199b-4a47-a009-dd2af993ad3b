<!-- 安卓手机，点击输入的input框，弹出此组件，ios不使用此组件 -->
<template>
  <div id="androidinputbox" @click="cancel">
    <div class="eidt-number-box" @click.stop>
      <div class="title">编辑购买数量</div>
      <div class="input-box">
        <div class="buybtn posmiddle" @click="addmincartnum">
          <span class="min">-</span>
          <input type="tel" ref="editinput" v-model="cartVal" @click.stop />
          <span class="add">+</span>
        </div>
      </div>
      <div class="opreat-box">
        <span class="cancel" @click="cancel">取消</span>
        <span class="confrim" @click="confrimEdit">确认</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { putRequest } from '@/http/api';
import { useStore } from "vuex";
import { getCurrentInstance, onMounted, ref } from 'vue';
import { actionTracking } from '@/config/eventTracking';
  const store = useStore();
  const props = defineProps({
    goodsId: {
      default: ''
    },
    dataVal: {
      default: false
    },
    isSplit: {
      default: ''
    },
    medPack: {
      default: ''
    },
    package: {
      default: false
    },
  });
  const { proxy } = getCurrentInstance();
  const cartVal = ref(props.dataVal);
  const dataId = ref(props.goodsId);
  const split = ref(props.isSplit);
  const mednum = ref(props.medPack);
  const ispackage = ref(props.package);

  function cancel() {
    store.commit('app/show_android_addCart', {
      id: '',
      val: '',
      showandoridedit: false,
      split: '',
      medpack: ''
    })
  };

  function addmincartnum(e) {
    proxy.$refs.editinput.focus()
    let className = e.target.className
    if (className === 'add') {
      cartVal.value += parseInt(mednum.value)
    } else if (className === 'min') {
      cartVal.value =
        split.value === 1
          ? cartVal.value - 1
          : cartVal.value - parseInt(mednum.value)
    }
    cartVal.value = cartVal.value > 0 ? cartVal.value : 0
  }

  function confrimEdit() {
    if (cartVal.value > 0) {
      const config = ispackage.value
        ? {
            merchantId: proxy.$merchantId,
            amount: cartVal.value,
            packageId: dataId.value,
            direct: 1
          }
        : {
            merchantId: proxy.$merchantId,
            amount: cartVal.value,
            skuId: dataId.value,
            direct: 1
          }
      putRequest('post', '/app/changeCart', config)
        .then(res => {
          if (res.status === 'success') {
            if (Number(res.data.qty)) {
              // actionTracking('h5_page_CommodityDetails_o', {
              //   commodityId: dataId.value,
              //   real: 1
              // })
            }
            if (res.data.qty != cartVal.value) {
              cartVal.value = parseInt(res.data.qty)
            }
            store.commit('app/show_android_addCart', {
              id: '',
              val: '',
              showandoridedit: false,
              split: '',
              medpack: ''
            })
            store.commit('app/android_response', {
              id: dataId.value,
              val: cartVal.value,
            });
            if (res.dialog != null) {
              if (res.dialog.style == 20) {
                store.commit('app/changesureDialog', { dialogmsg: res.dialog.msg, showsureDialog: true })
              } else {
                store.commit('app/changeprompt', { promptmsg: res.dialog.msg, showprompt: true })
              }
            }
            if (res.errorMsg) {
              store.commit('app/changeprompt', { promptmsg: res.errorMsg, showprompt: true })
            }
            try {
              if (ispackage.value == true) {
                window.hybrid.addPlanNumber(
                  dataId.value,
                  res.data.qty,
                  1,
                  1
                ) //android
              } else {
                window.hybrid.addPlanNumber(dataId.value, res.data.qty, 1) //android
              }
            } catch (erro) {}
          } else {
            
            store.commit('app/android_response', {
              id: dataId.value,
              val: 0,
            });
            store.commit('app/show_android_addCart', {
              id: '',
              val: '',
              showandoridedit: false,
              split: '',
              medpack: ''
            })
            if (res.errorMsg) {
              store.commit('app/changeprompt', { promptmsg: res.errorMsg, showprompt: true })
            } else {
              if (res.msg) {
                store.commit('app/changesureDialog', { dialogmsg: res.msg, showsureDialog: true })
              } else {
                store.commit('app/changeprompt', { promptmsg: res.dialog.msg, showprompt: true })
              }
            }
          }
        })
        .catch(err => {})
    } else {
      store.commit('app/show_android_addCart', {
        id: '',
        val: '',
        showandoridedit: false,
        split: '',
        medpack: ''
      })      
      store.commit('app/android_response', {
        id: dataId.value,
        val: cartVal.value,
      });

      const config = ispackage.value
        ? {
            merchantId: proxy.$merchantId,
            packageIds: dataId.value
          }
        : { merchantId: proxy.$merchantId, ids: dataId.value } //套餐与普通商品
      putRequest('post', `/app/batchRemoveProductFromCart`, config).then(
        res => {
          if (res.status === 'success') {
            store.commit('app/show_android_addCart', {
              id: '',
              val: '',
              showandoridedit: false,
              split: '',
              medpack: ''
            })
            //安卓输入商品数量为0后,加购缩回
            try {
              if (ispackage.value) {
                window.hybrid.addPlanNumber(dataId.value, 0, 1, 1) //android
              } else {
                window.hybrid.addPlanNumber(dataId.value, 0, 1) //android
              }
            } catch (err) {}
          } else {
            store.commit('app/show_android_addCart', {
              id: '',
              val: '',
              showandoridedit: false,
              split: '',
              medpack: ''
            })
          }
        }
      )
    }
  }

  onMounted(() => {
    proxy.$refs.editinput.focus()
    proxy.$refs.editinput.select()
  })

</script>

<style lang="scss" scoped>
#androidinputbox {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  z-index: 200;
  .eidt-number-box {
    width: 6.6rem;
    background: #fff;
    border-radius: 6px;
    /*margin-left:0.95rem;*/
    margin: 0 auto;
    margin-top: 4rem;
    text-align: center;
    overflow: hidden;
    .title {
      font-size: 16px;
      color: #333;
      line-height: 1rem;
      background: #eee;
    }
    .input-box {
      height: 1.6rem;
      position: relative;
      .buybtn {
        display: flex;
        display: -webkit-flex;
        flex-direction: row;
        -webkit-flex-direction: row;
        text-align: center;
        line-height: 24px;
        width: 86px;
        height: 28px;
        span {
          width: 26px;
          line-height: 24px;
          border: 1px solid #ccc;
          font-size: 18px;
          font-weight: 400;
          &.min {
            border-right: 0;
            border-radius: 4px 0 0 4px;
          }
          &.add {
            border-left: 0;
            border-radius: 0 4px 4px 0;
          }
        }
        input {
          width: 34px;
          height: 26px;
          font-size: 16px;
          border: 1px solid #ccc;
          text-align: center;
          background: #feffe4;
        }
      }
    }
    .opreat-box {
      font-size: 0;
      border-top: 1px solid #eee;
      span {
        display: inline-block;
        box-sizing: border-box;
        width: 50%;
        line-height: 1rem;
        color: #000;
        font-size: 16px;
        font-weight: 500;
        &:first-child {
          color: #666;
          border-right: 1px solid #eee;
        }
        &:last-child {
          color: #00b377;
        }
      }
    }
  }
}
</style>
