
<template>
  <div class="classifiedRank">
    <div class="compBox" v-if="productList.length">
      <TempMixedRow :productList="productList" 
                    :licenseStatus="licenseStatus" 
                    :buriedPoint="buriedPoint"
                    :categoryName="(route.query || {}).cname || '标品排行榜'" />
    </div>
    <div class="noGoods" v-else>
      <div>
        <img src="../../../assets/images/xyy-sorry.png" alt="">
        <span class="text">暂无商品</span>
      </div>
    </div>
    <div v-if="productList.length" class="bottom-tip">
      <span>{{bottomTip}}</span>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, getCurrentInstance, onMounted } from "vue";
  import { useRoute } from 'vue-router'
  import { useStore } from 'vuex';
  import { getRankingInfo } from '@/http/api';
  import { getSubRankingSkuList } from '@/http/api';
  import TempMixedRow from '../../../components/skuListMixedRow.vue';
  import Bridge from "@/config/Bridge";
  import { actionTracking } from '@/config/eventTracking';
  import { debounce, sharePage } from '@/utils'

  const route = useRoute();

  // 顶部导航栏分享功能
  sharePage();
  // 设置通栏背景
  Bridge.setNavigationBar('#FA2340', '#ffffff');
  actionTracking('page_Chart', { page_title: (route.query || {}).cname || '本地热销榜' });

  const { proxy } = getCurrentInstance();
  const store = useStore();
  const productList = ref([]);
  const licenseStatus = ref('');
  const isEnd = ref(true);
  const loading = ref(false);
  const bottomTip = ref('');
  const pageInfo = ref({
    pageNum: 1,
    pageSize: 10,
  })
  const buriedPoint = reactive({
    sptype: '',
    sid: '',
    spid: ''
  });

  // 上拉加载
  function pullUpLoading() {
    let scrollHeight = document.documentElement.scrollHeight;
    let scrollTop = window.scrollY;//滚动的高度；
    let clientHeight = document.documentElement.clientHeight;//滚动的高度；
    if (scrollHeight - scrollTop - clientHeight < 30) {
      if (!isEnd.value && !loading.value) {
        getList();
      }
    }
  }

  // 获取信息
  function getList() {
    if (isEnd.value) return;
    if (loading.value) return;
    loading.value = true;
    // loading组件
    store.commit('app/showLoading', true);
    getSubRankingSkuList({
      ...pageInfo.value,
      cname: (route.query.cname || '').slice(0,route.query.cname.length-1),
      masterStandardProductId : route.query.masterStandardProductId,
      branchCode : route.query.branchCode
    }).then((res) => {
      console.log(res)
      loading.value = false;
      store.commit('app/showLoading', false);
      if (res.code === 1000) {
        productList.value = productList.value.concat(res.data.rows || []);
        licenseStatus.value = res.data.licenseStatus || '';
        isEnd.value = res.data.isEnd;
        pageInfo.value = {
          pageNum: res.data.requestParam.pageNum || 1,
          pageSize: res.data.requestParam.pageSize || 10,
        }
        bottomTip.value = res.data.isEnd ? '没有更多了' : '查看更多';
        buriedPoint.sid = res.data.sid;
        buriedPoint.spid = res.data.spid;
        buriedPoint.sptype = res.data.sptype;
      }
    }).catch(() => {
      store.commit('app/showLoading', true);
      loading.value = false;
    })
  }

  function getInfo() {
    productList.value = [];
    isEnd.value = false;
    getList();
  }

  getInfo();

  onMounted(() => {
    window.addEventListener('scroll', debounce(pullUpLoading, 300))
    actionTracking("page_Chart_productList", {
      cname: (route.query.cname || '').slice(0,route.query.cname.length-1),
      masterStandardProductId : route.query.masterStandardProductId,
      action : window.location.href
    });
  })

</script>

<style lang="scss" scoped>
.classifiedRank {
  background: #F7F7F8;
  min-height: 100vh;
  .bannerBox {
    img {
      width: 100%;
    }
  }
  .compBox {
    padding: rem(20);
    padding-bottom: 0;
  }
  .noGoods{
    position: relative;
    text-align: center;
    height: rem(210);
    margin: 30% 0;
    img{
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width:rem(264);
      height:rem(174);
    }
    .text{
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      color: #A9AEB7;
    }
  }
}
</style>
