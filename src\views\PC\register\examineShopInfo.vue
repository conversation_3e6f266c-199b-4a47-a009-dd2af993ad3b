<template>
  <div class="registerWrapper">
    <RegisterHeader title="注册" :isLogin="true" />
    <div class="examineBox">
      <div class="examineTop">
        <img class="waitIcon" src="../../../assets/images/register/wait.png" alt="">
        <div class="examineTitle">店铺信息审核中！</div>
        <div class="examineTip">您添加的店铺新信息，预计24小时内完成审核，请您耐心等待，</div>
        <div class="examineTip">审核通过后可登入药帮忙平台</div>
      </div>
      <div class="shopInfo">
        <div class="shopTitle">店铺信息</div>
        <div class="shopItem">
          <span class="shopLabel">店铺名称：</span>
          <span class="shopValue">{{ shopInfo.name }}</span>
        </div>
        <div class="shopItem">
          <span class="shopLabel">店铺所在市区：</span>
          <span class="shopValue">{{ `${shopInfo.province}${shopInfo.city}${shopInfo.district}${shopInfo.street}` }}</span>
        </div>
        <div class="shopItem">
          <span class="shopLabel">详细地址：</span>
          <span class="shopValue">{{ shopInfo.address }}</span>
        </div>
      </div>
      <div class="btnBox">
        <el-button class="confirmBtn" type="primary" @click="loginOrtherShop">登录其他店铺</el-button>
      </div>
    </div>
    <RegisterFooter />
  </div>
</template>
<script setup>
  import { onMounted, ref } from "vue";
  import RegisterFooter from './components/registerFooter.vue';
  import RegisterHeader from './components/registerHeader.vue';
  import { getSimpleMerchantInfo, getLoginAccountInfo } from '@/http_pc/api';
  import { useRouter, useRoute } from 'vue-router';
  import { actionTracking } from '@/config/eventTracking';

  const router = useRouter();
  const route = useRoute();
  const shopInfo = ref({});
  const loginPhone = ref(0);

  const getInfo = () => {
    getSimpleMerchantInfo({
      merchantId: (route.query || {}).merchantId,
    }).then((res) => {
      shopInfo.value = res.data || {};
    })
  }

  const loginOrtherShop = () => {
    actionTracking('pc_action_poiAudit_selectPoi_click', { 
      phone: loginPhone.value,
    });
    router.push('/register/selectLoginShop');
  }

  const getLoginInfo = () => {
    getLoginAccountInfo({}).then((res) => {
      // loginRole.value = res.data.info.role;
      // loginMerchantId.value = res.data.info.merchantId || 0;
      loginPhone.value =  ((res.data || {}).info || {}).mobile || 0;
    })
  }

  onMounted(() => {
    getInfo();
    getLoginInfo();
  })

</script>
<style lang="scss" scoped>
.registerWrapper {
  background: #F8F8F8;
  width: 100%;
  min-height: 100vh;
  .examineBox {
    margin: 0 auto;
    width: 1200px;
    background: #fff;
    padding-bottom: 75px;
    padding-top: 85px;
    text-align: center;
    .examineTop {
      .waitIcon {
        width: 32px;
        height: 32px;
        margin-bottom: 4px;
      }
      .examineTitle {
        font-weight: bold;
        font-size: 18px;
        color: #222222;
        margin-bottom: 8px;
      }
      .examineTip {
        font-size: 12px;
        color: #888888;
        line-height: 20px;
      }
    }
    .shopInfo {
      margin: 0 auto;
      margin-top: 58px;
      width: 440px;
      border: 1px solid #EEEEEE;
      .shopItem {
        padding: 0 20px;
        height: 36px;
        line-height: 36px;
        border-top: 1px solid #EEEEEE;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        .shopLabel {
          color: #222222;
        }
        .shopValue {
          color: #888888;
        }
      }
      .shopTitle {
        height: 36px;
        line-height: 36px;
        font-size: 14px;
        color: #222;
        background: #EEEEEE;
      }
    }
    .btnBox {
      margin-top: 124px;
    }
    .confirmBtn {
      width: 300px;
    }
  }
}
</style>