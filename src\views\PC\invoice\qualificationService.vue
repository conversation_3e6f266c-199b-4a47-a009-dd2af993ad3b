<!-- 申请资质售后表单页 -->
<template>
  <div class="container">
    <h2 style="margin-bottom: 20px">资质相关售后-选择需要补发的资质</h2>

    <el-form :model="form" label-width="133px">
      <el-form-item class="deep_form_Item" label="企业相关资质：" style="position: relative;">
        <div class="required_icon" style="position: absolute; left: -123px;top: -6px;"></div>
        <div class="typeTag" v-for="item in typeTags" :key="item" @click="() => selectTypeTag(item.itemType)"
          :class="form.typeTag.includes(item.itemType) ? 'currentTag' : ''">
          {{ item.itemName }}
        </div>
      </el-form-item>
      <el-form-item label="商品相关资质:">
        <div class="data_table">
          <!-- <div class="data_table_header">
            <el-input v-model="searchName" placeholder="请输入商品名称搜索" style="width: 200px; height: 24px" />
          </div> -->
          <div class="data_table_row" v-for="item in showList" :key="item.skuId">
            <img style="width: 44px; height: 44px" :src="item.imageUrl" alt="" />
            <div class="data_table_row_text">
              <p>{{ item.productName }}</p>
              <p>{{ item.spec }}</p>
            </div>
            <el-checkbox v-model="item.drugTest" label="药检报告" size="large" @change="() => checkChange(item)" />
            <el-checkbox v-model="item.qualification" label="首营资质" size="large" @change="() => checkChange(item)" />
          </div>
        </div>
      </el-form-item>

      <el-form-item label="补充说明:" style="position: relative;">
        <div class="required_icon" style="position: absolute; left: -85px;top: 0px;"></div>
        <el-input v-model="form.desc" type="textarea" placeholder="为了您和商家更好的处理售后问题，请填写补充说明" style="width: 366px"
          maxlength="50" />
      </el-form-item>

      <div style="display: flex">
        <div style="width: 133px">
          <div style="float: right">
            <div style="font-size: 14px; color: #606266">上传凭证：</div>
            <div class="input_describe">（最多9张）</div>
          </div>
        </div>
        <div v-loading="uploadLoaind">
          <el-upload action v-model:file-list="imgFileList" list-type="picture-card" :limit="9" :http-request="toUploadImg"
          :before-upload="beforeUpload" accept="image/*"
          :on-remove="imageRemove" :on-preview="imagePreview" style="max-width: 847px" :on-exceed="onExceed">
          <el-icon>
            <Plus />
          </el-icon>
        </el-upload>
        </div>
      </div>

      <div style="
          display: flex;
          justify-content: center;
          width: 100%;
          margin-top: 83px;
        ">
        <el-button v-if="isSubmit" type="primary" @click="onSubmit" :loading="isLoading"
          style="width: 142px; height: 46px; background-color: #00b377">提交审核</el-button>
        <el-button v-else @click="onSubmit" disabled
          style="width: 142px; height: 46px; background-color: #999999;color:#fff;">提交审核</el-button>
      </div>
    </el-form>
    <el-dialog v-model="invoiceInfoDialog" title="售后须知" width="30%" @closed="closeInfoDialogFn()">
      <span v-html="invoiceInfo"></span>
      <template #footer>
        <div style=" text-align: center;">
          <el-button type="primary" @click="submitDialogFn()">
            已阅读并同意，继续申请售后
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
  <div class="demo-image__preview">
    <el-image-viewer hide-on-click-modal @close="closeImagePreview" v-if="dialogVisible" :url-list="[dialogImageUrl]"/>
  </div>
</template>

<script setup>
import { reactive, ref, watch, onBeforeMount } from 'vue'
import { uploadvoucherImg, queryServiceInfo, queryOrderList, addCredentialService, queryCredentialType } from '@/http_pc/api'
import { useRoute,useRouter } from 'vue-router';
import { ElMessage } from 'element-plus'
const route = useRoute();
const router = useRouter();
// 搜索内容
let searchName = ref('')
// 弹层展示图片
let dialogVisible = ref(false)
let dialogImageUrl = ref('')
const pageLocation = ref('');
const uploadLoaind = ref(false)
// 售后须知
let invoiceInfoDialog = ref(false)
let invoiceInfo = ref('')
let isBack = ref(true)
const imgFileList = ref([])
const imgPaths = ref([])
// 是否可提交
const isSubmit = ref(false);
const isLoading = ref(false);
const orderNo = ref('')
const orgId = ref('')
const accountId = ref()
// 必选标签
const typeTags = ref([]);
// 表格数据
let dataTableList = []
const showList = ref([])
const checkList = ref([])
// 表单数据
const form = reactive({
  desc: '',
  typeTag: [],
})
onBeforeMount(() => {
  orderNo.value = route.query?.orderNo ?? '';
  orgId.value = route.query?.orgId ?? '';
  accountId.value = route.query?.accountId;
  // 获取商品列表
  queryOrderList({ orderNo: orderNo.value, accountId: accountId.value }).then((res) => {
    if (res.status === 'success' && res.data != null) {
      dataTableList = [...res.data];
      showList.value = [...dataTableList];
    }
  })
  // 获取选项列表
  queryCredentialType({ orderNo: orderNo.value, accountId: accountId.value }).then((res) => {
    if (res.status === 'success' && res.data != null) {
      typeTags.value = res.data.items;
    }
  })
  // 获取售后须知
  queryServiceInfo({ orderNo: orderNo.value, orgId: orgId.value }).then((res) => {
    if (res.status === 'success' && res.data != null) {
      invoiceInfo.value = res.data;
      invoiceInfoDialog.value = true;
    }
  })
})
watch(searchName, (newName, oldName) => {
  if (searchName.value == '') {
    showList.value = [...dataTableList]
  } else {
    showList.value = dataTableList.filter((e) =>
      e.productName.includes(searchName.value)
    )
  }
})
watch(form, (newName, oldName) => {
  isSubmit.value = checkFormValue()
})
// 关闭的回调
const closeInfoDialogFn = () => {
  if(isBack.value) {
    router.go(-1)
  }
}
// 确认须知
const submitDialogFn = () => {
  isBack.value = false
  invoiceInfoDialog.value = false
}
// 错票tag事件
const selectTypeTag = (value) => {
  if (form.typeTag.includes(value)) {
    form.typeTag = form.typeTag.filter((e) => e != value)
  } else {
    form.typeTag.push(value)
  }
  console.log(form.typeTag)
}
// 选中商品
const checkChange = (item) => {
  if (item.drugTest || item.qualification) {
    var lis = []
    if (item.drugTest) lis.push('1')
    if (item.qualification) lis.push('2')
    var li = {
      skuId: item.skuId,
      credentialType: lis.join(',')
    };
    checkList.value = checkList.value.filter((e) => e.skuId !== item.skuId)
    checkList.value.push(li)
  } else {
    checkList.value = dataTableList.filter((e) => e.drugTest || e.qualification)
  }
}
// 预览图片
const imagePreview = (file) => {
  dialogImageUrl.value = file.url
  dialogVisible.value = true
  stop()
}
// 关闭图片凭证
const closeImagePreview =() => {
  dialogVisible.value = false
  move()
}
// 图片删除
const imageRemove = (file) => {
  imgPaths.value = imgPaths.value.filter((e) => e.uid != file.uid)
}
const beforeUpload = (file) => {
    const isJPG = file.type === 'image/jpg' || file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/bmp' || file.type === 'image/gif';
    if (!isJPG) {
      ElMessage.error('请上传正确格式的图片');
      return false;
    }
    const size = 5;
    const isLt2M = file.size / 1024 / 1024 < size;
    if (!isLt2M) {
      ElMessage.error(`图片大小不得超过${size}M`);
      return false;
    }
  };
// 图片上传
const toUploadImg = async (file) => {
  uploadLoaind.value = true
try {
  var res =  await uploadvoucherImg(file)
  if (res.status === 'success') {
      imgPaths.value.push({
        path: res.data.downloadPath[0],
        uid: file.file.uid
      })
}
uploadLoaind.value = false
} catch (error) {
  uploadLoaind.value = false
  imgFileList.value.pop()
  ElMessage.error('上传失败')
}

}
// 上传超出
const onExceed = (fie,lis) => {
  ElMessage.warning('最多上传9张图片')
}
// 表单提交
const onSubmit = () => {
  const baseUrl = import.meta.env.VITE_BASE_URL_PC;
  isLoading.value = true;
  let data = {
    orderNo:orderNo.value,
    accountId:accountId.value,
  }
  data['corpCredential'] = form.typeTag.join(',')
  data['remarks'] = form.desc
  if (checkList.value.length != 0) {
    data['productCredential'] = JSON.stringify(checkList.value)
  }
  if (imgPaths.value.length > 0) {
    var lis = [];
    imgPaths.value.forEach((e) => {
      lis.push(e.path);
    })
    data['evidences'] = JSON.stringify(lis)
  }
  console.log(data);
  addCredentialService(data).then((res) => {
    if (res.status === 'success') {
      ElMessage.success('提交成功');
      parent.window.open(`https:${baseUrl}merchant/center/order/index.htm?status=89`, "_self");
    }
    isLoading.value = false;
  })
}

// 检查必填字段
function checkFormValue() {
  if (form.desc.length < 1 || form.typeTag.length < 1) {
    return false;
  }

  return true;
}
 // 禁止滚动-在显示遮罩层的时候调用
 function stop() {
    let scrollTop = window.scrollY;//滚动的高度；
    pageLocation.value = scrollTop;
    document.body.style.position = 'fixed';
    document.body.style.top = '-' + scrollTop + 'px';
};
// 取消滑动限制-在关闭遮罩层的时候调用
function move() {
  document.body.style.position = 'static';
  window.scrollTo(0, pageLocation.value);
}
</script>
<style lang="scss" scoped>
.container {
  width: 980px;
  box-sizing: border-box;
  min-height: 606px;
  background: #ffffff;
  border-radius: 1px;
  padding: 18px;
  font-weight: 500;
  color: #333333;

  .typeTag {
    height: 32px;
    margin-right: 10px;
    padding-left: 6px;
    padding-right: 6px;
    background: #ffffff;
    border: 1px solid #dddddd;
    text-align: center;
    line-height: 32px;
    color: #666666;
  }

  .input_describe {
    font-size: 12px;
    color: #bbbbbb;
  }

  .data_table_header {
    display: flex;
    width: 500px;
    height: 44px;
    align-items: center;
    box-sizing: border-box;
    padding-left: 10px;
    border: 1px solid #eeeeee;
  }

  .data_table_row {
    display: flex;
    align-items: center;
    padding: 2px 20px 2px 10px;
    box-sizing: border-box;
    width: 500px;
    height: 52px;
    border: 1px solid #eeeeee;
    margin-top: -1px;
  }

  .data_table_row_text {
    width: 232px;
    margin-left: 10px;
  }

  .data_table_row_text p:nth-child(1) {
    font-size: 14px;
    color: #292933;
    line-height: 13.44px;
    margin-bottom: 6px;
  }

  .data_table_row_text p:nth-child(2) {
    font-size: 12px;
    color: #676773;
    line-height: 11.52px;
  }

  ::v-deep .deep_form_Item .el-form-item__label {
    line-height: 20px !important;
  }
}

// 动态选中类
.currentTag {
  border: 1px solid #00b377 !important;
  color: #00b377 !important;
}

.required_icon::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}
::v-deep .el-image-viewer__mask {
        opacity: .7;
    }
    ::v-deep .el-image-viewer__img {
      width: 50%;
      height: 50%;
    }
</style>
