<template>
    <div class="wrapper-box">
        <div class="abstract-wrap">
          <div class="abstract-item">
            <div class="head-line">
              <div class="side-left">
                {{ billName }}
                <img class="status" v-if="payoffflag===2"  src="../../../assets/images/pinganloans/status01.png"/>
                <img class="status" v-if="payoffflag===0" src="../../../assets/images/pinganloans/status02.png"/>
                <img class="status" v-if="payoffflag===1" src="../../../assets/images/pinganloans/status03.png"/>
              </div>
              <div class="side-right ">
                <div class="title">剩余应还本金</div>
                <div class="money" style="color: #FE2021; font-weight: 500;">{{curPrincipalAmountBalance}}</div>
              </div>
            </div>
            <div class="abstract-content">
              <div class="content-part01">
                <div class="side-left">已还本金（元）</div>
                <div class="side-right">{{ actPrincipalAmount }}</div>
              </div>
              <div class="content-part02">
                <div class="side-left">已还利息（元）</div>
                <div class="side-right">{{ actInterestAmount }}</div>
              </div>
              <div class="content-part03">
                <div class="side-left">
                  平台贴息（元）
                  <span class="warning-color">贴息截止日：{{ subsidyInterestDate }}</span>
                </div>
                <div class="side-right">
                  {{ actSubsidyInterestAmount }}
                </div>
              </div>
            </div>
            <div class="btn-line">
              <a :href="targetUrl" class="btn">
                  查账/还款
                  <img src="../../../assets/images/pinganloans/link-icon.png" alt="">
                </a>
            </div>
          </div>
          <div class="tab-box">
            <div :class="['tab-item', offflagPostValue==='' ? 'activate' : '']"  @click="handlepPyoffflag('')">全部</div>
            <div :class="['tab-item', offflagPostValue===1 ? 'activate' : '']"  @click="handlepPyoffflag(1)">已还清</div>
            <div :class="['tab-item', offflagPostValue===0 ? 'activate' : '']"  @click="handlepPyoffflag(0)">未还清</div>
          </div>
        </div>
        <div class="content-box">
            <div v-if="tableData.length>0">
              <div class="list-wrap" v-for="(column, index) in tableData" :key="index">
                  <div class="head-line">
                      <div class="side-left">{{column.billDate}}</div>
                  </div>
                  <div class="list-item" v-for="(item, indez) in column.records" :key="indez">
                    <div 
                      :class="['line-tags', item.payoffflag===1 ? 'line-tags-success' : 'line-tags-warning' ]">
                        <div class="side-left">
                          <span v-if="item.payoffflag===1">实际还款日：</span>
                          <span v-else>最后还款日：</span>
                          {{ item.curRepayDate }}
                        </div>
                        <div class="side-right">
                          <span v-if="item.payoffflag===1">已还清</span>
                          <span v-if="item.payoffflag===0">未还清</span>
                        </div>
                      </div>
                      <div class="list-content">
                        <div class="content-part00">
                              <div class="side-left" style="color: #292933;font-weight: 500;">{{ item.receiverName }}</div>
                              <div class="side-right" style="color: #676773;">剩余应还本金</div>
                          </div>
                          <div class="content-part01">
                              <div class="side-left" style="color: #676773;">{{ item.businessPayNo }}</div>
                              <div :class="['side-right', item.payoffflag===0 ? 'error-color': '']">{{ item.curPrincipalAmountBalance }}</div>
                          </div>
                          <div class="content-part02">
                              <div class="side-left" style="color: #676773;">
                                订单实付(元) {{ item.tradeAmount }}
                              </div>
                              <div class="side-right">
                                <div style="color: #676773;width: 50%;">
                                  已还本金(元) 
                                </div>
                                <div class="side-last" style="width: 50%;">
                                  <div style="color: #333333;">{{ item.actPrincipalAmount }}</div>
                                  <div class="warning-tag-color" style="margin-top:rem(10);" v-if="item.refund===1">有原路退款</div>
                                </div>
                              </div>
                          </div>
                          <div class="content-part02">
                              <div class="side-left" style="color: #676773;">
                                已还利息(元) {{ item.actInterestAmount }}
                              </div>
                              <div class="side-right">
                                <div style="color: #676773;width: 50%;">
                                  平台贴息(元) 
                                </div>
                                <div class="side-last" style=";width: 50%;">
                                  <div style="color: #FF7700;">{{ item.actSubsidyInterestAmount }}</div>
                                </div>
                              </div>
                          </div>
                          <!-- <div class="content-part03">
                              <div>已还本金：{{ item.actPrincipalAmount }}</div>
                              <div class="msg-tags warning-tag-color" v-if="item.refund===1">有原路退款</div>
                          </div>
                          <div class="content-part04">
                              已还利息：{{ item.actInterestAmount }}（<span class="warning-color">平台贴息：{{ item.actSubsidyInterestAmount }}</span>）
                          </div>
                          <div class="content-part05">
                              <div class="side-left error-color">最后还款日：{{ item.curRepayDate }}</div>
                              <div class="side-right">
                                  <div class="btn-tags success-tag-color"  v-if="item.payoffflag===1">已还清</div>
                                  <div class="btn-tags warning-tag-color"  v-if="item.payoffflag===0">未还清</div>
                              </div>
                          </div> -->
                      </div>
                  </div>
              </div>
            </div>
            <div v-else class="empty">
              <div>
                <img src="../../../assets/images/xyy-sorry.png" alt="">
                <span class="text">暂无数据</span>
              </div>
            </div>
      </div>
    </div>
</template>
<script setup>
import { onMounted, ref } from "vue";
import { getAppLoansDetail, getAppLoans,getUrl } from '@/http/pinganLoans';
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

const store = useStore()
const route = useRoute()
const billCode = ref("")
const billName = ref("")
const curPrincipalAmountBalance = ref(0.00)
const actPrincipalAmount = ref(0.00) 
const actInterestAmount = ref(0.00)
const actSubsidyInterestAmount = ref(0.00)
const subsidyInterestDate = ref("")
const tableData = ref([])
const payoffflag = ref('')
const offflagPostValue = ref('')
const targetUrl = ref("")
const epayErr = ref("")

onMounted(async () => {
    billCode.value=route.query.billCode
    billName.value = route.query.billName
    getCurrentLoans(billCode.value)
    getDetail()
    getUrls()
})
const getUrls = () => {
  getUrl({merchantId:localStorage.getItem('merchantId')}).then((res)=>{
      if(res.code===1000){
        targetUrl.value = res.data.pinganLoginCheckUrl
      } else {
        epayErr.value = res.msg
      }
  })
}
//状态切换
const handlepPyoffflag = (v) =>{
  offflagPostValue.value = v
  getDetail()
}

//当前账单
const getCurrentLoans = (billCode) => {
  getAppLoans({billCode:billCode}).then((res) => {
      if (res.code === 1000) {
          curPrincipalAmountBalance.value = res.data.records[0].curPrincipalAmountBalance
          actPrincipalAmount.value = res.data.records[0].actPrincipalAmount
          actInterestAmount.value = res.data.records[0].actInterestAmount
          actSubsidyInterestAmount.value = res.data.records[0].actSubsidyInterestAmount
          subsidyInterestDate.value = res.data.records[0].subsidyInterestDate
          payoffflag.value = res.data.records[0].payoffflag
      }
  }).catch(() => {
    console.log("系统异常")
  })
}
const getDetail = () => {
  store.commit('app/showLoading', true)
  getAppLoansDetail({billCode:billCode.value, payoffflag:offflagPostValue.value}).then((res) => {
    if (res.code === 1000) {
        store.commit('app/showLoading', false)
        tableData.value = res.data
    }
  }).catch(() => {
    console.log("系统异常")
  })
}
</script>
<style lang="scss" scoped>
  div,span {
    font-size: 14px;
  }
  .wrapper-box{
    background: #F7F7F8;
    height: 100vh;
    .abstract-wrap{
        background: #ffffff;
        position:fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 2;
        .abstract-item{
          padding: 0 rem(20) rem(20) rem(20);
          .head-line{
              display: flex;
              justify-content: space-between;
              padding: rem(20) 0;
              .side-left{
                font-weight: 600;
                display: flex;
                align-items: center;
                .status{
                  margin-left:rem(20);
                  width: 46px;
                  height: 20px;
                }
              }
              .side-right{
                text-align: right;
                .money{
                  margin-top: 5px;
                }
              }
            }
            .btn-line {
              display: flex;
              justify-content: flex-end;
              padding: rem(25) 0 0 0;
              .btn{
                  width: rem(200);
                  height: rem(60);
                  line-height: rem(60);
                  border: 1px solid #D3D3D3;
                  border-radius: rem(44);
                  display: block;
                  color:#333333;
                  background: none !important;
                  img {
                    margin-left: rem(5);
                    width: rem(10);
                    height: rem(14);
                  }
                }
            }
        }
        .abstract-content {
          padding: rem(20) 0;
          .content-part01{
            display: flex;
            justify-content: space-between;
          }
          .content-part02{
            padding-top: rem(20);
            display: flex;
            justify-content: space-between;
          }
          .content-part03{
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: rem(20);
          }
        }
        .tab-box {
          display: flex;
          justify-content: flex-start;
          padding: rem(20) rem(30)  rem(20) rem(30);
          background: #F7F7F8;
          .tab-item{
            width: rem(136);
            height: rem(60);
            text-align: center;
            line-height: rem(60);
            background: #ffffff;
            border-radius: 12px;
            margin-right: rem(20)
          }
          .tab-last{
            margin-left: auto;
            width: rem(209);
            height: rem(60);
            margin-right: 0;
            img{
              margin-left: rem(5);
              width: rem(10);
              height: rem(14);
              transform: rotate(90)
            }
          }
          .activate {
            color: #FF3000;
          }
        }
      }
      .content-box {
        padding: rem(520) rem(30) rem(40) rem(30);
        .list-wrap{
            margin-bottom: rem(20);
            background: #ffffff;
            .head-line{
                padding: rem(20);
                .side-left{
                    font-weight: 600;
                }
            }
            .list-item{
                .line-tags{
                  display: flex;
                  justify-content: space-between;
                  height: rem(60);
                  padding:0 rem(20);
                  align-items: center;
                }
                .line-tags-warning{
                  background:rgba($color: #FE2021, $alpha: 0.1);
                  color: #FE2021;
                }
                .line-tags-success{
                  background:rgba($color: #1DBB86, $alpha: 0.1);
                  color: #1DBB86;
                }
                .list-content {
                    padding: rem(20);
                    border-bottom: 1px solid #dfdfdf;
                    .content-part00{
                      display: flex;
                      justify-content: space-between;
                    }
                    .content-part01{
                        margin-top:rem(10);
                        display: flex;
                        justify-content: space-between;
                    }
                    .content-part02 {
                        display: flex;
                        justify-content: space-between;
                        padding-top: rem(20);
                        .side-left{
                          width: 50%;
                        }
                        .side-right{
                          width: 50%;
                          display: flex;
                          justify-content: flex-start;
                        }
                    }
                    .content-part03{
                        display: flex;
                        justify-content: flex-start;
                        padding-top: rem(20);
                        align-items: center;
                        .msg-tags{
                            margin-left: rem(10);
                        }
                    }
                    .content-part04 {
                        padding-top: rem(20);
                    }
                    .content-part05 {
                        display: flex;
                        justify-content: space-between;
                        padding-top: rem(20);
                        align-items: center;
                    }
                }
            }
        }
    }
    .empty{
        position: relative;
        text-align: center;
        height: rem(210);
        margin: 30% 0;
        img{
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          width:rem(264);
          height:rem(174);
        }
        .text{
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          color: #A9AEB7;
        }
      }
  }
  .btn{
    width: rem(250);
    border-radius: rem(60);
    height: rem(60);
    background: #dfdfdf;
    text-align: center;
    line-height: rem(60);
  }
  .msg-tags{
    padding: rem(10) rem(20);
    border-radius:rem(10);
    color:#ffffff;
  }
  .btn-tags {
    padding: rem(10) rem(40);
    border-radius: rem(30);
    color:#ffffff;
  }
  .error-tag-color{
    background: red;
  }
  .warning-tag-color{
    color: #FF7700
  }
  .success-tag-color{
    background: #13c775;
  }
  .success-color{
    color :#13c775;
  }
  .error-color{
    color: #FE2021;
  }
  .warning-color{
    color: #FF7700;
  }
  .van-cell--clickable{
    padding-top: 5px;
  }
</style>
