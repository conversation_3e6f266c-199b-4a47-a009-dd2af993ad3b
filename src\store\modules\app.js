const state = () => ({
  loading: false,
  detailBaseUrl: 'ybmpage://productdetail?', // 商品详情默认链接
  isExtend: false,
  sureDialogdata: { // 带有确认按钮的提示
    showsureDialog: false,
    dialogmsg: ''
  },
  promptData: { // 普通提示框信息-2s后自动关闭
    showprompt: false,
    promptmsg: ''
  },
  android_addCart: { // 安卓点击input弹框的数据
    id: "",//商品id
    val: '',//商品数量
    split: '',//是否可拆零
    medpack: '',//中包装数量
    showandoridedit: false,
    package: false,//true,表示是套餐
  },
  changeProductInfo: { // 安卓编辑购买数量
    id: '',
    val: '',
  },
  childEvent: { // 显示加购图标或输入框
    isExtend: false,
    dataId: '',
  },
  trackingDatas: [], // 埋点暂存数据
  trackingParams: {
    sptype: 0,
    spid: '',
    sid: ''
  }, // 埋点追踪参数
  productDatas: [], // 商品曝光暂存数据
  accountInfo: {}, //平安银行账户信息
})

// mutations
const mutations = {
  showLoading(state, val) {
    state.loading = val;
  },
  setDetailBaseUrl(state, type) {
    state.detailBaseUrl = type
  },
  listenToChildEvent(state, val) {
    state.childEvent = val
  },
  // 监听是否显示有按钮的提示框
  changesureDialog(state, val) {
    state.sureDialogdata = val
  },
  // 监听普通提示框
  changeprompt(state, val) {
    state.promptData = val;
  },
  // 安卓点击加购输入框后弹起弹框
  show_android_addCart(state, val) {
    state.android_addCart = val
  },
  android_response(state, val) {
    state.changeProductInfo = val;
  },
  // 埋点暂存数据
  setTrackingDatas(state, val) {
    state.trackingDatas = val
  },
  // 埋点追踪参数
  setTrackingParams(state, option) {
    state.trackingParams[option.name] = option.value;
  },
  // 商品曝光暂存数据
  setProductDatas(state, val) {
    state.productDatas = val;
  },
  setAccountInfo(state, val) {
    state.accountInfo = val
  },
}

// actions
const actions = {

}

export default {
  namespaced: true,
  state,
  actions,
  mutations
}