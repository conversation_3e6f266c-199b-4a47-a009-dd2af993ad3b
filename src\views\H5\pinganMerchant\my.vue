<template>
  <div class="my">
    <!-- <div class="balance">
      <h4>{{ balance }}</h4>
      <span>可用余额（元）</span>
      <div class="account-info" @click="handleGoAccountInfo">
        帐户信息
      </div>
    </div> -->
    <!-- <div class="explain flex">
      <span>平安商户钱包用于部分支付渠道收付款</span>
      <span class="exp flex" @click="handleGoIntroduce">
        查看说明
        <img src="@/assets/images/pinganMerchant/arrow-right.png" />
    </span>
    </div> -->
    <div style="height: 0.3rem;"></div>
    <div class="recharge first flex">
      <span>可用余额</span>
      <span><span style="position: relative;top: -0.2rem;">￥</span><span style="font-size: 1rem;font-weight: 600;">{{ balance }}</span></span>
    </div>
    <div class="recharge flex online" @click="handleGoRecharge">
      <span>充值</span>
      <img src="@/assets/images/pinganMerchant/arrow-right2.png" />
    </div>
    <div class="recharge flex online" @click="buyMoneyOut" v-if="Number(version)>=11917">
      <span>转到购物金</span>
      <div class="payTitle">
        <span>{{ payTitle }}</span>
        <img src="@/assets/images/pinganMerchant/arrow-right2.png" />
      </div>
    </div>
    <div class="recharge flex" style="margin-top: 0.2rem;" @click="handleGoAccountInfo">
      <span>帐户信息</span>
      <img src="@/assets/images/pinganMerchant/arrow-right2.png" />
    </div>
    <div style="height: 0.3rem;"></div>
    <pingAnMerchantsDetails></pingAnMerchantsDetails>
  </div>
</template>

<script setup>
  import { queryPingAnAccountBalance,queryTopUpBankCardInfo,getRechargeDiscount } from '@/http/pinganMerchant';
  import { onMounted, ref,defineComponent } from "vue";
  import { useRouter, useRoute } from 'vue-router';
  import { ElMessageBox } from 'element-plus';
  import Bridge from '@/config/Bridge';
  import pingAnMerchantsDetails from './components/pingAnMerchantsDetails.vue';
  defineComponent({
    pingAnMerchantsDetails 
  })
  const router = useRouter();
  const route = useRoute();
  const balance = ref(0);
  const { merchantId } = route.query;
  const payTitle = ref('');
  // Bridge.setAppRightMenu("2")
  queryPingAnAccountBalance({ merchantId }).then((res) => {
    if (res.code === 1000) {
      balance.value = res.data.availableBalance;
    }
  })
  let version= ref('')
  Bridge.getVersion(e=>{
    version.value = e
    })
  // const version = ref(localStorage.getItem("appVersion"));
  const handleGoIntroduce = () => {
    router.push({
      path: '/pinganMerchant/introduce',
      query: { from: 'my'},
    });
  };
  const handleGoAccountInfo = () => {
    router.push({
      path: '/pinganMerchant/account',
      query: { merchantId }
    });
  }
  const handleGoRecharge = () => {
    router.push({
          path: '/pinganMerchant/recharge',
          query: { merchantId }
        });
    // queryTopUpBankCardInfo({merchantId}).then(res =>{
    
    //   // if(res.code === 1000){
    //   //   router.push({
    //   //     path: '/pinganMerchant/recharge',
    //   //     query: { merchantId }
    //   //   });
    //   // }else if(res.code === 9999){
    //   //   ElMessageBox.alert('平安商户余额目前仅支持用于平安贷还款和转移至购物金，您未开通平安贷产品，暂时不允许充值', '提示', {
    //   //     confirmButtonText: '确定',
    //   //   })
    //   // }
    // })
    
  };
  const buyMoneyOut=()=>{
    router.push({
      path: '/pinganMerchant/buyMoneyOut',
      query: { merchantId }
    });
  }
  const initRechargeDiscount = () => {
    //channel: 1 平安商户充值  channel: 0 购物金充值
    const formData = new FormData();
    formData.append("channel", 1);
    getRechargeDiscount(formData).then(res=>{
      const {highLevelRedPacketMsgUpperHalf,highLevelRedPacketMsgLowerHalf} = res.data;
      payTitle.value = `${highLevelRedPacketMsgUpperHalf}${highLevelRedPacketMsgLowerHalf}`;
    })
  }
  initRechargeDiscount()
</script>

<style lang="scss" scoped>
#app {
  background: #fff;
}
.my {
  // padding-top: 0.3rem;
  min-height: 100%;
  background-color: #F7F7F8;
  height: 100vh;
  .flex {
    display: flex;
    align-items: center;
  }
  .balance {
    padding: rem(30) 0 rem(53) 0;
    text-align: center;
    position: relative;
    h4 {
      font-weight: 500;
      font-size: rem(48);
      color: #00B77A;
      letter-spacing: 0;
      line-height: rem(50);
    }
    span {
      font-size: rem(24);
      line-height: rem(33);
      color: #969798;
    }
    .account-info {
      padding: 5px 10px;
      box-sizing: border-box;
      background: #00B955;
      border-radius: rem(100) 0 0 rem(100);
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: rem(28);
      color: #FFFFFF;
      letter-spacing: 0;
      position: absolute;
      right: 0;
      top: rem(20);
    }
  }
  .explain {
    background: #FFF7EF;
    padding: rem(30);
    font-size: rem(28);
    color: #7A2809;
    letter-spacing: 0;
    line-height: rem(30);
    justify-content: space-between;
    .exp {
      border: rem(1) solid #7A2809;
      border-radius: 21px;
      padding: rem(9) rem(13);
      flex-shrink: 0;
      margin-left: rem(10);
      img {
        width: rem(7);
        height: rem(15.88);
        margin-left: rem(10);
      }
    }
  }

  .recharge {
    background: white;
    padding: rem(30) rem(30);
    font-weight: 500;
    font-size: rem(32);
    color: #292933;
    line-height: rem(30);
    justify-content: space-between;
    padding-top: rem(34);
    img {
      width: rem(7);
      height: rem(15.88);
      margin-left: rem(10);
    }
  }
  .first{
    padding: rem(35) rem(30);
    padding-top: rem(54);
  }
  .online{
    border-top: 1px solid rgb(250,250,250);
  }
  .payTitle {
    span {
      display: inline-block;
      font-size: rem(25);
      background: #FFECE9;
      padding: 2px;
      color: #FE0F23;
      border-radius: 2px;
    }
  }
}
</style>