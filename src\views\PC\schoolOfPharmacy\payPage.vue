<template>
  <div class="payWrapper">
    <div class="contentTop">
      <img class="payIcon" src="../../../assets/images/school/paper.png" alt="">
      <div class="payInfo">
        <div class="tip">请扫描下面的二维码及时付款，以便订单尽快处理！</div>
        <div class="text">订单编号：YBM20220914110301122156</div>
        <div class="text">实付金额：<span style="color: red;">￥199</span></div>
      </div>
    </div>
    <div class="tjTip">
      <el-icon color="#f39800"><Warning /></el-icon>
        请在提交订单后1小时内付款，否则订单会被取消！
    </div>
    <div class="payContent">
      <div class="payQrCode">
        <img v-if="payType === 1" src="../../../assets/images/school/zfbCode.png" alt="">
        <img v-if="payType === 2" src="../../../assets/images/school/wxCode.png" alt="">
        <div class="qrText">{{ {1: '支付宝', 2: '微信'}[payType] }}扫一扫支付</div>
      </div>
      <img class="sjbg" src="../../../assets/images/school/sjbg.png" alt="">
    </div>
  </div>
</template>
<script setup>
  import { ref } from "vue";
  const props = defineProps({
		payType: {
			default: 1,
		},
  });
</script>
<style lang="scss" scoped>
.payWrapper {
  width: 1200px;
  margin: 0 auto;
  margin-top: 20px;
  background: #fff;
	padding-bottom: 80px;
}
.contentTop {
  border: 1px dashed #e0e0e0;
  display: flex;
  align-items: center;
  padding: 30px 50px;
  .payIcon {
    width: 100px;
    height: auto;
  }
  .payInfo {
    margin-left: 50px;
    .tip {
      font-size: 24px;
      font-weight: bold;
      color: #f39800;
    }
    .text {
      font-size: 14px;
      margin-top: 10px;
    }
  }
}
.tjTip {
  font-size: 14px;
  border: 1px solid #f39800;
  color: #f39800;
  height: 50px;
  line-height: 50px;
  background: #F8EBD5;
  margin-top: 20px;
  padding: 0 20px;
}
.payContent {
  margin-top: 20px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  .sjbg {
    width: 200px;
    height: auto;
  }
  .payQrCode {
    text-align: center;
    img {
      width: 200px;
      height: 200px;
    }
    .qrText {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-top: 10px;
    }
  }
}
</style>