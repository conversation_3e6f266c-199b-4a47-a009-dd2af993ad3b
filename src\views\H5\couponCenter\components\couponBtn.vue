<template>
  <div class="couponBtnBox">
    <!-- 立即领取 -->
    <div class="collect voucher-btn"
      :class="{'voucher-btn-zp': couponInfo.voucherType == 9}"
      :style="btnStyle"
      v-if="couponInfo.isLq == 0"
      @click.stop="getVoucher(couponInfo)"
    >
      立即领取
    </div>
    <!-- 去使用 -->
    <div class="lookup voucher-btn"
      :class="{'voucher-btn-zp': couponInfo.voucherType == 9}"
      :style="btnStyle"
      v-else-if="couponInfo.isLq == 1"
      @click="toUse"
    >
      去使用
    </div>
  </div>
</template>
<script setup>
  import { getCurrentInstance } from "vue";
  import { useStore } from "vuex";
  import { receiveCoupon } from '@/http/api';
  import { actionTracking } from '@/config/eventTracking';

  const props = defineProps({
    couponInfo: {
      default: {}
    },
    btnStyle: {
      default: {}
    }
  });

  const store = useStore();
  const { proxy } = getCurrentInstance();

  function toUse() {
    actionTracking('h5_couponCentre_Click', { couponId: props.couponInfo.templateId, type: '去使用' });
    location.href = props.couponInfo.appUrl;
  }
  function getVoucher(info) {
    actionTracking('h5_couponCentre_Click', { couponId: info.templateId, type: '立即领取' });
    store.commit('app/showLoading', true);
    receiveCoupon({
      merchantId: proxy.$merchantId,
      voucherTemplateId: info.templateId,
    }).then((res) => {
      store.commit('app/showLoading', false);
      if (res.status == "success") {
          info.isLq = 1;
          store.commit('app/changeprompt', { promptmsg: res.msg, showprompt: true })
        } else {
          let msg = res.errorMsg ? res.errorMsg : res.msg;
          store.commit('app/changeprompt', { promptmsg: msg, showprompt: true })
        }
    }).catch(err => {
      console.log(err, "请求失败了");
    });
  };

</script>
<style lang="scss" scoped>
.couponBtnBox {
  .voucher-btn {
    width: rem(156);
    height: rem(46);
    line-height: rem(46);
    font-size: rem(24);
    font-weight: bold;
    border-radius: rem(28);
    display: inline-block;
    text-align: center;
    position: relative;
    a {
      display: block;
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      color: rgba(255, 66, 68, 1);
    }
    &.ineffective {
      color: #fff;
      background: linear-gradient(
        90deg,
        rgba(255, 96, 37, 1) 0%,
        rgba(255, 66, 68, 1) 100%
      );
      opacity: 0.5;
    }
    &.ineffective2 {
      color: rgba(148, 148, 166, 1);
      background: rgba(247, 247, 248, 1);
    }
    &.collect {
      color: #fff;
      background: linear-gradient(
        90deg,
        rgba(255, 96, 37, 1) 0%,
        rgba(255, 66, 68, 1) 100%
      );
    }
    &.lookup {
      color: #FF0000;
      border: rem(2) solid #FC0000;;
    }
  }
  .voucher-btn-zp {
    &.collect {
      color: #222222;
      background: #F6DB4E;
      border: rem(2) solid #F6DB4E;;
    }
    &.lookup {
      color: #222222;
      background: #F6DB4E;
      border: rem(2) solid #F6DB4E;;
    }
  }
}
</style>
