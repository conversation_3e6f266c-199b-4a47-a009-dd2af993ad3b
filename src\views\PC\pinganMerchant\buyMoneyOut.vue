<template>
<div class="onlineRecharge">
    <div class="myShoppingGlodTitle">
        <div style="display: flex;align-items: center;">
            <span></span>
            转出到购物金
        </div>
        <div>
            <el-button @click="goBack">返回</el-button>
        </div>
    </div>
    <div class="notice" v-if="noticeText">
        <div class="notice-icon"><i class="promotion-icon"></i></div>
        <div class="notice-text" ref="textContainer" :class="{ scroll: shouldScroll }">
            <div class="text-inner">{{ noticeText }}</div>
        </div>
    </div>
    <div class="recharge-container">
        <div class="selectMoney">
            <div class="title fixd">选择充值金额</div>
            <div class="money-list">
                <div 
                    v-for="(item, index) in selectMoneyList" 
                    :key="index"
                    class="money-item"
                    :class="{ 
                        'money-item-active': item.selected,
                    }"
                    @click="toggleSelectMoney(index)"
                >
                    <div class="money-item-img" v-if="item.recommendStr">{{ item.recommendStr }}</div>
                    <div class="money-item-content">
                        <span style="font-size: 16px;margin-right: 4px;font-weight: 600;">¥</span>{{ item.rechargeAmount }}
                    </div>
                    <div class="money-item-title" v-if="item.discountStr">{{ item.discountStr }}</div>
                </div>
            </div>
        </div>
        <div class="otherMoney">
            <div class="title otherTitle">其他转出金额</div>
            <div style="width: 100%;">
                <el-input
                    placeholder="请输入转出金额"
                    style="width: 90%;"
                    clearable
                    size="large"
                    autocomplete="off"
                    @input="verity"
                    @focus="handleInputFocus"
                    maxlength="10"
                    :controls="false"
                    v-model.sync="inputMoney"
                >
                    <template #prefix>
                        <span style="color: #606266;font-size: 20px;">¥</span>
                    </template>
                </el-input>
                <div>
                    <div class="content-buyMoney">
                        <div style="color: red;" class="explain-buy" v-if="msg" >
                            {{ msg }}
                            <span style="color: #606266;">&nbsp;可用余额&nbsp;<span style="color: rgb(0,198,117);">{{balance.toFixed(2)}}元</span></span>
                            <span class="all-out" @click="allTransferOut">全部转出</span>
                        </div>
                        <div v-else class="explain-buy"><span style="opacity: 0.6;">本次最多可转出{{balance.toFixed(2)}}元</span><span class="all-out" @click="allTransferOut">全部转出</span></div>    
                        <div style="height: 10px;"></div>
                        <span class="all-out" style="font-weight: 600;" @click="jumpServicePdf" ref="servicePdf">《平台代收款说明》</span>    
                    </div>
                </div>
            </div>
        </div>
        <div class="submitBtn">
            <el-button type="primary" @click="submitRecharge" :class="{buttonDis:isButtonDisabled}" :disabled="isButtonDisabled">确认转出</el-button>
        </div>
    </div>
</div>
<pay ref="payRef" :inputMoney="inputMoney" :tranNo="tranNo" @handleClose='handleClose'></pay>
</template>

<script setup>
import { onMounted, ref,onUpdated, onBeforeUnmount,defineComponent,watch,nextTick,computed,getCurrentInstance } from 'vue'
import { useRouter,useRoute } from 'vue-router';
import { ElMessage,ElMessageBox,ElLoading } from 'element-plus'
import { getRechargeDiscount } from '@/http_pc/shoppingGold/index.js'
import pay from "./components/pay.vue"
import { queryPingAnAccountBalance,getPaymentDescPC,payPwdQueryState } from '@/http_pc/api';
import  install from "@/utils/qt"
if(!window.aplus_queue) {
    install()
}
defineComponent({
    pay,
})
const router = useRouter();
const route = useRoute();
const noticeText = ref('');
const textContainer = ref(null)
const shouldScroll = ref(false)
const selectMoneyIndex  = ref(-1)
const selectMoneyList = ref([])
// 支付相关参数
const tranNo = ref('')
const inputMoney = ref('')
const balance = ref(0)
const buttonDis = ref(true)
const msg = ref('')
const payRef=ref()
inputMoney.value = route.query.inputMoney || ''
balance.value = Number(route.query.balance) || 0
tranNo.value = route.query.tranNo || ''
// 检测文本是否需要滚动
const checkOverflow = () => {
    if (!textContainer.value) return
    const container = textContainer.value
    const content = container.querySelector('.text-inner')
    shouldScroll.value = content.scrollWidth > container.offsetWidth
}
// 处理窗口变化
const handleResize = () => {
    checkOverflow()
}
// 获取购物金活动充值信息
const getRechargeInfo = () => {
    getRechargeDiscount({channel: 1}).then((res) => {
        const stairsList = res.data.data.stairsList || [];
        // 给每个都加上selected标识
        stairsList.forEach((item, index) => {
            item.selected = false;
        })
        selectMoneyList.value = stairsList;
        noticeText.value = res.data.data.topUpStr;
    })
}
// 生命周期钩子
onMounted(() => {
    getRechargeInfo();
    checkOverflow()
    window.addEventListener('resize', handleResize)
})

onUpdated(() => {
    checkOverflow()
})

onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize)
})
const goBack = () => {
    router.go(-1);
}
const toggleSelectMoney = (index) => {
    if (selectMoneyList.value[index].selected) {
        selectMoneyList.value[index].selected = false;
        selectMoneyIndex.value = -1;
    } else {
        selectMoneyList.value.forEach(item => {
            item.selected = false;
        });
        selectMoneyList.value[index].selected = true;
        selectMoneyIndex.value = index;
        inputMoney.value = ""
    }
}
const isButtonDisabled = computed(() => {
  // 如果选择了预设金额，或者手动输入的金额有效，则按钮可用
  return buttonDis.value && selectMoneyIndex.value === -1;
});
const BASEURL = import.meta.env.VITE_BASE_URL_PC;
const submitRecharge = () => {
    if(selectMoneyIndex.value !== -1) {
        if(selectMoneyList.value[selectMoneyIndex.value].rechargeAmount > balance.value) {
            return ElMessage.warning('充值金额需小于等于可用余额');
        }
        inputMoney.value = selectMoneyList.value[selectMoneyIndex.value].rechargeAmount;
    }else {
        if(inputMoney.value < 1) {
            return ElMessage.warning('充值金额需大于等于1元');
        }
    }
    const loadingInstance = ElLoading.service()
    payPwdQueryState().then(data=>{
        if (data.data.state == 1) {
            //有支付密码
            payRef.value.openDialog()     
        } else {
            //没有支付密码
            ElMessageBox.confirm("为了您的资金安全，请先设置支付密码", '支付密码设置提醒', {
            confirmButtonText: '去设置',
            cancelButtonText: "关闭",
            customClass:'del-model'
        }).then(res=>{
            window.open(`https:${BASEURL}merchant/center/setPayPwd/index`)
        })
    }
    }).finally(e=>{
        loadingInstance.close()
    })
}
const verity=()=>{ 
  if(Number(inputMoney.value)>Number(balance.value)){
    msg.value='超出可转金额上限'
    buttonDis.value=true
  }else if(!inputMoney.value||Number(inputMoney.value)<=0){
    buttonDis.value=true
    msg.value = ''
  }  
  else{
    msg.value=''
    buttonDis.value=false
  }
 }
 // 页面曝光
 const handleExposure = () => {
    try {
      if(window.aplus_queue) {
        window.updateSpmESix();
        aplus_queue.push({
          'action': 'aplus.record',
          'arguments': ['page_exposure','EXP',{
            'spm_cnt': `1_4.transferToVirtualGold_0-0_0.0.0.${window.getSpmE()}`,
          }]
        })
      }
    }catch (e) {
      console.log(e)
    }
 }
 handleExposure()
 // 子模块点击
  const { proxy } = getCurrentInstance();
  const servicePdf = ref(null)
  const handleModelClick = (fileType,fileId) => {
    try {
      const innerText = servicePdf.value.innerText
      if(window.aplus_queue) {
        aplus_queue.push({
          'action': 'aplus.record',
          'arguments': ['action_sub_module_click','CLK',{
            'spm_cnt': `<EMAIL>@3.${window.getSpmE()}`,
            'scm_cnt': `order.0.all_0.text-${innerText}_fileType-${fileType}_fileId-${fileId}.${proxy.scmEActive(14)}`
          }]
        })
      }
    }catch (e) {
      console.log(e)
    }
  }
 // 跳转协议
 const jumpServicePdf = () => {
    getPaymentDescPC({
  }).then(data => {
    let {fileType,fileId} = data.data
    handleModelClick(fileType,fileId)
    var pdfUrl = data.data.agreementUrl;
    window.open(pdfUrl)
  })
  }
  const merchantId = route.query.merchantId
const handleClose=(flag)=>{
  if(flag){
    router.push({
      path: '/pinganMerchant/pc/my',
      query: { merchantId }
    });
    return
  }
  queryPingAnAccountBalance({ merchantId }).then((res) => {
    if (res.code === 1000) {
      balance.value = res.data.availableBalance;
    }
  })
 }
 //校验输入
 watch(() => inputMoney.value, (newVal,ordVal) => { 
  if(/^([0]([.][0-9]{0,2})?|[1-9][0-9]*([.][0-9]{0,2})?)?$/.test(newVal)){
    inputMoney.value=newVal
  }else{
   nextTick(()=>{
    inputMoney.value=ordVal
  })
  }
}) 
const allTransferOut = () => {
    inputMoney.value = balance.value;
    verity();
    selectMoneyList.value.forEach(item => {
        item.selected = false;
    });
    selectMoneyIndex.value = -1;
}
// 处理输入框获得焦点
const handleInputFocus = () => {
    // 取消所有选中的金额选项
    selectMoneyList.value.forEach(item => {
        item.selected = false;
    });
    // 重置选中索引
    selectMoneyIndex.value = -1;
}
</script>

<style lang='scss' scoped>
.onlineRecharge{
  height: 800px;
  background: #fff;
  width: 980px;
  margin: auto;
  padding: 20px 20px 0 20px;
  box-sizing: border-box;
  .myShoppingGlodTitle {
        span {
            width: 4px;
            height: 16px;
            display: inline-block;
            background: #00B377;
            margin-right: 10px;
        }
        font-weight: 600;
        font-size: 18px;
        color: #292933;
        letter-spacing: 0;
        line-height: 20px;
        display: flex;
        justify-content: space-between;
        padding: 5px 0;
  }
  .notice {
    display: flex;
    align-items: center;
    height: 30px;
    width: 100%;
    background-color: #FFF8E6;
    border-radius: 4px;
    margin: 20px 0;
    .notice-icon {
        padding: 0 8px;
        display: flex;
        align-items: center;
        .promotion-icon {
            display: inline-block;
            width: 14px;
            height: 14px;
            background: url('@/assets/images/notice.png') no-repeat;
            background-size: contain;
            margin-right: 6px;
        }
    }
    .notice-text {
        flex-grow: 1;
        overflow: hidden;
        position: relative;
    }
  }
  .recharge-container {
    .title {
        width: 110px;
        height: 16px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        line-height: 16px;
        margin-right: 10px;
    }
    .selectMoney {
        display: flex;
        max-height: 460px;
        overflow: auto;
        .title {
            text-align: right;
        }
        .fixd {
            margin-top: 3px;
            position: sticky;
            top: 3px;
        }
        .money-list {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            flex: 1;
            padding-top: 3px;
            .money-item {
                width: 180px;
                height: 98px;
                background: #F6F6F9;
                border-radius: 6px;
                cursor: pointer;
                position: relative;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                border: 2px solid #F6F6F9;
                &.money-item-disabled {
                    background-color: #F5F7FA;
                    border-color: #E4E7ED;
                    color: #C0C4CC;
                    cursor: not-allowed;
                    &:hover {
                        border-color: #E4E7ED;
                    }    
                    .money-item-title {
                        background-color: #909399;
                        opacity: 0.5;
                    }                    
                    .money-item-img {
                        background: #F5F7FA;
                        color: #C0C4CC;
                        border: 1px solid #E4E7ED;
                    }
                }
                .money-item-img {
                    position: absolute;
                    top: -5px;  /* 上移1px */
                    left: -5px;  /* 左移1px */
                    background: #FFF3F3;
                    color: #FF223B;
                    padding: 2px 4px;
                    border-radius: 3px 0 3px 0;
                    z-index: 10; 
                }
                .money-item-content {
                    font-size: 28px;
                    font-weight: bold;
                    margin-bottom: 4px;
                }
                .money-item-title {
                    padding: 0 5px;
                    height: 20px;
                    background: #FC443D;
                    border-radius: 4px;
                    color: #FFFFFF;
                    line-height: 20px;
                    text-align: center;
                }
            }
            .money-item-active {
                background: #F9FFFD;
                border: 2px solid #00B955;
                border-radius: 6px;
                &::after {
                    content: "";
                    position: absolute;
                    width: 16px;  /* 图标尺寸 */
                    height: 16px;
                    right: -1px;  /* 对齐边框外侧 */
                    bottom: -1px;
                    background: url('@/assets/images/checkBoxMoney.png');
                    background-size: contain;
                }
            }
        }
    }
    .otherMoney {
        margin-top: 20px;
        display: flex;
        align-items: flex-start;
        .otherTitle {
            width: 125px;
            text-align: right;
            padding-top: 10px;
        }
        .otherMoney-tip {
            margin-top: 10px;
        }
    }
    .submitBtn {
        margin-top: 30px;
        text-align: center;
        .el-button {
            width: 160px;
            height: 48px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 18px;
            color: #FFFFFF;
            background: #00B955;
            text-align: center;
            line-height: 18px;
        }
    }
  }   
}
.text-inner {
  display: inline-block;
  white-space: nowrap;
  transition: 0.3s transform;
  font-size: 14px;
}

.notice-text.scroll .text-inner {
  animation: scroll 40s linear infinite;
  padding-left: 100%;
}

.notice-text.scroll:hover .text-inner {
  animation-play-state: paused;
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}
.buttonDis{
    background-color: #999 !important;
    border: none;
}
.explain-buy{
    font-size: 13px;
    padding-top: 10px;
    margin-left: 6px;

}
.all-out{
    color: rgb(0,198,117);
    margin-left: 5px;
    cursor: pointer;
    opacity: 1 !important;
}
</style>
