<template>
  <div class="voucher">
   
    <div class="amount_box">
      <!-- assemblePrice || '').toFixed(2)).split('.')[0]) -->
      <div v-if="voucher.discount"  class="money_in_voucher">{{voucher.discount.toFixed(2).split('.')[0]}}.<span class="priceInt">{{voucher.discount.toFixed(2).split('.')[1]}}折</span></div>
      <div v-else class="money_in_voucher">¥{{voucher.moneyInVoucher.toFixed(2).split('.')[0]}}.<span class="priceInt">{{voucher.moneyInVoucher.toFixed(2).split('.')[1]}}</span></div>
      <div class="voucher_title">{{voucher.minMoneyToEnableDesc}}</div>
    </div> 
    <div class="info_box">
      <div class="voucher_type">{{voucher.voucherTypeDesc}}</div>
      <div class="voucher_info">{{voucher.voucherTitle}}</div>
        <div v-if="voucher.state==1" class="voucher_button" @click.stop="voucherAction(voucher)"><div class="float_warpper">{{stateDesc(voucher.state)}}</div></div>
        <div v-else class="voucher_button_actived" @click.stop="voucherAction(voucher)"><div class="float_warpper">{{stateDesc(voucher.state)}}</div></div>
      <div class="voucher_scope">{{voucher.voucherInstructions}} </div>
      <div class="voucher_date">{{formatDate(voucher.validDate,  'yyyy-MM-dd')}}-{{formatDate(voucher.validDate,  'yyyy-MM-dd')}}</div>
    </div>
  </div>
</template>

<script setup>
  import { ref, watch, reactive, onMounted, getCurrentInstance} from 'vue';
  import { getskuActionInfo, getVoucher} from '@/http/api';
  import { formatDate } from '@/utils/index';
  import { useStore } from "vuex";

  const store = useStore();
  const { proxy } = getCurrentInstance();
  const props = defineProps({
    voucher: {
      default: null
    },
    skuId :{
      default:""
    }
  });

  function voucherAction(voucher){
      console.log(`voucher action as ${voucher.state}, `);
      switch(voucher.state){
        case 1 :
          getVoucher({
              merchantId : proxy.$merchantId,
              voucherTemplateId : voucher.templateId
          })
          .then((res) =>{
              if(res.status == 'success'){
                  voucher.state = 4;//本地临时状态
                  store.commit('app/changeprompt', { promptmsg: "领券成功", showprompt: true })
              }
          })
          .catch((err)=>{
            store.commit('app/changeprompt', { promptmsg: "领券失败", showprompt: true })
            console.log(err, "领券失败")
          })
          //领券，修改state
          break;
        case 2:
          //去凑单页
          // if(voucher.appUrl != null){
            let uri = voucher.appUrl //? voucher.appUrl : ("ybmpage://couponavailableactivity/" + voucher.templateId + "/" + 1)
          //   console.log(`uri = ${uri}`)
            window.location.href = uri;
          // }
          break;
        case 3:
          break;
    }
  }

  function stateDesc(voucherState){
    if(voucherState == 1){
      return "立即领取"
    }
    else if(voucherState == 2){
      return "去凑单"
    } 
    else if(voucherState == 3){
      return "已用完"
    }
    else if(voucherState == 4){
      return "已领取"
    }
    else{
      return "券错误"
    }
  }
  const hasMask = ref(props.hasMask);

  onMounted(()=>{
  })

</script>

<style lang="scss" scoped>
  .voucher{
    width: 100%;
    height:rem(200);
    background: #f8f8f8;
    padding-top: rem(10);
    padding-right:rem(10);
    display: inline-flex;
    flex-direction: row;
    flex-wrap:  nowrap;

    .amount_box{
      display: inline-flex;
      background: #FaE2E6;
      width: 25%;
      height:100%;
      flex-direction: column;
      align-items : center;
      justify-content: center;
      border-radius: rem(10);
      background-image: radial-gradient(circle at top right, #f8f8f8, #f8f8f8 rem(15), transparent 0), radial-gradient(circle at right bottom, #f8f8f8, #f8f8f8 rem(15), transparent 0);
      .money_in_voucher{
        font-size:rem(35);
        font-weight: bold;
        text-align: center;
        color : red;
        .priceInt{
          font-size: rem(20);
        }
      }
      .voucher_title{
        font-size: rem(20);
        color : red; 
        padding: 0 rem(5);
        margin-top: rem(20);
        text-align: center;
      }
    }
    .info_box{
      background: #fff;
      padding-left: rem(20);
      width: 75%;
      height:100%;
      border-radius: rem(10);
      background-image: radial-gradient(circle at top left, #f8f8f8, #f8f8f8 rem(15), transparent 0), radial-gradient(circle at left bottom, #f8f8f8, #f8f8f8 rem(15), transparent 0);

      .voucher_type{
        display: inline-block;
        width: rem(100);
        color: #fff;
        text-align: center;
        font-size: rem(22);
        font-weight: bold;
        background: #fdae66;
        border-radius: rem(50);
        padding:rem(5);
        margin-top: rem(15);
      }
      .voucher_info{
        margin-top: rem(10);
        font-size: rem(25);
      }
      .voucher_scope{
        margin-top:rem(25);
        font-size: rem(20);
        color:#8d8d8d;
      }
      .voucher_button{
        float:right;
        margin-right: rem(25);
        width:rem(150);
        height:rem(60);
        background: red;
        border-radius: rem(50);
        text-align: center;
        .float_warpper{
          position: relative;
          width: 100%;
          height: rem(30);
          top : rem(15);
          color:white;
          font-size: rem(25);
        }
      }
   
      .voucher_button_actived{
        float:right;
        margin-right: rem(25);
        color:white;
        width:rem(150);
        height:rem(60);
        border-color : red;
        border-width: rem(2);
        border-style: solid;
        border-radius: rem(50);
        text-align: center;
        .float_warpper{
          position: relative;
          width: 100%;
          height: rem(30);
          top : rem(15);
          color:red;
          font-size: rem(25);
        }
      }
      
      .voucher_date{
        margin-top: rem(20);
        font-size: rem(20);
        color:#8d8d8d; 
      }
    }
  }
</style>
