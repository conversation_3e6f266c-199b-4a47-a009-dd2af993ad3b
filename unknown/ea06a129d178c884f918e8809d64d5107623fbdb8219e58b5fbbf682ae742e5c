<template>

    <div class="result-container">
        <div :class="{'result-title':true,error:code==2 , success:code == 0}">{{ title }}</div>
        <div class="result-content">{{ content }}</div>
        <div class="bottom-btn" >
            <el-button type="primary" @click="go" class="comon">{{ btnText }}</el-button>
        </div>
    </div>

</template>
<script setup>
import { ref } from 'vue'
import {useRoute} from 'vue-router'
const route = useRoute()
const code= ref(route.query.code)
const title = ref(code.value==0?'预申请已通过':'预申请未通过')
const content = ref(decodeURIComponent(route.query.msg))
const btnText = ref(code.value == 0 ? '立即申请额度' : '返回我的财富')
import Bridge from "@/config/Bridge";
const go = ()=>{
    if(code.value == 0){
        window.location.href = route.query.h5Url
    }else{
        Bridge.closeWebView()
    }
}
</script>
<style scoped>
.success{
    color:#00B955 !important
}
.error{
    color: red !important;
}
.result-container{
    padding: 25px;
   background: white;
}
.result-title{
    text-align: center;
    margin-top: 20px;
    font-size: 20px;
    color: black;
}
.result-content{
    margin-top: 20px;
    font-size: 15px;
    line-height: 25px;
    text-align: center;
}
.comon {
    width: 100%;
    height: 38px;
    line-height: 38px;
    text-align: center;
    margin-top: 20px;
    border-radius: 5px;
    background: #00B955;
    font-size: 18px;
    padding: 20px 0;
}

.noCommit {

    background: #7FDCAA;
    border-radius: 4px;
    border: none;

}

.bottom-btn {
    text-align: center;
    height: 35px;
    /* background-color: #F0F2F5; */
    padding: 10px 0;
}
</style>