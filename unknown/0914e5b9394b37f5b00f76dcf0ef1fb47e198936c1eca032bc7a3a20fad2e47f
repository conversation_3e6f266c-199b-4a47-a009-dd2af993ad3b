<!-- 自动关闭的toast -->
<template>
	<div class="prompt" @touchmove.prevent>
		<div class="promptmsg">{{prompt}}</div>
	</div>
</template>

<script setup>
  import { ref } from 'vue';
  import { useStore } from 'vuex';
  const store = useStore();
  const props = defineProps({
    prompt: {
      default: ''
    }
  });

  setTimeout(() => {
    store.commit('app/changeprompt', { promptmsg: '', showprompt: false })
  }, 2000)

</script>

<style lang="scss" scoped>
	.prompt{
		position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    text-align: center;
    font-size: 0;
    z-index:1000;
		.promptmsg{
			display: inline-block;
			font-size:14px;
      color: #f0f0f0;
      background-color: rgba(0,0,0,0.7);
      border-radius: 5px;
      line-height: normal;
      padding: 0.1rem 0.24rem;
      box-shadow: 0 0 3px silver;
      margin: 3px;
      white-space: pre-wrap;
      vertical-align: top;
		}
	}
</style>