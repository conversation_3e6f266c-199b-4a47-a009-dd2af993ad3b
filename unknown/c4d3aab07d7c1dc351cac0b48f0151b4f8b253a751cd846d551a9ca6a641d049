import http from '../index';

// 我的平安货
export const getAppLoans = (params) => {
	return http.post('/app/pinganLoan/my_loan', params);
}
// 我的平安货明细
export const getAppLoansDetail = (params) => {
	return http.post('/app/pinganLoan/loan_detail', params);
}
//查询平安ePay可用余额
export const getAppPingAnCreditBalance = (params) => {
	return http.post('/app/pinganaccount/queryPingAnCreditBalance?merchantId='+params.merchantId, {});
}
//获取url
export const getUrl = (params) => {
	return http.post('/app/pinganaccount/preApply?merchantId='+params.merchantId, {});
}