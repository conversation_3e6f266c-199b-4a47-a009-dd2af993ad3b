<template>
  <div class="dialogBox">
		<div class="loadingBox">
			<img class="loadingIcon" src="../../../../assets/images/intelligentProcurement/greenloading.png" alt="">
			<span>数据匹配中</span>
			<span class="greenText">{{ loadingProgress }}%</span>
			<div class="cancelBtn" @click="cancelLoading">取消匹配</div>
		</div>
	</div>
</template>
<script setup>
  import { watch } from "vue";

	 const props = defineProps({
    loadingProgress: {
      default: 0,
    },
		cancelDialog: {
			type: Function,
			default: () => {}
		},
  });

	const cancelLoading = () => {
		props.cancelDialog();
	}

	watch(
    () => props.loadingProgress,
    (val) => {
			if (val === 100) {
     		props.cancelDialog();
			}
		}
  );
  
</script>
<style lang="scss" scoped>
	.dialogBox {
		z-index: 200;
		position: fixed;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		background: rgba(0, 0, 0, 0.5);
		.loadingBox {
			position: fixed;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			width: 236px;
			height: 254px;
			background: #FFFFFF;
			border-radius: 2px;
			display: flex;
			flex-direction: column;
			align-items: center;
			.loadingIcon {
				width: 46px;
				height: 46px;
				margin: 30px 0 12px;
				-webkit-animation: fadenum 2s steps(12, end) infinite;
				animation: fadenum 2s steps(12, end) infinite;
			}
			@keyframes fadenum {
				0% {
					transform: rotate(0deg); 
					-webkit-transform: rotate(0deg); 
				}
				100% {
					transform: rotate(360deg); 
					-webkit-transform: rotate(360deg);
				}
			}
			@-webkit-keyframes fadenum {
				0% {
					transform: rotate(0deg); 
					-webkit-transform: rotate(0deg); 
				}
				100% {
					transform: rotate(360deg); 
					-webkit-transform: rotate(360deg);
				}
			}
			span {
				color: #333;
				margin: 10px 0;
			}
			.greenText {
				color: #00B955;
				font-weight: Bold;
				font-size: 24px;
			}
			.cancelBtn {
				padding: 9px 18px;
				border: 1px solid #E2E2E2;
				border-radius: 2px;
				color: #555555;
			}
		}
	}
</style>