<template>
  <el-dialog
    v-model="showVis"
    title="账号登录验证"
    width="500px"
    :before-close="handleClose"
  >
    <p style="margin-bottom: 20px;">为了您的账号安全，需要进行手机验证码验证后才可登录</p>
    <p>您当前账号：{{ loginPhone }}</p>
    <el-input v-model="code" placeholder="请输入短信验证码" style="width: 400px;marginTop:20px;">
      <template #append>
        <el-button v-if="showCount">重新发送({{ countdownSeconds }})</el-button>
        <el-button v-else @click="showCountdown">获取短信验证码</el-button>
      </template>
    </el-input>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submitCode()">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
  import { ref } from "vue";
  import { sendCrawlerCode, checkCrawlerCode } from '@/http_pc/api';
  import { ElMessage } from 'element-plus';
  import { useRoute } from 'vue-router';
  import { actionTracking } from '@/config/eventTracking';

  const props = defineProps({
    showVis: {
      default: false
    },
    loginPhone: {
      default: '',
    },
    currentMerent: {
      default: '',
    },
  });
  const emit = defineEmits(['cancelDialog']); 
  const loading = ref(false);
  const showCount = ref(false);
  const countdownSeconds = ref(60);
  const timer = ref(null);
  const code = ref('');
  const route = useRoute();

   // 获取验证码，展示倒计时
   const showCountdown = () => {
    // 调用发送验证码接口
    sendCrawlerCode({}).then((data) => {
      if (data.status === "success") {
        showCount.value = true;
        timer.value = setInterval(() => {
          if (countdownSeconds.value > 0) {
            countdownSeconds.value--;
          } else {
            clearInterval(timer.value);
            showCount.value = false;
            countdownSeconds.value = 60;
          }
        }, 1000);
        ElMessage.success('发送成功');
      } else {
        ElMessage.error(data.errorMsg);
      }
    })
  }

  const handleClose = () => {
    emit('cancelDialog', false);
  }

	const submitCode = () => {
    loading.value = true;
    checkCrawlerCode({ code: code.value }).then((res) => {
      console.log('res', res);
      loading.value = false;
      if (res.status === 'success') {
        handleClose();
        const { redirectUrl } = route.query;
        actionTracking('pc_action_login', {
          user_id: currentMerent.value,
        })
        if (redirectUrl) {
          window.location.href = redirectUrl;
        } else {
          window.location.href = '/';
        }
      } else {
        ElMessage.error(res.errorMsg);
      }
    })
	}

</script>
<style lang="scss" scoped>
</style>