import http from './index'
//领券
export function getVoucher(params){
	return http.postFormData('app/voucher/receiveVoucher', params)
}
//拉取品类可用优惠券
export function getskuActionInfo(params){
	return http.postFormData('app/promotion/csuDetail/activityInfo', params)
}
//新热卖排行榜三级页面
export function getSubRankingSkuList(params){
	return http.postFormData('app/recommend/v1/hot/cat/standard/skuList', params);
}
//新热卖排行榜二级页面
export function getSubRankingList(params){
	return http.postFormData('/app/recommend/v1/hot/cat/standard', params);
}

// 热卖排行榜二级页面
export function getRankingList(params) {
  return http.postFormData('/app/recommend/v1/hot/cat', params);
}
// 分类排行榜
export function getRankingInfo(params) {
  return http.postFormData('/app/recommend/v1/hot/catInfo', params);
}
// 折后价
export const handleDiscount = (params) => {
  return http.postFormData('/app/marketing/discount/satisfactoryInHandPrice', params);
};
export const putRequest = (type, url, params) => { //data为键值对的post请求或者普通的get请求
	let tuc = type.toUpperCase();
	if (tuc == 'GET') {
		return http.get(url, params);
	} else if (tuc == 'POST') {
    return http.postFormData(url, params);
	}
};
// 券包购买记录
export const getPurchaseRecord = (params) => {
  return http.postFormData('/app/virtual/order/list', params);
};
// 券包专题页
export const couponPackageSubject = (params) => {
	return http.postFormData('/app/marketing/virtualSku/couponPackageSubject', params);
}
// 券包优惠券列表
export const couponPackageCouponList = (params) => {
	return http.postFormData('/app/marketing/virtualSku/couponPackageCouponList', params);
}
// 券包下单
export const payOrder = (params) => {
	return http.postFormData('/app/virtual/order/submit', params);
}
// 领取优惠券
export const receiveCoupon = (params) => {
	return http.postFormData('/app/voucher/receiveVoucher', params);
}
// 领券中心首页
export const receiveCenterIndex = (params) => {
	return http.postFormData('/app/voucher/receiveCenter/index', params);
}
// 领券中心首页-精选店铺券
export const shopSelect = (params) => {
	return http.postFormData('/app/voucher/receiveCenter/shopSelect', params);
}
// 领券中心店铺券
export const receiveCenterShopCoupon = (params) => {
	return http.postFormData('/app/voucher/receiveCenter/shopCoupon', params);
}
// 领券中心商品券
export const receiveCenterProductCoupon = (params) => {
	return http.postFormData('/app/voucher/receiveCenter/productCoupon', params);
}
// 我的平安货
export const getAppLoans = (params) => {
	return http.post('/app/pinganLoan/my_loan', params);
}
// 我的平安货明细
export const getAppLoansDetail = (params) => {
	return http.post('/app/pinganLoan/loan_detail', params);
}
//查询平安ePay可用余额
export const getAppPingAnCreditBalance = (params) => {
	return http.post('/app/pinganaccount/queryPingAnCreditBalance', params);
}
//解绑
export const unBoundCard = (params) => {
	return http.post('/app/pinganaccount/unBoundCard', params);
}