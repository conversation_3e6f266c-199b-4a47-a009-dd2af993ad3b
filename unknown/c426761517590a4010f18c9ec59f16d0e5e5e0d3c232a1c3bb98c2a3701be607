<template>
  <div class="couponBox">
    <div v-for="item in couponList" :key="item.templateId" class="couponItem">
      <div class="shopIcon">
        <img :src="`${imgUrl}/${item.shopLogoUrl}`" alt="">
      </div>
      <div class="shopName textellipsis">{{ item.shopName }}</div>
      <div class="voucher-cost">
        <span v-if="item.discount" class="voucher-zhekou voucher-value">
          {{ item.discount }}
          <b class="discountB">折</b>
        </span>
        <span v-else class="voucher-value">
          <b class="moneyB">¥</b>
          {{ item.moneyInVoucher }}
        </span>
      </div>
      <div class="moneyDesc">{{ item.minMoneyToEnableDesc }}</div>
      <!-- 操作按钮 -->
      <CouponBtn :couponInfo="item" />
    </div>
    <div v-if="couponList.length" class="bottom-tip">
      <span>{{ bottomTip }}</span>
    </div>
  </div>
</template>

<script setup>
  import CouponBtn from './couponBtn.vue';

  const props = defineProps({
    couponList: {
      default: []
    },
    bottomTip: {
      default: ''
    }
  });

  const imgUrl = import.meta.env.VITE_IMG;

</script>

<style lang="scss" scoped>
.couponBox {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin-top: rem(10);
  .couponItem {
    background: url('../../../../assets/images/couponBig.png') no-repeat;
    background-size: 100% 100%;
    min-width: rem(216);
    height: rem(302);
    margin: rem(10) 0;
    text-align: center;
    margin-right: rem(20);
    .shopIcon {
      margin-top: rem(14);
      img {
        border-radius: 50%;
        width: rem(50);
        height: rem(50);
      }
    }
    .shopName {
      width: rem(180);
      margin: 0 auto;
      color: #D79066;
      font-size: rem(22);
      font-weight: bold;
    }
    .voucher-cost {
      margin-top: rem(10);
      height: rem(68);
      font-size: rem(64);
      .voucher-value {
        color: #f04134;
        font-weight: bold;
        b {
          font-size: rem(24);
          font-weight: bold;
        }
        .discountB {
          margin-left: rem(-16);
        }
        .moneyB {
          margin-right: rem(-16);
        }
        &.voucher-zhekou {
          margin-top: rem(-10);
          s {
            font-size: rem(30);
            font-style: normal;
            text-decoration: none;
            margin-left: rem(-20);
          }
        }
      }
    }
    .moneyDesc {
      font-size: rem(26);
      font-weight: bold;
      color: #BC4707;
      margin: rem(15) 0;
    }
  }
  .couponItem:nth-of-type(3n){
    margin-right: rem(0);
  }
}
.couponBox::-webkit-scrollbar {
  display: none;
}
</style>