
<template>
  <div class="couponCenterBox">
    <div class="tabBox">
      <div class="tabItem" v-for="item in tabData" :key="item.key" @click="changeTab(item.key)">
        <div class="titleText" :class="{activeTitle: activeTab === item.key}">{{ item.title }}</div>
        <div v-if="activeTab === item.key" class="titleBorder"></div>
      </div>
    </div>
    <div class="contentBox">
      <!-- 首页 -->
      <HomePage v-if="activeTab === 0" />
      <!-- 店铺券 -->
      <ShopCoupons v-else-if="activeTab === 1" />
      <!-- 商品券 -->
      <CommodityCoupons v-else-if="activeTab === 2" />
    </div>
  </div>
</template>

<script setup>
  import { ref ,onMounted} from "vue";
  import Bridge from "@/config/Bridge";
  import { actionTracking } from '@/config/eventTracking';
  import HomePage from './homePage.vue';
  import ShopCoupons from './shopCoupons.vue';
  import CommodityCoupons from './commodityCoupons.vue';
  import  install from "@/utils/qtH5"
  const tabData = ref([]);
  const activeTab = ref(0);
  const pageExposureCoupon = () => {
    try {
      if(window.aplus_queue){
        aplus_queue.push({
          'action': 'aplus.record',
          'arguments': ['page_exposure', 'EXP', {
          "spm_cnt":`1_3.couponCenter_0-0_${ {0:'SY',1:'DPQ',2:'SPQ'}[activeTab.value] }.0.0.${window.getSpmEV2()}`
          }]
        });
    }
    }catch (e) {
      console.log(e)
    }
  }
  window.isHaveSession=()=>{
    pageExposureCoupon();
  }
  install();
  Bridge.setAppRightMenu("我的优惠券", "ybmpage://couponmeber");

  actionTracking('h5_page_ActivePage', { pageName: '领券中心' });

  tabData.value = [{
    title: '首页',
    key: 0,
  }, {
    title: '店铺券',
    key: 1,
  }, {
    title: '商品券',
    key: 2,
  }]
  function changeTab(key) {
    window.scrollTo(0, 0);
    activeTab.value = key;
    actionTracking('h5_couponCentre_tab', { title: tabData.value[key].title });
    pageExposureCoupon();
  }
  setTimeout(() => {
          console.log('设备sdk')
          if(window.aplus){
            console.log('设备sdk', aplus.getMetaInfo('_dev_id'))
          }     
        }, 10000);
</script>
<style lang="scss" scoped>
.couponCenterBox {
  background: #F7F7F8;
  min-height: 100vh;
  padding-top: rem(80);
  .tabBox {
    background: #fff;
    display: flex;
    align-items: baseline;
    justify-content: space-around;
    z-index: 100;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    .tabItem {
      margin: rem(20) 0 0;
      .titleText {
        font-size: rem(30);
        color: #575766;
      }
      .activeTitle {
        font-size: rem(32);
        color: #292933;
        font-weight: bold;
      }
      .titleBorder {
        margin: rem(10) auto;
        margin-bottom: 0;
        width: rem(51);
        height: rem(8);
        background: #00B377;
        border-radius: rem(4);
      }
    }
  }
}
</style>
