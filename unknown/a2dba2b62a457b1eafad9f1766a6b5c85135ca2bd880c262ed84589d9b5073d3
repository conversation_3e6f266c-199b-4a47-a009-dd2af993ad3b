const isIphone = navigator.userAgent.indexOf("iPhone") >= 0 || navigator.userAgent.indexOf("iPad") >= 0;
const isAndroid = navigator.userAgent.indexOf("Android") >= 0;
const messageHandlers = (window.webkit || {}).messageHandlers || {};
const Bridge = {
  count: 0,
  _setupWebViewJavascriptBridge(callback) {
    if (window.WebViewJavascriptBridge) {
      return callback(WebViewJavascriptBridge);
    } else {
      if (this.count > 3) return;
      this.count++;
      setTimeout(() => {
        this._setupWebViewJavascriptBridge(callback);
      }, 100);
    }
    if (window.WVJBCallbacks) {
      return window.WVJBCallbacks.push(callback);
    }
    window.WVJBCallbacks = [callback];
    let WVJBIframe = document.createElement("iframe");
    WVJBIframe.style.display = "none";
    WVJBIframe.src = "wvjbscheme://__BRIDGE_LOADED__";
    document.documentElement.appendChild(WVJBIframe);
    setTimeout(() => {
      document.documentElement.removeChild(WVJBIframe);
    }, 0);
  },
  callJDBindBankCardResult(status, msg) {
    if (isIphone) {
      // this._setupWebViewJavascriptBridge((bridge) => {
      //   bridge.callHandler('callJDBindBankCardResult', { status, msg })
      // });
      if (window.webkit) {
        messageHandlers.callJDBindBankCardResult.postMessage({status, msg});
      }
    } else {
      if (window.hybrid) {
        let count = 0;
        function re_do() {
          if (window.hybrid.callJDBindBankCardResult) {
            window.hybrid.callJDBindBankCardResult(status, msg);
            count = 3
          } else {
            setTimeout(() => {
              if (count < 3) {
                re_do()
              }
            }, 50)
          }
          count++;
        }
        re_do()
      }
    }
  },
  getVersion(callback) {
    if (isIphone) {
      this._setupWebViewJavascriptBridge((bridge) => {
        bridge.callHandler('JSGetVersion', { 'key': 'value' }, (responseData) => {
          if (responseData) {
            callback(responseData);
            Bridge.count = 4;
          }
        })
      });
    } else if (isAndroid) {
      if (window.hybrid) {
        let count = 0;
        function re_do() {
          if (window.hybrid.getVersionCode) {
            callback(window.hybrid.getVersionCode());
            count = 3
          } else {
            setTimeout(() => {
              if (count < 3) {
                re_do()
              }
            }, 50)
          }
          count++;
        }
        re_do()
      } else {
        callback('');
      }
    }
  },
  getToken(callback) {
    if (isIphone) {
      this._setupWebViewJavascriptBridge((bridge) => {
        bridge.callHandler('JSGetToken', { 'key': 'value' }, (responseData) => {
          if (responseData) {
            callback(responseData);
            Bridge.count = 4;
          }
        })
      });
    } else if (isAndroid) {
      if (window.hybrid) {
        let count = 0;
        function re_do() {
          if (window.hybrid.getToken) {
            callback(window.hybrid.getToken());
            count = 3
          } else {
            setTimeout(() => {
              if (count < 3) {
                re_do()
              }
            }, 50)
          }
          count++;
        }
        re_do()
      } else {
        callback('');
      }
    } else {
      callback('');
    }
  },
  closeWebView() {
    if (isIphone) {
      this._setupWebViewJavascriptBridge((bridge) => {
        bridge.callHandler('JSCloseWebView', {'key':'value'}, responseData => {})
      })
    } else if (isAndroid) {
      if (window.hybrid) {
        if (window.hybrid.closeWebView) {
          window.hybrid.closeWebView();
        }
      }
    }
  },
  getMerchantId(callback) {
    let merchantId = '';
    let accountId = '';
    if (isIphone) {
      this._setupWebViewJavascriptBridge(bridge => {
        bridge.callHandler("JSGetParameter", { key: "value" }, responseData => {
          if (responseData) {
            if (responseData.indexOf(',') > 0) {
              const str = responseData.split(',');
              merchantId = str[0];
              accountId = str[1];
            } else {
              merchantId = responseData;
            }
            callback(merchantId, accountId);
            Bridge.count = 4;
          }
        });
      });
    } else if (isAndroid) {
      //安卓获取merchantId逻辑加强
      if (window.hybrid) {
        // window.hybrid.setRightMenu("1", "");
        let count = 0;
        function re_do() {
          if (window.hybrid.getMerchantId()) {
            const responseData = window.hybrid.getMerchantId();
            if (responseData.indexOf(',') > 0) {
              const str = responseData.split(',');
              merchantId = str[0];
              accountId = str[1];
            } else {
              merchantId = responseData;
            }
            callback(merchantId, accountId);
            count = 3;
          } else {
            setTimeout(() => {
              if (count < 3) {
                re_do();
              }
            }, 50);
          }
          count++;
        }
        re_do();
      }
      else {
        callback("");
      }
    } else {
      callback("0");
    }
  },
  share_link(title, url, desc, desc_pyq, type = "1", logoUrl) {
    // type 1：双十二 2：控销 3和4的时候logoUrl有效
    try {
      if (isIphone) {
        messageHandlers.getShareLinks.postMessage({
          title: title,
          url: url,
          desc: desc,
          desc_pyq: desc_pyq,
          type: type,
          logoUrl: logoUrl
        });
      } else if (isAndroid) {
        if (window.hybrid) {
          window.hybrid.getShareLinks(title, url, desc, desc_pyq, type, logoUrl);
        }
      }
    } catch (e) {}
  },
  setControlTitle(type = "2", startColor, endColor) {
    try {
      if (isIphone) {
        if (window.webkit) {
          messageHandlers.setControlTitle.postMessage({
            type: type,
            startColor: startColor,
            endColor: endColor
          });
        }
      } else if (isAndroid) {
        if (window.hybrid) {
          window.hybrid.setControlTitle(type, startColor, endColor);
        }
      }
    } catch (e) {}
  },
  controlMallShare(
    title = "控销商城",
    url,
    desc = "药帮忙开控销商城啦！品牌高毛，好卖又赚钱，品种多多，快来选购吧！",
    desc_pyq = "个大品牌药品，等你来购！",
    type = "1"
  ) {
    // type 1：双十二 2：控销
    try {
      if (isIphone) {
        messageHandlers.controlMallShare.postMessage({
          title: title,
          url: url,
          desc: desc,
          desc_pyq: desc_pyq,
          type: type
        });
      } else if (isAndroid) {
        if (window.hybrid) {
          window.hybrid.controlMallShare(title, url, desc, desc_pyq, type);
        }
      }
    } catch (e) {}
  },
  setToolBar(type = "showTitle", content) {
    try {
      if (isIphone) {
        if (window.webkit) {
          messageHandlers.setToolBar.postMessage({
            type: type,
            content: content
          });
        }
      } else if (isAndroid) {
        if (window.hybrid) {
          window.hybrid.setToolBar(type, content);
        }
      }
    } catch (e) {}
  },
  makeCall(data) {
    try {
      if (isIphone) {
        if (window.webkit) {
          messageHandlers.callPhone.postMessage({
            phone: data,
          });
        }
      } else if (isAndroid) {
        if (window.hybrid) {
          window.hybrid.callPhone(data);
        }
      }
    } catch (e) {}
  },
  setNavigationBar(bgColor, fontColor) {
    try {
      if (isIphone) {
        if (window.webkit) {
          messageHandlers.setNavigationBar.postMessage({
            bgColor: bgColor,
            fontColor: fontColor
          });
        }
      } else if (isAndroid) {
        if (window.hybrid) {
          window.hybrid.setNavigationBar(bgColor, fontColor);
        }
      }
    } catch (e) {}
  },
  setAppTitle(value) {
    try {
      if (isIphone) {
        if (window.webkit) {
          messageHandlers.setTitle.postMessage({
            title: value,
          });
        }
      } else if (isAndroid) {
        if (window.hybrid) {
          window.hybrid.setTitle(value);
        }
      }
    } catch (e) {}
  },
  setAppRightMenu(title, action) { //设置右上角
    try {
      if (isIphone) {
        if (window.webkit) {
          messageHandlers.setRightMenu.postMessage({
            title: title,
            action: action
          });
        }
      } else if (isAndroid) {
        if (window.hybrid) {
          window.hybrid.setRightMenu(title, action);
        }
      }
    } catch (e) {}
  },
  getQtTrackParams(callback) {
    if (isIphone) {
      this._setupWebViewJavascriptBridge((bridge) => {
        bridge.callHandler('getQtTrackParams', { 'key': 'value' }, (responseData) => {
          if (responseData) {
            callback(responseData);
            Bridge.count = 4;
          }else{
            callback('{}');
          }
        })
      });
    } else if (isAndroid) {
      if (window.hybrid) {
        let count = 0;
        function re_do() {
          if (window.hybrid.getQtTrackParams) {
            callback(window.hybrid.getQtTrackParams());
            count = 3
          } else {
            setTimeout(() => {
              if (count < 3) {
                re_do()
              }
            }, 50)
          }
          count++;
        }
        re_do()
      } else {
        callback('{}');
      }
    } else {
      callback('{}');
    }
  },
};

export default Bridge;
