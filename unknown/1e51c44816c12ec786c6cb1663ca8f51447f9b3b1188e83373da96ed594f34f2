
<template>
  <div class="couponPackageBox">
    <div class="bannerBox">
      <img src="//upload.ybm100.com/ybm/app/layout/cmsimages/2022-11/0e832fb691798966cd3b01c3ab2e53aa.jpg">
    </div>
    <div class="couponCardBox" v-if="couponCard.length">
      <div v-for="item in couponCard" :key="item.skuId" class="cardItem">
        <img src="../../../assets/images/coupon.png" alt="">
        <div class="couponInfo">
          <div class="couponTitle textellipsis2">{{item.name}}</div>
          <div class="couponBtn" @click="payPackage(item)">
            ￥{{item.discountPrice}}
            <span>立即购买</span>
          </div>
        </div>
      </div>
    </div>
    <div class="noGoods" v-else>
      <div>
        <img src="../../../assets/images/xyy-sorry.png" alt="">
        <span class="text">暂无活动</span>
      </div>
    </div>
    <div class="descriptionBox">
      <div class="descriptionTitle">
        <div class="line"></div>
        <div class="dot"></div>
        <div class="descriptionText">使用说明</div>
        <div class="dot"></div>
        <div class="line"></div>
      </div>
      <div class="descriptionInfo">
        <div>
          1、本券包购券活动仅针对11月1日—11日控销实付GMV≥3000元单体/诊所客户有效；
        </div>
        <div>
          2、购买方式：点击本页面“立即购买”按钮支付9.9元下单，11月30日购买截止；
        </div>
        <div>
          3、符合购券条件的客户，同一客户仅可购买一次，不支持重复购买；
        </div>
        <div>
          4、9.9元券包优惠券为全平台优惠券，可购买全平台自营、商业店铺商品、控销店铺商品；
        </div>
        <div>
          5、使用跨店券下单，如整单退货退款，优惠券退回；如部分退款，优惠券不退还。
        </div>
        <div>
          该活动解释权归小药药所有
        </div>
      </div>
    </div>
    <!-- <div class="empty"></div> -->
    <div class="bottomBtn">
      <div class="purchaseRecord" @click="toRecord">购买记录</div>
      <div class="splitLine"></div>
      <div class="myCoupons" @click="toCouponCenter">我的优惠券</div>
    </div>
    <CouponPackageModal v-if="showModal" :couponPackageInfo="couponPackageInfo" @closeModal="closeModal" />
  </div>
</template>

<script setup>
  import { ref, onMounted, getCurrentInstance } from "vue";
  import { useRouter } from 'vue-router';
  import { couponPackageSubject } from '@/http/api';
  import Bridge from "@/config/Bridge";
  import { sharePage } from '@/utils';
  import { actionTracking } from '@/config/eventTracking';
  import CouponPackageModal from './components/couponPackageModal.vue';

  const { proxy } = getCurrentInstance();
  const router = useRouter();
  const couponCard = ref([]);
  const showModal = ref(false);
  const loading = ref(false);
  const couponPackageInfo = ref({});

  Bridge.setAppRightMenu("1", "");
  // 顶部导航栏分享功能
  sharePage({
    shareTitle: '恭喜您获得一个福利，半价优惠购',
    shareDesc: '半价买超级神券，限时抢购',
    shareIcon: '//upload.ybm100.com/ybm/app/layout/cmsimages/2022-3/f224d38dbdcfd330c4cf73d8f97bc21e.png'
  });
  actionTracking('h5_payCouponList', { name: '券包专区', action: window.location.href });

  // 获取信息
  function getInfo() {
    loading.value = true;
    couponPackageSubject({
      merchantId: proxy.$merchantId,
    }).then((res) => {
      loading.value = false;
      couponCard.value = res.data.list || [];
    }).catch(() => {
      loading.value = false;
    })
  }

  function toRecord() {
    const baseUrl = import.meta.env.VITE_BASE_URL;
    const pageUrl = `${baseUrl}newstatic/#/couponPackage/purchaseRecord?`
    // 使用这种跳转方式因为如用router.push跳转安卓会跟目标页面共用一个导航栏，目标页面不需展示分享按钮，此方法是为了另外打开一个webview
    location.href = `ybmpage://commonh5activity?cache=0&url=${pageUrl}`;
    // 本地调试时使用下面方式
    // router.push('/couponPackage/purchaseRecord');
  };

  function toCouponCenter() {
    actionTracking('h5_payCoupon_myCoupon　', { name: '券包专区' });
    location.href = 'ybmpage://couponmeber';
  }

  // 记录页面滚动位置
  const pageLocation = ref('');

  // 禁止滚动
  function stop(e) {
    let scrollTop = window.scrollY;//滚动的高度；
    pageLocation.value = scrollTop;
    document.body.style.position = 'fixed';
    document.body.style.top = '-' + scrollTop + 'px';
  };

  // 取消滑动限制
  function move() {
    document.body.style.position = 'static';
    window.scrollTo(0, pageLocation.value);
  }

  function payPackage(item) {
    // actionTracking('h5_payCoupon_purchase', { id: item.marketingId });
    couponPackageInfo.value = {
      marketingId: item.marketingId,
      discountPrice: item.discountPrice,
      name: item.name,
      personalQty: item.personalQty,
      voucherQty: item.voucherQty,
      marketingCouponList: item.marketingCouponList,
      skuId: item.skuId,
    }
    showModal.value = true;
    stop();
  };

  function closeModal() {
    showModal.value = false;
    move();
  }

  onMounted(() => {
    getInfo();
  })

</script>
<style lang="scss" scoped>
.couponPackageBox {
  background: #D3382D;
  min-height: 100vh;
  z-index: 10;
  padding-bottom: rem(142);
  .bannerBox {
    img {
      width: 100%;
    }
  }
  .couponCardBox {
    padding: rem(20) rem(24);
    .cardItem {
      padding: rem(24);
      background: #FFFDF6;
      border-radius: rem(20);
      display: flex;
      margin-bottom: rem(20);
      img {
        width: rem(200);
        height: rem(200);
      }
      .couponInfo {
        width: rem(426);
        margin-left: rem(24);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .couponTitle {
          margin-top: rem(8);
          color: #38394B;
          font-size: rem(32);
          line-height: rem(45);
          font-weight: bold;
        }
        .couponBtn {
          margin-bottom: rem(4);
          background-image: linear-gradient(180deg, #FFA270 0%, #FF3402 75%);
          border: rem(3) solid #FFC196;
          box-shadow: inset 0 rem(-2) 0 0 #FFD6CC;
          border-radius: rem(36);
          color: #FFFDF6;
          font-size: rem(32);
          text-align: center;
          font-weight: bold;
          height: rem(74);
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            font-weight: bold;
            margin-left: rem(20);
          }
        }
      }
    }
  }
  .descriptionBox {
    margin: 0 rem(24);
    padding: rem(30) rem(24);
    background: #BD2C21;
    border-radius: rem(20);
    color: #FFFDF6;
    .descriptionTitle {
      text-align: center;
      font-size: rem(28);
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
      .descriptionText {
        margin: 0 rem(6);
      }
      .line {
        width: rem(23);
        height: rem(2);
        background: #FFFDF6;
        border-radius: rem(1);
        opacity: 0.5;
      }
      .dot {
        width: rem(4);
        height: rem(4);
        background: #FFFDF6;
        border-radius: 50%;
        opacity: 0.5;
        margin: 0 rem(6px);
      }
    }
    .descriptionInfo {
      line-height: rem(36);
      margin-top: rem(20);
      font-size: rem(24);
      font-weight: normal;
    }
  }
  .bottomBtn {
    z-index: 100;
    position: fixed;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: rem(112);
    background: #FFFDF6;
    box-shadow: 0 rem(-3) rem(6) 0 rgba(168,71,0,0.09);
    border-radius: rem(20) rem(20) 0 0;
    .splitLine {
      width: rem(2);
      height: rem(32);
      opacity: 0.2;
      background: #741E06;
      border-radius: 1px;
    }
    .purchaseRecord, .myCoupons {
      width: 100%;
      color: #741E06;
      font-size: rem(30);
      font-weight: bold;
      text-align: center;
    }
  }
  .empty {
    height: rem(142);
  }
  .noGoods{
    position: relative;
    text-align: center;
    height: rem(210);
    margin: 20% 0;
    img{
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width:rem(264);
      height:rem(174);
    }
    .text{
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      color: #FFFDF6;
    }
  }
}
::-webkit-scrollbar {
  display: none;
}
</style>
