<template>
  <div class="left_title">
    <div class="green_style"></div>
    <h2>售后申请详情</h2>
  </div>
  <div class="info_list">
    <div class="info_item">
      <div>供应商：</div>
      <div>{{ info.companyName }}</div>
      <div @click="customer" style="display: inline-block; cursor: pointer; margin-left: auto;">
        <img style="width: 16px; height: 16px;"
          src="../../../../assets/images/customer_service.png" alt="" />
      </div>
    </div>
    <div class="info_item">
      <div>申请时间：</div>
      <div>{{ formattedDate(info.createTime) }}</div>
    </div>
    <div class="info_item">
      <div>售后类型：</div>
      <div>{{ info.afterSalesTypeName }}</div>
    </div>
    <div class="info_item">
      <div>补充说明：</div>
      <div>{{ info.merchantRemark }}</div>
    </div>
    <div class="info_item">
      <div>上传凭证：</div>
      <div v-if="props.info.evidences != null &props.info.evidences != undefined && props.info.evidences?.length > 0" @click="checkImage" style="display: flex; cursor: pointer" >
        <img style="width: 16px; height: 12px; margin-top: 1px" src="../../../../assets/images/album.png" alt="" />
        <div style="color: #00b377; font-weight: 600">点击查看</div>
      </div>
    </div>
  </div>
  <div class="demo-image__preview">
    <el-image-viewer hide-on-click-modal @close="closeImagePreview" v-if="imgsVisible" :url-list="props.info.evidences"/>
  </div>
</template>

<script setup>
import { reactive, ref, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
// 是否放大图片
const imgsVisible = ref(false)
const pageLocation = ref('');
const props = defineProps({
  info: Object,
})
// 查看图片
const checkImage = () => {
  if (props.info.evidences != undefined && props.info.evidences?.length > 0) {
    imgsVisible.value = true
    stop()
  } else {
    ElMessage.warning('暂无凭证')
  }
}
// 关闭图片凭证
const closeImagePreview =() => {
  imgsVisible.value = false
  move()
}
const customer = () => {
  var merchantId = props.info.merchantId
  var merchantCode = props.info.orgId
  var orderNo = props.info.orderNo
  var merchantName = props.info.merchantName
  
   window.open(`http://chat-im.test.ybm100.com/custom/#/?appKey=78f9f774145e44f590d0ba0f637e3b20&userMerchantId=${merchantId}&userid=${merchantId}&sc=1003&portalType=1&merchantCode=${merchantCode}&pop=1&ws=1&orderNo=${orderNo}&merchantName=${merchantName}`,'webcall', 'toolbar=no, status=no,scrollbars=0,resizable=0,menubar＝0,location=0,width=680,height=680')
}
const formattedDate = (timestamp) => {
  const date = new Date(timestamp);
   const year = date.getFullYear();  
   const month = (date.getMonth() + 1).toString().padStart(2, '0');  
   const day = date.getDate().toString().padStart(2, '0'); 
   const hour = date.getHours().toString().padStart(2, '0');
   const minute = date.getMinutes().toString().padStart(2, '0');
   const second = date.getSeconds().toString().padStart(2, '0');
   const formattedDate = `${year}-${month}-${day} ${hour}:${minute}:${second}`;  
   return formattedDate
 }
 // 禁止滚动-在显示遮罩层的时候调用
 function stop() {
    let scrollTop = window.scrollY;//滚动的高度；
    pageLocation.value = scrollTop;
    document.body.style.position = 'fixed';
    document.body.style.top = '-' + scrollTop + 'px';
};
// 取消滑动限制-在关闭遮罩层的时候调用
function move() {
  document.body.style.position = 'static';
  window.scrollTo(0, pageLocation.value);
}
</script>
<style lang="scss" scoped>
.left_title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.info_item {
  display: flex;
  margin-bottom: 12px;
}

.info_item div:nth-child(1) {
  width: 70px;
  font-size: 14px;
  margin-right: 24px;
  color: #666666;
  line-height: 14px;
}

.info_item div:nth-child(2) {
  font-weight: 500;
  font-size: 14px;
  color: #333333;
  line-height: 14px;
}

.green_style {
  background-color: #01b377;
  margin-right: 8px;
  width: 3px;
  height: 18px;
  border-radius: 1px;
}

.el-carousel__item h3 {
  display: flex;
  color: #475669;
  opacity: 0.75;
  line-height: 300px;
  margin: 0;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  background-color: #d3dce6;
}
::v-deep .el-image-viewer__mask {
        opacity: .7;
    }
    ::v-deep .el-image-viewer__img {
      width: 50%;
      height: 50%;
    }
</style>
