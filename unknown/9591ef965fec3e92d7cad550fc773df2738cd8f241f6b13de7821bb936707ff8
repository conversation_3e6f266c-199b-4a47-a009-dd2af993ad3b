<!-- 加减购物车按钮，使用示例在temprow.vue组件 -->
<template>
  <div class="priceBox">
    <span v-if="suggestPrice" class="lingshou">
      <b>零售价</b>¥{{ fixedtwo(suggestPrice) }}
    </span>
    <span class="gross-margin" v-if="grossMargin && !(status == 2)">
      <b>毛利率</b>{{ grossMargin }}
    </span>
  </div>
</template>

<script setup>

  const props = defineProps({
    suggestPrice: {
      default: ''
    },
    grossMargin: {
      default: ''
    },
    status: {
      default: ''
    }
  });

  const fixedtwo = (val) => {
    return parseFloat(val).toFixed(2);
  }

</script>

<style lang="scss" scoped>
.priceBox {
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  height: 18px;
  .lingshou {
    height: 16px;
    opacity: 0.5;
    border: 1px solid #6f7397;
    border-radius: 2px;
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #6872a5;
    line-height: 18px;
    padding: 0 5px;
    display: inline-block;
    b {
      font-weight: normal;
      opacity: 0.5;
      font-size: 12px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: left;
      color: #6872a5;
      line-height: 18px;
      padding: 0 5px 0 0;
      border-right: 1px solid #6f7397;
      margin-right: 4px;
    }
  }
  .gross-margin {
    padding: 0 5px;
    height: 16px;
    opacity: 0.5;
    border: 1px solid #6f7397;
    border-radius: 2px;
    margin-left: 5px;
    line-height: 18px;
    padding: 0 5px;
    display: inline-block;
    b {
      font-weight: normal;
      font-size: 12px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: left;
      color: #6872a5;
      padding-right: 5px;
      border-right: 1px solid #6f7397;
      margin-right: 4px;
    }
  }
}
</style>
