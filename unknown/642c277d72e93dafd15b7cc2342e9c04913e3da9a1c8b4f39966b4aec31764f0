<template>
  <div class="couponListBox">
    <div v-for="item in couponList" :key="item.templateId">
      <ComponentItem :couponInfo="item" />
    </div>
  </div>
</template>

<script setup>
  import ComponentItem from './couponItem.vue';
  import { actionTracking } from '@/config/eventTracking';

  const props = defineProps({
    couponList: {
      default: []
    },
  });

  props.couponList.forEach((item) => {
    actionTracking('h5_couponCentre_Exposure', { couponId: item.templateId })
  })

</script>

<style lang="scss" scoped>
.couponListBox {
  margin-top: rem(20);
}
</style>