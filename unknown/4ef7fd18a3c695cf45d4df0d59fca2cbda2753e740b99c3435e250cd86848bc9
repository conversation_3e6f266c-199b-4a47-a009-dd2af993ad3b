<template>
  <div class="btn-container">
    <div
      class="plus-container"
      v-show="!isExtend"
      @click.stop.prevent="addProductCart('add')"
    />
    <div class="spread-add" v-show="isExtend">
      <div class="reduce" @click.stop.prevent="addProductCart('min')"></div>
      <div class="cont">
        <input
          class="input-val"
          :class="'input-val' + goodsId"
          type="tel"
          :value="productValue"
          @change="inputCart"
          @click.prevent.stop="androidclick"
        />
      </div>
      <div class="plus" @click.stop.prevent="addProductCart('add')"></div>
    </div>
  </div>
</template>

<script setup>
  import { useStore } from "vuex";
  import { putRequest } from '@/http/api';
  import {  ref, getCurrentInstance, computed, onMounted, watch } from 'vue';
  import { actionTracking } from '@/config/eventTracking';
  
  const store = useStore();
  const props = defineProps({
    dataId: {
      default: ''
    },
    productNum: {
      default: false
    },
    isSplit: {
      default: ''
    },
    medNum: {
      default: ''
    },
    isPack: {
      default: ''
    },
    nsid: {
      default: ''
    },
    sdata: {
      default: ''
    }
  });
  const { proxy } = getCurrentInstance();
  const goodsId = ref(props.dataId);
  const productValue = ref(props.productNum || 0);
  const is_split = ref(props.isSplit);
  const med_num = ref(props.medNum);
  const is_pack = ref(props.isPack);
  const nsid = ref(props.nsid);
  const sdata = ref(props.sdata);

  const isExtend = computed(() => {
    return productValue.value === 0 ? false : true;
  })
  
  function addProductCart(type) {
    if (type === "add") {
      productValue.value += med_num.value;
      proxy.$emit('addbuyAction')
    } else if (type === "min") {
      if (productValue.value > 0) {
        productValue.value = is_split.value == 1 ? productValue.value - 1 : productValue.value - med_num.value;
      }
    } else {
      return;
    }
    postCartData(productValue.value, type === "add" ? 3 : 1);
    store.commit('app/listenToChildEvent', { isExtend: isExtend.value, dataId: goodsId.value })
  }
  function inputCart(e) {
    let num = parseInt(e.target.value);
    num = num > 0 ? num : 0;
    productValue.value = num;
    postCartData(productValue.value, 2);
    store.commit('app/listenToChildEvent', { isExtend: isExtend.value, dataId: goodsId.value })
  }
  function androidclick(e) {
    let isAndroid = navigator.userAgent.indexOf('Android') >= 0;
    if (isAndroid) {
      e.target.setAttribute("readonly", true);
      e.target.blur();
      let val = parseInt(e.target.value);
      productValue.value = val;
      // 安卓点击加购输入框
      store.commit('app/show_android_addCart', {
        id: goodsId.value,
        val: val,
        showandoridedit: true,
        split: is_split.value,
        medpack: med_num.value,
        package: is_pack.value
      })
    }
  }
  function postCartData(val, addType) {
    if (val > 0) {
      const config = is_pack.value == true ? {
        'merchantId': proxy.$merchantId,
        'amount': val,
        "packageId": goodsId.value,
        "addType": addType,
        "nsid": nsid.value || '',
        "sdata": sdata.value || '',
        "direct": 1
      } : {
        'merchantId': proxy.$merchantId,
        'amount': val,
        "skuId": goodsId.value,
        "addType": addType,
        "nsid": nsid.value || '',
        "sdata": sdata.value || '',
        "direct": 1
      }
      putRequest('post', '/app/changeCart', config).then((res) => {
        if (res.status == "success") {
          if (Number(res.data.qty)) {
            // actionTracking('h5_page_CommodityDetails_o', {
            //   commodityId: goodsId.value,
            //   real: 1
            // })
          }
          if (res.data.qty != val) productValue.value = parseInt(res.data.qty);
          if (res.dialog != null) {
            if (res.dialog.style == 20) {
              store.commit('app/changesureDialog', { dialogmsg: res.dialog.msg, showsureDialog: true })
            } else {
              store.commit('app/changeprompt', { promptmsg: res.dialog.msg, showprompt: true })
            }
          }
          if (res.errorMsg) {
            store.commit('app/changeprompt', { promptmsg: res.errorMsg, showprompt: true })
          }
          try {
            const addconfig = is_pack.value == true ? {
              proid: goodsId.value,
              pronum: productValue.value,
              isAdd: 1,
              type: 1
            } : { proid: goodsId.value, pronum: productValue.value, isAdd: 1 }
            window.webkit.messageHandlers.addPlanNumber.postMessage(addconfig);//ios
          } catch (erro) {

          }

          try {
            if (is_pack.value == true) {
              window.hybrid.addPlanNumber(goodsId.value, productValue.value, 1, 1); //android
            } else {
              window.hybrid.addPlanNumber(goodsId.value, productValue.value, 1); //android
            }
          } catch (erro) {

          };
        } else {
          productValue.value = 0;
          if (res.errorMsg) {
            store.commit('app/changeprompt', { promptmsg: res.errorMsg, showprompt: true })
          } else {
            if (res.msg) {
              store.commit('app/changesureDialog', { dialogmsg: res.msg, showsureDialog: true })
            } else {
              store.commit('app/changeprompt', { promptmsg: res.dialog.msg, showprompt: true })
            }
          }
          store.commit('app/listenToChildEvent', { isExtend: isExtend.value, dataId: goodsId.value })
        }
      }).catch((err) => {

      })
    } else {
      const config = is_pack.value == true ? { "packageIds": goodsId.value, merchantId: proxy.$merchantId, } : { "ids": goodsId.value, merchantId: proxy.$merchantId, }
      putRequest('post', `/app/batchRemoveProductFromCart`, config).then((res) => {
        if (res.status == "success") {
          productValue.value = 0;
          try {
            const addconfig = is_pack.value == true ? {
              proid: goodsId.value,
              pronum: 0,
              isAdd: 1,
              type: 1
            } : { proid: goodsId.value, pronum: 0, isAdd: 1 }
            window.webkit.messageHandlers.addPlanNumber.postMessage(addconfig);//ios
          } catch (erro) {

          };
          try {
            if (is_pack.value == true) {
              window.hybrid.addPlanNumber(goodsId.value, 0, 1, 1); //android
            } else {
              window.hybrid.addPlanNumber(goodsId.value, 0, 1); //android
            }
          } catch (erro) {

          };
        }
      })
    }
  }

    // watch(
    //   () => props.dataId,
    //   (val) => {
        
    //   }
    // )
    // dataId(data) {
    //   goodsId.value = data.value;
    //   productValue.value = productNum.value ? productNum.value : 0;
    //   issplit.value = isSplit.value;
    //   med_num.value = parseInt(medNum.value);
    //   is_pack.value = isPack.value;
    // },
    // isExtend() {
    //   WS.Bus.$emit("cart_isExtend", isExtend.value, goodsId.value)
    // }

  onMounted(() => {
    const tempDataId = computed(() => store.state.app.childEvent.dataId);
    const tempIsExtend = computed(() => store.state.app.childEvent.isExtend);
    if (parseInt(tempDataId.value) === parseInt(goodsId.value)) {
      if (!tempIsExtend.value) {
        productValue.value = 0;
      }
    }
  })
  const changeProductInfo = computed(() => store.state.app.changeProductInfo);
  watch(
    () => changeProductInfo,
    (new_val) => {
      if (new_val.value.id === goodsId.value) {
        productValue.value = parseInt(new_val.value.val);
      }
    },
    {
      immediate: false,
      deep: true,
    },
  )
</script>

<style lang="scss" scoped>
div {
  box-sizing: border-box;
  background: #fff;
}
.plus-container {
  border: none;
  width: rem(46);
  height: rem(46);
  background: url("../assets/images/add.png") no-repeat;
  background-size: 100%;
  position: absolute;
  right: 0;
  top: rem(5);
}
.spread-add {
  display: flex;
  display: -webkit-flex;
  flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  border: 2px solid #00b377;
  border-radius: rem(46);
  box-sizing: border-box;
  .plus,
  .reduce {
    width: rem(46);
    height: rem(46);
  }
  .plus {
    background: url("../assets/images/icon-plus.png") no-repeat;
    background-size: 100% 100%;
  }
  .reduce {
    background: url("../assets/images/icon-reduce.png") no-repeat;
    background-size: 100% 100%;
  }
  .cont {
    width: rem(80);
    height: rem(46);
    line-height: rem(46);
    .input-val {
      box-sizing: border-box;
      padding: 0;
      width: 100%;
      height: rem(38);
      font-size: rem(24);
      font-weight: 600;
      text-align: center;
      color: #292933;
      border: none;
      outline: none;
    }
  }
}
.small {
  .plus-container {
    width: rem(40);
    height: rem(40);
  }
  .spread-add {
    border-width: 1px;
    width: rem(120);
    height: rem(40);
    overflow: hidden;
    box-sizing: border-box;
    .reduce, .plus {
      width: rem(38);
      height: rem(38);
      flex-shrink: 0;
    }
    .cont {
      width: rem(38);
      height: rem(38);
      flex: 1;
      font-size: rem(20);
      line-height: rem(37);
    }
  }
}
.plusContainer {
  .plus-container {
    top: 0;
  }
}
.setMealCar {
  background: inherit;
  .spread-add {
    width: rem(180);
    height: rem(60);
    border: rem(2) #e7eaec solid;
    .cont, .reduce, .plus {
      width: rem(58);
      height: rem(56);
      font-size: rem(30);
      line-height: rem(56);
    }
    .plus {
      background: url("../assets/images/icon-plus-setmeal.png") no-repeat;
      background-size: 100% 100%;
    }
    .reduce {
      background: url("../assets/images/icon-reduce-setmeal.png") no-repeat;
      background-size: 100% 100%;
    }
  }
  .plus-container {
    display: none !important;
  }
  .spread-add {
    display: flex !important;
  }
}
.bigStyle {
  height: rem(50);
  display: flex;
  position: relative;
  width: 100%;
  justify-content: flex-end;
  .spread-add {
    width: rem(150);
    height: rem(50);
    border: rem(2) #00b377 solid;
    overflow: hidden;
  }
  .plus-container {
    width: rem(50);
    height: rem(50);
    line-height: rem(50);
    top: 0;
  }
  .reduce, .plus,.cont {
    width: rem(48);
    height: rem(48);
    line-height: rem(48);
    .input-val {
      box-sizing: border-box;
      padding: 0;
      width: 100%;
      height: rem(48);
      font-size: rem(24);
      font-weight: 600;
      text-align: center;
      color: #292933;
      border: none;
      outline: none;
      line-height: rem(48);
    }
  }
}
</style>
