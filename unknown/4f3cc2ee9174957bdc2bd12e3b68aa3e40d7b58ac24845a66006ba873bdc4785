<!-- 请求数据的loading图 -->
<template>
	<div class="loading-box" @touchmove.prevent>
		<div class="image">
			<!-- <img :src="bannerUrl+'/static/images/loading/JZ_0'+gifidex+'.png'" alt=""> -->
			<img src="@/assets/images/loading.gif" alt="">
			<div class="text">
				{{ text }}...
			</div>
		</div>
	</div>
</template>
<script>
export default {
    props: {
      text: {
        type: String,
        default: "加载中"
      }
    },
  data() {
    return {
      // gifidex: 1,
      imgtimer: "",
      bannerUrl: ""
    };
  },
  methods: {
    // signImgmove() {
    // 	clearInterval(this.imgtimer)
    // 	this.imgtimer = setInterval(() => {
    // 		this.gifidex += 1;
    // 		if (this.gifidex == 50) {
    // 			this.gifidex = 1;
    // 		}
    // 	}, 50)
    // }
  },
  mounted() {
    // this.bannerUrl = process.env.NODE_ENV === 'production' ? '/static/xyyvue/dist' : '';
    // this.signImgmove();
  }
};
</script>
<style lang="scss" scoped>
.loading-box {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 9999;
  .image {
    width: 2rem;
    height: 2rem;
    border-radius: 4px;
    background-color: #2e2e2e;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -1.25rem;
    margin-left: -1.25rem;
    text-align: center;
    img {
      margin: 0 auto;
      margin-top: 0.2rem;
      width: 1.1rem;
      height: 1.1rem;
    }
    .text {
      font-size: 0.22rem;
      color: #fff;
      padding-left: 0.1rem;
    }
  }
}
</style>