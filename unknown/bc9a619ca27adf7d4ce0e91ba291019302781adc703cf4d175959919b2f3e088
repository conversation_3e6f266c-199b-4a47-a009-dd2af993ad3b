<template>
  <div class="buyMoneyOut">
    <div class="content">
      <div style="font-size: 0.4rem;font-weight: 500;font-family: PingFangSC-Medium;">转出金额</div>
      <div class="money"><span style="font-weight: 600;font-size: 0.7rem;">￥</span><van-field @click="openEnter" style="font-size: 1.2rem;" size="large"
          v-model="number"  type="number" @input="verity" /></div>
      <div style="margin-top: 0.2rem;font-size: 0.43rem" v-if="!msg">&nbsp;可用余额&nbsp;￥{{ money.toFixed(2) }} <span class="green"
          @click="number = money;verity()">全部转出</span></div>
      <div v-else style="margin-top: 0.2rem;font-size: 0.4rem;color: red;">{{ msg }}</div>
      <div style="text-align: center;"><van-button :style="{'text-align': 'center','background-color':buttonDis?'rgb(54, 54, 55) !important':''}" :class="{ butt: true, dis: buttonDis }" :disabled="buttonDis"
          @click="commit">确认转出</van-button></div>
    </div>
    <Pay ref="payRef" :number="number" :tranNo="tranNo"></Pay>
    <van-dialog v-model:show="show" show-cancel-button style="width: 75%;" @confirm="toConfig" @cancel="show=false"  confirmButtonColor="#00B955"
      confirmButtonText="去设置" cancelButtonText="稍后去设置" cancelButtonColor="rgb(148,148,165)">
      <div style="padding: 1rem;text-align: center">
        为了您的资金安全，请先设置支付密码
      </div>
    </van-dialog>
  </div>
</template>
<script setup>
import { ref, defineComponent,watch,nextTick ,onMounted} from 'vue';
import { virtualGold,payPwdQueryState,getTranNo } from '@/http/pinganMerchant';
import Pay from "./components/pay.vue"
import { useRouter, useRoute } from 'vue-router';
import { showToast } from 'vant';
// import { Dialog } from 'vant';
defineComponent({
  Pay
})
onMounted(()=>{
  getTranNo({
    timestamp:new Date().getTime(),
    merchantId: merchantId
  }).then(data => {
    tranNo.value = data.tranNo
  })
  
})
const tranNo=ref("")
const number = ref("")
const money = ref(0)
const msg = ref("")
const show = ref(false)
const payRef = ref("")//密码键盘
const enterRef = ref("")//数字输入键盘
const buttonDis = ref(true)
const route = useRoute();
const { merchantId } = route.query;
virtualGold({ merchantId,timestamp:new Date().getTime() }).then((res) => {
  if (res.code === 1000) {
    money.value = res.data.availableVirtualGold;
  }
})
// setTimeout(() => {
//   number.value=0
// }, 3000)
watch(() => number.value, (newVal,ordVal) => { 
  if(/^([0]([.][0-9]{0,2})?|[1-9][0-9]*([.][0-9]{0,2})?)?$/.test(newVal)){
    number.value=newVal

  }else{
   nextTick(()=>{
    number.value=ordVal
  })
  }
})
// const inputNum=(e)=>{
//   number.value=e.target.value
// }

//校验输入
const verity = () => {
 if (Number(number.value) > Number(money.value)) {
    msg.value = '超出可转金额上限'
    buttonDis.value = true
  }
  else if(!number.value||Number(number.value)<=0){
    buttonDis.value = true
    msg.value = ''
  } else {
    msg.value = ''
    buttonDis.value = false
  }

}
const commit = () => {
  //  //yz
  // show.value = true
  // payRef.value.openDialog()
  payPwdQueryState({
    timestamp:new Date().getTime(),
    merchantId: merchantId
  }).then(data => {
   
    if (data.data.state == 1) {
      //有支付密码
      payRef.value.openDialog()

    } else {
      //没有支付密码
      show.value = true

    }
  })

}
const toConfig=()=>{
  window.location.href='ybmpage://setpaypw?payPwdState=0&settingStatus=1'
}
// const openEnter = () => {
//   enterRef.value.openDialog()
// }
</script>
<style scoped lang="scss">
.buyMoneyOut {

  font-family: PingFangSC-Medium;
  padding: 0 0.4rem;
  background-color: white;

  .dis {
    background-color: rgb(148, 148, 165) !important;
  }

  .green {
    color: rgb(0, 185, 85);
    margin-left: 0.2rem;
  }
  .content {
    margin-top: 2rem;

    .butt {
      background-color: rgb(0, 185, 85);
      color: white;
      width: 60vw;
      font-size: 0.6rem;
      font-weight: 800 !important;
      margin-top: 0.8rem;
  
    }
  

    .money {
      font-size: 0.8rem;
      margin-top: 0.1rem;
      display: flex;
      align-items: center;
      border-bottom: 1px solid rgb(245, 245, 245);
    }
  }
}
</style>
<style lang="scss">
.buyMoneyOut input {
  caret-color: green;
   font-weight: 600;
}

</style>
