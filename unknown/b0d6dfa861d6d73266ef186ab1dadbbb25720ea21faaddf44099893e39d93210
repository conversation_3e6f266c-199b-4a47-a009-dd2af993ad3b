<template>
<el-tour v-model="show">
    <el-tour-step :target="ref1?.$el" title="Upload File">
      <img
        style="width: 240px"
        src="https://element-plus.org/images/element-plus-logo.svg"
        alt="tour.png"
      />
      <div>Put you files here.</div>
    </el-tour-step>
  </el-tour>
</template>

<script setup>
import { ref, watch } from "vue";
import { ButtonInstance } from 'element-plus'

const show = ref(false)
const open = ()=>{
    show.value = true;
}

</script>

<style scoped>
.follow-process-alert{

}


</style>


