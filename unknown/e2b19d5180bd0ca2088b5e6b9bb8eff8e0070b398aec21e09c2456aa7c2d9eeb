<template>
  <div class="box" :class="{'box-zp': couponInfo.voucherType == 9}">
    <div class="couponLeftImg" v-if="couponInfo.describeUrl">
      <img class="couponImg" :src="`${imgUrl}/ybm/product/min/${couponInfo.describeUrl}`" />
    </div>
    <div class="contentLeft" v-else>
      <div class="titleLine" :class="{'titleLine-zp': couponInfo.voucherType == 9}">
        <span v-if="couponInfo.voucherTypeDesc">{{ couponInfo.voucherTypeDesc }}</span>
        {{ couponInfo.voucherTitle }}
      </div>
       <!-- 券图片/使用说明 -->
      <div class="goodList">
        <div v-if="couponInfo.skuRelationType == 2 && (couponInfo.voucherSkuImages || []).length" class="imgBigBox">
          <div class="imgBox"
            v-for="(itemImg, ind) in couponInfo.voucherSkuImages.slice(0, 3)"
            :key="ind"
          >
            <img :src="imgUrl + '/ybm/product/min/' + itemImg.imageUrl" alt />
          </div>
        </div>
        <ul v-else-if="couponInfo.voucherInstructions">
          <li
            v-for="(itemInstructions,
            ind) in couponInfo.voucherInstructions.split(';')"
            :key="ind"
            class="range"
          >
            {{ itemInstructions }}
          </li>
        </ul>
        <!-- 店铺logo+店铺名 -->
        <div class="shopInfo" v-if="couponInfo.skuRelationType != 2">
          <img v-if="couponInfo.shopLogoUrl" :src="`${imgUrl}/${couponInfo.shopLogoUrl}`" alt="">
          <span v-if="couponInfo.shopName">{{ couponInfo.shopName }}</span>
        </div>
      </div>
    </div>
    <div class="contentRight">
      <div class="discount" :class="{'discount-zp': couponInfo.voucherType == 9}">
        <template v-if="couponInfo.discount">
          <span
            class="big" :class="{'big-zp': couponInfo.voucherType == 9}">{{
              couponInfo.discount ? String(couponInfo.discount).split('.')[0] : ''
            }}</span><span>{{ couponInfo.discount ? '.' + ((String(couponInfo.discount).split('.') || [])[1] || 0) : '' }}</span>折
        </template>
        <template v-else>
          ￥<span class="big" :class="{'big-zp': couponInfo.voucherType == 9}">{{ couponInfo.moneyInVoucher }}</span>
        </template>
      </div>
      <div class="minMoneyToEnableDesc" :class="{'minMoneyToEnableDesc-zp': couponInfo.voucherType == 9}">{{ couponInfo.minMoneyToEnableDesc }}</div>
      <div class="minMoneyToEnableDesc" :class="{'minMoneyToEnableDesc-zp': couponInfo.voucherType == 9}" v-if="couponInfo.maxMoneyInVoucherDesc">{{
          couponInfo.maxMoneyInVoucherDesc
        }}
      </div>
      <template v-if="couponInfo.isLq === 0">
        <el-button round class="lq" :class="{'lq-zp': couponInfo.voucherType == 9}" @click="toGetVoucher(couponInfo)">立即领取</el-button>
      </template>
      <template v-if="couponInfo.isLq === 1">
        <el-button round :class="{'lq-zp': couponInfo.voucherType == 9}" @click="toUseCoupon(couponInfo)">去使用</el-button>
      </template>
    </div>
  </div>
</template>

<script setup>
  import { toUse, getVoucher } from './btn';
  
  const props = defineProps({
    couponInfo: {
      default: {}
    },
  });
  const imgUrl = import.meta.env.VITE_IMG;

  function toUseCoupon(couponInfo) {
    toUse(couponInfo.pcUrl);
  }
  function toGetVoucher(info) {
    getVoucher(info);
  };

</script>

<style scoped lang="scss">
.box {
  width: 555px;
  height: 193px;
  background: url("../../../../assets/images/couponCenter_pc/coupon.png");
  background-size: 100% 100%;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  .couponLeftImg {
    width: 390px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 378px;
      height: 177px;
      border-radius: 8px;
    }
  }
  .contentLeft {
    width: 390px;
    height: 100%;
    box-sizing: border-box;
    padding: 25px 16px;
    position: relative;
    .titleLine {
      font-weight: 500;
      font-size: 20px;
      color: #292933;
      span {
        padding: 5px 12px;
        font-weight: 500;
        font-size: 18px;
        color: #FFFFFF;
        background-image: linear-gradient(180deg, #FF6B10 0%, #FD1801 100%);
        border-radius: 18.5px;
        margin-right: 10px;
      }
    }
    .titleLine-zp {
      span {
        background: #FF155D;
      }
    }
    .goodList{
      margin-top: 15px;
      width: 100%;
      height: 110px;
      .imgBigBox {
         display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: nowrap;
      }
      .imgBox{
        width: 106px;
        height: 106px;
        background: #FFFFFF;
        border: 1px solid #F2F2F2;
        border-radius: 4px;
        img{
          width: 100%;
          height: 100%;
        }
      }
    }
    .range {
      margin-top: 16px;
      font-weight: 400;
      font-size: 18px;
      color: #676773;
      position: relative;
      padding: 12px;
    }
    .range:before {
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      content: '';
      width: 6px;
      height: 6px;
      background: #676773;
      border-radius: 100%;
    }
    .shopInfo {
      position: absolute;
      left: 20px;
      bottom: 24px;
      font-weight: 400;
      font-size: 20px;
      color: #676773;
      display: flex;
      align-items: center;
      img {
        width: 32px;
        height: 32px;
        border-radius: 100%;
        margin-right: 7px;
      }
    }
  }
  .contentRight {
    width: 160px;
    height: 100%;
    box-sizing: border-box;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    flex-wrap: nowrap;
    .minMoneyToEnableDesc {
      font-weight: 400;
      font-size: 18px;
      color: #FF0000;
    }
    
    .discount {
      font-weight: 500;
      font-size: 24px;
      color: #FE2021;
      .big {
        font-size: 50px;
        color: #FF0000;
      }
    }
    .discount-zp, .big-zp, .minMoneyToEnableDesc-zp {
      color: #FF155D !important;
    }
    .el-button {
      width: 125px;
      height: 45px;
      line-height: 45px;
      padding: 0;
      border: 1.4px solid #FC0000;
      font-weight: 500;
      font-size: 18px;
      color: #FF0000;
      border-radius: 22.5px;
      background-color: transparent;
    }
    .el-button.lq {
      border: none;
      color: #FFFFFF;
      background-image: linear-gradient(180deg, #FF6B10 0%, #FD1801 100%);
    }
    .el-button.lq-zp {
      background: #F6DB4E;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 18px;
      color: #222222;
      border-color: #F6DB4E;
    }
  }
}
.box-zp {
  background: url("../../../../assets/images/couponCenter_pc/coupon_zhuanpinquan.png");
}
</style>
