<template>
  <div class="wrapperBox">
    <div class="topBox">
      <div class="timeBox">
        <div class="billTitle">
          我的账单
        </div>
        <div>
          <el-date-picker
            v-model="time"
            type="daterange"
            unlink-panels
            :clearable="false"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :shortcuts="shortcuts"
            @change="changeDate"
          />
        </div>
        <el-button type="primary" @click="toDownLoad()">下载对账单</el-button>
      </div>
      <div class="dataBox">
        <div v-for="(item, index) in amountData" :key="index" class="dataItem">
          <div v-for="item2 in item" :key="item2.key" class="labelItem">
            <div class="labelText">
              {{ item2.label }}
              <el-tooltip
                :content="item2.tip"
                placement="top"
              >
                <el-icon><Warning /></el-icon>
              </el-tooltip>
            </div>
            <div class="amountText">￥{{ (item2.amount || 0).toFixed(2) }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="tabBox">
      <div class="tabItemBox">
        <div class="defaultTab" :class="actTab === 1 ? 'activeTab' : ''" @click="changeTab(1)">采购总额明细</div>
        <div class="defaultTab" :class="actTab === 2 ? 'activeTab' : ''" @click="changeTab(2)">实际退款明细</div>
        <div class="defaultTab" :class="actTab === 3 ? 'activeTab' : ''" @click="changeTab(3)">退款中金额明细</div>
      </div>
      <div>
        <el-input placeholder="订单号/商品名称" v-model="queryStr" style="width: 300px;" />
        <el-button type="primary" style="margin-left: 20px;" @click="searchList">查询</el-button>
      </div>
    </div>
    <div class="tabContent">
      <DeatilTab :actTab="actTab" :dataSource="dataSource" :tableLoading="tableLoading" />
      <div class="pagination-container">
        <el-pagination
          v-model:currentPage="pageNum"
          v-model:page-size="pageSize"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="pagerChangeSize"
          @current-change="pagerChangePage"
        />
      </div>
    </div>
    <DownloadDialog
      v-if="showDownload"
      :showVis="showDownload"
      :cancelDialog="handleCancel"
    />
  </div>
</template>

<script setup>
	import { actionTracking } from '@/config/eventTracking';
  import { onMounted, ref } from "vue";
  import DeatilTab from './components/detailTab.vue';
  import DownloadDialog from './components/downloadDialog.vue';
  import { queryMyAccount, queryMyPurchaseOrderFee, queryMyPurchaseRefundFee, queryMyPurchaseRefundingFee } from '@/http_pc/api';
  import { formatDate } from '../../../utils/index';
  import { ElMessage } from 'element-plus'

  const actTab = ref(1);
  const time = ref('');
  const queryStr = ref('');
  const tableLoading = ref(false);
  const dataSource = ref([]);
  const total = ref(0);
  const pageNum = ref(1);
  const pageSize = ref(10);

  const showDownload = ref(false);
  const amountData = ref([
    [{
      key: 'totalAmount',
      label: '总采购金额',
      amount: 0,
      tip: '已完成支付的订单总金额。总采购金额=总优惠金额+在线支付+线下转账+购物金+平台账期+三方账期',
    }, {
      key: 'actualRefundAmount',
      label: '实际采购金额',
      amount: 0,
      tip: 'a、已完成支付的订单总金额-已退款成功的退款总金额；b、实际采购金额=总采购金额-实际退款金额-退款优惠金额；c、退款优惠金额=总优惠金额-实际优惠金额',
    }],
    [{
      key: 'originalTotalDiscountAmount',
      label: '总优惠金额',
      amount: 0,
      tip: '已完成支付的订单总优惠',
    }, {
      key: 'totalDiscountAmount',
      label: '实际优惠金额',
      amount: 0,
      tip: '已完成支付的订单总优惠-已退款成功的退款总优惠',
    }],
    [{
      key: 'refundFee',
      label: '实际退款金额',
      amount: 0,
      tip: '退款成功时间在当前时间段内且状态为退款成功的退款单总实退金额',
    }, {
      key: 'refundingAmount',
      label: '退款中金额',
      amount: 0,
      tip: '申请退款时间在当前时间段内且状态为退款审核中的退款单总实退金额',
    }],
    [{
      key: 'onLinePayAmount',
      label: '在线支付',
      amount: 0,
      tip: '当前时间段内通过在线支付方式支付成功的金额',
    }, {
      key: 'offLinePayAmount',
      label: '线下转账',
      amount: 0,
      tip: '当前时间段内线下转账订单店铺核款成功的订单实付金额',
    }],
    [{
      key: 'platformAccountPeriodAmount',
      label: '平台账期',
      amount: 0,
      tip: '当前时间段内自有账期订单的总实付金额',
    }, {
      key: 'tripartiteAccountAmount',
      label: '三方账期',
      amount: 0,
      tip: '当前时间段内银行授信支付订单的总实付金额',
    }],
    [{
      key: 'goldPayAmount',
      label: '购物金',
      amount: 0,
      tip: '当前时间段内订单使用购物金支付的购物金实付金额',
    }]
  ]);
  const shortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '本月',
    value: () => {
      const date = new Date()
      const start = date.setDate(1);
      // const end = new Date(date.getFullYear(), date.getMonth() + 1, 0)
      const end = new Date()
      return [start, end]
    },
  },
  {
    text: '上月',
    value: () => {
      const date = new Date()
      const start = new Date(date.getFullYear(), date.getMonth() - 1, 1);
      const end = date.setDate(0)
      return [start, end]
    },
  },
  {
    text: '最近30天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  ]

  const changeTab = (val) => {
    actTab.value = val;
    dataSource.value = [];
    total.value = 0;
    pageNum.value = 1;
    pageSize.value = 10;
    searchList();
  }
  const toDownLoad = () => {
    showDownload.value = true;
  }

  const handleCancel = () => {
    showDownload.value = false;
  }

  const changeDate = () => {
    const startDate = time.value[0];
    const endDate = time.value[1];
    if (startDate == "" || startDate == null){
      ElMessage.error('请选择日期');
    } else if(endDate == "" || endDate == null){
      ElMessage.error('请选择日期');
    } else if(endDate - startDate  > 3*30*24*60*60*1000 ){
      ElMessage.error('只允许查询3个自然月内的订单');
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      time.value = [start, end];
      getMyAccountInfo();
      searchList();
    } else {
      getMyAccountInfo();
      searchList();
    }
  }

  const pagerChangeSize = (size) => {
    pageSize.value = size;
    searchList();
  };
  const pagerChangePage = (page) => {
    pageNum.value = page;
    searchList();
  };
  const getMyAccountInfo = () => {
    queryMyAccount({
      startTime: formatDate(time.value[0], 'yyyy-MM-dd 00:00:00'),
      endTime: formatDate(time.value[1], 'yyyy-MM-dd 23:59:59'),
    }).then((res) => {
      amountData.value.forEach((arr) => {
        arr.forEach((item, index) => {
          Object.keys((res.data || {})).forEach((key) => {
            if (item.key === key) {
              item.amount = res.data[key];
            }
          })
        })
      });
    })
  }

  // 查询
  const searchList = () => {
    tableLoading.value = true;
    let params = {
      startTime: formatDate(time.value[0], 'yyyy-MM-dd 00:00:00'),
      endTime: formatDate(time.value[1], 'yyyy-MM-dd 23:59:59'),
      queryStr: queryStr.value,
      pageNo: pageNum.value,
      pageSize: pageSize.value,
    };
    if (actTab.value === 1) {
      queryMyPurchaseOrderFee(params).then((res) => {
        tableLoading.value = false;
        dataSource.value = (res.data || {}).list || [];
        total.value = (res.data || {}).total || 0;
      }).catch(() => {
        tableLoading.value = false;
      })
    } else if (actTab.value === 2) {
      queryMyPurchaseRefundFee(params).then((res) => {
        tableLoading.value = false;
        dataSource.value = (res.data || {}).list || [];
        total.value = (res.data || {}).total || 0;
      }).catch(() => {
        tableLoading.value = false;
      })
    } else if (actTab.value === 3) {
      queryMyPurchaseRefundingFee(params).then((res) => {
        tableLoading.value = false;
        dataSource.value = (res.data || {}).list || [];
        total.value = (res.data || {}).total || 0;
      }).catch(() => {
        tableLoading.value = false;
      })
    }
  }

	onMounted(() => {
    actionTracking('pc_action_purchaseOrder', { 
			pageName: '我的账单' ,
    });
    const end = new Date()
    const start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
		time.value = [start, end];

    getMyAccountInfo();
    searchList();
	});
  
</script>
<style lang="scss" scoped>
.wrapperBox {
  width: 978px;
  // padding: 20px;
  background: #fff;
  .topBox {
    padding: 20px;
    .timeBox {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .billTitle {
        font-size: 20px;
      }
    }
  }
  .dataBox {
    display: flex;
    padding: 20px;
    .dataItem {
      text-align: center;
      font-size: 14px;
      border-right: 1px solid;
      width: 200px;
      .labelItem {
        margin-top: 20px;
      }
      .amountText {
        margin-top: 10px;
        font-size: 18px;
        font-weight: bold;
      }
    }
    .dataItem:nth-last-child(1) {
      border: none;
    }
  }
  .tabBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    .tabItemBox {
      display: flex;
      .defaultTab {
        border: 1px solid #e4e7ed;
        font-size: 14px;
        color: #000;
        padding: 10px 20px;
      }
      .activeTab {
        color: #00B377;
        border-bottom: none;
      }
    }
  }
  .tabContent {
    padding: 20px;
  }
  .pagination-container {
    margin-top: 20px;
    float: right;
  }
}
</style>
