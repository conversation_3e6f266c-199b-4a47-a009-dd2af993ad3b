<template>
  <div>
    <div class="show-mask" @click="emit('closeModal', false)"> </div>
    <div class="show-layer">
      <div class="header-wrap">
        <img class="couponIcon" src="../../../../assets/images/coupon.png" alt="">
        <div class="titleInfo">
          <div class="titleText textellipsis">{{couponPackageInfo.name}}</div>
          <div class="limit" v-if="couponPackageInfo.personalQty > 0">限购{{couponPackageInfo.personalQty}}份</div>
        </div>
        <span class="exit-wrap" @click="emit('closeModal', false)"><i class="exit"></i></span>
      </div>
      <div class="coupon-wrap">
        <div class="packageTitle">券包详情</div>
        <div class="item-wrap" v-for="(item, ind) in couponList" :key="ind">
          <div class="couponLeft">
            <div class="priceBox">
              <span v-if="item.voucherState == 1">
                <span class="price">{{ item.moneyInVoucher }}</span>
                <span class="priceDesc">折</span>
              </span>
              <span v-else>
                <span class="priceDesc">￥</span>
                <span class="price">{{ item.moneyInVoucher }}</span>
              </span>
              <span class="priceCount">x{{item.voucherQty}}张</span>
            </div>
            <div class="priceCount">{{ item.minMoneyToEnableDesc }}</div>
          </div>
          <div class="couponRight">
            <div class="couponTitleBox">
              <span class="couponType">{{item.voucherTypeText}}</span>
              <span class="couponTitle textellipsis">{{ item.templateName }}</span>
            </div>
            <div class="couponDate">使用时间：{{item.validDateToString ? `${item.validDateToString}-${item.expireDateToString}` : `购买后${item.validDays}天内有效`}}</div>
          </div>
        </div>
      </div>
      <div class="bottomBtn">
        <div class="packagePrice" @click="toPay">
          ￥{{couponPackageInfo.discountPrice}} 立即购买
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, watch, getCurrentInstance } from 'vue';
  import { useStore } from "vuex";
  import { couponPackageCouponList, payOrder } from '@/http/api';
  import { formatDate } from '@/utils/index';

  const props = defineProps({
    couponPackageInfo: {
      default: {}
    },
  });

  const emit = defineEmits(['closeModal']); 
  const store = useStore();
  const info = ref(props.couponPackageInfo);
  const { proxy } = getCurrentInstance();
  const couponList = ref([]);
  const couponPackageInfo = ref({
    name: info.value.name,
    personalQty: info.value.personalQty,
    discountPrice: info.value.discountPrice,
    skuId: info.value.skuId,
  })

  function getCouponList() {
    couponPackageCouponList({
      actId: info.value.marketingId,
      merchantId: proxy.$merchantId,
    }).then((res) => {
      if (res.code === 1000) {
        couponList.value = res.data.list.map((item) => {
          info.value.marketingCouponList.map((i) => {
            if (i.voucherTemplateId === item.templateId) {
              item.voucherQty = i.voucherQty;
            }
          })
          return item;
        });
      }
    })
  }

  function toPay() {
    const isIphone = navigator.userAgent.indexOf("iPhone") >= 0 || navigator.userAgent.indexOf("iPad") >= 0;
    const isAndroid = navigator.userAgent.indexOf("Android") >= 0;
    payOrder({
      merchantId: proxy.$merchantId,
      orderSource: isIphone ? 1 : isAndroid ? 0 : 2,
      payMoney: couponPackageInfo.value.discountPrice,
      orderMoney: couponPackageInfo.value.discountPrice,
      discount: 0,
      skuId: couponPackageInfo.value.skuId,
    }).then((res) => {
      if (res.code === 1000) {
        location.href = `ybmpage://paywayfordrugschoolactivity?orderId=${res.data.id}&amount=${res.data.payMoney.toFixed(2)}`
      } else {
        store.commit('app/changeprompt', { promptmsg: res.msg, showprompt: true });
        emit('closeModal', false);
      }
    })
  }

  getCouponList();

</script>

<style lang="scss" scoped>
  .show-mask {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100vh;
    opacity: .5;
    background-color: #000;
    z-index: 1000;
  }
  .show-layer {
    width: 100%;
    height: rem(780);
    padding-bottom: rem(180);
    position: fixed;
    bottom: 0;
    // top: 0;
    left: 0;
    border-radius: rem(10) rem(10) 0 0;
    background-color: #F7F7F8;
    opacity: 1;
    z-index: 1001;
    transition: all 0.3s ease;
    .header-wrap {
      display: flex;
      padding: rem(24) rem(30);
      background: #fff;
      border-radius: rem(10) rem(10) 0 0;
      .couponIcon {
        width: rem(100);
        height: rem(100);
        margin-right: rem(20);
      }
      .titleInfo {
        display: inline-block;
        margin: rem(10) 0;
        .titleText {
          max-width: rem(514);
          color: #000000;
          font-size: rem(30);
        }
        .limit {
          margin-top: rem(20);
          color: #FF2121;
          font-size: rem(24);
        }
      }
      .exit-wrap {
        width: rem(88);
        height: rem(88);
        display: inline-block;
        position: absolute;
        right: 0;
        top: 0;
        .exit {
          width: rem(32);
          height: rem(32);
          background:url('../../../../assets/images/icon-exit.png') no-repeat;
          background-size:100%;
          display: inline-block;
          position: absolute;
          top: rem(32);
          right: rem(32);
        }
      }
    }
    .coupon-wrap {
      height: rem(640);
      padding: rem(20) rem(30);
      overflow-y: auto;
      .packageTitle {
        color: #292933;
        font-size: rem(28);
        font-weight: bold;
      }
      .item-wrap {
        background: url('../../../../assets/images/couponBg.png') no-repeat;
        background-size: 100%;
        height: rem(142);
        margin-top: rem(20);
        display: flex;
        .couponLeft  {
          padding: rem(20) rem(0);
          width: rem(220);
          color: #FF2121;
          text-align: center;
          .priceBox {
            display: flex;
            align-items: baseline;
            justify-content: center;
            margin-bottom: rem(10);
            .priceDesc {
              font-size: rem(24);
            }
            .price {
              font-size: rem(50);
              font-weight: bold;
            }
            .priceCount {
              font-size: rem(28);
            }
          }
        }
        .couponRight {
          padding: rem(30) rem(24) rem(24);
          .couponTitleBox {
            display: flex;
            align-items: center;
          }
          .couponType {
            background-image: linear-gradient(90deg, #FF5C5B 0%, #F21E1D 100%);
            border-radius: rem(18);
            color: #fff;
            padding: rem(2) rem(14);
            margin-right: rem(10);
            font-size: rem(24);
          }
          .couponTitle {
            max-width: rem(300);
            font-size: rem(28);
            font-weight: bold;
          }
          .couponDate {
            color: #929294;
            font-size: rem(22);
            margin-top: rem(28);
          }
        }
      }
    }
    .bottomBtn {
      width: 100%;
      height: rem(136);
      background: #fff;
      position: fixed;
      bottom: 0;
      .packagePrice {
        margin: rem(20);
        height: rem(96);
        background-image: linear-gradient(180deg, #FFA270 0%, #FF3402 75%);
        border-radius: rem(48);
        font-size: rem(34);
        font-weight: bold;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
</style>
