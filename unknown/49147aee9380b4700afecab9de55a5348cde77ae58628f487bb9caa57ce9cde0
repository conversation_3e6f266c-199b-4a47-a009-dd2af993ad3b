<template>
  <div class="registerWrapper">
    <div class="shopWrapper">
      <div class="shopHeader">
        <div class="title">已关联店铺</div>
        <el-button type="primary" @click="toConnectShop">新增关联店铺</el-button>
      </div>
      <div class="shopListBox">
        <div v-for="item in shopList" :key="item.merchantId" class="shopItem">
          <div>
            <div class="shopName">
              {{ item.name }}
              <span v-if="loginMerchantId == item.merchantId" class="selectSpan">登录中</span>
              <span v-if="item.status == 1" class="yellowSpan">待提审</span>
              <span v-if="item.status == 4 || item.status == 5 || item.merchantStatus == 1" class="greenSpan">审核中</span>
              <span v-if="item.status == 3 || item.merchantStatus == 3" class="redSpan">审核未通过</span>
            </div>
            <div class="shopAddress">{{ `${item.province}${item.city}${item.district}${item.street}` }}</div>
          </div>
          <div class="loginBtn">
            <span v-if="item.role == 1" class="activeText" @click="seeClerk(item.merchantId)">
              查看店员
            </span>
            <span class="redText" @click="cancelConnect(item)">
              取消关联
            </span>
          </div>
        </div>
      </div>
      <div class="pagination-container">
        <el-pagination
          v-model:currentPage="pageNo"
          v-model:page-size="pageSize"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="pagerChangeSize"
          @current-change="pagerChangePage"
        />
      </div>
    </div>
    <ClerkList v-if="showClerk" :showClerk="showClerk" :cancelDialog="cancelDialog" :merchantId="activeMerId" />
  </div>
</template>
<script setup>
  import { onMounted, ref } from "vue";
  import ClerkList from './components/clerkList.vue';
  import { getMerchantList, delRelShop, getLoginAccountInfo } from '@/http_pc/api';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { useRouter } from 'vue-router';
  import { actionTracking } from '@/config/eventTracking';

  const router = useRouter();
  const pageNo = ref(1);
  const pageSize = ref(10);
  const total = ref(0);
  const shopList = ref([]);
  const showClerk = ref(false);
  const activeMerId = ref(0);
  const loginMerchantId = ref(0); // 当前登录的merchantId
  const loginRole = ref(null); // 当前登录的角色
  const loginPhone = ref(0);

  const getLoginInfo = () => {
    getLoginAccountInfo({}).then((res) => {
      loginRole.value = ((res.data || {}).info || {}).role;
      loginMerchantId.value = ((res.data || {}).info || {}).merchantId || 0;
      loginPhone.value = ((res.data || {}).info || {}).mobile || 0;
    })
  }

  const getInfo = () => {
    getMerchantList({
      pageNo: pageNo.value,
      pageSize: pageSize.value,
    }).then((res) => {
      shopList.value = (res.data || {}).list || [];
      total.value = (res.data || {}).total || 0;
    })
  }

  const pagerChangeSize = (size) => {
    pageSize.value = size;
    getInfo();
  };
  const pagerChangePage = (page) => {
    pageNo.value = page;
    getInfo();
  };

  const toConnectShop = () => {
    window.parent.location.href = '/newstatic/#/register/connectPharmacy';
  }

  const cancelDialog = () => {
    showClerk.value = false;
  }

  const seeClerk = (merchantId) => {
    activeMerId.value = merchantId;
    showClerk.value = true;
    actionTracking('pc_action_managePoi_viewEmployees_click', { 
      phone: loginPhone.value,
      merchant_id: merchantId,
    });
  }

  const cancelConnect = (row) => {
    actionTracking('pc_action_managePoi_disassociate_click', { 
      phone: loginPhone.value,
      merchant_id: row.merchantId,
    });
    if (row.role === 1) {
      ElMessage.error('您为该店铺中的店长角色，暂无法取消关联，请联系客服处理');
    } else {
      ElMessageBox.confirm('取消关联后将无法访问该店铺，确定要取消关联吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        delRelShop({ merchantId: row.merchantId, }).then((res) => {
          if (res.status === 'success') {
            // 取消关联的是当前正在登录的店铺，则返回选择登录页面
            if (loginMerchantId.value == row.merchantId) {
              router.push('/register/selectLoginShop');
            } else {
              ElMessage.success('取消关联成功');
              setTimeout(() => {
                getInfo();
              }, 500);
            }
          } else {
            ElMessage.error(res.msg);
          }
        }).catch((err) => {
          ElMessage.error(err.msg);
        })
      })
    }
  }

  onMounted(() => {
    getLoginInfo();
    getInfo();
  })

</script>
<style lang="scss" scoped>
.registerWrapper {
  background: #fff;
  .activeText {
    color: #00B377;
    font-size: 12px;
    border-right: 1px solid #EEE;
    padding-right: 10px;
  }
  .redText {
    padding-left: 10px;
    color: #E62E2E;
    font-size: 12px;
  }
  .shopWrapper {
    background: #fff;
    width: 980px;
    padding: 30px 0;
    .shopHeader {
      padding-left: 30px;
      width: 758px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      .title {
        font-size: 16px;
        color: #333333;
        font-weight: bold;
        border-left: 4px solid #00B377;
        padding-left: 10px;
      }
      .tip {
        font-size: 12px;
        color: #555;
      }
    }
    .shopListBox {
      border-top: 1px solid #EEE;
      width: 758px;
      margin-left: 30px;
      margin-top: 20px;
      height: 500px;
      overflow-y: scroll;
      .shopItem {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 14px 10px;
        border-bottom: 1px solid #EEE;
        background: #fff;
        .shopName {
          font-size: 14px;
          color: #222222;
          .selectSpan, .yellowSpan, .greenSpan, .redSpan {
            display: inline-block;
            margin-left: 6px;
            font-size: 10px;
            padding: 0px 3px;
          }
          .selectSpan {
            color: #fff;
            background: #00C675;
          }
          .yellowSpan {
            color: #FF8C1A;
            background: rgba(255,140,26,0.05);
            border: 1px solid rgba(255,140,26,0.50);
          }
          .greenSpan {
            color: #00B377;
            background: rgba(0,179,119,0.05);
            border: 1px solid rgba(0,179,119,0.50);
          }
          .redSpan {
            color: #FE2021;
            background: rgba(254,32,33,0.05);
            border: 1px solid rgba(254,32,33,0.45);
          }
        }
        .shopAddress {
          font-size: 12px;
          color: #888888;
          margin-top: 6px;
          max-width: 600px;
        }
        .loginBtn {
          .moreIcon {
            width: 6px;
            height: 10px;
            margin-left: 4px;
          }
        }
      }
      .shopItem:hover {
        background: #EBFBF4;
        .shopName {
          font-weight: bold;
        }
      }
    }
    .pagination-container {
      margin-top: 20px;
      width: 758px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>