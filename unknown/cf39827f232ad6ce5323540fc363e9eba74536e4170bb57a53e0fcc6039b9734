<template>
  <div class="recordBox">
    <div v-if="recordList.length">
      <div v-for="item in recordList" :key="item.id" class="recordItem">
        <div class="orderInfo">
          <div class="orderNo">订单编号：{{item.orderNo}}</div>
          <div class="orderStatus" :class="{activeStatus: item.payStatus == 1}">{{ {1: '待支付', 2: '已完成', 4: '已关闭' }[item.payStatus] }}</div>
        </div>
        <div class="orderContent">
          <img class="couponIcon" src="../../../assets/images/coupon.png" alt="">
          <div class="couponInfo">
            <div class="topContent">
              <div class="couponTitle textellipsis2">
                <span v-for="(tag, index) in item.tagList" :key="index" class="tag">
                  {{ tag.tagName }}
                </span>
                <span class="name">
                  {{item.goodsName}}
                </span>
              </div>
              <div class="couponPrice">
                <div class="priceDec">￥</div>
                <div class="priceInt">{{String(item.orderMoney.toFixed(2)).split('.')[0]}}</div>
                <div class="priceDec">.{{String(item.orderMoney.toFixed(2)).split('.')[1] || '00'}}</div>
              </div>
            </div>  
            <div class="orderDate">订单时间：{{formatDate(item.createTime, 'yyyy-MM-dd hh:mm:ss')}}</div>
          </div>
        </div>
        <div class="bottomContent" v-if="item.payStatus == 1">
          <div class="payBtn" @click="toPay(item)">去支付</div>
        </div>
      </div>
    </div>
    <div class="noGoods" v-else>
      <div>
        <img src="../../../assets/images/xyy-sorry.png" alt="">
        <span class="text">暂无订单</span>
      </div>
    </div>
    <div v-if="recordList.length" class="bottom-tip">
      <span>{{ bottomTip }}</span>
    </div>
  </div>
</template>
<script setup>
  import { ref, onMounted, getCurrentInstance } from "vue";
  import { getPurchaseRecord } from '@/http/api';
  import { formatDate, sharePage } from '@/utils/index';
  import Bridge from "@/config/Bridge";

  const { proxy } = getCurrentInstance();
  const pageNum = ref(1);
  const pageSize = ref(20);
  const recordList = ref([]);
  const isEnd = ref(false);
  const isLoading = ref(false);
  const bottomTip = ref('');

  Bridge.setAppRightMenu(" ", " ");

  // 上拉加载
  function pullUpLoading() {
    let scrollHeight = document.documentElement.scrollHeight;
    let scrollTop = window.scrollY;//滚动的高度；
    let clientHeight = document.documentElement.clientHeight;//滚动的高度；
    if (scrollHeight - scrollTop - clientHeight < 30) {
      if (!isEnd.value && !isLoading.value) {
        pageNum.value++;
        getRecord();
      }
    }
  };

  function getRecord() {
    isLoading.value = true;
    getPurchaseRecord({
      merchantId: proxy.$merchantId,
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      orderType: 2,
    }).then((res) => {
      isLoading.value = false;
      recordList.value = recordList.value.concat(res.data.data.list || []);
      if (!res.data.data.hasNextPage) {
        isEnd.value = true;
      }
      bottomTip.value = isEnd.value ? '没有更多了' : '查看更多';
    }).catch(() =>{
      isLoading.value = false;
    })
  };

  function toPay(item) {
    location.href = `ybmpage://paywayfordrugschoolactivity?orderId=${item.id}&amount=${item.orderMoney.toFixed(2)}`;
  }

  onMounted(() => {
    getRecord();
    window.addEventListener('scroll', pullUpLoading);

    // 监听支付成功后刷新页面-ios
    Bridge._setupWebViewJavascriptBridge(function (bridge) {
      bridge.registerHandler('successCallBack', (responseData) => {
        pageNum.value = 1;
        getRecord();
      })
    });
    // 监听支付成功后刷新页面-安卓
    window._self_fn.successCallBack = () => {
      pageNum.value = 1;
      getRecord();
    }
  });

</script>
<style lang="scss" scoped>
.recordBox {
  min-height: 100vh;
  background: #F7F7F8;
  padding: rem(20);
  .recordItem {
    background: #FFFFFF;
    border-radius: rem(12);
    margin-bottom: rem(20);
    padding: rem(28) rem(20);
    .orderInfo {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .orderNo {
        color: #676773;
        font-size: rem(24);
      }
      .orderStatus {
        width: rem(90);
        text-align: right;
        color: #676773;
        font-size: rem(24);
      }
      .activeStatus {
        color: #FF5729;
      }
    }
    .orderContent {
      display: flex;
      margin-top: rem(24);
    }
    .couponIcon {
      max-width: rem(140);
      max-height: rem(140);
    }
    .couponInfo {
      width: 100%;
      height: rem(140);
      margin-left: rem(20);
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      
      .topContent {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }
      .couponTitle {
        max-width: rem(390);
        color: #000;
        font-size: rem(28);
        font-weight: bold;
        .tag {
          display: inline-block;
          background: rgba(247,141,0,0.05);
          border: rem(1) solid #FF7200;
          border-radius: rem(2);
          color: #FF7200;
          font-size: rem(20);
          margin-right: rem(10);
          padding: 0 rem(2);
          // margin-top: rem(-4);
        }
        .name {
          line-height: rem(40);
        }
      }
      .couponPrice {
        display: flex;
        align-items: baseline;
        color: #000;
        .priceDec {
          font-weight: bold;
          font-size: rem(18);
        }
        .priceInt {
          font-weight: bold;
          font-size: rem(32);
        }
      }
      .orderDate {
        color: #676773;
        font-size: rem(24);
        margin-top: rem(16);
        margin-top: rem(10);
      }
    }
    .bottomContent {
      margin-top: rem(28);
      padding-top: rem(20);
      border-top: 1px solid #F7F7F8;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .payBtn {
        padding: rem(12) rem(42);
        background: #FF2121;
        border-radius: rem(28);
        color: #FFFFFF;
        font-size: rem(24);
      }
    }
  }
  .noGoods{
    position: relative;
    text-align: center;
    height: rem(210);
    margin: 30% 0;
    img{
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width:rem(264);
      height:rem(174);
    }
    .text{
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      color: #A9AEB7;
    }
  }
}
</style>
