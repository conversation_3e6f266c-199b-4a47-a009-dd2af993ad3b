<!-- 静态加购，加减过程中不调接口，仅使用于智能采购页面 -->
<template>
  <div class="btn-container">
    <div class="content">
      <div class="spread-add">
        <div class="reduce" @click.stop.prevent="addProductCart('min')">-</div>
        <div class="cont">
          <input
            @click.stop.prevent
            class="input-val"
            type="tel"
            v-model="productValue"
            @change="inputCart"
            @blur="handleCheckValue"
          />
        </div>
        <div class="plus" @click.stop.prevent="addProductCart('add')">+</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { ref, inject } from 'vue'

	const props = defineProps({
    goodsInfo: {
      default: {}
    },
    excelInfo: {
      default: {}
    },
    hideSubTotal: {
      default: false,
    }
  });

  const skuItem = ref(props.goodsInfo);
  const excelItem = ref(props.excelInfo);

  // 接收爷爷的空的函数
	const sendGrandson = inject('carrier');
	const changeQty = (e) => {
    // (excelItem.value.skus || []).forEach((item, index) => {

    //   if (item.skuId === skuItem.value.skuId) {
    //     item = skuItem.value;
    //   }
    // })
excelItem.value.skus[0] = skuItem.value;
    if (!props.hideSubTotal) {
      // 触发函数，向爷爷通信
		  sendGrandson(excelItem.value,productValue.value);
    }
	}

	const productValue = ref(props.goodsInfo.qty);
  const lastValue = ref(props.goodsInfo.qty)
	const addProductCart = (type) => {
    if (type === "add") {
      let add = props.goodsInfo.isSplit == 1 ? 1 : props.goodsInfo.mediumPackageNum;
      let min = 0
      
      if(productValue.value + add> props.goodsInfo?.limitedQty){
      
        ElMessage.warning(props.goodsInfo.limitedQty+'单位限购')
        return
      }
      if(productValue.value + add> props.goodsInfo.availableQty){
        ElMessage.warning('剩余库存'+props.goodsInfo.availableQty)
        return
      }
      productValue.value += add
      skuItem.value.qty = productValue.value;
      changeQty(productValue.value);
      
      // addType.value = 3;
    } else if (type === "min") {
     let sub = props.goodsInfo.isSplit == 1 ? 1 : props.goodsInfo.mediumPackageNum;
     if( props.goodsInfo.isSplit){
      if(productValue.value - sub < props.goodsInfo?.leastPurchaseNum ){
        ElMessage.warning(props.goodsInfo.leastPurchaseNum+'单位起购')
        return
     }
 
     if(productValue.value - sub< 1 ){
      ElMessage.warning(1+'单位起购')
      return
     }

    }else{
      if(productValue.value - sub < props.goodsInfo?.leastPurchaseNum ){
        ElMessage.warning(props.goodsInfo.leastPurchaseNum+'单位起购')
        return
     }

     if(productValue.value - sub< props.goodsInfo?.mediumPackageNum ){
      ElMessage.warning('中包装数量'+props.goodsInfo.mediumPackageNum)
      return
     }
    }
      if (productValue.value > 0) {
        productValue.value = props.goodsInfo.isSplit == 1 ? productValue.value - 1 : productValue.value - props.goodsInfo.mediumPackageNum;
        lastValue.value = productValue.value;
        // 减完后仍然大于0
        if (productValue.value > 0) {
          skuItem.value.qty = productValue.value;
          changeQty(productValue.value);
        } else {
          // 还原成最后一次输入值
          productValue.value = props.goodsInfo.isSplit == 1 ? productValue.value + 1 : productValue.value + props.goodsInfo.mediumPackageNum;
          lastValue.value = productValue.value;
        }
        // addType.value = 1;
      }
    } else {
      return;
    }
	}
	const inputCart = (e) => {
    let num = parseInt(e.target.value);
    if(num<=0){
      productValue.value = lastValue.value;
      ElMessage.warning('请输入大于0的数字')
      return
    }
    if(num<props.goodsInfo.leastPurchaseNum){
      productValue.value = lastValue.value;
      ElMessage.warning(props.goodsInfo.leastPurchaseNum+'单位起购')
      return
    }
    if(num>props.goodsInfo.limitedQty){
      productValue.value = lastValue.value;
      ElMessage.warning(props.goodsInfo.limitedQty+'单位限购')
      return
    }
    if(num>props.goodsInfo.availableQty){
      productValue.value = lastValue.value;
      ElMessage.warning('剩余库存'+props.goodsInfo.availableQty)
      return
    }
    if (props.goodsInfo.isSplit == 0) {
      const remainder = num % props.goodsInfo.mediumPackageNum;
      if (remainder > 0) {
        ElMessage.warning('此商品不可拆零，已调整采购数量')
        if(num/props.goodsInfo.mediumPackageNum>=1){
          productValue.value = num - remainder;
     
        }else{
          productValue.value = props.goodsInfo.mediumPackageNum;
        }
    
        if(productValue.value != lastValue.value){
          lastValue.value = num - remainder;
          skuItem.value.qty = productValue.value;
          changeQty(productValue.value);
        }
        return
    }
  }
    num = num > 0 ? num : skuItem.value.qty;
    productValue.value = num;
    lastValue.value = num;
    skuItem.value.qty = productValue.value;
    changeQty(productValue.value);
    // addType.value = 2;
  };
  const handleCheckValue = (e) => {
    let num = parseInt(e.target.value);
    if (num < props.goodsInfo.leastPurchaseNum) {
      productValue.value = props.goodsInfo.leastPurchaseNum;
      lastValue.value = props.goodsInfo.leastPurchaseNum;
      skuItem.value.qty = productValue.value;
      changeQty(productValue.value);
      return false;
    }
    if (props.goodsInfo.isSplit == 0) {
      const remainder = num % props.goodsInfo.mediumPackageNum;
      if (remainder > 0) {
        productValue.value = num - remainder;
        lastValue.value = num - remainder;
        skuItem.value.qty = productValue.value;
        changeQty(productValue.value);
      }
    }
    
  };
</script>

<style lang="scss" scoped>
div {
  box-sizing: border-box;
}
.btn-container {
  width: 100%;
  height: 100%;
  position: relative;
}
.content {
  background: #ffffff;
  display: inline-block;
}
.spread-add {
  display: flex;
  display: -webkit-flex;
  flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  box-sizing: border-box;
	border: 1px solid #E0E0E0;
	border-radius: 1px;
  .plus,
  .reduce {
    width: 40px;
    height: 32px;
    line-height: 32px;
    color: #999999;
    text-align: center;
		font-size: 20px;
    cursor: pointer;
  }
  .plus {
    border-left: 1px solid #d7d7d7;
  }
  .reduce {
    border-right: 1px solid #d7d7d7;
  }
  .cont {
    width: 80px;
    .input-val {
      box-sizing: border-box;
      padding: 0;
      width: 100%;
      text-align: center;
      color: #292933;
      border: none;
      outline: none;
      font-size: 16px;
      font-family: MicrosoftYaHei;
      text-align: left;
      color: #333333;
      line-height: 32px;
      text-align: center;
    }
  }
}
</style>
