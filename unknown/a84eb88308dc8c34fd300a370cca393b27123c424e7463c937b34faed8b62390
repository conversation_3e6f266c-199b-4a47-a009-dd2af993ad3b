<template>
  <div class="footerBox">
    <div class="fastEntry">
      <div @click="jumpPage('adoutUs')">关于我们</div>
      <div @click="jumpPage('contactUs')">联系我们</div>
      <div @click="jumpPage('helpCenter')">帮助中心</div>
      <div @click="jumpPage('officialWeb')">公司官网</div>
    </div>
    <div class="linkBox">
      <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010502049946" target="_blank">
        <img src="../../../../assets/images/ba.png" style="width: 20px;height: 20px;" />
        京公网安备11010502049946号
      </a>
      <a href="https://beian.miit.gov.cn/" target="_blank">京ICP备2022016495号-1</a>
      <a href="http://www.ybm100.com/branchCertificate/showByName/1101.htm" target="_blank">药品医疗器械网络信息服务备案编号：(京)网药械信息备字(2022)第00246号</a>
      <a href="http://www.ybm100.com/branchCertificate/showByName/1102.htm" target="_blank">医疗器械网络交易服务第三方平台备案凭证：(京)网械平台备字(2022)第00019号</a>
    </div>
    <div>
      Copyright ©2016-2019 ybm100.com All rights reserved. 版权所有:药帮忙
    </div>
  </div>
</template>
<script setup>
  const jumpPage = (type) => {
    const url = {
      'adoutUs': '/helpCenter/about.htm',
      'contactUs': '/helpCenter/contact.htm',
      'helpCenter': '/helpCenter/about.htm',
      'officialWeb': '//app.mokahr.com/apply/xyy/28491#/?anchorName=default_banner&sourceToken=',
    }[type];
    window.open(url);
  }
</script>
<style lang="scss" scoped>
.footerBox {
  background: #F8F8F8;
  width: 1200px;
  margin: 0 auto;
  margin-top: 35px;
  color: #888888;
  text-align: center;
  .fastEntry {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    div {
      padding: 0 10px;
      border-right: 1px solid #888;
    }
    div:nth-last-child(1){
      border-right: none;
    }
    div:hover {
      color: #00B377;
    }
  }
  .linkBox {
    margin: 20px 0 10px;
    display: flex;
    align-items: center;
    a {
      color: #888;
      display: flex;
      align-items: center;
      margin: 0 5px;
    }
    a:hover {
      color: #222;
    }
  }
}
</style>