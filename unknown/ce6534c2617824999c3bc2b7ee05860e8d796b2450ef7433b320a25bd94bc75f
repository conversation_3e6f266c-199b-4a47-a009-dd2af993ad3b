<template>
  <div class="registerWrapper">
    <RegisterHeader title="注册" :isLogin="true" />
    <div class="infoWrapper">
      <div class="infoHeader">
        <span class="title">上传资质信息</span>
        <span class="tip">*请上传相关资质，审核通过后完成店铺的关联</span>
      </div>
      <div class="uploadWrapper">
        <div class="uploadHeader">
          <div>
            <span style="color: #E62E2E;">*</span>
            授权委托书
          </div>
          <div class="tipBox">
            <span @click="seeEgImg">查看示例图片</span>
            <a class="download" download="授权委托书模板.doc" href="/licenseAudit/down/downloadLicenseTemplate.json?categoryCode=FRSQ" >
              <span>委托书模板下载</span>
            </a>
          </div>
        </div>
        <div class="uploadBox">
          <el-upload
            v-model:file-list="FRSQ_FileList"
            action
            list-type="picture-card"
            :http-request="toUploadImg"
            :before-upload="beforeUpload"
            :on-preview="handlePictureCardPreview"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </div>
      </div>
      <div class="uploadWrapper">
        <div class="uploadHeader">
          <div>
            <span style="color: #E62E2E;">*</span>
            被委托人身份证复印件
          </div>
        </div>
        <div class="uploadBox">
          <el-upload
            v-model:file-list="WTRZ_FileList"
            action
            list-type="picture-card"
            :http-request="toUploadImg2"
            :before-upload="beforeUpload2"
            :on-preview="handlePictureCardPreview"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </div>
      </div>
      <div class="btnBox">
        <el-button class="submitBtn" type="primary" @click="onSubmit">提交资质</el-button>
      </div>
      <el-dialog v-model="dialogVisible">
        <img w-full :src="dialogImageUrl" alt="Preview Image" />
      </el-dialog>
    </div>
    <RegisterFooter />
  </div>
</template>
<script setup>
  import { onMounted, ref } from "vue";
  import RegisterFooter from './components/registerFooter.vue';
  import RegisterHeader from './components/registerHeader.vue';
  import { getMerchantList, getUploadAuthorization, uploadImg, uploadAuthorization } from '@/http_pc/api';
  import { Plus, Delete } from '@element-plus/icons-vue'
  import { ElMessage } from "element-plus";
  import { useRouter, useRoute } from 'vue-router';

  const router = useRouter();
  const route = useRoute();
  const imgUrl = import.meta.env.VITE_IMG;
  const fromType = (route.query || {}).fromType || '';
  const pageNo = ref(1);
  const pageSize = ref(10);
  const total = ref(0);
  const shopList = ref([]);
  const FRSQ_FileList = ref([]);
  const WTRZ_FileList = ref([]);
  const dialogImageUrl = ref('');
  const dialogVisible = ref(false);

  const getInfo = () => {
    getMerchantList({
      pageNo: pageNo.value,
      pageSize: pageSize.value,
    }).then((res) => {
      shopList.value = (res.data || {}).list || [];
      total.value = (res.data || {}).total || 0;
    })
  }

  const toUploadImg = (file) => {
    uploadImg({ file, }).then((res) => {
      if (res.status === 'success') {
        FRSQ_FileList.value.push({
          name: `https:${imgUrl}${res.fileName[0]}`,
          url: `https:${imgUrl}${res.fileName[0]}`
        });
      }
    });
  }

  const beforeUpload = (file) => {
    if (FRSQ_FileList.value.length === 3) {
      ElMessage.error('最多上传3张图片');
      return false;
    }
    const isJPG = file.type === 'image/jpg' || file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/bmp' || file.type === 'image/gif';
    if (!isJPG) {
      ElMessage.error('请上传正确格式的图片');
      return false;
    }
    const size = 2;
    const isLt2M = file.size / 1024 / 1024 < size;
    if (!isLt2M) {
      ElMessage.error(`图片大小不得超过${size}M`);
      return false;
    }
    if (isJPG && isLt2M) {
      toUploadImg(file);
    }
    return false;
  };

  const toUploadImg2 = (file) => {
    uploadImg({ file, }).then((res) => {
      if (res.status === 'success') {
        WTRZ_FileList.value.push({
          name: `https:${imgUrl}${res.fileName[0]}`,
          url: `https:${imgUrl}${res.fileName[0]}`
        });
      }
    });
  }

  const beforeUpload2 = (file) => {
    if (WTRZ_FileList.value.length === 3) {
      ElMessage.error('最多上传3张图片');
      return false;
    }
    const isJPG = file.type === 'image/jpg' || file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/bmp' || file.type === 'image/gif';
    if (!isJPG) {
      ElMessage.error('请上传正确格式的图片');
      return false;
    }
    const size = 2;
    const isLt2M = file.size / 1024 / 1024 < size;
    if (!isLt2M) {
      ElMessage.error(`图片大小不得超过${size}M`);
      return false;
    }
    if (isJPG && isLt2M) {
      toUploadImg2(file);
    }
    return false;
  };

  const handlePictureCardPreview = (file) => {
    dialogImageUrl.value = file.url;
    dialogVisible.value = true;
  }

  const seeEgImg = () => {
    dialogImageUrl.value = '//upload.ybm100.com//ybm/product/pic/min/6923341201797-678797.png';
    dialogVisible.value = true;
  }

  const onSubmit = () => {
    const FRSQObj = {
      name: "授权委托书(模版在PC端下载)",
      licenseCode: 'FRSQ',
      licenseImgUrls:(FRSQ_FileList.value.map(i => i.url)).join(),
    };

    const WTRZObj = {
      name: "被委托人身份证复印件(正反两面)",
      licenseCode: 'WTRZ',
      licenseImgUrls:(WTRZ_FileList.value.map(i => i.url)).join(),
    }
    
    const merchantId = (route.query || {}).merchantId;
    const licenseAuditImgListStr = JSON.stringify([FRSQObj, WTRZObj]);
    uploadAuthorization({
      merchantId,
      licenseAuditImgListStr,
    }).then((res) => {
      if (res.status === 'success') {
        router.push(`/register/examineShopRelation?merchantId=${merchantId}`);
      } else {
        ElMessage.error(res.msg);
      }
    })
  }

  const getUpload = () => {
    const merchantId = (route.query || {}).merchantId;
    getUploadAuthorization({ merchantId, }).then((res) => {
      // console.log('获取已上传的资质', res);
      if (res.status === 'success') {
        ((res.data || {}).list || []).map((item) => {
          if (item.licenseCode === 'FRSQ') {
            item.listImgUrls.map((i) => {
              FRSQ_FileList.value.push({ name: i, url: i });
            })
          } else if (item.licenseCode === 'WTRZ') {
            item.listImgUrls.map((i) => {
              WTRZ_FileList.value.push({ name: i, url: i });
            })
          }
        })
      }
    })
  }

  onMounted(() => {
    getInfo();
    // if (fromType === 'seeDetail') {
    //   getUpload();
    // }
  })

</script>
<style lang="scss" scoped>
.registerWrapper {
  background: #F8F8F8;
  width: 100%;
  min-height: 100vh;
  .activeText {
    color: #00B377;
  }
  .infoWrapper {
    background: #fff;
    width: 1200px;
    margin: 0 auto;
    padding: 70px 0;
    .infoHeader {
      margin: 0 auto;
      width: 758px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      font-size: 12px;
      .title {
        font-size: 16px;
        color: #333333;
        font-weight: bold;
      }
      .tip {
        margin-left: 25px;
        font-size: 12px;
        color: #888;
      }
    }
    .uploadWrapper {
      width: 760px;
      margin: 0 auto;
      border: 1px solid #EEEEEE;
      margin-top: 30px;
      .uploadHeader {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 20px;
        background: #F5F5F5;
        color: #292933;
        font-size: 14px;
        .tipBox {
          color: #00B377;
          font-size: 12px;
          .download {
            color: #00B377 !important;
            border-left: 1px solid #00B377;
            padding-left: 6px;
            margin-left: 6px;
          }
        }
      }
      .uploadBox {
        height: 170px;
        margin: 20px;
      }
    }
  }
  .btnBox {
    width: 100%;
    text-align: center;
    margin-top: 40px;
  }
  .submitBtn {
    width: 360px;
    // margin: 0 auto;
  }
}
</style>