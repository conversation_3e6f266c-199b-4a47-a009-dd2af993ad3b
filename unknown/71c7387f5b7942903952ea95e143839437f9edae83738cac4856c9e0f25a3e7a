<template>
  <div class="titleWrapper">
    <div class="title-border" />
    <div class="title">
      {{ title }}
    </div>
  </div>
</template>

<script setup>
	const props = defineProps({
    title: {
      default: ''
    },
  });
</script>
<style lang="scss" scoped>
  .titleWrapper {
    color: #000;
    font-size: 14px;
    display: flex;
    align-items: center;
    .title-border {
      width: 3px;
      height: 16px;
      background: #00B955;
      border-radius: 2px;
      margin-right: 8px;
    }
    .title {
      font-weight: bold;
    }
  }
</style>
