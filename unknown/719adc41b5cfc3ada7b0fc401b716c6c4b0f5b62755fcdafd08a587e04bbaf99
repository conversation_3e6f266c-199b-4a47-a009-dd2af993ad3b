<template>
  <el-dialog
    v-model="dialogVisible"
    title="下载对账单"
    width="500px"
    :before-close="handleClose"
  >
    <p style="margin-bottom: 20px;">选择月份后，下载所选月份下的药店账单数据</p>
    <span>月份</span>
    <el-date-picker
        style="width: 300px;
        margin-left: 20px;"
        v-model="yearMonth"
        :disabled-date="disabledDate"
        type="month"
        placeholder="选择月份"
        :value="getLastMonthDate"
      />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose()">关闭</el-button>
        <el-button type="primary" :loading="loading" @click="downloadExcel">确认下载</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
  import { actionTracking } from '@/config/eventTracking';
  import { onMounted, ref } from "vue";
  import { downLoadLoan } from '@/http_pc/pinganLoans';
  import { formatDate, exportExcel } from '../../../../utils/index';
  import { ElMessage } from 'element-plus';

  const props = defineProps({
    cancelDialog: {
      type: Function,
      default: () => {}
    },
  });
  const yearMonth = ref('');
  const loading = ref(false);
  const dialogVisible = ref(true)

  const disabledDate = (time) => {
    //return time.getFullYear() < 2020
    const year = time.getFullYear();
    // let month = time.getMonth() + 1;
    // if (month < 10) month = '0' + month;
    const date = year; //把所有年月和需要建立的月份匹配,把没有匹配上的返回出去,让月
    return date < '2023'; // 如果日期小于2023年1月，则被禁用
  }
  //默认选中上个月
  const getLastMonthDate = () => {
    const date = new Date();
    const lastMonth = new Date(date.getFullYear(), date.getMonth() - 1, date.getDate());
    return lastMonth;
  }
  const handleClose = () => {
    props.cancelDialog();
  }

  // 下载对账单
	const downloadExcel = () => {
    if (!yearMonth.value) {
      ElMessage.error('请选择月份');
      return;
    }
    actionTracking('pc_action_pingan_loan_download', { 
			text: '下载对账单' ,
    });
    loading.value = true;
    const year = formatDate(yearMonth.value, 'yyyy-MM').split('-')[0];
    const month = formatDate(yearMonth.value, 'yyyy-MM').split('-')[1];
    const billCode = year+'-'+month
    downLoadLoan({billCode:billCode,merchantId:localStorage.getItem("merchantId")}).then((res) => {
      loading.value = false;
      if(res.data.type === "application/json") {
				const reader = new FileReader();
				reader.onload = function(){
					const { errorMsg } = JSON.parse(reader.result);
          console.log('111', JSON.parse(reader.result));
					//处理错误
					ElMessage.error(errorMsg);
				};
				reader.readAsText(res.data);
			} else {
				const contentDisposition = res.headers['content-disposition'];
        exportExcel(res.data, decodeURIComponent(contentDisposition.split('filename=')[1]));
			}
    }).catch(() => {
      loading.value = false
			ElMessage.error('所选月份暂无账单明细数据')
		})
	}
</script>
<style lang="scss" scoped>
</style>