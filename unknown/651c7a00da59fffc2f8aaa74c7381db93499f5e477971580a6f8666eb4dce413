<!-- 申请发票售后表单页 -->
<template>
  <div class="container">
    <h2 style="margin-bottom: 20px">发票售后相关</h2>

    <el-form :model="form" label-width="133px">
      <el-form-item label="发票售后类型:" style="position: relative;">
        <div class="required_icon" style="position: absolute; left: -111px;top: -1px;"></div>
        <div class="typTag" v-for="item in Object.keys(typeTags)" :key="item" @click="() => selectTypeTag(item)"
          :class="form.subType == item ? 'currentTag' : ''">
          {{ item }}
        </div>
      </el-form-item>
      <div v-if="form.subType == '错票'">
        <el-form-item class="deep_form_Item" label="请选择发票中有误的信息:" style="position: relative;">
          <div class="required_icon" style="position: absolute; left: -144px;top: -6px;"></div>
          <div class="errInfoTag" v-for="item in errInfoTags" :key="item.itemType"
            @click="() => selectErrInfoTag(item.itemType)"
            :class="form.errInfoTag.includes(item.itemType) ? 'currentTag' : ''">
            {{ item.itemName }}
          </div>
        </el-form-item>
      </div>
      <div v-if="form.subType == '申请专票'">
        <el-form-item label="请核实专票信息:" style="position: relative;">
          <div class="required_icon" style="position: absolute; left: -125px;top: -1px;"></div>
          <el-checkbox v-model="form.isElectronicInvoice" label="接受电子专票" size="large" />
        </el-form-item>
        <el-form-item>
          <div slot="label" class="required_icon">
            公司名称<span class="input_describe">（专票要求的公司名称）</span>
          </div>
          <div style="width: 100%">
            <el-input v-model="form.companyName" placeholder="请输入" style="width: 366px" maxlength="150" />
          </div>
        </el-form-item>
        <el-form-item>
          <div slot="label" class="required_icon">
            纳税人识别号<span class="input_describe">（营业执照上的统一社会信用代码）</span>
          </div>
          <div style="width: 100%">
            <el-input v-model="form.enterpriseRegistrationNo" placeholder="请输入" style="width: 366px" maxlength="50" />
          </div>
        </el-form-item>
        <el-form-item>
          <div slot="label" class="required_icon">
            地址<span class="input_describe">（专票要求的公司地址）</span>
          </div>
          <div style="width: 100%">
            <el-input v-model="form.registerAddress" placeholder="请输入" style="width: 366px" maxlength="300" />
          </div>
        </el-form-item>
        <el-form-item>
          <div slot="label" class="required_icon">
            电话<span class="input_describe">（专票要求的公司电话）</span>
          </div>
          <div style="width: 100%">
            <el-input v-model="form.phone" placeholder="请输入" style="width: 366px" maxlength="20" />
          </div>
        </el-form-item>
        <el-form-item>
          <div slot="label" class="required_icon">
            开户银行<span class="input_describe">（XX银行XX支行）</span>
          </div>
          <div style="width: 100%">
            <el-input v-model="form.bankName" placeholder="请输入" style="width: 366px" maxlength="100" />
          </div>
        </el-form-item>
        <el-form-item>
          <div slot="label" class="required_icon">
            银行账号<span class="input_describe">（专票要求的银行账号）</span>
          </div>
          <div style="width: 100%">
            <el-input v-model="form.acct" placeholder="请输入" style="width: 366px" maxlength="30" />
          </div>
        </el-form-item>
      </div>
      <el-form-item label="补充说明:" style="position: relative;">
        <div class="required_icon" style="position: absolute; left: -84px;top: -1px;"></div>
        <el-input v-model="form.desc" maxlength="50" type="textarea" placeholder="为了您和商家更好的处理售后问题，请填写补充说明"
          style="width: 366px" />
      </el-form-item>

      <div style="display: flex">
        <div style="width: 133px">
          <div style="float: right">
            <div style="font-size: 14px; color: #606266">上传凭证：</div>
            <div class="input_describe">（最多9张）</div>
          </div>
        </div>
        <div v-loading="uploadLoaind">
        <el-upload action v-model:file-list="imgFileList" list-type="picture-card" :limit="9" :http-request="toUploadImg"
        :before-upload="beforeUpload" accept="image/*"
          :on-remove="imageRemove" :on-preview="imagePreview" style="max-width: 847px" :on-exceed="onExceed">
          <el-icon>
            <Plus />
          </el-icon>
        </el-upload>
      </div>
      </div>

      <div style="display: flex; justify-content: center; width: 100%">
        <el-button v-if="isSubmit" type="primary" @click="onSubmit" :loading="isLoading"
          style="width: 142px; height: 46px; background-color: #00b377">提交审核</el-button>
        <el-button v-else @click="onSubmit" disabled
          style="width: 142px; height: 46px; background-color: #999999;color:#fff;">提交审核</el-button>
      </div>
    </el-form>
    <el-dialog v-model="invoiceInfoDialog" title="售后须知" width="30%"  @closed="closeInfoDialogFn()">
      <span v-html="invoiceInfo"></span>
      <template #footer>
        <div style=" text-align: center;">
          <el-button type="primary" @click="submitDialogFn()">
            已阅读并同意，继续申请售后
          </el-button>
        </div>
      </template>
    </el-dialog>
  <div class="demo-image__preview">
    <el-image-viewer hide-on-click-modal @close="closeImagePreview" v-if="dialogVisible" :url-list="[dialogImageUrl]"/>
  </div>
  </div>
</template>

<script setup>
import { reactive, ref, watch, onBeforeMount } from 'vue'
import { querySpecialInvoice,uploadvoucherImg, queryServiceInfo, queryInvoiceErrType, addInvoiceService } from '@/http_pc/api'
import { ElMessage } from 'element-plus'
import { useRoute,useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();
// 是否可提交
const isSubmit = ref(false);
const isLoading = ref(false);
// 弹层展示图片
let dialogVisible = ref(false)
let dialogImageUrl = ref('')
const pageLocation = ref('');
const uploadLoaind = ref(false)
// 售后须知
let invoiceInfoDialog = ref(false)
let invoiceInfo = ref('')
let isBack = ref(true)
// 已选图片
const imgFileList = ref([])
const imgPaths = ref([])
const errInfoTags = ref([])
const orderNo = ref('')
const orgId = ref('')
const accountId = ref()
// 表单数据
const form = reactive({
  desc: '',
  subType: '',
  errInfoTag: [],
  isElectronicInvoice: true,
  companyName: '',
  enterpriseRegistrationNo: '',
  registerAddress: '',
  phone: '',
  bankName: '',
  acct: '',
})
// 必选标签
const typeTags = {
  '无票': '1',
  '错票': '2',
  '申请专票': '3',
}
watch(form, (newName, oldName) => {
  isSubmit.value = checkFormValue()
})
onBeforeMount(() => {
  orderNo.value = route.query?.orderNo ?? '';
  orgId.value = route.query?.orgId ?? '';
  accountId.value = route.query?.accountId;
  // 获取错票tag列表
  queryInvoiceErrType().then((res) => {
        if (res.status === 'success' && res.data != null) {
      errInfoTags.value = res.data
    }
  })
  // 获取专票信息
  querySpecialInvoice({ orderNo: orderNo.value,merchantId:**********}).then((res) => {
    if (res.status === 'success' && res.data != null) {
      form.enterpriseRegistrationNo = res.data.enterpriseRegistrationNo
      form.phone = res.data.phone
      form.registerAddress = res.data.registerAddress
      form.bankName = res.data.bankName
      form.acct = res.data.acct
      form.companyName = res.data.companyName
    }
  })
  // 获取售后须知
  queryServiceInfo({ orderNo: orderNo.value, orgId: orgId.value }).then((res) => {
    if (res.status === 'success' && res.data != null) {
      invoiceInfo.value = res.data;
      invoiceInfoDialog.value = true;
    }
  })
})
// 关闭的回调
const closeInfoDialogFn = () => {
  if(isBack.value) {
    router.go(-1)
  }
}
// 确认须知
const submitDialogFn = () => {
  isBack.value = false
  invoiceInfoDialog.value = false
}
// tag事件
const selectTypeTag = (value) => {
  if (form.subType == value) {
    form.subType = ''
  } else {
    form.subType = value
  }
}
// 错票tag事件
const selectErrInfoTag = (value) => {
  if (form.errInfoTag.includes(value)) {
    form.errInfoTag = form.errInfoTag.filter((e) => e != value)
  } else {
    form.errInfoTag.push(value)
  }
}
// 预览图片
const imagePreview = (file) => {
  dialogImageUrl.value = file.url
  dialogVisible.value = true
  stop()
}
// 关闭图片凭证
const closeImagePreview =() => {
  dialogVisible.value = false
  move()
}
// 图片删除
const imageRemove = (file) => {
  imgPaths.value = imgPaths.value.filter((e) => e.uid != file.uid)
}
const beforeUpload = (file) => {
    const isJPG = file.type === 'image/jpg' || file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/bmp' || file.type === 'image/gif';
    if (!isJPG) {
      ElMessage.error('请上传正确格式的图片');
      return false;
    }
    const size = 5;
    const isLt2M = file.size / 1024 / 1024 < size;
    if (!isLt2M) {
      ElMessage.error(`图片大小不得超过${size}M`);
      return false;
    }
  };
// 图片上传
const toUploadImg = async (file) => {
  uploadLoaind.value = true
try {
  var res =  await uploadvoucherImg(file)
  if (res.status === 'success') {
      imgPaths.value.push({
        path: res.data.downloadPath[0],
        uid: file.file.uid
      })
}
uploadLoaind.value = false
} catch (error) {
  uploadLoaind.value = false
  imgFileList.value.pop()
  ElMessage.error('上传失败')
}
}
// 上传超出
const onExceed = (fie,lis) => {
  ElMessage.warning('最多上传9张图片')
}
// 表单提交
const onSubmit = () => {
  const baseUrl = import.meta.env.VITE_BASE_URL_PC;
  isLoading.value = true;
  let data = {
    orderNo: orderNo.value,
    accountId: accountId.value,
  }
  data['subType'] = typeTags[form.subType];
  data['remarks'] = form.desc
  if (imgPaths.value.length > 0) {
    var lis = [];
    imgPaths.value.forEach((e) => {
      lis.push(e.path);
    })
    data['evidences'] = JSON.stringify(lis)
  }
  if (form.subType == '错票') {
    data['incorrectInvoiceType'] = form.errInfoTag.join(',')
  } else if (form.subType == '申请专票') {
    data['isElectronicInvoice'] = form.isElectronicInvoice ? '1' : '0'
    data['companyName'] = form.companyName
    data['enterpriseRegistrationNo'] = form.enterpriseRegistrationNo
    data['registerAddress'] = form.registerAddress
    data['phone'] = form.phone
    data['bankName'] = form.bankName
    data['acct'] = form.acct
  }
    addInvoiceService(data).then((res) => {
    if (res.status === 'success') {
      ElMessage.success('提交成功');
      parent.window.open(`https:${baseUrl}merchant/center/order/index.htm?status=89`, "_self");
    }
    if (res.status === 'failure') {
      ElMessage.warning(res?.errorMsg ?? res?.msg);
    }
    isLoading.value = false;
  })
}
// 检查必填字段
function checkFormValue() {
  if (form.desc.length < 1) {
    return false;
  }
  switch (form.subType) {
    case '错票':
      if (form.errInfoTag.length < 1) {
        return false;
      }
      break;
    case '申请专票':
      if (form.companyName.length < 1 ||
        form.enterpriseRegistrationNo.length < 1 ||
        form.registerAddress.length < 1 ||
        form.phone.length < 1 ||
        form.bankName.length < 1 ||
        form.acct.length < 1
      ) {
        return false;
      }
      break;
  }
  return true;
}
 // 禁止滚动-在显示遮罩层的时候调用
 function stop() {
    let scrollTop = window.scrollY;//滚动的高度；
    pageLocation.value = scrollTop;
    document.body.style.position = 'fixed';
    document.body.style.top = '-' + scrollTop + 'px';
};
// 取消滑动限制-在关闭遮罩层的时候调用
function move() {
  document.body.style.position = 'static';
  window.scrollTo(0, pageLocation.value);
}
</script>
<style lang="scss" scoped>
.o {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;

}

.container {
  width: 980px;
  box-sizing: border-box;
  min-height: 606px;
  background: #ffffff;
  border-radius: 1px;
  padding: 18px;
  font-weight: 500;
  color: #333333;

  .tagBox {
    display: flex;
    font-size: 14px;
    margin-top: 20px;
    align-items: center;
  }

  .typTag {
    width: 112px;
    height: 32px;
    margin-right: 10px;
    background: #ffffff;
    border: 1px solid #dddddd;
    text-align: center;
    line-height: 32px;
    color: #666666;
  }

  .errInfoTag {
    height: 32px;
    margin-right: 10px;
    padding-left: 6px;
    padding-right: 6px;
    background: #ffffff;
    border: 1px solid #dddddd;
    text-align: center;
    line-height: 32px;
    color: #666666;
  }

  .input_describe {
    font-size: 12px;
    color: #bbbbbb;
  }

  ::v-deep .deep_form_Item .el-form-item__label {
    line-height: 20px !important;
  }
}

// 动态选中类
.currentTag {
  border: 1px solid #00b377 !important;
  color: #00b377 !important;
}

.required_icon::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}
::v-deep .el-image-viewer__mask {
        opacity: .7;
    }
    ::v-deep .el-image-viewer__img {
      width: 50%;
      height: 50%;
    }
</style>
