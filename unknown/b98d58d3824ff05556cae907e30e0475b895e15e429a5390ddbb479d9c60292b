<template>
    <div class="quality-notice-page">
        <van-cell :label="formatDate(item.updateTime, 'yyyy-MM-dd')" center :title="item.noticeTitle" v-for="item in noticeListData" :key="item.id" is-link @click="cellClick(item.id)" />
    </div>
</template>


<script setup>
import { onMounted, ref } from "vue";
import { getQualityNoticeList } from '@/http/qualityNotice';
import { useRouter } from 'vue-router';
import { formatDate } from '@/utils/index';
let noticeListData = ref([]);

const pageNum = ref(1);
const pageSize = ref(20);
const isLoading = ref(false);

const qualityNoticeList = () => {
    isLoading.value = true;
    getQualityNoticeList({ pageNum: pageNum.value, pageSize: pageSize.value }).then((res) => {
        isLoading.value = false;
        if (res.code == 1000) {
            noticeListData.value = noticeListData.value.concat(res.data.rows || []);
            if (pageNum.value === res.data.pageCount) {
                isEnd.value = true;
            }
        }
    }).catch(() => {
      isLoading.value = false;
    })
};

const router = useRouter();
const cellClick = (id) => {
    router.push(`/quelityNotice/detail?id=${id}`)
}
const isEnd = ref(false);
// 上拉加载
function pullUpLoading() {
    let scrollHeight = document.documentElement.scrollHeight;
    let scrollTop = window.scrollY;//滚动的高度；
    let clientHeight = document.documentElement.clientHeight;//滚动的高度；
    if (scrollHeight - scrollTop - clientHeight < 30) {
        if (!isEnd.value && !isLoading.value) {
            pageNum.value++;
            qualityNoticeList();
        }
    }
};
onMounted(function (){
    window.addEventListener('scroll', pullUpLoading);
    qualityNoticeList();
});

</script>


<style lang="scss">
.quality-notice-page {
    .van-cell {
        margin-bottom: 10px;
    }
}
</style>
<style lang="scss" scoped>
.quality-notice-page {
    width: 100%;
    height: 100vh;
    font-size: 15px;
    font-weight: bold;
}
</style>