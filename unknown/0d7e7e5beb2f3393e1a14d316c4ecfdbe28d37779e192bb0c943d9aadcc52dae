import { receiveCoupon } from '@/http_pc/api';
import { ElMessage } from 'element-plus';

const merchantId = (window.parent.document.getElementById('merchantId') || {}).value || "0";

export function toUse(pcUrl) {
  window.open(pcUrl);
}

export function getVoucher(info) {
  receiveCoupon({
    merchantId: merchantId,
    voucherTemplateId: info.templateId,
  }).then((res) => {
    if (res.status === "success") {
      info.isLq = 1;
      ElMessage.success('领取成功');
			// refresh();
    } else {error
      ElMessage.error('领取失败')
    }
  }).catch(err => {
    console.log(err, "请求失败了");
  });
};