<template>
  <div class="registerWrapper">
    <div class="shopWrapper">
      <div class="shopHeader" >
        <div class="title">提醒发货进度</div>
        <div class="rightHeader">
          <div class="rightFont" v-if="expireTimeRemain > 0">处理倒计时：<span class="active">{{formattedTime}}</span></div>
          <div class="rightFont" v-if="Lightning">处理倒计时：<span class="active">0小时0分</span></div>
          <div class="more" v-if="cexiao" @click="reminder" v-loading="loading">
            <img src="@/assets/images/reminderShipment/reminder.png" alt="" style="width: 11px;height: 10px;">
            <span style="cexiaomargin">撤销提醒</span>
          </div>
        </div>
      </div>
      <div class="shopListBox">
        <el-timeline >
          <el-timeline-item
            v-for="(activity, index) in shippingReminderHistory"
            :key="index"
            :type="activity.type"
            :color="activity.color"
            :size="activity.size"
            :hollow="activity.hollow"
            placement="top"
          >
          <template #dot>
            <div :class="index == 0 ? 'sizeSuccess' : 'sizeGrey'">
              <div class="small"></div>
            </div>
          </template>
           <template #default>
            <div class="timeline">
                <div class="historyCreateTime">{{activity.historyCreateTime}}</div>
                <div class="main">
                  <div class="timelineTitle">{{activity.eventStatusStr}}</div>
                  <div class="representations" v-if="activity.eventStatus == 30" >
                      <div class="line">
                        <div class="lineTitle">申述类型:</div>
                        <div class="prompt">{{activity.customFields.appealCategory}}</div>
                      </div>
                      <!-- <div class="line">
                        <div class="lineTitle">申诉说明:</div>
                        <div class="prompt">{{activity.customFields.appealDescription}}</div>
                      </div>
                      <div class="lineUrl">
                        <div class="lineTitle">申诉凭证:</div>
                        <div class="imgList">
                          <template v-for="(item,index) in activity.customFields.appealEvidence" :key="index">
                            <div @click="showList(activity.customFields.appealEvidence,index)">
                              <el-image
                                ref="img" 
                                style="width: 100px; height: 100px;z-index:'10000';cursor: pointer;"
                                :initial-index="index"
                                :zoom-rate="1.2"
                                :max-scale="7"
                                :min-scale="0.2"
                                :src="item"
                                hide-on-click-modal="true"
                                
                                fit="cover"
                              >
                              </el-image>
                            </div>
                          </template>
                        </div>
                      </div> -->
                  </div>
                  <div class="representations" v-if="activity.eventStatus == 31 || activity.eventStatus == 32" >
                      <div class="line" v-if="activity.customFields.auditDescription">
                        <div class="lineTitle">审核说明:</div>
                        <div class="prompt">{{activity.customFields.auditDescription}}</div>
                      </div>
                      <div class="line" v-if="activity.customFields.prompt">
                        <div class="lineTitle">最迟发货时间:</div>
                        <div class="prompt">{{activity.customFields.prompt}}</div>
                      </div>
                  </div>
                  <div v-else> 
                    <div class="prompt"> {{activity.customFields.prompt}}</div>
                  </div>
                </div>
            </div>
           </template>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
     <div class="demo-image__preview" ref="preview">
      <el-image-viewer  hide-on-click-modal @close="closeImagePreview" v-if="imgDialogVisible" :url-list="imgDialogImageUrl"/>
    </div>

  </div>
</template>
<script setup>
  import { onMounted, ref ,reactive,nextTick,onUnmounted,computed} from "vue";
  import { getMerchantList, delRelShop, getLoginAccountInfo, history,cancel} from '@/http_pc/api';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { actionTracking } from '@/config/eventTracking';
  import { useRoute ,useRouter} from 'vue-router';
  const route = useRoute();
  const expireTimeRemain = ref('');//时间倒计时
  const shippingReminderHistory = ref([
  ]);
  const router = useRouter();
  const pageNo = ref(1);
  const pageSize = ref(10);
  const total = ref(0);
  const shopList = ref([]);
  const showClerk = ref(false);
  const activeMerId = ref(0);
  const loginMerchantId = ref(0); // 当前登录的merchantId
  const loginRole = ref(null); // 当前登录的角色
  const loginPhone = ref(0);
  const imgDialogVisible = ref(false);
  const imgDialogImageUrl = ref([]);
  let show = ref(false);
  const pageLocation = ref('');
  let imagsList = ref([
  ]);
  let initIndex = ref(0);
  let intervalId;
  let orderNo = ref("");
  let img = ref(null)
  let preview = ref(null)
  let loading = ref(false);
  let cexiao = ref(false);
  let Lightning = ref(false);
  //时间戳转时间日期
  function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
  // 禁止滚动-在显示遮罩层的时候调用
  const   stop = ()=> {
      let scrollTop = window.scrollY;//滚动的高度；
      pageLocation.value = scrollTop;
      document.body.style.position = 'fixed';
      document.body.style.top = '-' + scrollTop + 'px';
      window.parent.document.body.style.overflow = 'hidden';
      setTimeout(()=>{
        const element = window.document.querySelector('.demo-image__preview');
        if(element){
          window.top.document.body.append(element)
        }
      },100)
      
  };
// 取消滑动限制-在关闭遮罩层的时候调用
const  move = ()=> {
    document.body.style.position = 'static';
    window.scrollTo(0, pageLocation.value);
  }
  /**关闭预览弹窗 */
  let closeImagePreview = () => {
    document.body.style.overflow = "auto";
    imgDialogVisible.value = false;
    const element = window.top.document.querySelector('.demo-image__preview');
    // 检查元素是否存在
    if (element) {
      // 删除元素
      element.remove();
    } else {
      console.error('Element not found');
    }
    console.log("jjj");
    move()
  };
  /**展示图片 */
  const showList = async(list,index)=>{
    console.log('qiyu',imagsList.value,index);
    setTimeout(()=>{
      console.log(window.parent.postMessage,'qq2q');
      console.log('qiyu',imagsList.value,index);
      window.parent.postMessage(
        {
          action: "openIMG",
          url:JSON.stringify(list),
          index:index
        },
        "*"
      ); // 通知父网页移除iframe
    },0)
    // img.value[index].showViewer = true
    // imgDialogVisible.value = true;
    // console.log(imagsList.value);
    // console.log(window.top.outerHeight,preview.value);
    
    // preview.value.style.outerHeight = window.top.outerHeight
    // imgDialogImageUrl.value = list;
    // await stop();
  }
  /**获取所有照片 */
  let getImageList = () => {
    console.log(img);
    imagsList.value = historyCreateTime.value[1].customFields.appealEvidence;
  // nextTick(() => {
  //   const contentDom = document.querySelector(".imgList");
  //   const imgg = contentDom.querySelectorAll("img");
  //   console.log("获取到的图片--------------", imgg);
  //   if(imgg.length > 0){
  //     imgg.forEach((item, idx) => {
  //       const src = item.getAttribute("src");
  //       item.classList.add("figger");
  //       imagsList.value.push(src);
  //       item.offsetParent.addEventListener("click", () => {
  //         initIndex.value = idx;
  //         console.log(initIndex.value);
  //         document.body.style.overflow = "hidden";
  //         if (show.value == false) show.value = true;
  //       });
  //     });
  //     console.log(imagsList.value,'qiyu');
  //   }
  // });
  
};
  const reminder = ()=>{
    loading.value = true;
    cancel({orderNo:orderNo.value}).then(res=>{//'YBM20241021214312100012'//orderNo.value
      if(res.status === 'success'){
        getData();
        ElMessage.success('撤销成功')
      }else if(res.status === 'failure'){
        if(intervalId){
          clearInterval(intervalId);
        }
        ElMessage.error(res.msg)
      }
      loading.value = false;
    })
  }
  const startCountdown = () => {
  intervalId = setInterval(() => {
    if (expireTimeRemain.value > 0) {
      expireTimeRemain.value--;
    } else {
      clearInterval(intervalId);
    }
  }, 1000);
};
const formattedTime = computed(() => {
    const hours = Math.floor(expireTimeRemain.value / 3600);
    const minutes = Math.floor((expireTimeRemain.value % 3600) / 60);
    const seconds = expireTimeRemain.value % 60;
    return `${String(hours).padStart(2, '0')}小时${String(minutes).padStart(2, '0')}分`;
});
  /**初始化数据 */
  const getData = async()=>{
    history({orderNo:orderNo.value}).then(res=>{////orderNo.value
      if(res.status === 'success'){
        shippingReminderHistory.value = res.data.list.shippingReminderHistory;
        expireTimeRemain.value = res.data.list.expireTimeRemain;
        if(expireTimeRemain.value == 0){
          Lightning.value = true;
        }else if(expireTimeRemain.value>0){
          startCountdown();
        }
        cexiao.value = res.data.list.cancelExpedite;
      }
    })
  }
  onMounted(() => {
    orderNo.value = route.query.orderNo ? route.query.orderNo : '';
    getData();
  })
  onUnmounted(()=>{
    if(intervalId){
      clearInterval(intervalId); 
    }
  })
</script>
<style lang="scss" scoped>
.registerWrapper {
  background: #fff;
  padding-right: 15px;
  .shopWrapper {
    background: #fff;
    width: 100%;
    // padding: 30px 0;
    .shopHeader {
      // padding-left: 30px;
      padding-top: 10px;
      width:100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      .tip {
        font-size: 12px;
        color: #555;
      }
      .title {
        font-size: 16px;
        color: #333333;
        font-weight: 600;
        padding-left: 10px;
        font-family: PingFangSC;
      }
      .rightHeader{
        display: flex;
        align-items: center;
        padding-right: 10px;
        .rightFont{
          color: #333333;
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 500;
          line-height: 0;
          letter-spacing: 0;
          text-align: left;
          .active{
            color: #ff2c2c;
            font-size: 12px;
            font-family: PingFangSC;
            font-weight: 500;
            line-height: 0;
            letter-spacing: 0;
            text-align: left;
          }
        }
        .more{
        cursor: pointer;
        margin-left: 10px;
        width: 83px;
        height: 26px;
        background: #F1F1F1;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        .cexiaomargin{
          width: 48px;
          height: 17px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #555555;
        }
      }
      }
    }
    .shopListBox {
      border-top: 1px solid #EEE;
      width: 100%;
      // margin-left: 30px;
      margin-top: 10px;
      height: 209px;
      padding: 15px 0 0 10px;
      overflow-y: scroll;
      overflow-x: scroll;
      ::v-deep .el-timeline-item__wrapper{
        top:0px !important;
        display: flex;
        justify-content: start;
        align-items: center;
        .el-timeline-item__content {
          flex-grow: 1;
        }
      }
    }
  }
}
.sizeSuccess{
  width: 11px;
  height: 11px;
  background: #00dc8233;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  .small{
  width: 5px;
  height: 5px;
  background: #00B377;
  border-radius: 50%;
  }
}
.sizeGrey{
  width: 11px;
  height: 11px;
  background: #F8F8F8;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  .small{
  width: 5px;
  height: 5px;
  background: #D7D7D7;
  border-radius: 50%;
  }
}
.timeline{
  display: flex;
  width: 100%;
  .historyCreateTime{
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 12px;
    color: #333333;
    letter-spacing: 0;
    white-space: nowrap;
  }
  .main{
    display: flex;
    flex-direction: column;
    align-items: baseline;
    flex-grow: 1;
    .timelineTitle{
      padding-left: 10px;
      font-family: PingFangSC-SNaNpxibold;
      font-weight: 600;
      font-size: 14px;
      color: #333333;
      letter-spacing: 0;
    }
    .representations{
      width: 100%;
      .line{
        display: flex;
        align-items: center;
        .lineTitle{
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #666666;
          letter-spacing: 0;
          padding-left: 10px;
          white-space: nowrap;
        } 
      }
      .lineUrl{
        display: flex;
        align-items: flex-start;
        width: 100%;
        .lineTitle{
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #666666;
          letter-spacing: 0;
          padding-left: 10px;
          white-space: nowrap;
        } 
        .imgList{
          display: grid;
          padding-left: 10px;
          padding-right: 10px;
          width: 100%;
          grid-template-columns: repeat(3, 100px);
          gap: 10px;
          justify-content: start; /* 从左对齐，不占用剩余空间 */
          .imgItem {
            position: relative;
            padding-bottom: 100%;
            img {
              position: absolute;
              width: 100%;
              height: 100%;
              object-fit: cover;
              top: 0;
              left: 0;
            }
          }
        } 
      }
      
    }
    .prompt{
      margin: 6px 0;
      padding-left: 10px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #666666;
      letter-spacing: 0;
    }
  }
}
::v-deep .el-image-viewer__img{

width: 50% !important;

height: auto !important;

}
.el-image__inner {
  z-index: 10001 !important;
}
</style>