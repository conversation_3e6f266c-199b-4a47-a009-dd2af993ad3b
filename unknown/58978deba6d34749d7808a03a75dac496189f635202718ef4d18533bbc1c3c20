<template>
  <div class="couponBox">
    <div class="couponTop">
      <div>您还有这些已领可用券</div>
      <div class="more" @click="toMore">
        更多
        <img src='../../../../assets/images/more.png' />
      </div>
    </div>
    <div class="couponItem">
      <div class="leftBox" />
      <div class="rightBox">
        <div class="desc">{{ couponInfo.minMoneyToEnableDesc }} {{ couponInfo.maxMoneyInVoucherDesc }}</div>
        <div class="couponContent">
          <div class="voucher-cost">
            <span v-if="couponInfo.discount" class="voucher-zhekou voucher-value">
              {{ couponInfo.discount }}
              <b class="discountB">折</b>
            </span>
            <span v-else class="voucher-value">
              <b class="moneyB">¥</b>
              {{ couponInfo.moneyInVoucher }}
            </span>
          </div>
          <!-- 操作按钮 -->
          <CouponBtn :couponInfo="couponInfo" :btnStyle="{height: `${56/75}rem`, lineHeight: `${56/75}rem`, fontSize: `${28/75}rem`, }" />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
  import CouponBtn from './couponBtn.vue';
  import { actionTracking } from '@/config/eventTracking';

  const props = defineProps({
    couponInfo: {
      default: {}
    },
  });

  actionTracking('h5_couponCentre_Exposure', { couponId: props.couponInfo.templateId })

  function toMore() {
    actionTracking('h5_couponCentre_more_Click', { title: '更多', action: 'ybmpage://couponmeber' });
    location.href = 'ybmpage://couponmeber';
  }

</script>

<style lang="scss" scoped>
  .couponBox {
    // background: url('//upload.ybm100.com/ybm/app/layout/cmsimages/2022-3/1da30e84580ffef1e4234bdc14bb3d24.png') no-repeat;
    background: url('//upload.ybm100.com/ybm/app/layout/cmsimages/2022-3/04b209c010632ccb9460973c02fd11c2.jpg') no-repeat;
    background-size: 100%;
    padding: rem(20);
    margin-top: rem(20);
    .couponTop {
      font-size: rem(30);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      div {
        font-weight: bold;
      }
      .more {
        font-size: rem(22);
        background: rgba(255, 255, 255, 0.3);
        border-radius: rem(21);
        padding: rem(6) rem(20);
        img {
          width: rem(9);
          height: rem(16);
        }
      }
    }
    .couponItem {
      margin-top: rem(20);
      background: url('../../../../assets/images/couponYeBg.png') no-repeat;
      background-size: 100%;
      height: rem(200);
      display: flex;
      .leftBox {
        width: rem(198);
      }
      .rightBox {
        width: rem(452);
        .desc {
          color: #BC4707;
          font-size: rem(28);
          font-weight: bold;
          margin-top: rem(40);
          margin-left: rem(50);
        }
        .couponContent {
          margin: rem(20) rem(30) 0 rem(50);
          display: flex;
          align-items: end;
          justify-content: space-between;
          .voucher-cost {
            height: rem(68);
            font-size: rem(64);
            .voucher-value {
              color: #f04134;
              font-weight: 700;
              b {
                font-size: rem(24);
              }
              .discountB {
                margin-left: rem(-16);
              }
              .moneyB {
                margin-right: rem(-16);
              }
              &.voucher-zhekou {
                margin-top: rem(-10);
                s {
                  font-size: rem(30);
                  font-style: normal;
                  text-decoration: none;
                  margin-left: rem(-20);
                }
              }
            }
          }
          .toUseBtn {
            border: rem(1) solid #FC0000;
            border-radius: rem(32);
            margin-top: rem(26);
            color: #FF0000;
            font-size: rem(28);
            font-weight: bold;
            padding: rem(12) rem(30);
          }
        }
      }
    }
  }
</style>