<template>
  <el-dialog
    v-model="showTipVis"
    title="温馨提示"
    width="400px"
    :lock-scroll="false"
    :before-close="handleClose"
  >
    <span>系统将根据您上次导入的报货清单，重新计算采购方案</span>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">不需要</el-button>
        <el-button type="primary" @click="handleOk">重新计算</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue';
const props = defineProps({
	showTipVis: {
		default: false
	},
  cancelDialog: {
    type: Function,
    default: () => {}
  },
  refresh: {
    type: Function,
    default: () => {}
  }
});
const showTipVis = computed({
	get: () => props.showTipVis,
	set: (value) => emit("cancelDialog", value),
})

const handleClose = () => {
  props.cancelDialog();
}
const handleOk = () => {
  props.refresh();
}
</script>
<style scoped lang="scss">
.dialog-footer button:first-child {
  margin-right: 10px;
}
</style>
