<template>
  <el-dialog
    v-model="showErrFile"
    title="导入文件反馈"
    width="400px"
    :lock-scroll="false"
    :before-close="handleClose"
  >
    <div>
			导入失败，原因见错误文件：<span class="linkText" @click="downloadLink">{{ errUrl[1] }}</span>
		</div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleClose">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';
import { downloadWithPath } from '@/http_pc/api';
import { exportExcel } from '../../../../utils/index';

const props = defineProps({
	showErrFile: {
		default: false
	},
	cancelDialog: {
		type: Function,
		default: () => {}
	},
  errorUrl: {
    default: ''
  }
});
const showErrFile = computed({
	get: () => props.showErrFile,
	set: (value) => emit("cancelDialog", value),
})
const errUrl = ref(props.errorUrl.split('?filename='));

const handleClose = () => {
	props.cancelDialog();
}
const downloadLink = () => {
	downloadWithPath({
    path: errUrl.value[0],
    fileName: errUrl.value[1],
  }).then((res) => {
    exportExcel(res.data, errUrl.value[1]);
  })
}
</script>
<style lang="scss" scoped>
.dialog-footer button:first-child {
  margin-right: 10px;
}
.linkText {
  color: #409eff;
  cursor: pointer;
}
</style>
