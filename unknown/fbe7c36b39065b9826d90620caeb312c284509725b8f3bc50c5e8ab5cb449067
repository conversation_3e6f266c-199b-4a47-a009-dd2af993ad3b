<!-- 此组件样式为普通商品流和拼团商品流混排-->
<template>
  <div>
    <ul class="templist">
      <li class="templist-item" v-for="(item, index) in datalist" :key="index" 
        @click="goToProductDetail(item, `${detailUrl}product_id=${item.id}&nsid=${item.nsid}&sdata=${item.sdata || ''}&sid=${buriedPoint.sid}&spid=${buriedPoint.spid}&sptype=${buriedPoint.sptype}`)" >
         <!-- dd :href="`${detailUrl}product_id=${item.id}&nsid=${item.nsid}&sdata=${item.sdata || ''}&sid=${buriedPoint.sid}&spid=${buriedPoint.spid}&sptype=${buriedPoint.sptype}`" -->
        <div
          class="images" 
        >
          <div v-if="item.status === 2" class="sold-out">售罄</div>
          <div>
            <img
              class="pic"
              v-lazy="imgUrl+'/ybm/product/min/'+item.imageUrl"
              :key="item.imageUrl"
              :alt="item.showName"
            />
            <img
              v-if="(item.markerUrl || '').length && !item.reducePrice"
              :src="imgUrl+item.markerUrl"
              class="activity-token"
              alt
            />
            <!-- 促销图标 -->
            <div
              class="promotionSkuPrice"
              v-if="(item.promotionSkuImageUrl)&&(item.isControl != 1 || (item.isPurchase == true && item.isControl == 1))"
            >
              <img :src="imgUrl+item.promotionSkuImageUrl" alt />
              <span>￥{{ item.promotionSkuPrice.toFixed(2) }}</span>
            </div>
            <div class="active-tags" v-if="item.activityTag && item.activityTag.tagNoteBackGroupUrl">
              <img :src="imgUrl+item.activityTag.tagNoteBackGroupUrl" alt />
              <div class="tejia806">
                <span class="discount"> {{item.activityTag.timeStr}} </span>
                <div class="labelBox">
                  <span
                    class="price806"
                    v-for="(item3,index) in item.activityTag.skuTagNotes"
                    :key="index"
                    :style="{color:'#'+item3.textColor}"
                  >{{item3.text}}</span>
                </div>
              </div>
            </div>
            <!-- 店铺活动标签 -->
            <div class="active-tags shop-active-tags" v-if="item.activityTag && item.activityTag.tagNoteBackGroupUrl && +item.activityTag.sourceType === 2">
              <img :src="item.activityTag.tagNoteBackGroupUrl" alt />
              <div v-if="item.activityTag.customTopNote" class="customTopNote">{{ item.activityTag.customTopNote }}</div>
              <div>
                <span class="shop-discount"> {{item.activityTag.timeStr}} </span>
                <span
                  class="shop-text"
                  v-for="(item3,index) in item.activityTag.skuTagNotes"
                  :key="index"
                >{{item3.text}}</span>
              </div>
              <div v-if="item.activityTag.customBottomNote" class="customBottomNote">{{ item.activityTag.customBottomNote }}</div>
            </div>
          </div>
        </div>
        <div v-if="item.actPt && (!item.controlTitle || item.controlType == 5)">
          <GroupProduct :groupData="item" :licenseStatus="licenseStatus" 
          :buriedPoint="buriedPoint"
          :categoryName="categoryName" />
        </div>
        <div v-else>
          <CommonProduct :productItem="item" :licenseStatus="licenseStatus" 
          :buriedPoint="buriedPoint" 
          :categoryName="categoryName" />
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { computed, ref, watch, getCurrentInstance, onMounted } from 'vue'
import { useStore } from "vuex";
import { handleDiscount } from '@/http/api';
import CommonProduct from './commonProduct.vue';
import GroupProduct from './groupProduct.vue';
import { actionTracking } from "@/config/eventTracking";


  const props = defineProps({
    productList: {
      default: []
    },
    licenseStatus: {
      default: '',
    },
    categoryName :{
      default : '',
    },
    // 埋点数据
    buriedPoint: {
      default: {
        spid: '',
        sid: '',
        sptype: ''
      },
    }
  });
  const { proxy } = getCurrentInstance();
  const store = useStore();
  const imgUrl = import.meta.env.VITE_IMG;
  const datalist = ref(props.productList);
  const detailUrl = computed(() => store.state.app.detailBaseUrl);

  // 添加折后价
  function addDiscount(data) {
    if (data && data.length > 0) {
      const idList = data.map(item => { return item.id });
      handleDiscount({ skuIds: idList.join(',') })
        .then((res) => {
          if (res.status == "success") {
            const priceArr = res.data;
            priceArr.map((item, index) => {
              data.map((item2,index2) => {
                if (item.skuId == item2.id) {
					console.log(item2);
					console.log(item);
                  const zheHouPrice = Number(item.price.split('￥')[1]);
				  if (!isNaN(zheHouPrice) && zheHouPrice < item2.fob) {
					item2.zheHouPrice = item.price;
				  }
                }
              })
            })
          }
        })
        .catch((err) => {  
          console.log(err, "请求失败了");
        });
    }
    datalist.value = data;
  }

  function goToProductDetail(item, url){
    actionTracking("page_Chart_productList_Click", {
      categoryName : props.categoryName,
      standardProduct_id : item.masterStandardProductId,
      sku_id : item.id,
      ...props.buriedPoint
    });
    window.location.href = url;
  }

  onMounted(() => {
    addDiscount(props.productList);
    actionTracking("h5_page_ListPage_Exposure", { list: props.productList });
 
  })
  
  watch(
    () => props.productList,
    (val) => {
      if (val) {
        addDiscount(props.productList);
      }
    }
  );
</script>

<style lang="scss" scoped>
.templist {
  font-family: PingFangSC;
  .templist-item {
    position: relative;
    padding: rem(30) 0 rem(10) rem(20);
    border-bottom: 1px solid rgba(245, 245, 245, 1);
    overflow: hidden;
    display: flex;
    background: #fff;
    border-radius: rem(15);
    margin-bottom: rem(15);
    .images {
      position: relative;
      width: rem(200);
      height: rem(200);
      display: block;
      .sold-out {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 1.1rem;
        height: 1.1rem;
        background-color: rgba(0, 0, 0, 0.4);
        text-align: center;
        font-size: 14px;
        color: #fff;
        border-radius: 50%;
        line-height: 1.1rem;
        z-index: 100;
      }
     
      .pic {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: rem(200);
        max-height: rem(200);
      }
      .activity-token {
        position: absolute;
        width: rem(200);
        height: rem(200);
        left: rem(15);
        top: rem(15);
        z-index: 3;
      }
      .mark-text {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        z-index: 5;
        img {
          width: 100%;
          height: 100%;
        }
        h4 {
          position: absolute;
          left: 0;
          bottom: 0;
          font-size: rem(20);
          color: #fff;
          width: 100%;
          text-align: center;
        }
      }
      .promotionSkuPrice {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 5;
        span {
          position: absolute;
          right: 0;
          bottom: 0;
          font-size: 14px;
          color: #ff6201;
          display: flex;
          display: -webkit-flex;
          justify-content: center;
          -webkit-justify-content: center;
          align-items: center;
          -webkit-align-items: center;
          width: rem(140);
          height: rem(40);
          text-shadow: 1px 1px 1px #ffd9b4;
          font-weight: 600;
        }
      }
      .active-tags {
        position: absolute;
        left: rem(20);
        bottom: rem(10);
        img {
          width: rem(200);
          height: rem(200);
        }
        .tejia806 {
          position: absolute;
          bottom: rem(25.7);
          font-size: rem(20);
          text-align: center;
          width: rem(200);
          height: rem(24);
          .discount {
            font-size: rem(24);
            position: absolute;
            height: rem(24);
            line-height: rem(24);
            top: rem(-14);
            text-align: center;
            color: #fff;
            left: 50%;
            white-space: nowrap;
            transform: translateX(-50%) scale(0.3);
          }
          .labelBox {
            width: 160%;
            transform: scale(0.6);
            -webkit-transform-origin-x: 0%;
            padding-left: rem(7);
            span {
              color: #fff;
              display: inline-block;
              font-size: rem(22);
              overflow: hidden;
              height: rem(34);
            }
          }
        }
      }
      .shop-active-tags {
        width: rem(200);
        height: rem(200);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        img {
          width: 100%;
          height: 100%;
        }
        .customTopNote {
          width: 200%;
          position: absolute;
          color: #fff;
          top: rem(5);
          left: 0;
          font-size: rem(26);
          line-height: rem(40);
          height: rem(40);
          text-align: center;
          transform: scale(0.5);
          display: inline-block;
          transform-origin: 0 0;
        }
        .customBottomNote {
          position: absolute;
          color: #fff;
          bottom: rem(-12);
          text-align: center;
          font-size: rem(20);
          line-height: rem(28);
          height: rem(28);
          transform: scale(0.5);
          width: 200%;
          display: inline-block;
          transform-origin: 0 0;
        }
        .shop-discount {
          width: 100%;
          position: absolute;
          color: #fff;
          font-size: rem(26);
          line-height: rem(32);
          height: rem(32);
          bottom: rem(29);
          left: rem(3);
          transform: scale(0.5);
          width: 200%;
          display: inline-block;
          transform-origin: 0 0;
        }
        .shop-text {
          width: 100%;
          position: absolute;
          color: #E23B3B;
          font-size: rem(32);
          line-height: rem(44);
          height: rem(44);
          bottom: rem(-3);
          text-align: center;
          transform: scale(0.5);
          width: 200%;
          display: inline-block;
          transform-origin: 0 0;
        }
      }
    }
  }
}
</style>
