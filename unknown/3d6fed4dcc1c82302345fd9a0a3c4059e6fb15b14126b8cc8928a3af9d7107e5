import vue from '@vitejs/plugin-vue'
import legacy from '@vitejs/plugin-legacy'
import { resolve } from 'path'

const pathResolve = (dir) => {
  return resolve(__dirname, ".", dir)
}

const alias = {
  '@': pathResolve("src")
}

// https://vitejs.dev/config/
export default ({ command }) => {
  const prodMock = true;
  return {
    base: '/newstatic/',
    resolve: {
      alias
    },
    // 打包配置
    build: {
      target: 'es2015',
      outDir: 'dist', //指定输出路径
      // minify: 'terser', // 混淆器，terser构建后文件体积更小
      // terserOptions: {
      //   compress: {
      //     drop_console: process.env.NODE_ENV === 'production'
      //   }
      // },
    },
    plugins: [
      vue(),
      legacy({
        targets: ['defaults', 'ie >= 11', 'chrome 48'], //需要兼容的目标列表，可以设置多个
        additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
      })
    ],
    server: {
      port: 3003,
      host: '0.0.0.0',
      open: true,
      proxy: { // 代理配置
        '/api': {
          // target: 'https://new-app.test.ybm100.com/', // app代理接口域名
          target: 'https://new-www.test.ybm100.com/', // pc代理接口域名
          // target: 'http://localhost:8016/', // pc本地
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
          configure: proxy => {
						proxy.on("proxyReq", function (proxyReq, req) {
							proxyReq.setHeader("Origin", "https://new-www.test.ybm100.com/")
							proxyReq.setHeader(
								"Cookie",
								"crosSdkDT2019DeviceId=-lggmcd--7dndw7-60trpak6jvaqqxh-7t2xnwggk; isDeviceUpload=1; uid=CgoUFGUBcl5DvlbxGHX9Ag==; Hm_lvt_9f4bfe0c69e174e92f282183ee72bed2=1694593632; web_8b5e1b0f250a436e8c6af9871354bfba=%7B%22sid%22%3A%201694601555585%2C%22updated%22%3A%201694601555586%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22channelCode%5C%22%3A%20%5C%22B2B%5C%22%7D%22%2C%22referrerDomain%22%3A%20%22www.ybm100.com%22%7D; web_did=%7B%22did%22%3A%20%222764c365-a9b2-4fd4-a1af-38a876e10311%22%7D; xyy_principal=142316&Mzk2Mzk4ODY3MmE3YjExYzkxODJmNmEwNjVhMzM3MjI2MjViZDZlYQ&142316; xyy_last_login_time=1694613292410; xyy=MTQyMzE2JjEzMTMwMTk4NTMx; Hm_lpvt_9f4bfe0c69e174e92f282183ee72bed2=1694678094; web_dc504156a0b54a04bd19c57da5422a32=%7B%22sid%22%3A%201694678094045%2C%22updated%22%3A%201694678094059%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22channelCode%5C%22%3A%20%5C%22B2B%5C%22%7D%22%2C%22referrerDomain%22%3A%20%22new-www.test.ybm100.com%22%7D; web_info=%7B%22sid%22%3A%201694678094045%2C%22updated%22%3A%201694678094059%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22channelCode%5C%22%3A%20%5C%22B2B%5C%22%7D%22%2C%22referrerDomain%22%3A%20%22new-www.test.ybm100.com%22%2C%22cuid%22%3A%20%22142316%22%7D; JSESSIONID=398CF4190DCDEB7C04D0417C6D0AA136"
							)
						})
					}
        }
      },
    },
    css: {
      // css预处理器
      preprocessorOptions: {
        scss: {
          charset: false,
          additionalData: `@use "./src/assets/scss/common.scss" as *;`,
          // additionalData: '@import "./src/assets/scss/common.scss";', // 添加公共样式
        },
      },
    },
  }
}
