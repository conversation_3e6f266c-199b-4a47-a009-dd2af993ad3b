<template>
  <div class="couponBox">
    <div class="box" v-for="couponInfo in couponList" :key="couponInfo.templateId">
      <img class="logo" :src="`${imgUrl}/${couponInfo.shopLogoUrl}`" alt="">
      <div class="shopName">{{ couponInfo.shopName }}</div>
      <div class="discount">
        <template v-if="couponInfo.discount">
          <span class="big">{{ couponInfo.discount ? String(couponInfo.discount).split('.')[0] : '' }}</span><span>{{ couponInfo.discount ? '.' + ((String(couponInfo.discount).split('.') || [])[1] || 0) : '' }}</span>折
        </template>
        <template v-else>
          ￥<span class="big">{{ couponInfo.moneyInVoucher }}</span>
        </template>
      </div>
      <div class="desc">{{ couponInfo.minMoneyToEnableDesc }}</div>
      <template v-if="couponInfo.isLq === 0">
        <el-button round class="lq" @click="toGetVoucher(couponInfo)">立即领取</el-button>
      </template>
      <template v-if="couponInfo.isLq === 1">
        <el-button round @click="toUseCoupon(couponInfo)">去使用</el-button>
      </template>
    </div>
    <div v-if="couponList.length" class="bottom-tip">
      <span @click="handleNextPage">{{ bottomTip }}</span>
    </div>
  </div>
</template>

<script setup>
  import { toUse, getVoucher } from './btn';

  const props = defineProps({
    couponList: {
      default: []
    },
    bottomTip: {
      default: ''
    },
    getInfo: {
      type: Function,
      defult: () => {}
    }
  });
  const imgUrl = import.meta.env.VITE_IMG;
  function toUseCoupon(couponInfo) {
    toUse(couponInfo.pcUrl);
  }
  function toGetVoucher(info) {
    getVoucher(info);
  };

  function handleNextPage() {
    if (props.bottomTip === '查看更多') {
      props.getInfo();
    }
  }

</script>

<style scoped lang="scss">
.couponBox {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin-top: rem(10);
  width: 100%;
}
.box{
  width: 262px;
  height: 310px;
  background: url("../../../../assets/images/couponCenter_pc/jxdp.png");
  background-size: 100% 100%;
  margin: 10px;
  text-align: center;
  box-sizing: border-box;
  padding:16px;
  .logo{
    width: 46px;
    height: 46px;
    border-radius: 100%;
  }
  .shopName{
    font-weight: 600;
    font-size: 18px;
    color: #2C2C2C;
    margin-top: 10px;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1; /* 这里是超出几行省略 */
    overflow: hidden;
  }
  .discount{
    margin-top: 40px;
    font-weight: 500;
    font-size: 24px;
    color: #FE2021;
    .big{
      font-size: 50px;
      color: #FF0000;
    }
  }
  .desc{
    font-weight: 500;
    font-size: 20px;
    color: #BC4707;
  }
  .el-button{
    width: 136px;
    height: 46px;
    padding: 0;
    line-height: 40px;
    margin-top: 30px;
    border: 1.4px solid #FC0000;
    font-weight: 500;
    font-size: 20px;
    color: #FF0000;
    border-radius: 23px;
  }
  .el-button.lq{
    color: #FFFFFF;
    background-image: linear-gradient(180deg, #FF6B10 0%, #FD1801 100%);
  }
}
</style>
