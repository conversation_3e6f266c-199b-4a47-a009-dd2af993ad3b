<template>
  <div class="orderWrapper">
    <div class="headerTop">
      <div class="headerBox">
        <span class="leftHeader">
          <img class="hraderImg" src="../../../assets/images/school/logo_login.png" alt="">
          <span class="headerText">收银台</span>
        </span>
        <img class="progressImg" src="../../../assets/images/school/buzhou3.png" alt="">
      </div>
    </div>
    <div class="contentBox" v-if="!showPayPage">
      <div class="topTip">
        <el-icon color="#108ee9"><WarningFilled /></el-icon>
        您的订单已生成，请尽快完成付款！
      </div>
      <div class="orderInfo">
        <div class="left">
          <div>订单编号：YBM20220914110301122156</div>
          <div>发票信息：电子普通发票</div>
          <div>实付金额：<span style="color: red;">￥199.00</span></div>
        </div>
        <div class="right">
          <div>下单时间：{{ formatDate(Number(orderTime), 'yyyy-MM-dd hh:mm:ss') }}</div>
        </div> 
      </div>
      <div class="payType">
        <div>支付方式：</div>
        <div class="payIcon">
          <span class="zfbIcon" :class="payType === 1 ? 'activeIcon' : 'commonIcon'" @click="onPayType(1)">
            <img src="../../../assets/images/school/zfb-new.png" />
          </span>
          <span class="wxIcon" :class="payType === 2 ? 'activeIcon' : 'commonIcon'" @click="onPayType(2)">
            <img src="../../../assets/images/school/weixin-new.png" />
          </span>
        </div>
      </div>
      <div class="payInfo">
        <div class="payBtn" @click="toPay">立即支付</div>
        <span class="payTime">
          剩余支付时间：
          <LimitTime :endTime="Number(endTime)" timeColor="red" />
        </span>
      </div>
    </div>
    <div v-else>
      <PayPage :pay-type="payType"></PayPage>
    </div>
    
  </div>
</template>
<script setup>
  import { onMounted, ref } from "vue";
  import { formatDate, getUrlParam } from '@/utils/index';
  import { useRoute } from 'vue-router'
  import PayPage from './payPage.vue';
  import LimitTime from '../../../components/limit_time.vue';

  const route = useRoute();
  const showPayPage = ref(false);
  const payType = ref(1);
  const orderTime = getUrlParam('orderTime');
  console.log('拿到的时间', getUrlParam('orderTime'));
  const endTime = Number(orderTime) + 3600000;
  console.log('结束时间', endTime);
  const onPayType = (val) => {
    payType.value = val;
  }
  const toPay = () => {
    showPayPage.value = true;
  }
</script>
<style lang="scss" scoped>
.orderWrapper {
  width: 100%;
  background: #F8F8F8;
	padding-bottom: 80px;
}
.headerTop {
	width: 100%;
	height: auto;
  background: #FFFFFF;
}
.headerBox {
	width: 1200px;
	margin: 0 auto;
	display: flex;
	justify-content: space-between;
  align-items: center;
  .leftHeader {
    display: flex;
    align-items: center;
  }
	.hraderImg {
		width: 160px;
    height: 60px;
	}
  .headerText {
    font-size: 21px;
    color: #333333;
    margin: 10px 0 0 10px;
    padding-left: 10px;
    border-left: 1px solid #ededed;
  }
	.progressImg {
		width: 752px;
	}
}
.contentBox {
  width: 1200px;
  margin: 0 auto;
  margin-top: 30px;
  background: #FFFFFF;
  .topTip {
    font-size: 16px;
    text-align: center;
    color: #666666;
    font-weight: bold;
    background: #e6f3fd;
    border: 2px solid #add8f7;
    height: 55px;
    line-height: 55px;
  }
}
.orderInfo {
  display: flex;
  padding: 26px;
  font-size: 14px;
  border-bottom: 1px solid #e0e0e0;
  div {
    width: 50%;
  }
  .left div, .right div {
    margin-top: 20px;
  }
}
.payType {
  padding: 26px;
  font-size: 14px;
}
.payIcon {
  margin-top: 20px;
  .zfbIcon, .wxIcon {
    display: inline-block;
    margin-right: 35px;
    // width: 200px;
    padding: 13px 23px;
    text-align: center;
    // height: 80px;
    // line-height: 80px;
    img {
      width: 154px;
      height: auto;
    }
  }
  .commonIcon {
    border: 2px solid #e0e0e0;
  }
  .activeIcon {
    border: 2px solid #00dc7d !important;
    background: url("../../../assets/images/school/zhifugou.png") no-repeat 167px 45px;
  }
}
.payInfo {
  padding: 26px;
  .payBtn {
    width: 200px;
    height: 46px;
    line-height: 46px;
    display: inline-block;
    text-align: center;
    background: #f39800;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    margin-right: 37px;
    border-radius: 2px;
  }
  .payTime {
    color: #e73736;
    font-size: 16px;
    font-weight: bold;
  }
}
</style>