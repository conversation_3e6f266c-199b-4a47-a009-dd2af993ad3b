<template>
  <div class="couponItem">
    <!-- 券左半部分-券图片 -->
    <div class="couponLeftImg" v-if="couponInfo.describeUrl">
      <img class="couponImg" :src="`${imgUrl}/ybm/product/min/${couponInfo.describeUrl}`" />
    </div>
    <!-- 券左半部分-券信息 -->
    <div class="couponLeft" v-else>
      <!-- 券标签+券标题 -->
      <div class="titleBox">
        <span class="titleIcon" :class="'voucherType' + couponInfo.voucherType">{{ couponInfo.voucherTypeDesc }}</span>
        <span class="titleText textellipsis">{{ couponInfo.voucherTitle }}</span>
      </div>
      <!-- 券图片/使用说明 -->
      <div class="voucher-show">
        <div v-if="couponInfo.skuRelationType == 2 && (couponInfo.voucherSkuImages || []).length">
          <div class="voucher-img"
            v-for="(itemImg, ind) in couponInfo.voucherSkuImages.slice(0, 3)"
            :key="ind"
          >
            <a :href="`${detailUrl}product_id=${itemImg.skuId}`">
              <img :src="imgUrl + '/ybm/product/min/' + itemImg.imageUrl" alt />
            </a>
          </div>
        </div>
        <ul v-else-if="couponInfo.voucherInstructions" class="voucher-Instructions">
          <li
            v-for="(itemInstructions,
            ind) in couponInfo.voucherInstructions.split(';')"
            :key="ind"
            class="textellipsis"
          >
            <i>•</i>
            {{ itemInstructions }}
          </li>
        </ul>
      </div>
      <!-- 店铺logo+店铺名 -->
      <div class="shopBox" v-if="couponInfo.skuRelationType != 2">
        <img v-if="couponInfo.shopLogoUrl" :src="`${imgUrl}/${couponInfo.shopLogoUrl}`" alt="">
        <div v-if="couponInfo.shopName" class="shopName">{{ couponInfo.shopName }}</div>
      </div>
    </div>
    <!-- 券右半部分 -->
    <div class="couponRight">
      <!-- 价钱 -->
      <div class="voucher-cost">
        <span v-if="couponInfo.discount" class="voucher-zhekou voucher-value" :class="{'voucher-value-zp': couponInfo.voucherType == 9}">
          {{ numFilterInt(couponInfo.discount) }}
          <s>.{{ numFilterFloat(couponInfo.discount) }}</s>
          <b>折</b>
        </span>
        <span v-else class="voucher-value" :class="{'voucher-value-zp': couponInfo.voucherType == 9}">
          <b class="moneyB">¥</b>
          {{ couponInfo.moneyInVoucher }}
        </span>
      </div>
      <!-- 满xx可用 -->
      <div class="manJian" :class="{'manJian-zp': couponInfo.voucherType == 9}">
        <p>{{ couponInfo.minMoneyToEnableDesc }}</p>
        <p v-if="couponInfo.maxMoneyInVoucherDesc">{{ couponInfo.maxMoneyInVoucherDesc }}</p>
      </div>
      <!-- 操作按钮 -->
      <CouponBtn :couponInfo="couponInfo" />
    </div>
  </div>
</template>

<script setup>
  import { computed } from "vue";
  import { useStore } from "vuex";
  import { numFilterInt, numFilterFloat } from '@/utils';
  import CouponBtn from './couponBtn.vue';

  const props = defineProps({
    couponInfo: {
      default: {}
    },
  });
  const store = useStore();
  const imgUrl = import.meta.env.VITE_IMG;
  const detailUrl = computed(() => store.state.app.detailBaseUrl);

</script>


<style lang="scss" scoped>
  .couponItem {
    display: flex;
    height: rem(240);
    background: url('../../../../assets/images/couponComBg.png') no-repeat;
    background-size: 100%;
    margin-bottom: rem(20);
    .couponLeft {
      width: rem(470);
      padding: rem(20);
      .titleBox {
        display: flex;
        align-items: center;
        .titleIcon {
          height: rem(36);
          line-height: rem(37);
          font-size: rem(26);
          text-align: center;
          border-radius: rem(18);
          color: #fff;
          margin-right: rem(12);
          padding: 0 rem(10);
        }
        .voucherType1 {
          background: linear-gradient(90deg, rgba(250, 178, 97, 1) 0%, rgba(247, 107, 28, 1) 100%);
        }
        .voucherType2 {
          background: linear-gradient(129deg, rgba(255, 206, 178, 1) 0%, rgba(215, 165, 138, 1) 100%);
        }
        .voucherType5 {
          background: linear-gradient(90deg, rgba(242, 121, 38, 1) 0%, rgba(236, 46, 36, 1) 100%);
        }
        .voucherType6 {
          background: linear-gradient(103deg, rgba(255, 107, 113, 1) 0%, rgba(224, 46, 36, 1) 100%);
        }
        .voucherType7 {
          background: linear-gradient(129deg, rgba(255, 206, 178, 1) 0%, rgba(215, 165, 138, 1) 100%);
        }
        .voucherType8 {
          background: linear-gradient(90deg, #ff5d5d 7%, #f61d1d 92%);
        }
        .voucherType9 {
          background: #FF155D;
        }
        .titleText {
          display: inline-block;
          max-width: rem(318);
          color: #292933;
          font-size: rem(28);
          font-weight: blod;
        }
      }
      .voucher-show {
        height: rem(116);
        .voucher-img {
          margin-top: rem(30);
          font-size: 0;
          display: inline-block;
          margin-right: rem(45);
          a {
            display: block;
          }
          img {
            display: inline-block;
            width: rem(120);
            height: rem(120);
          }
          &:nth-child(3) {
            margin-right: rem(0);
          }
        }
        .voucher-Instructions {
          color: #676773;
          font-size: rem(24);
          margin-top: rem(6);
          li {
            font-weight: 400;
            line-height: rem(30);
            i {
              font-size: rem(30);
              margin-right: rem(6);
            }
          }
        }
      }
      .shopBox {
        display: flex;
        align-items: center;
        border-top: 1px solid #F5F5F5;
        padding-top: rem(6);
        margin-right: rem(20);
        img {
          width: rem(44);
          height: rem(44);
          margin-right: rem(10);
          border-radius: 50%;
        }
        .shopName {
          color: #676773;
          font-size: rem(24);
        }
      }
      
    }
    .couponLeftImg {
      width: rem(470);
      padding: rem(10);
      .couponImg {
        width: 100%;
        height: 100%;
      }
    }
    .couponRight {
      width: rem(200);
      text-align: center;
      .voucher-cost {
        height: rem(68);
        font-size: rem(64);
        margin-top: rem(20);
        .voucher-value {
          color: #f04134;
          font-weight: 700;
          b {
            font-size: rem(30);
          }
          .moneyB {
            margin-right: rem(-16);
          }
          &.voucher-zhekou {
            margin-top: rem(-10);
            s {
              font-size: rem(30);
              font-style: normal;
              text-decoration: none;
              margin-left: rem(-20);
            }
          }
        }
      }
      .manJian {
        font-size: rem(24);
        font-weight: 400;
        color: rgba(255, 66, 68, 1);
        width: 95%;
        height: rem(76);
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        text-align: center;
        margin-bottom: rem(8);
      }
      .voucher-value-zp, .manJian-zp {
        color: #FF155D !important;
      }
    }
  }
</style>