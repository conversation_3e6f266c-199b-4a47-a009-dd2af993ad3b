<template>
<div>
    <el-dialog
        title="购物金使用说明"
        width="55%"
        v-model="showDialog"
        :before-close="close">
        <div class="content">
            <div>
                <span class="title">一、药帮忙“购物金”是什么</span>
                <span class="font">药帮忙“购物金”指用户在药帮忙平台的资金账户，账户内的金额类型为用户充值或平台返还，可在您购买符合本规则范围内的商品时抵扣。</span>               
            </div>
            <div class="line">
                <span class="title">二、购物金使用规则</span>
                <span class="font">1、您在药帮忙平台通过线下打款或在线充值的方式，向购物金账户充值；  </span>
                <span class="font">2、或您符合其他赠金发放要求（包括但不限于“商家小额赔付”、“平台介入赔偿”、“催发货逾期处理”等），平台将按照具体规则向您发放相应的赠予金额；</span>
            </div>
            <div class="line">
                <span class="title">三、购物金的有效期</span>
                <span class="font">购物金金额无有效期的限制，您可通过“药帮忙 APP-我的-购物金”，查看每一笔收入或支出项的具体明细。</span>
            </div>
            <div class="line">
                <span class="title">四、当订单发生退款</span>
                <span class="font">1、购物金使用范围：仅限在符合本规则的商品内使用；</span>
            </div>
            <div class="line">
                <span class="title">五、关于“购物金”使用与服务</span>
                <span class="font">1、药帮忙“购物金”内金额可在您购买药帮忙平台的商品时（自营&第三方商业公司均可）进行抵扣使用，具体以商品详情页说明、订单结算页信息及相关活动页面规则为准； </span>
                <span class="font">2、您使用“购物金”金额进行支付时，其他优惠券、拼团、满减、满赠、特价、秒杀、红包等促销活动均参与叠加 使用，购物金作为您的资产，在以上各类型活动优惠减价后根据“剩余需支付金额”进行抵扣，具体以购物车结算信息为准；</span>
                <span class="font">3、若您在充值购物金时，享受了“充值返红包或优惠券”活动，红包或优惠券会在购物金充值成功后的1个工作日内返还到您的账户。返还红包或优惠券的使用规则、有效期，以红包或优惠券页面规则为准； </span>
                <span class="font">4、若您在充值购物金时，享受了“充值返红包或优惠券”活动，充值金额暂不支持退还；</span>
            </div>
            <div class="line">
                <span class="title">六、关于开票</span>
                <span class="font">您在使用购物金进行抵扣支付时，药帮忙平台的商家会按照订单实付金额（包含购物金抵扣部分）向您的下单主体开具发票。</span>
                <span class="font">药帮忙可根据实际情况，在法律允许的范围内，对本规则进行变动或调整，相关变动或调整将公布在购物金规则页面上。</span>
            </div>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="close">确认</el-button>
            </div>
        </template>
    </el-dialog>
</div>
</template>

<script setup>
import {ref,watch} from 'vue'
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  }
})
const emit = defineEmits(['update:modelValue'])
const showDialog = ref(props.modelValue)
watch(()=>props.modelValue,()=>{
    showDialog.value = props.modelValue
})
const close = () => {
    emit('update:modelValue',false)
}
</script>

<style lang='scss' scoped>
.content {
    .line {
        margin-top: 10px;
    }
    .title {
        display: block;
        margin-bottom: 5px;
    }
    .font {
        display: block;
    }
}
::v-deep .el-dialog__header {
    text-align: center;
}
::v-deep .el-dialog__body {
  max-height: 450px;
  overflow: auto;
  padding: 5px 15px;
  border: solid 1px #e4eaf1;
}
</style>