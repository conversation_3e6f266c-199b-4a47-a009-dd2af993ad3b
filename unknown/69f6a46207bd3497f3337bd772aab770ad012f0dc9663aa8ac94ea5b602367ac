<template>
  <el-dialog v-model="colDialog" :title="colData.title" width="900px">
    <div class="purchase-plan-bill-file">
      <div class="purchase-plan-bill-file-view">
        <el-upload
          :action="uploadUrl"
          :http-request="uploadFile('采购计划单')"
          :show-file-list="false"
          :before-upload="beforeImportData"
        >
          <el-button
            v-if="!colData.file"
            type="primary"
            style="
              width: 372px;
              height: 80px;
              font-size: 16px;
              background-color: #00b955;
            "
            :loading="importLoading"
            :icon="Upload"
            >导入采购计划单</el-button
          >
          <div v-if="colData.file" class="file-view">
            <div class="file-view-icon">
              <div class="file-view-icon-img">
                <img src="@/assets/images/excel.png" alt="" />
              </div>
              <div class="file-view-icon-title">
                <div style="font-weight: 500; font-size: 20px; color: #333333">
                  {{ colData.file.name }}
                </div>
                <div
                  style="
                    font-weight: 400;
                    font-size: 20px;
                    color: #a5a5a5;
                    margin-top: 8px;
                  "
                >
                  共计{{ colData.count || 0 }}个商品
                </div>
              </div>
            </div>
            <el-button
              type="text"
              :loading="importLoading"
              style="
                font-weight: 700;
                font-size: 20px;
                color: #00b955;
                margin-right: 40px;
              "
              >重新上传</el-button
            >
          </div>
        </el-upload>
      </div>

      <div class="warning-tip">
        <div style="width: 80px; line-height: 24px">温馨提示：</div>
        <div style="line-height: 24px">
          <span
            >请上传正确的文件格式 .xlsx或
            .xsl；2.文件大小不超过{{props.maxM}}M，文件行数不超过{{props.maxRow}}行；3.上传模版中需包含通用名、规格和厂家，且必填。</span
          >
          <span style="color: #00b955; cursor: pointer" @click="download()"
            >点击下载</span
          >
        </div>
      </div>
    </div>
    <div
      style="
        font-weight: 600;
        font-size: 16px;
        color: #333333;
        margin-top: 20px;
      "
    >
      关联表格
    </div>
    <el-form
      v-if="colData.col.length > 0"
      style="margin-top: 20px"
      :inline="true"
      :model="colData.form"
      label-width="90px"
      ref="formRef"
    >
      <el-form-item
        v-for="(item, i) in colData.col"
        :key="i"
        :label="item.label"
        :prop="item.prop"
        :rules="item.rule"
      >
        <el-select
          :model-value="colData.form[item.prop]"
          @change="(val) => valueSelect(item.prop, val)"
          clearable
          style="width: 160px"
        >
          <el-option
            v-for="(val, n) in colData.options"
            :key="n"
            :label="val"
            :value="val"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item :label="'需要匹价'" prop="needAutoPrice">
        <el-radio-group
          v-model="billRadio"
          style="width: 400px"
          @change="billRadioChange"
        >
          <el-radio value="0" size="large"
            >需要匹价，不直接结算，先匹配价格</el-radio
          >
          <el-radio value="1" size="large"
            >无需匹价，已有商品ID（药帮忙编码），直接下单结算。</el-radio
          >
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="colDialog = false">取消</el-button>
      <el-button
        :type="getType"
        @click="fileSubmit(1)"
        v-if="showBuyBtn === false && colData.file"
        :disabled = "isDisable"
        :class="{'disCololr':isDisable}"
        >开始匹价</el-button
      >
      <el-button type="primary" @click="tobuyFunction()" v-if="showBuyBtn === true && colData.file"
        >下单结算</el-button
      >
    </template>
  </el-dialog>
</template>

<script setup>
import {
  importPurchasePlan,
  importPlan,
  downloadQuotationV3,
} from "@/http_pc/api";
import { actionTracking } from "@/config/eventTracking";
import { onMounted, ref, provide, onUnmounted, computed } from "vue";
import { getFormData } from "../data";
import { Upload } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
const props = defineProps({
  maxM:{
    default:0
  },
  maxRow:{
    default:0
  }
})
const isDisable = ref(false)
const formRef = ref(null);
const importLoading = ref(false);
const colDialog = ref(false);
const uploadUrl = "/matchPrice/importPurchasePlan";
const emit = defineEmits(["done"]);
const showBuyBtn = ref(false);
const getType = computed(()=>isDisable.value ? 'info' : 'primary')
const open = () => {
  colDialog.value = true;
  clearData();
};

defineExpose({ open ,isDisable,importLoading,colDialog});

const billRadio = ref("0");

const colData = ref({
  title: "导入采购计划单",
  form: {},
  col: [],
  file: null,
  count: 0,
  options: [],
  headRowIndex: "",
});
const valueSelect = (key, val) => {
  for (const k in colData.value.form) {
    if (colData.value.form[k] == val && val) {
      ElMessage.error("该数据已被选用，请勿重复选择");
      return;
    }
  }
  colData.value.form[key] = val;
  if (colData.value.form["csuId"]) {
    showBuyBtn.value = true;
    billRadio.value = "1";
  } else {
    showBuyBtn.value = false;
  }
};

const billRadioChange = (val) => {
  if (val === "0") {
    colData.value.form["csuId"] = "";
    showBuyBtn.value = false;
  } else {
    if (!colData.value.form["csuId"]) {
      ElMessage.error("请先选择商品ID");
      billRadio.value = "0";
      showBuyBtn.value = false;
      return;
    }
  }
};

// 下载模板
const download = () => {
  window.open("/fileTransfer/downloadTemplate?fileName=采购单导入模板.xlsx");
};
const beforeImportData = (uploadInfo) => {
  const fileName = uploadInfo.name;
  const fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
  const fileSize = uploadInfo.size;
  if (fileType !== "xlsx" && fileType !== "xls") {
    ElMessage.error("请上传正确的文件格式.xlsx或.xsl ！");
    return false;
  }
  if (fileSize > 3 * 1024 * 1024) {
    ElMessage.error("文件过大，文件大小不得超过3M！");
    return false;
  }
  return true;
};
const clearData = () => {
  // 清空数据
  showBuyBtn.value = false;
  colData.value = {
    title: "导入采购计划单",
    form: {},
    col: [],
    file: null,
    count: 0,
    options: [],
    headRowIndex: "",
  };
};
// 导入采购单
const uploadFile = (type) => {
  return (params) => {
    actionTracking("pc_page_bulkPurchase_import", {
      text: "导入采购计划单",
    });
    const { file } = params;
    let fileFormData;
    if (file && file.name !== "") {
      fileFormData = new FormData();
      fileFormData.append("file", file);
    } else {
      ElMessage.warning("请选择上传文件!");
      return;
    }

    importLoading.value = true;

    colData.value.form = {};
    downloadQuotationV3(fileFormData)
      .then((res) => {
        if (res.code == 0) {
          colData.value = {
            ...getFormData(res.result.headerInformation, "采购计划单"),
            file: file,
            count: res.result.rowNum,
            headRowIndex: res.result.headRowIndex,
          };

          if (colData.value.form.csuId) {
            showBuyBtn.value = true;
            billRadio.value = "1";
          }else{
            showBuyBtn.value = false;
            billRadio.value = "0";
          }
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((err) => {
        ElMessage.error("获取表头失败");
      })
      .finally(() => {
        importLoading.value = false;
      });
  };
};

/**
 *
 * @param { 0 | 1} status  0:计划单    1:采购单
 */
const fileSubmit = (status) => {
 
  if (formRef) {
    formRef.value.validate((valid) => {
      if (valid) {
        importLoading.value = true;
        isDisable.value = true
        emit("done", colData, status, function (needclose) {
          if (needclose === true) {
            colDialog.value = false;
            importLoading.value = false;
            isDisable.value = false
          }
        });
      }
    });
  }
};
const tobuyFunction = () => {
  if (!colData.value.form.purchaseNum) {
    ElMessage.error("请先选择采购数量");
    return;
  }
  emit("done", colData, 0, function (needclose) {
    if (needclose === true) {
      colDialog.value = false;
      importLoading.value = false;
      isDisable.value = false
    }
  });
};
</script>

<style scoped>
.warning-tip {
  display: flex;
  flex-direction: row;
  margin-top: 10px;
  font-size: 14px;
}

.purchase-plan-bill-file-view {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 860px;
  height: 160px;
  border: 2px dashed #cacaca;
  background-color: #f9f9f9;
}

.purchase-plan-bill-file {
}

.file-view {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 864px;
  height: 164px;
}

.file-view-icon {
  display: flex;
  flex-direction: row;
}

.file-view-icon-img {
  width: 92px;
  height: 92px;
  background: #00b955;
  border-radius: 4px;
  margin-left: 24px;
}

.file-view-icon-title {
  display: flex;
  flex-direction: column;
  margin-left: 20px;
  margin-top: 14px;
}
</style>