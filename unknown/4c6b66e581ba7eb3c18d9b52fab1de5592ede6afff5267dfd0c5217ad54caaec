<template>
  <div class="groupProductBox">
    <a @click="toDetail(groupData.id)">
      <!-- 价格  -->
      <div class="controlTitleText" v-if="groupData.controlTitle && groupData.controlType != 5">
        {{ groupData.controlTitle }}
      </div>
      <div class="price-wrap" v-else>
        <div class="price-container">
          <span class="priceDec">￥</span>
          <span>
            <span>
              <span class="priceInt">{{
                String((groupData.actPgby || {}).assemblePrice.toFixed(2)).split(".")[0]
              }}</span>
              <span class="priceFloat"
                >.{{
                  String((groupData.actPgby || {}).assemblePrice.toFixed(2)).split(".")[1] || "00"
                }}</span
              >
            </span>
          </span>
        </div>
      </div>
      <!-- 标题 -->
      <div class="commonName">
        <!-- 药名和规格 -->
        <span class="name">{{ groupData.showName.trim() }}</span>
        <!-- <span class="spec">{{ groupData.spec }}</span> -->
      </div>
      <div class="collageBox"></div>
    </a>
  </div>
</template>
<script setup>
import groupLimitTime from "@/components_pc/groupLimitTime.vue";
import { onMounted, ref, watch } from "vue";
import { actionTracking } from "@/config/eventTracking";
const props = defineProps({
  groupData: {
    default: {},
  },
  trackInfo: {
    default: {},
  },
  licenseStatus: {
    default: 0,
  },
});

//   const activityStatus = ref(((props.groupData || {}).actPgby || {}).assembleStatus === 0 ? 'notStart' : ((props.groupData || {}).actPgby || {}).assembleStatus === 1 ? 'inProgress' : '')

const BASEURL = import.meta.env.VITE_BASE_URL_PC;

function parseRGBA(val) {
  val = val.trim().toLowerCase(); //去掉前后空格
  if (val.length > 8) {
    let color = {};
    try {
      let argb = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(val);
      color.r = parseInt(argb[2], 16);
      color.g = parseInt(argb[3], 16);
      color.b = parseInt(argb[4], 16);
      color.a = parseInt(argb[1], 16) / 255;
    } catch (e) {
      console.log(e);
    }
    return (
      "rgba(" + color.r + "," + color.g + "," + color.b + "," + parseFloat(color.a).toFixed(1) + ")"
    );
  } else {
    return val;
  }
}

// const activityStatus = () => {
//   console.log('??????????', (props.groupData || {}).actPgby);
//   return ((props.groupData || {}).actPgby || {}).assembleStatus === 0 ? 'notStart' : ((props.groupData || {}).actPgby || {}).assembleStatus === 1 ? 'inProgress' : ''
// }

const toDetail = (id) => {
  // actionTracking('pc_page_CommodityDetails', {
  //   spid: props.trackInfo.spid,
  //   sid: props.trackInfo.sid,
  //   sptype: props.trackInfo.sptype,
  //   user_id: proxy.$merchantId,
  //   commodityId: id,
  // })
  window.open(`${BASEURL}search/skuDetail/${id}.htm`);
};
</script>

<style lang="scss" scoped>
.groupProductBox {
  padding: 4px 0 0 0;
  .commonName {
    margin-bottom: 5px;
    font-size: 16px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
    line-height: 22px;
    text-overflow: ellipsis;
    overflow: hidden;
    // white-space: nowrap;
    display: -webkit-box;
    /* autoprefixer: off */
    -webkit-box-orient: vertical;
    /* autoprefixer: on */
    -webkit-line-clamp: 2;
    .spec {
      margin-top: 4px;
      opacity: 0.8;
      font-size: 12px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: left;
      color: #666660;
      line-height: 18px;
    }
  }
  .tagBox {
    margin: 6px;
    margin-left: 0;
    white-space: nowrap;
    .tagItem {
      margin-right: 8px;
      padding: 1px 4px;
      font-size: 12px;
      border-radius: 4px;
    }
    .tagItemBig {
      padding: 4px;
      display: inline-block;
    }
    .tejia {
      color: #fff;
      background-image: linear-gradient(270deg, #ff0000 1%, #f96401 100%);
      border: 1px solid #ff9aa3;
      border-radius: 4px;
      padding: 3px 5px;
    }
  }
  .controlTitleText {
    color: #f49926;
    font-weight: 500;
    font-size: 18px;
  }
  .price-wrap {
    display: flex;
    align-items: flex-end;
    font-weight: Medium;
    .price-container {
      color: #ff2121;
      span {
        font-weight: bold;
      }
      .priceDec {
        font-size: 14px;
      }
      .priceInt {
        font-size: 22px;
      }
      .priceFloat {
        font-size: 18px;
      }
    }
    .retailPrice {
      font-size: 12px;
      color: #666666;
      text-decoration: line-through;
      margin-left: 8px;
    }
  }
  .manufacturer {
    background: none;
    font-size: 12px;
    color: #999999;
    margin: 2px 0 8px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .collageBox {
    display: flex;
    align-items: center;
    font-size: 14px;
    .tagBox {
      margin: 0;
    }
    .startedText {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 200px;
      height: 24px;
      background: url(@/assets/images/search/ptTime.png) no-repeat;
      background-size: 100%;
      padding: 0 10px;
      .orderNumText {
        // font-size: 16px;
        color: #ffffff;
      }
      .skuStartNumText {
        color: #282828;
      }
    }
    .noStartText {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 200px;
      height: 24px;
      background: url("@/assets/images/search/notStart.png") no-repeat;
      background-size: 100%;
      padding: 0 30px;
      color: #222222;
    }
  }
  .self-support {
    margin-top: 6px;
    height: 21px;
    display: flex;
    align-items: center;
    span {
      background-color: #00b955;
      border-radius: 2px;
      color: #ffffff;
      font-size: 12px;
      padding: 2px;
      display: inline-block;
    }
    .shopName {
      flex-wrap: nowrap;
      background: none;
      font-size: 12px;
      color: #999999;
      display: inline-block;
      flex-grow: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .maxWidthName {
      max-width: 68px;
    }
    .purchaseTags {
      background: none;
      color: #009d48;
    }
  }
}
</style>
