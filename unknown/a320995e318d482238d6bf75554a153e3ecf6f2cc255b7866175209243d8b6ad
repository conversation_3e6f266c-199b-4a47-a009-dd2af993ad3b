<template>
  <el-dialog
    v-model="showVis"
    title="下载对账单"
    width="500px"
    :before-close="handleClose"
  >
    <p style="margin-bottom: 20px;">选择月份后，下载所选月份下的药店账单数据</p>
    <span>月份</span>
    <el-date-picker
      style="width: 300px;margin-left: 20px;"
      v-model="yearMonth"
      :disabled-date="disabledDate"
      type="month"
      placeholder="选择月份"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose()">关闭</el-button>
        <el-button type="primary" :loading="loading" @click="downloadExcel()">确认下载</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
  import { actionTracking } from '@/config/eventTracking';
  import { ref } from "vue";
  import { downloadBill } from '@/http_pc/api';
  import { formatDate, exportExcel } from '../../../../utils/index';
  import { ElMessage } from 'element-plus';

  const props = defineProps({
    showVis: {
      default: false
    },
    cancelDialog: {
      type: Function,
      default: () => {}
    },
  });
  const yearMonth = ref('');
  const loading = ref(false);

  const disabledDate = (time) => {
    return time.getFullYear() < 2020
  }

  const handleClose = () => {
    props.cancelDialog();
  }

  // 下载对账单
	const downloadExcel = () => {
    if (!yearMonth.value) {
      ElMessage.error('请选择月份');
      return;
    }
    actionTracking('pc_action_purchaseOrder_downloadToMailbox', { 
			text: '下载对账单' ,
    });
    loading.value = true;
    const year = formatDate(yearMonth.value, 'yyyy-MM').split('-')[0];
    const month = formatDate(yearMonth.value, 'yyyy-MM').split('-')[1];
    downloadBill({
      year,
      month,
    }).then((res) => {
      loading.value = false;
      if(res.data.type === "application/json") {
				const reader = new FileReader();
				reader.onload = function(){
					const { errorMsg } = JSON.parse(reader.result);
          console.log('111', JSON.parse(reader.result));
					//处理错误
					ElMessage.error(errorMsg);
				};
				reader.readAsText(res.data);
			} else {
				const contentDisposition = res.headers['content-disposition'];
        exportExcel(res.data, decodeURIComponent(contentDisposition.split('filename=')[1]));
			}
    })
	}
</script>
<style lang="scss" scoped>
</style>