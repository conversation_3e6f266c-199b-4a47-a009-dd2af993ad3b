el-select<template>
  <el-dialog
    v-model="dialogVisible"
    title="店员信息"
    width="460px"
    :before-close="handleClose"
  >
    <div>
      <div class="clerkBox" v-if="clerkList.length">
        <div v-for="item in clerkList" :key="item.id" class="clerkItem">
          手机号：<span class="mobile">{{ item.mobile }}</span>
        </div>
      </div>
      <div class="noListBox" v-else>
        <img class="noDataImg" src="../../../../assets/images/register/noData.png" alt="">
        <div class="noDataText">暂无数据</div>
      </div>
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:currentPage="pageNo"
          v-model:page-size="pageSize"
          :background="true"
          :pager-count="5"
          layout=" prev, pager, next"
          :total="total"
          @current-change="pagerChangePage"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import {
  getClerk
} from '@/http_pc/api';

const props = defineProps({
  showClerk: {
    default: false,
  },
  cancelDialog: {
    type: Function,
    default: () => {}
  },
  merchantId: {
    default: 0,
  }
});

const dialogVisible = ref(props.showClerk);
const pageNo = ref(1);
const pageSize = ref(10);
const total = ref(0);
const clerkList = ref([]);

const handleClose = () => {
  props.cancelDialog();
}

const getList = () => {
  getClerk({
    merchantId: props.merchantId,
    pageNo: pageNo.value,
    pageSize: pageSize.value,
  }).then((res) => {
    clerkList.value = (res.data || {}).list || [];
    total.value = (res.data || {}).total || 0;
  })
}

const pagerChangePage = (page) => {
  pageNo.value = page;
  getList();
};

onMounted(() => {
  getList();
})

</script>
<style lang="scss" scoped>
  .clerkBox {
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    .clerkItem {
      margin: 17px 20px 0 0;
      color: #333;
      font-size: 12px;
      .mobile {
        color: #666;
      }
    }
  }
  .noListBox {
    text-align: center;
    margin: 0 auto;
    .noDataImg {
      width: 100px;
      height: 100px;
    }
    .noDataText {
      font-size: 14px;
      color: #888888;
    }
  }
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
</style>
