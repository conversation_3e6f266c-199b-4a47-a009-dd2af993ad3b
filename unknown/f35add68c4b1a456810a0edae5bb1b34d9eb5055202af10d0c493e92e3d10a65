<template>
  <div class="couponCenter">
    <img src="../../../assets/images/couponCenter_pc/couponCenter_top.png"/>
    <div class="content">
      <div class="title"><i></i><span>领券中心</span><i></i></div>
      <div class="main">
        <div class="tabWarp">
          <div class="tabContent" :class="{active: activeTab === tab.value}" v-for="tab in tabs" :key="tab.value"
               @click="handleClick(tab.value)">{{ tab.label }}
          </div>
        </div>
        <div class="couponList">
          <allCoupons v-if="activeTab===1"/>
          <shopCoupons v-if="activeTab===2"/>
          <goodCoupons v-if="activeTab===3"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import allCoupons from './components/allCoupons.vue'
  import shopCoupons from './components/shopCoupons.vue'
  import goodCoupons from './components/goodCoupons.vue'
  import {receiveCenterIndex} from '@/http_pc/api';
  import {onMounted, ref} from "vue";
  import { useRoute } from 'vue-router'
  import  install from "@/utils/qt"
  install()
  const route = useRoute()
  const pageExposureCoupon = () => {
    try {
      if(window.aplus_queue){
        window.updateSpmESix();
        aplus_queue.push({
          'action': 'aplus.record',
          'arguments': ['page_exposure', 'EXP', {
          "spm_cnt":`1_4.couponCenter_0-0_${ {1:'QB',2:'DPQ',3:'SPQ'}[activeTab.value] }.0.0.${window.getSpmE()}`
          }]
        });
    }
    }catch (e) {
      console.log(e)
    }
  }
  const tabs = ref([
    {
      value: 1,
      label: '全部'
    },
    {
      value: 2,
      label: '店铺券'
    },
    {
      value: 3,
      label: '商品券'
    },
  ])

  const activeTab = ref(1)
  pageExposureCoupon()
  // onMounted(() => {
  //   const query = route.query
  //   Object.keys(query).forEach(key => {
  //     localStorage.setItem(`couponCenter_pc_${key}`, query[key])
  //   })
  // })

  const handleClick = (val) => {
    activeTab.value = val
    pageExposureCoupon()
  }
</script>
<style lang="scss" scoped>
.couponCenter {
  width: 100%;
  height: auto;
  background: #F8F8F8;
}
.content {
  width: 1200px;
  height: auto;
  font-size: 14px;
  margin: 0 auto;
  .title {
    font-size: 18px;
    color: #333;
    padding: 30px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    i {
      display: inline-block;
      width: 30px;
      height: 2px;
      background-color: black;
      margin: 0 5px;
    }
  }
  .main {
    background-color: white;
    box-sizing: border-box;
    padding: 0 30px;
    .tabWarp {
      display: flex;
      align-items: center;
      justify-content: center;
      .tabContent {
        font-size: 20px;
        padding: 14px 5px;
        cursor: pointer;
        font-weight: bold;
        border-bottom: 1px solid transparent;
        margin: 0 50px;
      }
      .tabContent.active {
        border-bottom: 1px solid #00B377;
        color: #00B377;
      }
    }
    .couponList {
      padding-top: 20px;
    }
  }
}
</style>
