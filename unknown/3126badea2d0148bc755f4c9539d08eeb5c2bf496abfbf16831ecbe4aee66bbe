<template>
  <div class="box">
    <div class="right_content">
      <div class="desc">{{ couponInfo.minMoneyToEnableDesc }} {{ couponInfo.maxMoneyInVoucherDesc }}</div>
      <div class="infoBox">
        <div class="discountInfo">
          <template v-if="couponInfo.discount">
            <span>{{ couponInfo.discount }}</span>折
          </template>
          <template v-else>
            ￥<span>{{ couponInfo.moneyInVoucher }}</span>
          </template>
        </div>
        <btn :couponInfo="couponInfo" />
      </div>
    </div>

  </div>
</template>

<script setup>
  import btn from './couponBtn.vue'

  const props = defineProps({
    couponInfo: {
      default: {}
    },
  });

</script>

<style lang="scss">
.box {
  width: 350px;
  height: 111px;
  background: url("../../../../assets/images/couponCenter_pc/yiling.png");
  background-size: 100% 100%;
  margin-bottom: 30px;

  .right_content {
    width: 240px;
    height: 100%;
    float: right;
    box-sizing: border-box;
    padding: 20px 18px 16px;

    .desc{
      font-weight: 500;
      font-size: 16px;
      color: #BC4707;
    }
    .infoBox{
      margin-top: 14px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .discountInfo{
        font-weight: 500;
        font-size: 14px;
        color: #FF0000;
        span{
          font-size: 32px;
          line-height: 45px;
        }
      }
      .el-button{
        border: 1px solid #FC0000;
        color: #FC0000;
        font-weight: bold;
        font-size: 14px;
        padding: 5px 15px;
        min-height: 30px;
      }
      .el-button:focus, .el-button:hover{
        background-color: #fae4e4;
      }
    }
  }
}
</style>
