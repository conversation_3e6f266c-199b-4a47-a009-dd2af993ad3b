<template>
  <div class="registerWrapper">
    <RegisterHeader title="注册" :isLogin="true" />
    <div class="registerBox">
      <div class="tip">
        请选择关联药店
      </div>
      <div class="searchBox">
        <div style="margin: 20px 0;">
          <span>店铺地址：</span>
          <el-select
            v-model="searchAddressInfo.provinceId"
            placeholder="省"
            @change="selectChange($event, 'cityList')"
            style="width: 150px;margin:0 3px;"
          >
            <el-option
              v-for="(item, index) in provList"
              :key="index"
              :label="item.areaName"
              :value="item.areaCode"
            />
          </el-select>
          <el-select
            v-model="searchAddressInfo.cityId"
            placeholder="市"
            @change="selectChange($event, 'areaList')"
            style="width: 150px;margin:0 3px;"
          >
            <el-option
              v-for="(item, index) in cityList"
              :key="index"
              :label="item.areaName"
              :value="item.areaCode"
            />
          </el-select>
          <el-select
            v-model="searchAddressInfo.districtId"
            placeholder="区"
            @change="selectChange($event, 'streeList')"
            style="width: 150px;margin:0 3px;"
          >
            <el-option
              v-for="(item, index) in areaList"
              :key="index"
              :label="item.areaName"
              :value="item.areaCode"
            />
          </el-select>  
          <el-select
            v-model="searchAddressInfo.streetId"
            placeholder="街道"
            @change="selectChange($event, '')"
            style="width: 150px;margin:0 3px;"
          >
            <el-option
              v-for="(item, index) in streeList"
              :key="index"
              :label="item.areaName"
              :value="item.areaCode"
            />
          </el-select>
        </div>
        <div style="display:flex;alignItems:center;">
          <span>店铺名称：</span>
          <el-input v-model="shopName" style="width: 300px;marginRight: 10px;" placeholder="请输入店铺名称" />
          <el-button type="primary" @click="toSearch">搜索</el-button>
        </div>
        
      </div>
      <!-- <div class="cascaderBox">
        <el-cascader
          ref="areaCodesRef"
          v-model="areaCodes"
          :props="{
            lazy: true,
            lazyLoad: lazyLoad
          }"
          style="width: 300px"
          placeholder="请选择店铺所在的省/市/区/街道"
        />
        <el-input v-model="shopName" style="width: 300px;margin: 0 20px;" placeholder="请输入店铺名称" />
        <el-button type="primary" @click="toSearch">搜索</el-button>
      </div> -->
      <div class="searchResult">
        <span>共{{ shopCount }}个结果</span>
        <span v-if="shopCount != '--'">没有找到要关联的店铺？<span class="activeText" @click="onAddShop">点此添加</span></span>
      </div>
      <div class="shopListTip" v-if="loading">
        搜索中...
      </div>
      <div class="shopListTip" v-else-if="shopCount === '--'">
        请选择地区并搜索您的店铺
      </div>
      <div class="shopListBox" v-else-if="shopList.length">
        <div v-for="item in shopList" :key="item.poiId" class="shopItem">
          <el-radio :label="item.poiId" v-model="selectPoiId">{{ item.name }}</el-radio>
          <div class="addressText">{{ item.address }}</div>
        </div>
      </div>
      <div class="noListBox" v-else-if="shopCount === 0">
        <img class="noDataImg" src="../../../assets/images/register/noData.png" alt="">
        <div class="noDataText">暂无搜索结果</div>
      </div>
      <div class="pagination-container" v-if="shopCount > 0">
        <el-pagination
          :v-model:currentPage="pageNo+1"
          v-model:page-size="pageSize"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="shopCount"
          :page-sizes="[10,20,30,50]"
          @size-change="pagerChangeSize"
          @current-change="pagerChangePage"
        />
      </div>
      <el-button type="primary" :disabled="!selectPoiId" class="confirmBtn" @click="toConfirm">确定</el-button>
    </div>
    <AddShop v-if="showAddShop" :showAddShop="showAddShop" :cancelDialog="cancelDialog" />
    <RegisterFooter />
  </div>
</template>
<script setup>
  import { onMounted, ref } from "vue";
  import { useRouter } from 'vue-router';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import AddShop from './addShop.vue';
  import RegisterFooter from './components/registerFooter.vue';
  import RegisterHeader from './components/registerHeader.vue';
  import { findAreaByParentId, searchShops, relShop, getLoginAccountInfo } from '@/http_pc/api';
  import { actionTracking } from '@/config/eventTracking';

  const router = useRouter();
  const shopCount = ref('--');
  const shopList = ref([]);
  const selectPoiId = ref(null);
  const areaCodes = ref([]);
  const shopName = ref('');
  const showAddShop = ref(false);
  const loading = ref(false);
  const areaCodesRef = ref(null);
  const loginPhone = ref(0);
  const pageNo = ref(0);
  const pageSize = ref(10);

  const provList = ref([]);
  const cityList = ref([]);
  const areaList = ref([]);
  const streeList = ref([]);

  const searchAddressInfo = ref({
    cityId: '',
    districtId: '',
    streetId: '',
    provinceId: '',
    prov: '',
    city: '',
    area: '',
    streetName: ''
  })


  // const lazyLoad = (node, resolve) => {
  //   const { level } = node;
  //   const arr = [];
  //   findAreaByParentId({
  //     parentId: node.value || 0,
  //   }).then((res) => {
  //     if (res.areaList && res.areaList.length > 0) {
  //       res.areaList.map((item) => {
  //         let obj = {
  //           ...item,
  //           value: item.areaCode,
  //           label: item.areaName,
  //           leaf: level > 2,
  //         };
  //         arr.push(obj);
  //       });
  //       resolve(arr);
  //     } else {
  //       resolve(arr);
  //     }
  //   });
  // }

  const getAddres = (code, listStr) => {
    findAreaByParentId({
      parentId: code || 0,
    }).then((res) => {
      if ((res.areaList || []).length) {
        switch (listStr) {
        case 'provList':
          provList.value = res.areaList;
          break;
        case 'cityList':
          cityList.value = res.areaList;
          break;
        case 'areaList':
          areaList.value = res.areaList;
          break;
        case 'streeList':
          streeList.value = res.areaList;
          break;
        }
      }
    });
  }

  const selectChange = (value, listStr) => {
    switch (listStr) {
      case 'cityList':
        cityList.value = [];
        areaList.value = [];
        streeList.value = [];
        searchAddressInfo.value.cityId = '';
        searchAddressInfo.value.districtId = '';
        searchAddressInfo.value.streetId = '';
        if (searchAddressInfo.value.provinceId) {
          provList.value.forEach((item) => {
            if (item.areaCode === searchAddressInfo.value.provinceId) {
              searchAddressInfo.value.prov = item.areaName;
            }
          });
        }
        break;
      case 'areaList':
        areaList.value = [];
        streeList.value = [];
        searchAddressInfo.value.districtId = '';
        searchAddressInfo.value.streetId = '';
        if (searchAddressInfo.value.cityId) {
          cityList.value.forEach((item) => {
            if (item.areaCode === searchAddressInfo.value.cityId) {
              searchAddressInfo.value.city = item.areaName;
            }
          });
        }
        break;
      case 'streeList':
        streeList.value = [];
        searchAddressInfo.value.streetId = '';
        if (searchAddressInfo.value.districtId) {
          areaList.value.forEach((item) => {
            if (item.areaCode === searchAddressInfo.value.districtId) {
              searchAddressInfo.value.area = item.areaName;
            }
          });
        }
        break;
      default:
        if (searchAddressInfo.value.streetId) {
          streeList.value.forEach((item) => {
            if (item.areaCode === searchAddressInfo.value.streetId) {
              searchAddressInfo.value.streetName = item.areaName;
            }
          });
        }
        break;
    }
    getAddres(value, listStr);
  };

  const toSearch = () => {
    loading.value = true;
    searchShops({
      latitude: 0,
      longitude: 0,
      offset: pageNo.value,
      limit: pageSize.value,
      shopName: shopName.value,
      provinceCode: searchAddressInfo.value.provinceId || '',
      cityCode: searchAddressInfo.value.cityId || '',
      regionCode: searchAddressInfo.value.districtId || '',
      streetCode: searchAddressInfo.value.streetId || '',
      // provinceCode: areaCodes.value[0] || '',
      // cityCode: areaCodes.value[1] || '',
      // regionCode: areaCodes.value[2] || '',
      // streetCode: areaCodes.value[3] || '',
    }).then((res) => {
      loading.value = false;
      shopCount.value = (res.data || {}).total || 0;
      shopList.value = (res.data || {}).list || [];
    }).catch(() => {
      loading.value = false;
    })
  }

  const pagerChangeSize = (size) => {
    pageSize.value = size;
    toSearch();
  };
  const pagerChangePage = (page) => {
    pageNo.value = page - 1;
    toSearch();
  };

  const onAddShop = () => {
    actionTracking('pc_action_associatePoi_addPoi_click', { 
      phone: loginPhone.value,
    });
    showAddShop.value = true;
  }

  const cancelDialog = () => {
    showAddShop.value = false;
  }

  const toConfirm = () => {
    actionTracking('pc_action_associatePoi_confirm_click', { 
      phone: loginPhone.value,
    });
    ElMessageBox.confirm('确定关联此店铺？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      relShop({ poiId: selectPoiId.value }).then((res) => {
        if (res.status === 'success') {
          // 返回当前角色是店长-完成关联；返回店员-去提交资质
          if ((res.data || {}).role === 1) {
            ElMessage.success('关联成功');
            setTimeout(() => {
              window.location.href = '/merchant/center/licenseAudit/findLicenseCategoryInfo.htm';
            }, 800);
          } else if ((res.data || {}).role === 2) {
            ElMessage.success('关联成功');
            setTimeout(() => {
              router.push(`/register/uploadQualificationsInfo?merchantId=${res.data.merchantId}`);
            }, 800);
          } else {
            ElMessageBox.alert(res.msg, '提示', {
              confirmButtonText: '我知道了',
              type: 'warning'
            })
          }
        } else {
          ElMessageBox.alert(res.msg, '提示', {
            confirmButtonText: '我知道了',
            type: 'warning'
          })
        }
      })
    })
  }

  const getLoginInfo = () => {
    getLoginAccountInfo({}).then((res) => {
      // loginRole.value = res.data.info.role;
      // loginMerchantId.value = res.data.info.merchantId || 0;
      loginPhone.value =  ((res.data || {}).info || {}).mobile || 0;
    })
  }

  onMounted(() => {
    getAddres(0, 'provList');
    getLoginInfo();
  })

</script>
<style lang="scss" scoped>
.registerWrapper {
  background: #F8F8F8;
  width: 100%;
  min-height: 100vh;
  .registerBox {
    margin: 0 auto;
    width: 1200px;
    background: #fff;
    padding-bottom: 75px;
    .activeText {
      color: #00B377;
    }
    .tip {
      width: 600px;
      margin: 0 auto;
      font-weight: bold;
      font-size: 16px;
      padding: 72px 20px 0;
      text-align: left;
      color: #333;
    }
    .searchBox {
      margin: 20px 0 20px 300px;
    }
    .cascaderBox {
      width: 600px;
      margin: 20px auto;
      display: flex;
      justify-content: space-between;
    }
    .searchResult {
      width: 600px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
    }
    .shopListTip {
      width: 600px;
      margin: 0 auto;
      height: 400px;
      line-height: 400px;
      color: #999;
      margin-top: 20px;
      text-align: center;
    }
    .shopListBox {
      text-align: left;
      width: 600px;
      margin: 0 auto;
      height: 400px;
      overflow-y: scroll;
      margin-top: 20px;
      border-top: 1px solid #EFEFEF;
      .shopItem {
        display: block;
        height: 60px;
        border-bottom: 1px solid #F8F8F8;
        padding-left: 12px;
        .addressText {
          color: #999;
          font-size: 12px;
          margin-left: 20px;
        }
      }
      .shopItem:hover {
        background: #EBFBF4;
      }
    }
    .noListBox {
      text-align: center;
      height: 400px;
      width: 600px;
      margin: 0 auto;
      margin-top: 20px;
      border-top: 1px solid #EFEFEF;
      .noDataImg {
        width: 160px;
        height: 160px;
        margin-top: 120px;
      }
      .noDataText {
        font-size: 14px;
        color: #888888;
      }
    }
    .pagination-container {
      margin: 0 auto;
      margin-top: 20px;
      width: 758px;
      display: flex;
      justify-content: flex-end;
    }
    .confirmBtn {
      width: 300px;
      margin: 50px 0 0 462px;
    }
  }
}
</style>