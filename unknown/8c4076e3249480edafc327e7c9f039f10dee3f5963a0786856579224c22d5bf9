const map = new Map();
map.set('商品名', 'commonName');
map.set('通用名', 'commonName');
map.set('名称', 'commonName');
map.set('化学名', 'commonName');
map.set('商品名称', 'commonName');
map.set('Trade name', 'commonName');
map.set('规格', 'spec');
map.set('包装', 'spec');
map.set('specifications', 'spec');
map.set('生产厂家', 'manufacturer');
map.set('厂家', 'manufacturer');
map.set('生产企业', 'manufacturer');
map.set('委托生产厂家', 'manufacturer');
map.set('Manufacturer', 'manufacturer');
map.set('69码', 'code');
map.set('小包装条码', 'code');
map.set('条形码', 'code');
map.set('商品条码', 'code');
map.set('商品条码（69码）', 'code');
map.set('条码', 'code');
map.set('批准文号','approvalNumber');
map.set('采购数量','purchaseNum');
map.set('数量','purchaseNum');
map.set('quantity','purchaseNum');
map.set('price','purchasePrice');
map.set('采购价格','purchasePrice');
map.set('价格','purchasePrice');
map.set('商品ID','csuId');
map.set('商品ID(药帮忙商品编码)','csuId');
map.set('商家编码','merchantCode');
map.set('药帮忙商品编码','csuId');
map.set('CSUID','csuId');
const purchasePlan = {
	form: {
		commonName: '',  //通用名
		spec: '', //规格    
		manufacturer: '',  //生产厂家
		approvalNumber: '',   //批准文号
		code: '', //商品条码（69码）
		purchaseNum: '',   //采购数量
		purchasePrice: '', //采购价格
		merchantCode: '',   //商家编码
	},
	col: [
		{ label: '通用名', prop: 'commonName', rule: [{ required: true, trigger: 'change', message: '请选择' }] }, 
		{ label: '规格', prop: 'spec', rule: [{ required: true, trigger: 'change', message: '请选择' }] }, 
		{ label: '生产厂家', prop: 'manufacturer', rule: [{ required: true, trigger: 'change', message: '请选择' }] }, 
		{ label: '批准文号', prop: 'approvalNumber' }, 
		{ label: '商品条码（69码）', prop: 'code' }, 
		{ label: '采购数量', prop: 'purchaseNum' }, 
		{ label: '采购价格', prop: 'purchasePrice' }, 
		{ label: '商家编码', prop: 'merchantCode' }
	]
}
const purchasePlanBill = {
	form: {
		commonName: '',  //通用名
		spec: '', //规格    
		manufacturer: '',  //生产厂家
		csuId:'', //商品ID
		purchaseNum: '',   //采购数量
		approvalNumber: '',   //批准文号
		code: '', //商品条码
		purchasePrice: '', //采购价格
		merchantCode: '',   //商家编码
	},
	col: [
		{ label: '通用名', prop: 'commonName', rule: [{ required: true, trigger: 'change', message: '请选择' }] }, 
		{ label: '规格', prop: 'spec', rule: [{ required: true, trigger: 'change', message: '请选择' }] }, 
		{ label: '生产厂家', prop: 'manufacturer', rule: [{ required: true, trigger: 'change', message: '请选择' }] }, 
		{ label: '商品ID', prop: 'csuId' }, 
		{ label: '采购数量', prop: 'purchaseNum' }, 
		{ label: '批准文号', prop: 'approvalNumber' }, 
		{ label: '商品条码', prop: 'code' }, 
		{ label: '采购价格', prop: 'purchasePrice' }, 
		{ label: '商家编码', prop: 'merchantCode' }
	]
}
const plan = {
	form: {
		purchaseNum: '',   //采购数量
		csuId: '',    //商品ID
	},
	col: [
		{ label: '商品ID', prop: 'csuId', rule: [{ required: true, trigger: 'change', message: '请选择' }] },
		{ label: '采购数量', prop: 'purchaseNum', rule: [{ required: true, trigger: 'change', message: '请选择' }] }
	]
}
/**
 * @param { string[]} col
 * @param { '采购计划单' | '计划单' } type
 */
export const getFormData = (col, type) => {
	const result = {
		title: '',
		form: {},
		col: [],
		options: col
	}
	if (type == '采购计划单') {
		result.title = "导入采购单";
		result.form = {
			...purchasePlanBill.form
		};
		result.col = purchasePlanBill.col;
	} else if (type == '计划单') {
		result.title = "导入计划单";
		result.form = {
			...plan.form
		};
		result.col = plan.col;
	}
	/* result.push("aaaaaaa") */
	col.forEach((val) => {
		const r = map.get(val);

		const keys = Object.keys(result.form);
		if (keys.includes(r)) {
			result.form[r] = val;
		}
	})
	return result;

}