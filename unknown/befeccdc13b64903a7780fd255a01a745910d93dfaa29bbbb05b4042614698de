<template>
	<div style="padding:10px 0;background-color: #fff;" v-if="activeName==2">
			<el-button style="color: white; background-color: #00b955;margin-left:35px;" @click="matchAll" v-if="isShowMatchAll">一键模糊匹配</el-button>
			<span style="margin:0 20px;">
				<span v-if="isShowMatchAll">点击后没有匹配结果的商品将重新匹配，匹配的商品可能与导入的商品不一致，需手动确认加入采购计划</span>
				<span v-else>匹配出的商品可能和导入的商品不一致，请手动确认后再加入采购计划列表</span>
			</span>
		</div>
	<div :class="{'Title':true, 'sceondTitle':activeName==2}">
			<div style="padding-left:18px;width:330px;">商品匹配结果</div>
			<div style="width:242px;">导入商品信息</div>
			<div style="width:210px;">数量</div>
			<div style="width:150px;">价格（元）</div>
			<div style="width:140px;">商家</div>
			<div style="">操作</div>
		</div>
		
	<div :class="`lazyContainer${activeName}`" >
		<!-- <div style="padding:10px;background-color: #fff">点击后没有匹配结果的商品将重新匹配，匹配的商品可能与导入的商品不一致，需手动确认加入采购计划</div> -->
		
		
	
		<div v-for="excelInfo in LazyList" :key="`${excelInfo.excelId}${Date.now()}`" >
			<div class="infoBox">
				<!-- <div class="infoHeader">
					<div>
						<span class="lineNumText">{{ excelInfo.lineNum }}</span>
						<span> {{ `${excelInfo.excelCommonName} ${excelInfo.excelSpec} ${excelInfo.excelManufacturer}`}}</span>
						<span class="buyNumBox">
							预计采购数量：<span :class="excelInfo.autoAdjustNum === 0 ? 'excelBuyNumText':'excelBuyNumTextRed'">{{ excelInfo.excelBuyNum }} </span>
							<span class="iconBox" @click="showEdit(excelInfo)">
								<img class="editIcon" src="../../../../assets/images/intelligentProcurement/edit.png" alt="" >
							</span>
						</span>
					</div>
					<div>
						<span v-if="excelInfo.optimalPrice">最低价:<span class="redColor">￥{{(excelInfo.optimalPrice || 0).toFixed(2)}}</span></span>
						<span class="searchBtn" @click="showSearch(excelInfo)">极速搜索</span>
					</div>
				</div> -->
				<div>
					<div>
						<GoodsItem 
						:goodsInfo="excelInfo.skus[0]" 
						:excelInfo="excelInfo" 
						:curtab="activeName" 
						@changeIsCHangeStatus="changeIsCHangeStatus"
						@showSearch="showSearch"
						@showEditCount = "showEditCount"
						/>
					</div>
					
				</div>
				<!-- <div v-else-if="excelInfo.noMatchFlag" class="showNoBuyMsg">
					<el-checkbox :v-model="false" label="" disabled />
					<span v-if="excelInfo.noMatchFlag === 13">
						{{ excelInfo.noMatchMsg }}<span class="greenText" @click="onSetRule">【设置比价规则】</span>
					</span>
					<span v-else>
						{{ excelInfo.noMatchMsg }}<span class="greenText" @click="showSearch(excelInfo)">【极速搜索】</span>
					</span>
				</div> -->
			</div>
		</div>
		<!-- 设置采购数量 -->
		<EditCount
			v-if="showEditVis"
			:showEditVis="showEditVis"
			:defultCount="activeItem.excelBuyNum"
			:cancelDialog="handleCancel"
			:okDialog="handleOkEditCount"
		/>
		<!-- 极速搜索 -->
		<FastSearch
			v-if="showSearchVis"
			ref="fastSearchRef"
			:show-search-vis="showSearchVis"
			:addSkus="handleAddSkus"
			:defaultItem="activeItem"
			:changeNextActive="changeNext"
			:allList="dataList"
			:curtab="activeName"
			@cancelDialog="handleCancel"
			@replaceData = "replaceData"
		/>
	</div>
  
</template>

<script setup>
  import { updateStock ,secondMatch,bigSearchGoods} from '@/http_pc/api';
  import _ from 'lodash'
  import { ref, watch, inject,onMounted, onUnmounted } from "vue";
	import GoodsItem from './goodsItem.vue';
	import EditCount from './editCount.vue';
	import FastSearch from './fastSearch.vue';
	import { ElMessage } from 'element-plus';
	import { actionTracking } from '@/config/eventTracking';
	
	const props = defineProps({
		requestId:{
			default:''
		},
		fatherActiveName:{
			default:''
		},
		activeName:{
			default:''
		},
		allChecked: {
			default: false,
		},
		needChangeChecked: {
			default: false,
		},
		excelList: {
			default: [],
		},
		refresh: {
			type: Function,
			default: () => {},
		},
		recalculate: {
			type: Function,
			default: () => {},
		},
		onSetRule: {
			type: Function,
			default: () => {},
		}
  });
  const LazyList = ref([])
  const showAddBuyPlans = ref(false)
  const secondRequestId  = ref('')
  const delLazyGoods = (ids)=>{
	ids.forEach((id) => {
        LazyList.value.forEach((item, index) => {
          (item.skus || []).forEach((sku, skuIndex) => {
            if (id == sku.skuId) {
              // skus的长度为1时，把整条都删掉
              if (item.skus.length === 1) {
                LazyList.value.splice(index, 1);
              } else {
                item.skus.splice(skuIndex, 1);
              }
            }
          });
        });
      });
  } 
  const isChangeStatus = ref(false)
	const dataList = ref(JSON.parse(JSON.stringify(props.excelList)));
	const showEditVis = ref(false);
	// 当前操作的项
	const activeItem = ref({});
	const showSearchVis = ref(false);
	const fastSearchRef = ref(null); 
	const changeIsCHangeStatus = (id)=>{
		LazyList.value.forEach((item)=>{
		    if(item.skus[0]?.skuId == id){
			
			
				item.skus[0].selectStatus = 0
			}
		})
		dataList.value.forEach((item)=>{
		    if(item.skus[0]?.skuId == id){
				item.skus[0].selectStatus = !item.skus[0].selectStatus?1:0
			}
		})
	
	}
	onMounted(() => {
		
	  const containers = document.querySelectorAll('[class^="lazyContainer"]')
	  containers.forEach(item=>{
		item.addEventListener('scroll', handleScroll);
	  })
    });

    onUnmounted(() => {
      const containers = document.querySelectorAll('[class^="lazyContainer"]');
	  containers.forEach(item=>item?.removeEventListener('scroll', handleScroll))
    });
	
	const handleScroll = _.throttle(() => {
		const container = document.querySelector('.lazyContainer'+props.fatherActiveName);
      if (container.scrollTop + container.clientHeight >= container.scrollHeight - 50) {
        loadMoreData();
      }
    },200);
	const loadMoreData = () => {
		if(props.activeName!=props.fatherActiveName){
			return
		}
			if(props.excelList.length  <= LazyList.value.length){
			return
			}
		
      const startIndex = 0;
      const endIndex = LazyList.value.length + 100;
      const newData = props.excelList.slice(startIndex, endIndex);
      LazyList.value = newData;
	
    };
	const getuuid = ()=> {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random()*16|0, v = c == 'x' ? r : (r&0x3|0x8);
        return v.toString(16);
    });
}
	const isShowMatchAll = ref(true)
	const emit = defineEmits(['secondMatchToFather'])
	const matchAll = ()=>{
		secondRequestId.value = getuuid()
		secondMatch({
			'originRequestId':props.requestId,
			'requestId':secondRequestId.value,
		}).then((res)=>{
			
			isShowMatchAll.value = false
			emit('secondMatchToFather',secondRequestId.value)
		
		})
	}
	const initData = () => {
		dataList.value.forEach((item) => {
			(item.skus || []).forEach((sku) => {
				sku.selectStatus = Number(props.allChecked);
			})
		});
	}
	const addBuyPlans = ()=>{

	}
	const replaceData = (item,lineNum)=>{ 
		dataList.value.forEach((i)=>{
		    if(item.lineNum == lineNum){
				i.skus[0] = item
			}
		})
		LazyList.value.forEach((i)=>{
		    if(item.lineNum == lineNum){
		        i.skus[0] = item
		    }
		})
	}
	const showEditCount = (item)=>{
		showEditVis.value = true;
		activeItem.value =item
	}
	const showEdit = (item) => {
		showEditVis.value = true;
		activeItem.value = item;
	}

	const handleCancel = () => {
		showEditVis.value = false;
		showSearchVis.value = false;
	}
	
	const sendGrandson = inject('sendHandle');

	const handleAddSkus = (arr, type) => {
		activeItem.value.skus = arr || [];
		if (type === 1) {
			showSearchVis.value = false;
		}
		sendGrandson();
	}

	const handleOkEditCount = (num) => {
		showEditVis.value = false;
		updateStock({
			excelId: activeItem.value.excelId,
			qty: Number(num),
		}).then((res) => {
			if (res.code === 0) {
				props.refresh();
			} else {
				ElMessage.error(res.msg);
			}
		})
	}

	const showSearch = (item) => {
		activeItem.value = JSON.parse(JSON.stringify(item))  
	
		showSearchVis.value = true;
	
		// debugger
		actionTracking('pc_page_bulkPurchase_fastSearch', { 
			text: '极速搜索' ,
    });
	}
	
   const addLazyList = (item) =>{
	debugger
	const startIndex = 0;
      const endIndex = LazyList.value.length+1;
      const newData = props.excelList.slice(startIndex, endIndex);
      LazyList.value = newData;
   }
	const changeNext = (activeExcelId) => {
		let findNext = false;
		let nextItem = {};
		
		for (let index = 0; index < dataList.value.length; index++) {
			const item = dataList.value[index];
			// 找到下一个
			if (item.excelId === activeExcelId && dataList.value[index+1]) {
				nextItem = dataList.value[index+1];
				activeItem.value = dataList.value[index+1];
				findNext = true;
				break;
			} else {
				findNext = false;
			}
		}
		if (findNext) {
			fastSearchRef.value.getNewActiveItem(nextItem);
		} else {
			fastSearchRef.value.getNewActiveItem(false);
		}
	};

	const getSelectedIds = () => {
		let checkedCodes = [];
		dataList.value.forEach((item) => {
			(item.skus || []).forEach((sku) => {
				if (sku.selectStatus) {
					checkedCodes.push(sku.skuId);
				}
			})
		})
		return checkedCodes
	}

	defineExpose({ getSelectedIds,delLazyGoods ,addLazyList,isShowMatchAll});

	watch(
    () => props.needChangeChecked,
    (val) => {
			if (val) {
     		initData();
			}
		},
		{
			immediate: true,
			deep: true,
		},
  );
	watch(
    () => props.allChecked,
    () => {
			if (props.needChangeChecked) {
     		initData();
			}
		},
		{
			immediate: true,
			deep: true,
		},
  );

	watch(
    () => props.excelList,
    (newVal,oldVal) => {
			dataList.value = newVal
			if(newVal.length!==oldVal?.length){
			LazyList.value = dataList.value.slice(0,5)

		}
	},
		{
			immediate: true,
			deep: true,
		},
  );
  
</script>
<style lang="scss" scoped>
.infoBox {
  width: 1200px;
  height: auto;
  background: #FFFFFF;
}
.infoHeader {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: #F1F3F5;
	padding: 8px 20px;
	.lineNumText {
		display: inline-block;
		// width: 20px;
		// height: 20px;
		padding: 0 5px;
		background: #00B955;
		border-radius: 2px;
		color: #fff;
		text-align: center;
		line-height: 20px;
		margin-right: 14px;
	}
	.buyNumBox {
		margin-left: 20px;
		.editIcon {
			width: 14px;
			height: 14px;
			margin-left: 10px;
		}
		.iconBox {
			cursor: pointer;
		}
	}
	.excelBuyNumText {
		color: #00B377;
		font-weight: bold;
	}
	.excelBuyNumTextRed{
		color: #FF2122;
		font-weight: bold;
	}
	.redColor {
		color: #FF2122;
	}
	.searchBtn {
		margin-left: 20px;
		display: inline-block;
		background: #FFFFFF;
		border: 1px solid #E2E2E2;
		border-radius: 2px;
		color: #3F4156;
		padding: 4px 12px;
		cursor: pointer;
	}
}
.showNoBuyMsg {
	padding: 20px;
	span {
		margin-left: 10px;
	}
}
.greenText {
	color: #00B377;
	cursor: pointer;
}
.lazyContainer1{
overflow-y: scroll;
height:90vh;
background-color: white;
//padding-top:36px;
}
.lazyContainer2{
overflow-y: scroll;
height:90vh;
background-color: white;
//padding-top:36px;
}
.lazyContainer3{
overflow-y: scroll;
height:90vh;
background-color: white;

//padding-top:36px;
}
.Title{
	display: flex;
	//position: absolute;
	padding:10px 20px;
	width:100%;
	z-index:10;
	//top:0px;
	background-color: #fff;
	
}
.sceondTitle{
	// display: flex;
	// position: absolute;
	// padding:10px 20px;
	// width:100%;
	// z-index:10;
	// //top:47px;
	// background-color: #fff;
}
</style>
