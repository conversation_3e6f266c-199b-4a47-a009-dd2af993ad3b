<template>
  <div class="shopCouponBox">
    <div class="conditionBox">
      <div v-for="item in conditions" :key="item.key" class="conditionItem" :class="{activeItem: activeCondition === item.key}" @click="changeTab(item.key)">
        {{ item.title }}
      </div>
    </div>
    <div class="couponListBox" v-if="couponList.length">
      <div v-for="item in couponList" :key="item.templateId">
        <ComponentItem :couponInfo="item" />
      </div>
      <div v-if="couponList.length" class="bottom-tip">
        <span>{{ bottomTip }}</span>
      </div>
    </div>
    <div class="noGoods" v-else>
      <div>
        <img src="../../../assets/images/xyy-sorry.png" alt="">
        <span class="text">暂无优惠券</span>
      </div>
    </div>
  </div>
  
</template>
<script setup>
  import { ref, onMounted, getCurrentInstance } from "vue";
  import { useStore } from 'vuex';
  import { receiveCenterShopCoupon } from '@/http/api';
  import ComponentItem from './components/couponItem.vue';
  import { actionTracking } from '@/config/eventTracking';

  const { proxy } = getCurrentInstance();
  const store = useStore();
  const conditions = ref([]);
  const couponList = ref([]);
  const activeCondition = ref(-1);
  const pageNo = ref(1);
  const loading = ref(false);
  const isEnd = ref(false);
  const bottomTip = ref('');

  conditions.value = [{
    title: '全部',
    key: -1,
  }, {
    title: '自营',
    key: 1,
  }, {
    title: '商家',
    key: 3,
  }];

  // 上拉加载
  function pullUpLoading() {
    let scrollHeight = document.documentElement.scrollHeight;
    let scrollTop = window.scrollY;//滚动的高度；
    let clientHeight = document.documentElement.clientHeight;//滚动的高度；
    if (scrollHeight - scrollTop - clientHeight < 30) {
      if (!isEnd.value && !loading.value) {
        getInfo();
      }
    }
  };

  // 获取信息
  function getInfo() {
    loading.value = true;
    store.commit('app/showLoading', true);
    receiveCenterShopCoupon({
      merchantId: proxy.$merchantId,
      pageNo: pageNo.value,
      pageSize: 20,
      bizType: activeCondition.value,
    }).then((res) => {
      loading.value = false;
      store.commit('app/showLoading', false);
      couponList.value = couponList.value.concat(res.couponList || []);
      (res.couponList || []).forEach((item) => {
        actionTracking('h5_couponCentre_Exposure', { couponId: item.templateId })
      })
      isEnd.value = res.isEnd;
      pageNo.value = res.nextPage;
      bottomTip.value = isEnd.value ? '没有更多了' : '查看更多';
    }).catch(() => {
      loading.value = false;
      store.commit('app/showLoading', false);
    })
  }

  function changeTab(key) {
    window.scrollTo(0, 0);
    activeCondition.value = key;
    couponList.value = [];
    pageNo.value = 1;
    getInfo();
  }

  onMounted(() => {
    getInfo();
    window.addEventListener('scroll', pullUpLoading);
  });

</script>
<style lang="scss" scoped>
.shopCouponBox {
  padding: rem(80) rem(30) rem(20);
  .conditionBox {
    display: flex;
    align-items: center;
    position: fixed;
    top: rem(74);
    left: rem(30);
    right: rem(30);
    background: #F7F7F8;
    padding-bottom: rem(20);
    z-index: 100;
    .conditionItem {
      margin: rem(20) rem(36) 0 0;
      padding: rem(8)  rem(30);
      font-size: rem(24);
      color: #292933;
      background: #fff;
      border-radius: rem(4);
    }
    .activeItem {
      color: #fff;
      background: #00B377;
    }
  }
}
.noGoods{
  position: relative;
  text-align: center;
  height: rem(210);
  margin: rem(200) 0;
  img{
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width:rem(264);
    height:rem(174);
  }
  .text{
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    color: #A9AEB7;
  }
}
</style>
