<template>
  <span class="limit-content" :style="{color: timeColor}">
    <span v-if="day > 0">{{day}}天 </span>
    {{hour}}:{{min}}:{{sec}}
  </span>
</template>
<script setup>
  import { onMounted, ref, watch } from "vue";

  const props = defineProps({
    endTime: {
      default: ''
    },
    timeColor: {
      default: ''
    },
  });

  const day = ref('0');
  const hour = ref('00');
  const min = ref('00');
  const sec = ref('00');
  const timer = ref(null);
    
  onMounted(() => {
    compute_timer(props.endTime);
  })
 
  const compute_timer = (end_time) => {
    timer.value = setInterval(() => {
      let nowTime = new Date();
      if (nowTime.getTime() > end_time) {
        day.value = "0";
        hour.value = "00";
        min.value = "00";
        sec.value = "00";
        clearInterval(timer.value);
        return;
      }
      let endTime = new Date(end_time);
      let t = endTime.getTime() - nowTime.getTime();
      let tempDay = Math.floor(t / 86400000);
      hour.value = Math.floor((t / 3600000) % 24);
      min.value = Math.floor((t / 60000) % 60);
      sec.value = Math.floor((t / 1000) % 60);
      day.value = tempDay;
      hour.value = hour.value < 10 ? "0" + hour.value : hour.value;
      min.value = min.value < 10 ? "0" + min.value : min.value;
      sec.value = sec.value < 10 ? "0" + sec.value : sec.value;
    }, 1000);
  }
  watch(
    () => props.endTime,
    (new_val) => {
      if (new_val) {
        clearInterval(timer.value);
        compute_timer(props.endTime)
      }
    },
  )
  
</script>

<style lang="scss" scoped>
  .limit-content {
    line-height: 40px;
    text-align: right;
    font-size: 14px;
    color: #9494A6;
  }
</style>
