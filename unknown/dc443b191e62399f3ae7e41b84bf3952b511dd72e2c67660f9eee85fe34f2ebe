<template>
	<div class="payPwdPage" v-loading="loading">
		<div class="x-title">
			<span></span>
			<span>设置支付密码</span>
		</div>
		<div class="x-form">
			<el-form ref="formRef" label-width="180px" :rules="rules" :model="form">
				<el-form-item ref="inputRef1" label="请输入支付密码：" prop="pwd">
					<!-- <password v-model:value="form.pwd" :length="6" size="32px" :second="1"></password> -->
					<el-input type="password" :model-value="form.pwd" maxlength="6" placeholder="请输入6位数字" @input="(val) => {check(val, form, 'pwd', /^[0-9]{0,6}$/)}" show-password></el-input>
				</el-form-item>
				<el-form-item ref="inputRef2" label="请再次输入支付密码：" prop="again">
					<!-- <password v-model:value="form.again" :length="6" size="32px" :second="1"></password> -->
					<el-input type="password" :model-value="form.again" maxlength="6" placeholder="请输入6位数字" @input="(val) => {check(val, form, 'again', /^[0-9]{0,6}$/)}" show-password></el-input>
				</el-form-item>
				<el-form-item label="请输入验证码：" prop="verifyCode">
					<verifyCodeInput v-model:code="form.verifyCode"></verifyCodeInput>
				</el-form-item>
				<el-form-item style="width: 620px;">
					<div style="line-height: 30px;">
						<p>收不到验证码？</p>
						<p>请确认手机号 {{ phone }} 是否可以正常使用，若该号码已停用，请先联系销售运营换绑手机号，您还可以尝试：</p>
						<p>1、确认短信是否被手机安全软件拦截或折叠隐藏；</p>
						<p>2、查看手机网络状况是否良好，是否可以正常接收其它号码短信</p>
					</div>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="submit" style="width: 200px;">确定</el-button>
				</el-form-item>
			</el-form>
		</div>
	</div>
</template>
<script setup>
import { getLoginAccountInfo } from '@/http_pc/api';
import { setPayPassword } from '../../../http_pc/payPassword/index';
import { sendRequest, verifyArithmeticProgression, checkChain, encrypto } from './util'
import verifyCodeInput from "./components/verifyCodeInput.vue";
import password from "./components/password.vue";
import { reactive,ref, toRaw } from 'vue';
import { ElMessage } from 'element-plus';
const phone = ref('');
const formRef = ref(null);
const form = reactive({
	pwd: '',
	again: '',
	verifyCode: ''
})
const loading = ref(false);
const validatePass1 = (rule, value, callback) => {
	if (!value) {
		callback(new Error('请输入支付密码'))
	} 
	if (value.length != 6) {
		callback(new Error('密码未输入完整，请重新输入'))
	}
	const arr = value.split('');
	if (checkChain([verifyArithmeticProgression, verifyArithmeticProgression, verifyArithmeticProgression], [1, 0, -1], arr)) {
		callback(new Error('密码过于简单，请避免输入相同或连续的数字'))
	}
	callback();
}
const validatePass2 = (rule, value, callback) => {
	if (!value) {
		callback(new Error('请输入支付密码'))
	} else if (value != form.pwd) {
		callback(new Error('两次密码输入的不一致，请重新输入'))
	} 
	callback();
}
const rules = reactive({
	pwd: [{ 
			required: true, validator: validatePass1, trigger: 'change'
	}],
	again: [{ 
			required: true, validator: validatePass2, trigger: 'change' 
	}],
	verifyCode: [{ 
		required: true, message: '请输入验证码', trigger: 'blur' 
	}],
})
sendRequest(getLoginAccountInfo, {}, '获取手机号失败', '', 1000).then((res) => {
	phone.value = res.data.info.mobile.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');
})
const submit = () => {
	if (loading.value) return;
	formRef.value.validate((valid) => {
		if (valid) {
			const formData = {
				pwd: encrypto(form.pwd),
				verifyCode: form.verifyCode
			}
			loading.value = true;
			sendRequest(setPayPassword, formData, '', '密码设置成功', 1000).then(res => {
				window.top.location.href = "/merchant/center/index.htm";
			}).catch(res => {
				if (res.status == 'failure') {
					ElMessage({type: 'error', message: res.errorMsg})
				}
			}).finally(res => {
				loading.value = false;
			})
		}
	})
}
const check = (val, form, key, rexExp) => {
	if (rexExp.test(val)) {
		form[key] = val;
	}
}
</script>
<style>
.payPwdPage {
	padding: 10px;
	background-color: white;
}
.payPwdPage .x-title {
	display: flex;
	align-items: center;
	gap: 15px;
	margin-bottom: 10px;
}
.payPwdPage .x-title span:first-child {
	width: 4px;
	height: 20px;
	background-color: #00B377;
	border-radius: 5px;
}
.payPwdPage .x-title span:last-child {
	line-height: 0;
	font-size: 18px;
	font-weight: 600;
}
.payPwdPage .x-form {
	width: 435px;
	margin: 0 auto;
}
</style>