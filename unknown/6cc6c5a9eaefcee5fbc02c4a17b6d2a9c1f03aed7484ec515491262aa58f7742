<!-- 此组件样式为普通商品流和拼团商品流混排-->
<template>
  <div>
    <ul class="templist">
      <li class="templist-item" v-for="(item, index) in datalist" :key="index">
        <div  @click="goToProductDetail(item, `${detailUrl}product_id=${item.id}&nsid=${item.nsid}&sdata=${item.sdata || ''}&sid=${buriedPoint.sid}&spid=${buriedPoint.spid}&sptype=${buriedPoint.sptype}`)" 
          class="item_card">
          <div v-if="index < 3" class="rank_img" :class="`sortBox${index + 1}`"> {{ index + 1 }} </div>
          <!-- 标题 -->
          <div class="sku_name">
            <span v-for="(tag, index) in ((item || {}).tags || {}).titleTags" class="titleTag" :key="index"
              :style="{ marginLeft: index > 0 ? '0.12rem' : '' }">
              <span v-if="tag.text && tag.uiStyle == 1"
                :style="{ color: parseRGBA(tag.textColor), background: parseRGBA(tag.bgColor) }">{{ tag.text }}</span>
            </span>
            {{ item.showName.trim() }}
          </div>
          <!-- 规格/公司 -->
          <div class="sku_spec"> {{ item.spec }} | {{ item.manufacturer }}</div>
          <!-- 价格 -->
          <div v-if="item.isShowMore" class="price_module">
            <div v-if="item.controlTitle && item.controlType != 5" class="no_authority">{{item.controlTitle}}</div>
            <!-- <div v-else class="sku_price_title"> 价格区间¥ <span class="sku_price">¥{{item.minPrice.toFixed(2)[0]}}.<span class="sub_price">{{item.minPrice.toFixed(2)[1]}}</span>-¥{{item.maxPrice.toFixed(2)[0]}}.<span class="sub_price">{{item.maxPrice.toFixed(2)[1]}}</span></span></div> -->
            <div v-else class="sku_price_title"> 价格区间¥ <span class="sku_price">¥{{item.minPrice.toFixed(2).split('.')[0]}}.<span class="sub_price">{{item.minPrice.toFixed(2).split('.')[1]}}</span>-¥{{item.maxPrice.toFixed(2).split('.')[0]}}.<span class="sub_price">{{item.maxPrice.toFixed(2).split('.')[1]}}</span></span></div>
            <div class="shopEntry" @click.stop="moreSku(item)">查看更多商品
              <img src="../assets/images/more_arrow.png" />
            </div>
          </div>
          <div class="force_break" />
          <div v-if="item.actPt && (!item.controlTitle || itemGoodsData.controlType == 5)" class="sku_detail">
            <SkuActionImage :productItem="item" :licenseStatus="licenseStatus" :buriedPoint="buriedPoint"  />
            <liteGroupProduct :groupData="item" :licenseStatus="licenseStatus" :buriedPoint="buriedPoint" :categoryName="categoryName"/>
          </div>
          <div v-else class="sku_detail">
            <SkuActionImage :productItem="item" :licenseStatus="licenseStatus" :buriedPoint="buriedPoint" />
            <liteCommonProduct :productItem="item" :licenseStatus="licenseStatus" :buriedPoint="buriedPoint" :categoryName="categoryName" />
          </div>
        </div>
       
      </li>
    </ul>
  </div>
</template>

<script setup>
import { computed, ref, watch, getCurrentInstance, onMounted } from 'vue'
import { useStore } from "vuex";
import { handleDiscount } from '@/http/api';
import liteCommonProduct from './liteCommonProduct.vue';
import liteGroupProduct from './liteGroupProduct.vue';
import SkuActionImage from './skuActionImage.vue';
import { useRouter } from 'vue-router';
import { actionTracking } from "@/config/eventTracking";

const router = useRouter();

const props = defineProps({
  productList: {
    default: []
  },
  licenseStatus: {
    default: '',
  },
  categoryName :{
    default : '',
  },
  // 埋点数据
  buriedPoint: {
    default: {
      spid: '',
      sid: '',
      sptype: ''
    },
  }
});
const { proxy } = getCurrentInstance();
const store = useStore();
const imgUrl = import.meta.env.VITE_IMG;
const datalist = ref(props.productList);
const detailUrl = computed(() => store.state.app.detailBaseUrl);

function parseRGBA(val) {
  val = val.trim().toLowerCase();  //去掉前后空格
  if (val.length > 8) {
    let color = {};
    try {
      let argb = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(val);
      color.r = parseInt(argb[2], 16);
      color.g = parseInt(argb[3], 16);
      color.b = parseInt(argb[4], 16);
      color.a = parseInt(argb[1], 16) / 255;
    } catch (e) {
      console.log(e)
    }
    return 'rgba(' + color.r + ',' + color.g + ',' + color.b + ',' + parseFloat(color.a).toFixed(1) + ')';
  } else {
    return val;
  }
}
//查看更多商品
function moreSku(item) {
   const link = `/classifiedRankingList/skuListPage?cname=${encodeURI(props.categoryName)}&masterStandardProductId=${encodeURI(item.masterStandardProductId)}&brachCode=${item.branchCode}`;
    actionTracking('h5_standardProductChart_more_Click', {
      categoryName: props.categoryName,
      standardProduct_id:item.masterStandardProductId,
      action: link
    });
    console.log(link)
    // router.push('http://www.baidu.com')
  router.push(link);
}

//跳转详情页
function goToProductDetail(item, url){
    //跳转到到详情页
   actionTracking("h5_standardProductChart_Product_Click", {
      categoryName : props.categoryName,
      standardProduct_id : item.masterStandardProductId,
      sku_id : item.id,
   })
    window.location.href = url;
}

// 添加折后价
function addDiscount(data) {
  if (data && data.length > 0) {
    const idList = data.map(item => { return item.id });
    handleDiscount({ skuIds: idList.join(',') })
      .then((res) => {
        if (res.status == "success") {
          const priceArr = res.data;
          priceArr.map((item, index) => {
            data.map((item2, index2) => {
				debugger
              if (item.skuId == item2.id) {
				console.log(item2);
				console.log(item);
                const zheHouPrice = Number(item.price.split('￥')[1]);
				  if (!isNaN(zheHouPrice) && zheHouPrice < item2.fob) {
					item2.zheHouPrice = item.price;
				  }
              }
            })
          })
        }
      })
      .catch((err) => {
        console.log(err, "请求失败了");
      });
  }
  datalist.value = data;
}

onMounted(() => {
  addDiscount(props.productList);
  actionTracking("h5_page_ListPage_Exposure", { list: props.productList });
})

watch(
  () => props.productList,
  (val) => {
    if (val) {
      addDiscount(props.productList);
    }
  }
);
</script>

<style lang="scss" scoped>
.templist {
  font-family: PingFangSC;
  background: #fff;

  .templist-item {
    position: relative;
    // padding: rem(30) 0 rem(10) rem(20);
    // border-bottom: 1px solid rgba(245, 245, 245, 1);
    overflow: hidden;

    .item_card {
      width: auto;
      margin-top: rem(20);
      height: auto;
      background: #FFF4E5;
      border-radius: rem(10);
      padding: rem(15);
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      align-items: center;

      .titleTag {
        border-radius: rem(2);
        font-size: rem(20);
        margin-right: rem(6);

        span {
          padding: 0 rem(8);
        }
      }

      .rank_img {
        width: rem(40);
        height: rem(43);
        color: #fff;
        font-weight: bold;
        font-size: rem(28);
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }

      .sku_name {
        margin-left: rem(10);
        color: #000;
        font-weight: bold;
        font-size: rem(30);
        color: #292933;
        line-height: rem(40);
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        /* autoprefixer: off */
        -webkit-box-orient: vertical;
        /* autoprefixer: on */
        -webkit-line-clamp: 1;
        word-break: break-all;
        word-wrap: break-word;
        flex-grow: 1;
        width:0;
      }

      .sku_spec {
        color: gray;
        font-size: rem(22);
        margin-top: rem(10);
        flex-grow: 1;
        width: 100%;
        
      }

      .sku_price_title {
        margin-top: rem(15);
        color: red;
        font-size: rem(22);
      }

      .sku_price {
        font-size: rem(35);
        span{
          font-size:rem(25);
        }
      }

      .price_module{
        display: flex;
        width:100%; 
        flex-wrap: wrap;
      align-content: flex-start;
      align-items: center;
      .no_authority {
        margin-top: rem(15);
        color: red;
        align-self: flex-end;
        font-size: rem(32);
      }

        .shopEntry {
          color: #292933;
          margin-right: rem(20);
          align-self: flex-end;
          flex-grow: 1;
          text-align: right;
          background: repeating-linear-gradient();

          img {
            position: absolute;
            width: rem(14);
            height: rem(24);
          }

        }
      }


      .force_break {
        flex-basis: 100%;
        height: rem(15);
      }

      .sku_detail {
        background: #fff;
        width: 100%;
        display: flex;
        margin-right: rem(20);
        height: rem(250);
      }

      .sortBox1 {
        background: url('../assets/images/sort1.png') no-repeat;
        background-size: 100%;
      }

      .sortBox2 {
        background: url('../assets/images/sort2.png') no-repeat;
        background-size: 100%;
      }

      .sortBox3 {
        background: url('../assets/images/sort3.png') no-repeat;
        background-size: 100%;
      }
    }
  }
}
</style>
