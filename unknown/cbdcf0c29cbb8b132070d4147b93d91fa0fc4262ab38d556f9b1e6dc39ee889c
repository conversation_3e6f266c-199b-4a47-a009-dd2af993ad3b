<template>
	<div class="verifyCode">
		<el-input type="text" :model-value="prop.code" @input="inputCheck" placeholder="请输入6位数验证码"></el-input>
		<el-button type="primary" @click="getCode" :disabled="data.expireTime != 0" :style="{opacity: data.expireTime ? '0.7': '1'}">{{ '获取验证码 ' + (data.expireTime == 0 ? '' : `(${data.expireTime})`) }}</el-button>
	</div>
</template>
<script setup>
import { getVerifyCode } from '../../../../http_pc/payPassword/index';
import { reactive, ref } from 'vue';
import { sendRequest} from '../util'
const prop = defineProps({
	code: String
})
const emit = defineEmits(['update:code'])
const data = reactive({
	expireTime: 0
})
const loading = ref(false);
const inputCheck = (val) => {
	if (/^[0-9]{0,6}$/.test(val)) {
		emit('update:code', val)
	}
}
const getCode = async () => {

	/* const result = await getVerifyCode();
	if (result.data && result.data.expireTime) {
		data.expireTime = result.data.expireTime;
		timer();
	} */
	if (loading.value) return;
	loading.value = true
	sendRequest(getVerifyCode, '', '获取验证码失败', '验证码已发送，请注意查看', 1000).then(result => {
		data.expireTime = result.data.expireTime;
		timer();
	}).finally(() => {
		loading.value = false;
	})
}
const timer = () => {
	const t = setTimeout(() => {
		if (data.expireTime > 0) {
			data.expireTime = data.expireTime - 1;
			timer();
		}
		clearTimeout(t)
	}, 1000)
}
</script>
<style>
.verifyCode {
	display: flex;
	width: 100%;
	gap: 10px;
}
</style>