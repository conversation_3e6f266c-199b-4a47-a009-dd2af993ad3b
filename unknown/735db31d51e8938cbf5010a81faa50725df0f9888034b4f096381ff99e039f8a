<template>
  <div class="info">
    <div >
       <!-- href="${detailUrl}product_id=${itemGoodsData.id}&nsid=${itemGoodsData.nsid}&sdata=${itemGoodsData.sdata || ''}&sid=${buriedPoint.sid}&spid=${buriedPoint.spid}&sptype=${buriedPoint.sptype}" -->
      <!-- v-tracking="{eventName:'page_Chart_productList_Click',params:{categoryName: categoryName, sku_id: itemGoodsData.id, sid: buriedPoint.sid, spid: buriedPoint.spid, sptype: buriedPoint.sptype, standardProduct_id: itemGoodsData.masterStandardProductId}}" -->
    <!-- > -->
      <!-- 标题 -->
      <div class="commonName">
        <span
          v-for="(item, index) in ((itemGoodsData || {}).tags || {}).titleTags"
          class="titleTag"
          :key="index"
          :style="{marginLeft: index > 0 ? '0.12rem' : ''}"
        >
          <span v-if="item.text && item.uiStyle == 1" :style="{color: parseRGBA(item.textColor), background: parseRGBA(item.bgColor)}">{{item.text}}</span>
        </span>
        <!-- 药名 -->
        <span class="name">
          {{itemGoodsData.showName.trim()}}
        </span>
      </div>
      <div class="factory textellipsis">
        <!-- 规格-->
        <span class="spec">{{itemGoodsData.spec}} | </span>
        <!-- 厂家 -->
        <span class="name">{{itemGoodsData.manufacturer}}</span>
      </div>
      <!-- 近效期  中包装-->
      <div class="date">
        <div class="detail textellipsis">有效期{{itemGoodsData.nearEffect.replace(/-/g, ".")}}/{{itemGoodsData.farEffect.replace(/-/g, ".")}}</div>
      </div>
    </div>
    <!-- 价格  -->
    <div class="price-add-wrap">
      <div class="price-wrap">
        <div class="price-container">
          <div v-if="itemGoodsData.controlTitle && itemGoodsData.controlType != 5" class="controlTitleText">{{itemGoodsData.controlTitle}}</div>
          <div v-else class="price-numer">
            <i v-if="itemGoodsData.priceType == 2&&itemGoodsData.skuPriceRangeList">
              ￥{{itemGoodsData.skuPriceRangeList[0].price.toFixed(2)}}~{{itemGoodsData.skuPriceRangeList[itemGoodsData.skuPriceRangeList.length -1].price.toFixed(2)}}
            </i>
            <div v-else class="pricewapper">
              <div class="price-box clearfixed">
                <div class="price-two">
                  <p>
                    <span>￥{{itemGoodsData.fob.toFixed(2)}}</span> 
                    <span v-if="itemGoodsData.zheHouPrice" class="zheHouPrice">{{ itemGoodsData.zheHouPrice || '' }}</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 加购按钮 -->
      <div class="inputNumber">
        <div v-if="itemGoodsData.status === 2" class="no-goods">
          <img src="../assets/images/bell.png" alt />
        </div>
        <div v-else>
          <!-- :product-num="" -->
          <CartBtn
            v-if="!itemGoodsData.controlTitle"
            :is-pack="false"
            :data-id="itemGoodsData.id"
            :is-split="itemGoodsData.isSplit"
            :product-num="itemGoodsData.cartProductNum"
            :med-num="itemGoodsData.mediumPackageNum"
            :show-icon="false"
            :nsid="itemGoodsData.nsid"
            :sdata="itemGoodsData.sdata || ''"
            @addbuyAction="onAddBuy(itemGoodsData)"
            ></CartBtn>
        </div>
      </div>
    </div>
    <!-- 零售价毛利 -->
    <div class="control-hid">
      <div class="control-box" v-if="!(licenseStatus === 1||licenseStatus ===5)">
        <PriceBox
          v-if="!itemGoodsData.controlTitle"
          :uniformPrice="itemGoodsData.uniformPrice"
          :suggestPrice="itemGoodsData.suggestPrice"
          :grossMargin="itemGoodsData.grossMargin"
        />
      </div>
    </div>
    <div class="label-box" v-if="itemGoodsData.tags.productTags && itemGoodsData.tags.productTags.length>0">
      <div class="labels">
        <div class="labels-span">
          <span
            v-for="(item2,index2) in itemGoodsData.tags.productTags.slice(0,3)"
            :key="index2"
            :class="`span${item2.uiType}`"
          >{{item2.name}}</span>
        </div>
        <span class="more" @click.stop="showAll(itemGoodsData.tags.productTags)"></span>
      </div>
    </div>
    <Layer v-if="hasMask"  :hasMask="hasMask" :maskList="maskList" @exit="exit" :skuId="itemGoodsData.id"></Layer>
     <div class="shopBox" v-if="itemGoodsData.shopUrl" @click.stop="setPreviewUrl(itemGoodsData.shopUrl, $event)">
      <div class="shopName">{{ itemGoodsData.shopName }}</div>
      <div class="shopEntry">进店
        <img src="../assets/images/toRight.png" />
      </div>
    </div>
  </div>
</template>
<script setup>
  import { reactive, ref, computed, onMounted } from "vue";
  import { useStore } from "vuex";
  import CartBtn from './cartBtn.vue';
  import PriceBox from "./priceBox.vue";
  import Layer from './layer.vue';
  import { actionTracking } from "@/config/eventTracking";

  const props = defineProps({
    productItem: {
      default: {}
    },
    licenseStatus: {
      default: ''
    },
    categoryName :{
       default : '',
    },
    // 埋点数据
    buriedPoint: {
      default: {
        spid: '',
        sid: '',
        sptype: ''
      },
    }
  });

  const store = useStore();
  const detailUrl = computed(() => store.state.app.detailBaseUrl);
  const itemGoodsData = reactive(props.productItem);
  const pageTitle = window.document.title;
  const hasMask = ref(false);
  const maskList = ref([]);

  function parseRGBA(val) {
    val = val.trim().toLowerCase();  //去掉前后空格
    if (val.length > 8) {
      let color = {};
      try {
        let argb = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(val);
        color.r = parseInt(argb[2], 16);
        color.g = parseInt(argb[3], 16);
        color.b = parseInt(argb[4], 16);
        color.a = parseInt(argb[1], 16) / 255;
      } catch (e) {
        console.log(e)
      }
      return 'rgba(' + color.r + ',' + color.g + ',' + color.b + ',' + parseFloat(color.a).toFixed(1) + ')';
    } else {
      return val;
    }
  }

  function setPreviewUrl(shopurl){
    window.location.href = shopurl;
  }

  function onAddBuy(itemGoodsData){
    actionTracking("page_Chart_productList_addbuy", {
      categoryName : props.categoryName,
      standardProduct_id : itemGoodsData.masterStandardProductId,
      sku_id : itemGoodsData.id,
      ...props.buriedPoint
    })
  }

  onMounted(() => {
    actionTracking("page_Chart_productList_Exposure", { //这个埋点应该移到外层，但实际上和外露商品是完全捆绑的，所以放在这里也没什么区别。
      categoryName : props.categoryName,
      standardProduct_id : itemGoodsData.masterStandardProductId,
      showName : itemGoodsData.showName,
      ...props.buriedPoint
    })
  })

  function exit() {
    hasMask.value = false;
    // maskList.value = [];
  }

  function showAll(arr) {
    // maskList.value = arr;
    maskList.value = arr.filter((obj)=>{return obj.promoType != 19});
    hasMask.value = true;
  };
</script>
<style lang="scss" scoped>
.info {
  padding-left: rem(20);
  width: rem(450);
  padding-right: rem(15);
  .commonName {
    position: relative;
    margin-bottom: rem(12);
    font-weight: bold;
    font-size: rem(30);
    color: #292933;
    line-height: rem(40);
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    /* autoprefixer: off */
    -webkit-box-orient: vertical;
    /* autoprefixer: on */
    -webkit-line-clamp: 1;
    word-break: break-all;
    word-wrap: break-word;
    .titleTag {
      border-radius: rem(2);
      font-size: rem(20);
      margin-right: rem(6);
      span {
        padding: 0 rem(8);
      }
    }
  }
  .factory {
    margin: 0 rem(12);
    font-size: rem(24);
    color: #676773;
    .name {
      margin-left: rem(5);
      line-height: rem(30);
      color: #676773;
    }
    .spec {
      letter-spacing: 0;
      line-height: rem(34);
    }
  }
  .date {
    position: relative;
    margin-top: rem(10);
    display: flex;
    box-sizing: border-box;
    justify-content: flex-start;
    .detail {
      margin-left: rem(5);
      line-height: rem(30);
      font-size: rem(24);
      color: #676773;
    }
  }
  .price-add-wrap {
    display: flex;
    display: -webkit-flex;
    flex-wrap: nowrap;
    -webkit-flex-wrap: nowrap;
    justify-content: space-between;
    margin-top: rem(10);
    position: relative;
    height: rem(60);
    .price-wrap {
      display: flex;
      display: -webkit-flex;
      flex-wrap: nowrap;
      -webkit-flex-wrap: nowrap;
      flex-direction: column;
      overflow: hidden;
      .price-container {
        display: flex;
        display: -webkit-flex;
        flex-wrap: nowrap;
        -webkit-flex-wrap: nowrap;
        justify-content: space-between;
        line-height: rem(60);
        .controlTitleText {
          font-size: rem(32);
          color: #f49926;
          font-weight: 600;
        }
        .qualifications {
          font-size: rem(28);
          color: #ff982c;
          font-weight: 400;
        }
        .nobuy {
          font-size: rem(32);
          color: #f49926;
          font-weight: 600;
        }
        .price-numer {
          font-size: rem(24);
          .price-permission {
            font-size: rem(32);
            color: #f49926;
            font-weight: 600;
          }
          .pricewapper {
            .price-box {
              overflow: hidden;
              .price-two {
                line-height: rem(60);
                float: left;
                letter-spacing: 0;
                p {
                  span {
                    font-family: Helvetica;
                    color: #ff2121;
                    font-size: rem(34);
                    font-weight: 600;
                  }
                  .zheHouPrice {
                    color: #666;
                    font-weight: 400;
                    font-size: rem(24);
                    margin-left: rem(4);
                  }
                  .original {
                    margin-left: rem(6);
                    text-decoration: line-through;
                    font-size: rem(24);
                    color: #9494a6;
                    text-align: center;
                    transform: scale(0.8);
                  }
                }
              }
              .midPack {
                line-height: rem(40);
                text-align: right;
                width: 35%;
                float: right;
                font-size: rem(20);
                color: #999;
              }
            }
          }
        }
      }
    }
    .inputNumber {
      width: rem(160);
      height: rem(60);
      position: absolute;
      right: 0;
      top: 0;
      .no-goods {
        img {
          position: absolute;
          bottom: rem(10);
          right: rem(10);
          width: rem(40);
          height: rem(40);
        }
      }
    }
  }
   .shopBox {
      color: #676773;
      font-size: rem(24);
      padding: rem(12) 0 rem(26);
      display: flex;
      align-items: center;
      .shopName {
        max-width: rem(289);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .shopEntry {
        color: #292933;
        display: flex;
        align-items: center;
        margin-left: rem(10);
        img {
          width: rem(24);
          height: rem(24);
        }
      }
    }
  .control-hid {
    .control-box {
      margin-top: rem(0);
      margin-bottom: rem(10);
      width: 100%;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      line-height: rem(40);
      span {
        color: #9494a6;
        font-size: rem(22);
        line-height: rem(27);
      }
    }
    .active-control-box {
      width: rem(145);
    }
  }
  .label-box {
    .labels {
      margin-bottom: rem(10);
      height: rem(36);
      line-height: rem(36);
      position: relative;
      font-size: 0;
      display: flex;
      display: -webkit-flex;
      flex-wrap: nowrap;
      -webkit-flex-wrap: nowrap;
      justify-content: space-between;
      .labels-span {
        display: flex;
        display: -webkit-flex;
        flex-wrap: wrap;
        -webkit-flex-wrap: wrap;
        height: rem(36);
        line-height: rem(36);
        overflow: hidden;
        span {
          margin-right: rem(10);
          margin-top: rem(4);
        }
        .fullXReductionY {
          position: relative;
          background: rgba(255, 147, 143, 1);
          color: rgba(255, 255, 255, 1);
          letter-spacing: 1px;
        }
        .fullXReductionY:before {
          content: "";
          display: inline-block;
          background-color: #fff;
          width: rem(7);
          height: rem(14);
          border-radius: 0 rem(14) rem(14) 0;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
        .fullXReductionY:after {
          content: "";
          display: inline-block;
          background-color: #fff;
          width: rem(7);
          height: rem(14);
          border-radius: rem(14) 0 0 rem(14);
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      /*标签样式*/
      @import "../assets/style/tagStyle";
      .more {
        max-width: rem(54);
        background: url("../assets/images/dot.png") no-repeat center;
        background-size: 100%;
        width: rem(54);
      }
    }
  }
}
</style>
