<template>
  <div>
    <div class="all_bg" v-if="Object.keys(recommendReceived).length">
      <div class="title">
        <div>您还有这些已领可用券</div>
        <el-button round class="seeMore" size="small" @click="seeMore">查看更多 ></el-button>
      </div>
      <div class="list">
        <receivedCoupon :couponInfo="recommendReceived" />
      </div>
    </div>
    <div v-if="newCustomerCoupon.length">
      <div class="title_line">新人券</div>
      <div class="list">
        <newPeopleCoupon :couponInfo="item" v-for="item in newCustomerCoupon" :key="item.id" />
      </div>
    </div>
    <div v-if="platformCoupon.length">
      <div class="title_line">平台券</div>
      <div class="list">
        <crossStoreCoupon :couponInfo="item" v-for="item in platformCoupon" :key="item.id" />
      </div>
    </div>
    <div v-if="shopCouponSelected.length">
      <div class="title_line">精选店铺券</div>
      <div class="list">
        <selectedStoreCoupon :couponList="shopCouponSelected" :bottomTip="bottomTip" :getInfo="getShopSelectInfo" />
      </div>
    </div>
  </div>
</template>

<script setup>
  import receivedCoupon from './receivedCoupon.vue'
  import newPeopleCoupon from './newPeopleCoupon.vue'
  import crossStoreCoupon from './crossStoreCoupon.vue'
  import selectedStoreCoupon from './selectedStoreCoupon.vue'
  import { useStore } from 'vuex';
  import { receiveCenterIndex, shopSelect } from '@/http_pc/api';
  import {onMounted, ref, getCurrentInstance } from 'vue'
  import {ElMessage} from 'element-plus'

  const store = useStore();
  const { proxy } = getCurrentInstance();
  const recommendReceived = ref({});
  const newCustomerCoupon = ref([]); // 新人券
  const platformCoupon = ref([]); // 跨店券
  const shopCouponSelected = ref([]); // 精选店铺券
  const pageNo = ref(1);
  const isEnd = ref(false);
  const bottomTip = ref('');
  const loading = ref(false);

  const getCoupon = () => {
    receiveCenterIndex({merchantId: proxy.$merchantId}).then(res => {
      if (res && res.status === 'success') {
        recommendReceived.value = res.recommendReceived || {};
        newCustomerCoupon.value = res.newCustomerCoupon || [];
        platformCoupon.value = res.platformCoupon || [];
      } else {
        ElMessage.error(res.msg || '获取优惠券失败')
      }
    }).catch(e => {
      console.log('页面接口错误', e)
    })
  }
  // 获取精选店铺券
  function getShopSelectInfo() {
    loading.value = true;
    if (pageNo.value > 1) {
      store.commit('app/showLoading', true);
    }
    shopSelect({
      merchantId: proxy.$merchantId,
      pageNo: pageNo.value,
      pageSize: 20,
    }).then((res) => {
      loading.value = false;
      store.commit('app/showLoading', false);
      shopCouponSelected.value = shopCouponSelected.value.concat(res.couponList || []);
      isEnd.value = res.isEnd;
      pageNo.value = res.nextPage;
      bottomTip.value = isEnd.value ? '没有更多了' : '查看更多';
    }).catch(() => {
      store.commit('app/showLoading', false);
      loading.value = false;
    })
  }

  // 上拉加载
  function pullUpLoading() {
    let scrollHeight = document.documentElement.scrollHeight;
    let scrollTop = window.scrollY;
    let clientHeight = document.documentElement.clientHeight;
    if (scrollHeight - scrollTop - clientHeight < 30) {
      if (!isEnd.value && !loading.value) {
        console.log('滚到底部，可以加载下一页1');
        getShopSelectInfo();
      }
    }
  };

  const seeMore = () => {
    window.open('/merchant/center/voucher/findAllVoucherInfoWithShop.htm');
  }

  onMounted(() => {
    getCoupon();
    getShopSelectInfo();
    window.addEventListener('scroll', pullUpLoading);
  })

</script>

<style scoped lang="scss">
.all_bg {
  width: 100%;
  height: 211px;
  background: url("../../../../assets/images/couponCenter_pc/yilin_bg.png");
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 24px 20px;
  overflow: hidden;
}

.title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  font-size: 20px;
  color: #FFFFFF;
}

.seeMore {
  font-size: 16px;
  background: #8C1700;
  color: #FFFFFF;
  border: none;
}

.el-link:hover {
  color: #FFFFFF;
}

.title_line {
  font-weight: 500;
  font-size: 21px;
  color: #333333;
  padding-left: 13px;
  position: relative;
  margin-top: 30px;
}

.title_line:before {
  position: absolute;
  top: 50%;
  left: 0;
  content: "";
  width: 3px;
  height: 19px;
  background: #00B377;
  border-radius: 1.5px;
  transform: translateY(-50%);
}

.list {
  width: 100%;
  height: auto;
  display: flex;
  flex-wrap: wrap;
  // justify-content: space-between;
  margin-top: 20px;
}
</style>
