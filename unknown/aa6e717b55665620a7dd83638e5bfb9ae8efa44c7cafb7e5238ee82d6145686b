<template>
  <div class="introduce">
    <div class="my flex">
      <span></span>
      我的平安商户
    </div>
    <div class="title">
      什么是平安商户?
      <p>平安商户是 <span style="color: #00B377">药帮忙</span> 联合 <span style="color: #FF3000">平安银行</span> 推出的药品流通行业资金支付解决方案，帮助药店一站式解决支付、税务、金融等问题</p>
    </div>
    <div class="line" />
    <div class="content">
      <img class="img1" src="@/assets/images/pinganMerchant/introduce-pc1.png" />
    </div>
    <div class="line" />
    <div class="content">
      <img class="img2" src="@/assets/images/pinganMerchant/introduce-pc2.png" />
    </div>
    <div v-if="!hide" class="openBtn">
      <div class="open" @click="handleOpen">立即开通</div>
    </div>
  </div>
</template>

<script setup>
  import { ref } from "vue";
  import { useRouter, useRoute } from 'vue-router';

  const router = useRouter();
  const route = useRoute();
  const hide = ref(false);
  const { from, merchantId } = route.query;
  if (from) {
    hide.value = true;
  }
  
  const handleOpen = () => {
    router.push({
      path: '/pinganMerchant/pc/open',
      query: { merchantId }
    });
  };
</script>
<style lang="scss" scoped>
#app {
  background: #fff;
}
.introduce {
  background: #fff;
  width: 980px;
  margin: auto;
  // background-color: #F7F7F8;
  background-size: 100%;
  box-sizing: border-box;
  padding: 30px 30px 0 30px;
  .flex {
    display: flex;
    align-items: center;
  }
  .my {
    span {
      width: 4px;
      height: 16px;
      background: #00B377;
      margin-right: 10px;
    }
    font-weight: 600;
    font-size: 18px;
    color: #292933;
    letter-spacing: 0;
    line-height: 20px;
  }
  .title {
    padding-top: 34px;
    font-weight: 500;
    font-size: 14px;
    color: #292933;
    letter-spacing: 0;
    line-height: 16px;
    p {
      margin: 10px 0 0 0;
      font-size: 12px;
      font-weight: 400;
      line-height: 14px;
    }
  }
  .line {
    height: 1px;
    background: #EEEEEE;
    margin: 25px 0 0 0;
  }
  .content {
    padding: 24px 0 0 0;
    .img1 {
      width: 537px;
      height: 141px;
    }
    .img2 {
      width: 335px;
      height: 54px;
    }
  }
  .openBtn {
    width: 100px;
    height: 32px;
    background: #FF3000;
    box-sizing: border-box;
    border-radius: 2px;
    margin: 50px 0 0 270px;
    cursor: pointer;
    .open {
      line-height: 32px;
      font-weight: 500;
      font-size: 14px;
      color: #FFFFFF;
      text-align: center;
    }
  }
}
</style>