// src/directives/v-visible.js

export default {
  mounted(el, binding) {
      const options = {
        root: null, // 使用视口作为根
        threshold: 0.1 // 当至少 10% 的元素在视口中时触发回调
      };
      const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            observer.unobserve(el);
            binding.value.fn(el,binding.value.data)
            //binding.value(el,true); // 元素可见时，调用传入的回调函数
          } else {
            //binding.value(el,false); // 元素不可见时，调用传入的回调函数
          }
        });
      }, options);
      observer.observe(el);
    }
  };
