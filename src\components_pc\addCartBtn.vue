<!-- 加减购物车按钮，使用示例在temprow.vue组件 -->
<template>
  <div class="btn-container">
    <div class="content">
      <div v-if="productData.status != 2" class="spread-add">
        <div class="reduce" @click.stop.prevent="addProductCart('min');bntClick('减',1)">-</div>
        <div class="cont">
          <input
           @focus="bntClick(productValue,2)"
            @click.stop.prevent
            class="input-val"
            :class="'input-val'+goodsId"
            type="tel"
            :value="productValue"
            @change="inputCart"
            @blur="handleCheckValue"
            :disabled="productData.status === 2"
          />
        </div>
        <div class="plus" @click.stop.prevent="addProductCart('add');bntClick('加',3)">+</div>
      </div>
      <div class="addPurchaseOrder soudOutBtn" v-if="productData.status === 2">已售罄</div>
      <div class="addPurchaseOrder skBtn" v-else-if="goodsType === 'actSk'" @click.stop.prevent="bntClick('立即抢购',4);addProductCart();">立即抢购</div>
      <div class="addPurchaseOrder pgbyBtn" v-else-if="goodsType === 'actPgby'" @click.stop.prevent="bntClick('去抢购',4);toDetail(productData.id);">去抢购</div>
      <div class="addPurchaseOrder" v-else @click.stop.prevent="bntClick('加入采购单',4);addProductCart();">加入采购单</div>
    </div>
  </div>
</template>

<script setup>
import { putRequest } from "@/http_pc/api";
import { ElMessage } from 'element-plus';
import { actionTracking } from '@/config/eventTracking';
import { onMounted, ref, watch, getCurrentInstance,computed  } from "vue";
import { useStore } from 'vuex';
import { formatDate } from '@/utils/index';
  const props = defineProps({
    productData: {
      default: {}
    },
    trackInfo: {
      default: {}
    },
    goodsType: {
      default: ''
    } ,
    jgAgentInfo: {
      default: {}
    },
    keyWord: {
      default: "",
    },
    searchSortStrategyId: {
      default: ""
    },
    index: {
      default: ''
    },
    qtData: {
      default: {}
    },
    scmE:{
      default: ''
    }
  });
  const qtData=computed(()=>{
    return  props.qtData;
  })
  const { proxy } = getCurrentInstance();
  const store = useStore();
  const productValue = ref('');
  const is_split = ref('');
  const goodsId = ref('');
  const med_num = ref('');
  const isPack = ref(false); // 套餐
  onMounted(() => {
    goodsId.value = (props.productData || {}).id;
    productValue.value = (props.productData || {}).cartProductNum ? (props.productData || {}).cartProductNum : 0;
    is_split.value = (props.productData || {}).isSplit;
    med_num.value = parseInt((props.productData || {}).mediumPackageNum);
  })

  const postCartData = (val) => {
      // WS.Bus.loading = true;
      console.log('proxy.$merchantId', proxy.$merchantId);
      if (val > 0) {
        const config = isPack.value == true ? {
          'merchantId': proxy.$merchantId,
          'amount': val,
          "packageId": goodsId.value
        } : { 'merchantId': proxy.$merchantId, 'amount': val, "skuId": goodsId.value }
        let jgInfo = JSON.parse(sessionStorage.getItem("jgInfo")) || null;
        if (jgInfo) {
          jgInfo.direct = "1";
          config.mddata = JSON.stringify(jgInfo);
        }
        if (window.fwData) {
          config.qtdata = JSON.stringify({
            spm_cnt: window.fwData['spm_cnt'],
            scm_cnt: window.fwData['scm_cnt']
          })
          if (qtData.value.commonName == "BigSearch") {
            config.qtdata = JSON.stringify({
              spm_cnt: window.fwData['spm_cnt'],
              scm_cnt: window.fwData['scm_cnt'],
              qt_list_data:JSON.parse(window.fwData['qt_list_data']),
              qt_sku_data:JSON.parse(window.fwData['qt_sku_data']) 
            })
          }
        }
        putRequest('post', '/merchant/center/cart/changeCart.json', config).then((res) => {
          // WS.Bus.loading = false;
          if (res.status == "success") {
            if (res.data.qty != val) {
              productValue.value = parseInt(res.data.qty);
            }
            
            if (res.dialog != null) {
              if (res.dialog.style == 20) {
                ElMessage.error(res.dialog.msg)
              } else {
                ElMessage.error(res.dialog.msg)
              }
            }
            if (res.data.message) {
              ElMessage.error(res.data.message);
            } else {
              // ElMessage.success('加购成功！')
              store.commit('app/changeprompt', { promptmsg: '加购成功！', showprompt: true })
              getCartNum();
            }

            // try {
            //   const addconfig = isPack.value == true ? {
            //     proid: goodsId.value,
            //     pronum: productValue.value,
            //     isAdd: 1,
            //     type: 1
            //   } : { proid: goodsId.value, pronum: productValue.value, isAdd: 1 }
            //   // window.webkit.messageHandlers.addPlanNumber.postMessage(addconfig);//ios
            // } catch (erro) {

            // }
            ;
          } else {
            productValue.value = 0;
            ElMessage.error(res.errorMsg || res.msg || res.dialog.msg);
          }
        }).catch((err) => {
          console.log('errrrrr', err);
          // WS.Bus.loading = false;
        })
      } else {
        const config = isPack.value == true ? { "packageIds": goodsId.value } : { "ids": goodsId.value }
        putRequest('post', `/app/batchRemoveProductFromCart`, config).then((res) => {
          if (res.status == "success") {
            productValue.value = 0;
            // try {
            //   const addconfig = isPack.value == true ? {
            //     proid: goodsId.value,
            //     pronum: 0,
            //     isAdd: 1,
            //     type: 1
            //   } : { proid: goodsId.value, pronum: 0, isAdd: 1 }
            //   window.webkit.messageHandlers.addPlanNumber.postMessage(addconfig);//ios
            // } catch (erro) {

            // }
            // ;
            // try {
            //   if (isPack.value == true) {
            //     window.hybrid.addPlanNumber(goodsId.value, 0, 1, 1); //android
            //   } else {
            //     window.hybrid.addPlanNumber(goodsId.value, 0, 1); //android
            //   }
            // } catch (erro) {

            // }
            // ;
          }
        })
      }
    }

  const getCartNum = () => {
    putRequest('get', '/merchant/center/cart/getCartNum', {
      r: Math.random(),
      num:0
    }).then((res) => {
      if(res.status === "success") {
        const numId = window.parent.document.getElementById('rigthCartNum');
        console.log('加购成功之后', numId);
        if (numId && res.num) {
          numId.className = 'topp cycle2';
          numId.innerText = res.num;
        }
      }
    })
  }
  const detailUrl = import.meta.env.VITE_BASE_URL_PC;
  const toDetail = (id) => {
    // actionTracking('pc_page_CommodityDetails', {
    //   spid: props.trackInfo.spid,
    //   sid: props.trackInfo.sid,
    //   sptype: props.trackInfo.sptype,
    //   user_id: merchantId,
    //   commodityId: id,
    // })
    postCartData(productValue.value);
    window.open(`${detailUrl}search/skuDetail/${id}.htm`);
  }
  
  const addProductCart = (type) => {
    if (type === "add") {
      productValue.value += med_num.value;
      return false;
      // this.addType = 3;
    } else if (type === "min") {
      if (productValue.value > 0) {
        productValue.value = is_split.value == 1 ? productValue.value - 1 : productValue.value - med_num.value;
        // this.addType = 1;
      }
      return false;
    }
    if (productValue.value == 0) {
      ElMessage.error('请输入购买数量！');
      return false;
    }
    actionTracking('加入采购单', {
      spid: props.trackInfo.spid,
      sid: props.trackInfo.sid,
      sptype: props.trackInfo.sptype,
      user_id: proxy.$merchantId,
      commodityId: goodsId.value,
      search_sort_strategy_id: props.trackInfo.searchSortStrategyId || null,
    })
    sessionStorage.setItem("jgInfo", JSON.stringify({
      ...props.jgAgentInfo,
      // rank: (props.pageNum - 1) * 20 + props.index + 1,
      rank: props.index + 1,
      search_sort_strategy_id:props.searchSortStrategyId || null,
      operation_id: props.productData.operationId && props.productData.operationId != 'null' ? `${props.productData.operationId}` : null, 
      operation_rank: props.productData.operationId && props.productData.operationId != 'null' ? 1 : null,
      list_position_type:`${props.productData.positionType}`,
      list_position_typename:props.productData.positionTypeName,
      product_id:props.productData.id,
      product_name:props.productData.productName,
      product_first:`${props.productData.categoryFirstId}`,
      product_present_price:getPrice(props.productData),
      product_type:`${props.productData.productType}`,
      product_shop_code:props.productData.shopCode,
      product_shop_name:props.productData.shopName,
    }))
    window.AnalysysAgent.track("action_product_button_click", {
      ...props.jgAgentInfo,
      rank: props.index + 1,
      key_word: props.keyWord,
      search_sort_strategy_id: props.searchSortStrategyId && props.searchSortStrategyId != 'null' ? `${props.searchSortStrategyId}` : null,
      operation_id: props.productData.operationId && props.productData.operationId != 'null' ? `${props.productData.operationId}` : null, 
      operation_rank: props.productData.operationId && props.productData.operationId != 'null' ? 1 : null,
      list_position_type:`${props.productData.positionType}`,
      list_position_typename:`${props.productData.positionTypeName}`,
      product_id: props.productData.id,
      product_name:`${props.productData.productName}`,
      product_first:`${props.productData.categoryFirstId}`,
      product_number: productValue.value,
      product_present_price: getPrice(props.productData),
      product_type:`${props.productData.productType}`,
      product_shop_code:props.productData.shopCode,
      product_shop_name:props.productData.shopName,
      btn_name: props.goodsType === 'actSk' ? "立即抢购" : "加入采购单",
      btn_desc: "列表页",
      direct: "1"
    });
    window.AnalysysAgent.track("add_to_cart", {
      ...props.jgAgentInfo,
      rank: props.index + 1,
      key_word: props.keyWord,
      search_sort_strategy_id: props.searchSortStrategyId && props.searchSortStrategyId != 'null' ? `${props.searchSortStrategyId}` : null,
      operation_id: props.productData.operationId && props.productData.operationId != 'null' ? `${props.productData.operationId}` : null, 
      operation_rank: props.productData.operationId && props.productData.operationId != 'null' ? 1 : null,
      list_position_type:`${props.productData.positionType}`,
      list_position_typename:`${props.productData.positionTypeName}`,
      product_id: props.productData.id,
      product_name:`${props.productData.productName}`,
      product_first:`${props.productData.categoryFirstId}`,
      product_number: productValue.value,
      product_present_price: getPrice(props.productData),
      product_type:`${props.productData.productType}`,
      product_shop_code:props.productData.shopCode,
      product_shop_name:props.productData.shopName,
      direct: "1",
      add_cart_time: formatDate(new Date() * 1, 'yyyy-MM-dd hh:mm:ss')
    });
    window.AnalysysAgent.track("action_list_product_click", {
      ...props.jgAgentInfo,
      rank: props.index + 1,
      key_word: props.keyWord,
      search_sort_strategy_id: props.searchSortStrategyId && props.searchSortStrategyId != 'null' ? `${props.searchSortStrategyId}` : null,
      operation_id: props.productData.operationId && props.productData.operationId != 'null' ? `${props.productData.operationId}` : null, 
      operation_rank: props.productData.operationId && props.productData.operationId != 'null' ? 1 : null,
      list_position_type:`${props.productData.positionType}`,
      list_position_typename:`${props.productData.positionTypeName}`,
      product_id: props.productData.id,
      product_name:`${props.productData.productName}`,
      product_first:`${props.productData.categoryFirstId}`,
      product_present_price: getPrice(props.productData),
      product_type:`${props.productData.productType}`,
      product_shop_code:props.productData.shopCode,
      product_shop_name:props.productData.shopName,
    });
    postCartData(productValue.value);
  }
  const inputCart = (e) => {
    let num = parseInt(e.target.value);
    num = num > 0 ? num : 0;
    productValue.value = num;
    // this.addType = 2;
  }
  const getPrice = (item) => {
    if (item.actPt) {
      if (item.actPt.stepPriceStatus == 1) {
        //阶梯\
        return item.actPt.minSkuPrice;
      } else if (item.actPt.stepPriceStatus == 2 && item.actPt.assembleStatus == 1) {
        return item.actPt.assemblePrice;
      }
      return ''
    } else if (item.actPgby) {
      return item.actPgby.assemblePrice;
    } else if (item.priceType == 2 && item.skuPriceRangeList) {
      return item.skuPriceRangeList[item.skuPriceRangeList.length -1].price;
    } else {
      return item.fob
    }
  }
  const handleCheckValue = (e) => {
    let num = parseInt(e.target.value);
    if (is_split.value == 0) {
      const remainder = num % med_num.value;
      if (remainder > 0) {
        productValue.value = num - remainder;
      }
    }
  }
  let scmEShopDetail =""
const bntClick = (text, index) => {
  // alert(qtData.value.commonName)
  //所有商品按钮点击qt埋点
  //商品按钮点击+商品点击(商品详情页)
  if (qtData.value.commonName == "productDetail" && qtData.value.isShopDetail && window.parent.getSpmE) {
    scmEShopDetail = props.scmE + proxy.scmEActive(6)
    window.fwData = {
      "spm_cnt": `1_4.productDetail_${qtData.value.id}-<EMAIL>@${qtData.value.index + 1}_btn@${index}.${window.parent.getSpmE()}`,
      "scm_cnt": `productBusiness.${props.searchSortStrategyId||'0'}.all_0.prod-${(props.productData || {}).id}_text-${text}.${scmEShopDetail}`,//yz 商品组和人群scme14位
      // "result_cnt": qtData.value.result_cnt,
      "product_id": (props.productData || {}).id,
      "product_name": (props.productData || {}).showName
    }
    aplus_queue.push({
      'action': 'aplus.record',
      'arguments': ['action_product_button_click', 'CLK', window.fwData]
    });
    aplus_queue.push({
      'action': 'aplus.record',
      'arguments': ['action_list_product_click', 'CLK', {
        "spm_cnt": `1_4.productDetail_${qtData.value.id}-<EMAIL>@${qtData.value.index+1}.${window.parent.getSpmE()}`,
        "scm_cnt": `productBusiness.${props.searchSortStrategyId||'0'}.all_0.prod-${(props.productData || {}).id}.${scmEShopDetail}`,//yz 商品组和人群scme14位
        // "result_cnt": qtData.value.result_cnt,
        "product_id": (props.productData || {}).id,
        "product_name": (props.productData || {}).showName
      }]
    });
  }
  if (qtData.value.commonName == "BigSearch" && window.parent.getSpmE) {
    scmEShopDetail = props.scmE + proxy.scmEActive(6)
    if(qtData.value.isOperation){
      try {
      let qtSkuData=JSON.parse((props.productData || {}).qtSkuData)
            const getStr = (str) => {
            return str.replace(/[^a-zA-Z0-9]/g, "")
          }
          window.fwData ={
            "spm_cnt":`<EMAIL>@${qtSkuData.rank}_prod@${qtSkuData.rank}-${qtSkuData.operation_rank}_btn@${index}.${window.parent.getSpmE()}`,
            "scm_cnt":`search.${props.searchSortStrategyId||'0'}.${props.productData.operationCustomerGroupId||"all"}_${getStr(props.productData.operationExhibitionId)}.prod-${(props.productData || {}).id}_text-${text}.${scmEShopDetail}`,
            "product_id": (props.productData || {}).id,
            "product_name":(props.productData || {}).showName,
            "qt_list_data": props.qtData.qtListData,
            'qt_sku_data': (props.productData || {}).qtSkuData
          }
          console.log(window.fwData,"window.fwData")
          aplus_queue.push({
                    'action': 'aplus.record',
                    'arguments': ['action_product_button_click', 'EXP',window.fwData]
              });
          aplus_queue.push({
                    'action': 'aplus.record',
                    'arguments': ['action_list_product_click', 'CLK', {
                      "spm_cnt":`<EMAIL>@${qtSkuData.rank}_prod@${qtSkuData.rank}-${qtSkuData.operation_rank}.${window.parent.getSpmE()}`,
                      "scm_cnt":`search.${props.searchSortStrategyId||'0'}.${props.productData.operationCustomerGroupId||"all"}_${getStr(props.productData.operationExhibitionId)}.prod-${(props.productData || {}).id}.${scmEShopDetail}`,
                      "product_id": (props.productData || {}).id,
                      "product_name":(props.productData || {}).showName,
                      "qt_list_data": props.qtData.qtListData,
                      'qt_sku_data': (props.productData || {}).qtSkuData
                    }]
        });
       } catch (error) {
        
       }
    }else{
        window.fwData = {
        "spm_cnt": `1_4.searchResult_0-0_0.${props.qtData.goodsType == 3 ? 'recommendList' : 'searchList'}@5.prod@${qtData.value.index + 1}_btn@${index}.${window.parent.getSpmE()}`,
        "scm_cnt": `${props.qtData.goodsType == 3 ? 'recommend' : 'search'}.${props.searchSortStrategyId||'0'}.all_0.prod-${(props.productData || {}).id}_text-${text}.${scmEShopDetail}`,
        // "result_cnt": qtData.value.result_cnt,
        "product_id": (props.productData || {}).id,
        "product_name": (props.productData || {}).showName,
        "qt_list_data": props.qtData.qtListData,
        'qt_sku_data': (props.productData || {}).qtSkuData

      }
      aplus_queue.push({
        'action': 'aplus.record',
        'arguments': ['action_product_button_click', 'CLK', window.fwData]
      });
      aplus_queue.push({
        'action': 'aplus.record',
        'arguments': ['action_list_product_click', 'CLK', {
          "spm_cnt": `1_4.searchResult_0-0_0.${props.qtData.goodsType == 3 ? 'recommendList' : 'searchList'}@5.prod@${qtData.value.index+1}.${window.parent.getSpmE()}`,
          "scm_cnt": `${props.qtData.goodsType == 3 ? 'recommend' : 'search'}.${props.searchSortStrategyId||'0'}.all_0.prod-${(props.productData || {}).id}.${scmEShopDetail}`,
          // "result_cnt": qtData.value.result_cnt,
          "product_id": (props.productData || {}).id,
          "product_name": (props.productData || {}).showName,
          "qt_list_data": props.qtData.qtListData,
          'qt_sku_data': (props.productData || {}).qtSkuData
        }]
      });
    }
    
  }
}

</script>

<style lang="scss" scoped>
div {
  box-sizing: border-box;
}

.btn-container {
  .addPurchaseOrder {
    z-index: 9;
    cursor: pointer;
    width: 100px;
    height: 42px;
    margin-left: 10px;
    background: #00B955;
    border-radius: 6px;
    font-size: 16px;
    font-family: MicrosoftYaHei;
    text-align: center;
    color: #ffffff;
    line-height: 42px;
    flex-grow: 1;
  }
  .skBtn {
    background-image: linear-gradient(90deg, #FEA527 0%, #FE5427 100%);
  }
  .pgbyBtn {
    background-image: linear-gradient(-64deg, #FF223B 0%, #FF834A 100%, #FF834A 100%, #FF834A 100%, #FF834A 100%);
  }
  .soudOutBtn {
    background: #838A93;
  }
}

.content {
  display: flex;
  align-items: center;
}

.spread-add {
  display: flex;
  display: -webkit-flex;
  flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  box-sizing: border-box;
  z-index: 9;
  border-radius: 6px;
  border: 1px solid #d7d7d7;
  overflow: hidden;
  .plus,
  .reduce {
    width: 32px;
    height: 42px;
    line-height: 42px;
    color: #757575;
    text-align: center;
    background: #EFEFEF;
    cursor: pointer;
    font-size: 20px;
  }

  .plus {
    border-left: 1px solid #d7d7d7;
  }

  .reduce {
    border-right: 1px solid #d7d7d7;
  }

  .cont {
    width: 42px;
    .input-val {
      box-sizing: border-box;
      padding: 0;
      width: 100%;
      text-align: center;
      color: #292933;
      border: none;
      outline: none;
      font-size: 16px;
      font-family: MicrosoftYaHei;
      text-align: left;
      color: #333333;
      line-height: 42px;
      text-align: center;
    }
  }
}
</style>
