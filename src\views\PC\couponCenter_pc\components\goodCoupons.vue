<template>
  <div class="list" v-if="couponList.length">
    <crossStoreCoupon :couponInfo="item" v-for="item in couponList" :key="item.id" />
    <div class="bottom-tip">
      <span @click="handleNextPage">{{ bottomTip }}</span>
    </div>
  </div>
  <div class="noGoods" v-else>
    <div>
      <img src="../../../../assets/images/xyy-sorry.png" alt="">
      <span class="text">暂无优惠券</span>
    </div>
  </div>
</template>

<script setup>
  import crossStoreCoupon from './crossStoreCoupon.vue'
  import { ref, onMounted, getCurrentInstance } from 'vue'
  import { useStore } from 'vuex';
  import { receiveCenterProductCoupon } from '@/http_pc/api';
  
  const store = useStore();
  const { proxy } = getCurrentInstance();
  const couponList = ref([]);
  const pageNo = ref(1);
  const loading = ref(false);
  const isEnd = ref(false);
  const bottomTip = ref('');

  // 上拉加载
  function pullUpLoading() {
    let scrollHeight = document.documentElement.scrollHeight;
    let scrollTop = window.scrollY;//滚动的高度；
    let clientHeight = document.documentElement.clientHeight;//滚动的高度；
    if (scrollHeight - scrollTop - clientHeight < 30) {
      console.log('滚到底部，可以加载下一页3');
      if (!isEnd.value && !loading.value) {
        getInfo();
      }
    }
  };

  // 获取信息
  function getInfo() {
    loading.value = true;
    store.commit('app/showLoading', true);
    receiveCenterProductCoupon({
      merchantId: proxy.$merchantId,
      pageNo: pageNo.value,
      pageSize: 20,
    }).then((res) => {
      loading.value = false;
      store.commit('app/showLoading', false);
      couponList.value = couponList.value.concat(res.couponList || []);
      isEnd.value = res.isEnd;
      pageNo.value = res.nextPage;
      bottomTip.value = isEnd.value ? '没有更多了' : '查看更多';
    }).catch(() => {
      loading.value = false;
      store.commit('app/showLoading', false);
    })
  }

  onMounted(() => {
    getInfo();
    window.addEventListener('scroll', pullUpLoading);
  })

  function handleNextPage() {
    if (!isEnd.value) {
      getInfo();
    }
  }

</script>

<style scoped lang="scss">
.btns {
  .el-button {
    padding: 12px 25px;
  }
  .el-button:hover {
    color: #00B377;
  }
  .el-button + .el-button {
    margin-left: 18px;
  }
  .el-button--primary {
    background: #00B377;
    --el-button-background-color: #00B377;
    --el-button-border-color: #00B377;
  }
  .el-button--primary:hover {
    color: #fff;
  }
  .el-button:active {
    color: #00B377;
    border-color: #00B377;
  }
}
.list {
  width: 100%;
  height: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 20px;
}
.noGoods{
  position: relative;
  text-align: center;
  height: 210px;
  margin: 200px 0;
  img{
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 264px;
    height: 174px;
  }
  .text{
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    color: #A9AEB7;
  }
}
</style>
