<template>
  <div class="wrapperBox">
    <div class="headerTop">
      <div class="headerBox">
        <img
          class="hraderImg"
          src="../../../assets/images/intelligentProcurement/header-1.png"
          alt=""
        />
        <el-button
            ref="uploadModule"
            style="color: white; background-color: #00b955; margin-right: 20px"
            @click="setRuleNew"
            >导入采购计划单</el-button
          >
      </div>
    </div>
    <div class="content" v-loading="loading">
      <!-- <div class="contentBtn">
        <div>
          <span class="contentTitle">采购计划单</span>
          <el-button
            type="primary"
            style="margin-left: 10px"
            @click="tableColDialog('采购计划单')"
            >导入采购单</el-button
          >
          <el-button
            type="primary"
            style="margin: 0 10px"
            @click="tableColDialog('计划单')"
            >导入计划单</el-button
          >
          <el-button @click="download">下载模板</el-button>
        </div>
        <div class="xx-primary" @click="toShowRefresh" style="margin: 10px">
          <div style="color: white">刷新</div>
        </div>
      </div> -->
      <div class="content-info">
        <div
           
               style="margin:10px 0;font-size: 16px;;color:#333333;font-weight: 600;"
            >
              我的比价规则
            </div>
        <div style="display: flex; justify-content: space-between; align-items: center">
          <div>
            <span >比价商家：</span>
            <span style="font-weight: 600;font-size: 14px;">{{getMerchant}}</span>
            <span style="margin-left:20px;font-size: 14px;">活动类型：</span>
            <span style="font-weight: 600;font-size: 14px;">{{getActiveType}}</span>
            <span style="margin-left:20px;font-size: 14px;">采购条件：</span>
            <span style="font-weight: 600;font-size: 14px;">{{ruleArr}}</span>
          </div>
         
            <el-button type="success" plain @click="setRule">配置比价规则</el-button>
        </div>
      </div>
      <!-- <div class="rules-modules">
        <div class="rules-modules-left">
          <div class="rules-modules-left-left">
            <div
              style="
                font-size: 16px;
                font-weight: 700;
                margin-left: 20px;
                margin-top: 10px;
              "
            >
              我的比价规则
            </div>
            <div
              class="rules-modules-left-shops"
              style="font-size: 14px; margin-left: 20px; margin-top: 10px"
            >
              <div>比价商家：</div>
              <div style="color: #00b955">
                {{ ruleTemp.shop.length == 0 ? "无" : ruleTemp.shop.join(",") }}
              </div>
            </div>
            <div
              class="rules-modules-left-shops"
              style="font-size: 14px; margin-left: 20px; margin-top: 10px"
            >
              <div>比价条件：</div>
              <div style="color: #00b955">{{ ruleTemp.condition }}</div>
            </div>
          </div>
          <el-button
            style="color: white; background-color: #00b955; margin-right: 20px"
            @click="setRule"
            >配置比价规则</el-button
          >
        </div> -->

        <!-- <div class="rules-modules-right">
          <div class="rules-modules-right-left">
            <div
              class="rules-modules-left-shops"
              style="margin-left: 20px; margin-top: 10px"
            >
              <div style="font-size: 16px; font-weight: 700">采购计划单</div>
              <el-button
                ref="dowloadModule"
                @click="download"
                type="text"
                style="margin-left: 10px; margin-top: -7px;"
                >下载模板</el-button
              >
            </div>
            <div
              style="
                font-size: 14px;
                margin-left: 20px;
                margin-top: 4px;
                width: 346px;
                letter-spacing: 0;
                line-height: 22px;
              "
            >
              文件格式 .xlsx或
              .xsl，大小不超过{{MaxM}}M，行数不超过{{MaxRow}}行；文件中必需包含通用名、规格和厂家。
            </div>
          </div>
       
        </div> -->
        <!-- <div class="x-rules">
          <div style="margin-right: 30px">我的比价规则</div>
          <div>比价商家：</div>
          <div>
            {{ ruleTemp.shop.length == 0 ? "无" : ruleTemp.shop.join(",") }}
          </div>
          <div style="margin-left: 30px">比价条件：</div>
          <div>{{ ruleTemp.condition }}</div>
          <el-button
            style="
              color: white;
              background: transparent;
              border-color: white;
              margin-left: 30px;
            "
            @click="setRule"
            >配置比价规则</el-button
          >
        </div> -->
      <!-- </div> -->
      <!-- <div
        style="
          width: 100%;
          height: 10px;
          background-color: #f4f4f4;
        "
      ></div> -->
      <div class="progressContainer" v-if="showImportVis">
        <div style="flex:1">
          <div style="margin-bottom:13px;">
           
          {{ dataTotalNum  }} 条数据处理中，已有  <span style="color:#00B955">{{matchedNum}}</span> 条匹配完成…
        </div>
        <div class="newpro">
          <el-progress
           :text-inside="true"
           :stroke-width="15"
           :percentage="loadingProgress"
    >
   <img src="@/assets/images/智能采购-有进度条.png" alt="" >
    </el-progress>
        </div>
    
        </div>
        
   
          
    <div class="progress-right">{{ loadingProgress }}<span style="font-size: 14px;position: relative;top:2px">%</span></div>
    
   
       
        
      </div>
      <div class="float-row-view-row">
        <div
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-left: 10px;
          "
        >
          <span
            style="
              width: 70px;
              margin-left: 10px;
              font-weight: 400;
              font-size: 12px;
              color: #000000a6;
            "
            >供应商：</span
          >
          <el-select
            style="width: 200px"
            v-model="matchBaseParamVO.shopCodes"
            @change="onHandleOkRule(1)"
            multiple
            clearable
          >
            <el-option
              v-for="(item, i) in supplierList"
              :key="i"
              :label="item.name"
              :value="item.shopCode"
            ></el-option>
          </el-select>
        </div>
        <div
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-left: 10px;
          "
        >
          <span
            style="
              width: 70px;
              margin-left: 10px;
              font-weight: 400;
              font-size: 12px;
              color: #000000a6;
            "
            >是否可买：</span
          >
          <el-select
            ref="isCanBuyEl"
            style="width: 200px"
            v-model="matchBaseParamVO.canBuyStr"
            @change="onHandleOkRule(2)"
            clearable
          >
            <el-option label="可买&不可买" :value="'all'"></el-option>
            <el-option label="可买" :value="'canBuy'"></el-option>
            <el-option label="不可买" :value="'notCanBuy'"></el-option>
            <el-option label="缺少必填字段" :value="'deficiency'"></el-option>
          </el-select>
        </div>
        <div
          style="
            margin-left: 20px;
            margin-right: 10px;
            color: #eeeeee;
            font-weight: bold;
          "
        >
          |
        </div>
        <el-checkbox-group
          v-model="checkArr"
          style="display: inline-block; margin-left: 10px"
        >
          <el-checkbox
            :label="1"
            style="font-weight: 400; font-size: 12px; color: #000000a6"
            >近效期</el-checkbox
          >
          <el-checkbox
            :label="2"
            style="font-weight: 400; font-size: 12px; color: #000000a6"
            >有优惠券</el-checkbox
          >
          <el-checkbox
            :label="3"
            style="font-weight: 400; font-size: 12px; color: #000000a6"
            >买过的店</el-checkbox
          >
        </el-checkbox-group>
        <el-button style="margin-left:50px;" type="success" plain @click="openRuleDialog">更换比价规则</el-button>
        <el-button
          style="margin-left:40px;"
          icon="refresh"
          @click="pageRefresh()"
          :disabled="isDisableRefresh"
          >刷新</el-button
        >
      </div>
    
      <!-- <div class="float-row-view-row">
        <div
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-left: 10px;
          "
        >
          <span
            style="
              width: 70px;
              margin-left: 10px;
              font-weight: 400;
              font-size: 12px;
              color: #000000a6;
            "
            >供应商：</span
          >
          <el-select
            style="width: 200px"
            v-model="matchBaseParamVO.shopCodes"
            @change="onHandleOkRule(1)"
            multiple
            clearable
          >
            <el-option
              v-for="(item, i) in supplierList"
              :key="i"
              :label="item.name"
              :value="item.shopCode"
            ></el-option>
          </el-select>
        </div>
        <div
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-left: 10px;
          "
        >
          <span
            style="
              width: 70px;
              margin-left: 10px;
              font-weight: 400;
              font-size: 12px;
              color: #000000a6;
            "
            >是否可买：</span
          >
          <el-select
            ref="isCanBuyEl"
            style="width: 200px"
            v-model="matchBaseParamVO.canBuyStr"
            @change="onHandleOkRule(2)"
            clearable
          >
            <el-option label="可买&不可买" :value="'all'"></el-option>
            <el-option label="可买" :value="'canBuy'"></el-option>
            <el-option label="不可买" :value="'notCanBuy'"></el-option>
            <el-option label="缺少必填字段" :value="'deficiency'"></el-option>
          </el-select>
        </div>
        <div
          style="
            margin-left: 20px;
            margin-right: 10px;
            color: #eeeeee;
            font-weight: bold;
          "
        >
          |
        </div>
        <el-checkbox-group
          v-model="checkArr"
          style="display: inline-block; margin-left: 10px"
        >
          <el-checkbox
            :label="1"
            style="font-weight: 400; font-size: 12px; color: #000000a6"
            >近效期</el-checkbox
          >
          <el-checkbox
            :label="2"
            style="font-weight: 400; font-size: 12px; color: #000000a6"
            >有优惠券</el-checkbox
          >
          <el-checkbox
            :label="3"
            style="font-weight: 400; font-size: 12px; color: #000000a6"
            >买过的店</el-checkbox
          >
        </el-checkbox-group>
        <el-button style="margin-left:50px;" type="success" plain @click="openRuleDialog">更换比价规则</el-button>
        <el-button
          style="margin-left:40px;"
          icon="refresh"
          @click="pageRefresh()"
          :disabled="isDisableRefresh"
          >刷新</el-button
        >
      </div> -->
      <!-- <div class="float-row-view-no-row" v-if="isTop === true">
        <div
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-left: 10px;
          "
        >
          <span
            style="
              width: 60px;
              font-weight: 400;
              font-size: 12px;
              color: #000000a6;
            "
            >供应商：</span
          >
          <el-select
            style="width: 200px"
            v-model="matchBaseParamVO.shopCodes"
            @change="onHandleOkRule(3)"
            multiple
            clearable
          >
            <el-option
              v-for="(item, i) in supplierList"
              :key="i"
              :label="item.name"
              :value="item.shopCode"
            ></el-option>
          </el-select>
        </div>
        <div
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-left: 10px;
          "
        >
          <span
            style="
              width: 70px;
              margin-left: 10px;
              font-weight: 400;
              font-size: 12px;
              color: #000000a6;
            "
            >是否可买：</span
          >
          <el-select
            ref="isCanBuyEl"
            style="width: 200px"
            v-model="matchBaseParamVO.canBuyStr"
            @change="onHandleOkRule(4)"
            clearable
          >
            <el-option label="可买&不可买" :value="'all'"></el-option>
            <el-option label="可买" :value="'canBuy'"></el-option>
            <el-option label="不可买" :value="'notCanBuy'"></el-option>
            <el-option label="缺少必填字段" :value="'deficiency'"></el-option>
          </el-select>
        </div>
        <div
          style="
            margin-left: 20px;
            margin-right: 10px;
            color: #eeeeee;
            font-weight: bold;
          "
        >
          |
        </div>
        <el-checkbox-group
          v-model="checkArr"
          style="display: inline-block; margin-left: 10px"
        >
          <el-checkbox
            :label="1"
            style="font-weight: 400; font-size: 12px; color: #000000a6"
            >近期效</el-checkbox
          >
          <el-checkbox
            :label="2"
            style="font-weight: 400; font-size: 12px; color: #000000a6"
            >有优惠券</el-checkbox
          >
          <el-checkbox
            :label="3"
            style="font-weight: 400; font-size: 12px; color: #000000a6"
            >买过的店</el-checkbox
          >
        </el-checkbox-group>
        <el-button style="margin-left:50px;"  type="success" plain @click="openRuleDialog">更换比价规则</el-button>
        <el-button
          style="margin-left: 40px"
          icon="refresh"
          @click="pageRefresh()"
          >刷新</el-button
        >
      </div> -->
      <el-tabs v-model="activeName" class="demo-tabs" @tab-change="tabChange" v-if="list.length||showImportVis">

        <el-tab-pane name="1" >
          <template v-slot:label>
        <span style="padding-left:10px">匹配成功（{{  matchSuccessList.length }}）</span>
      </template>
     
          <div class="goodsBox"  >
        
        <ExcelList
          v-if="matchSuccessList.length"
          ref="excelItemRef1"
          :excelList="matchSuccessList"
          :allChecked="allChecked"
          :needChangeChecked="needChangeChecked"
          :refresh="onRefresh"
          :recalculate="onRecalculate"
          :onSetRule="setRule"
          :activeName="1"
          :fatherActiveName="activeName"
        />
        
        <el-empty :image-size="200"        v-else style="background-color: #fff;height:90vh"/>
      
          </div>
      
      </el-tab-pane>

      <el-tab-pane name="2">
        <template v-slot:label >
        <span style="padding-left:10px">未匹配到商品（{{  matchFailList.length }}）</span>
      </template>
    
        <div class="goodsBox"  >
        
        <ExcelList
          v-if="matchFailList.length"
          ref="excelItemRef2"
          :secondMatchList="secondMatchList"
          :excelList="matchFailList"
          :allChecked="allChecked"
          :needChangeChecked="needChangeChecked"
          :refresh="onRefresh"
          :recalculate="onRecalculate"
          :onSetRule="setRule"
          :activeName="2"
          :fatherActiveName="activeName"
          :requestId="requestId"
          @secondMatchToFather = "secondMatchToFather"
        />
      
        <el-empty :image-size="200"        v-else style="background-color: #fff;height:90vh"/>
        </div>
        
      </el-tab-pane>

      <el-tab-pane name="3">
        <template v-slot:label  >
        <span style="padding-left:10px">失效商品（{{  loseEffectList.length }}）</span>
      </template>
    
        <div class="goodsBox" >
        
        <ExcelList
          v-if="loseEffectList.length"
          ref="excelItemRef3"
          :excelList="loseEffectList"
          :allChecked="allChecked"
          :needChangeChecked="needChangeChecked"
          :refresh="onRefresh"
          :recalculate="onRecalculate"
          :onSetRule="setRule"
          :activeName="3"
          :fatherActiveName="activeName"
        />
        <el-empty :image-size="200"        v-else style="background-color: #fff;height:90vh"/>
        </div>
       
      </el-tab-pane>

    </el-tabs>
    <div v-else class="noGoods">
        <div v-if="matchBaseParamVO.canBuyStr !== 'deficiency'">
          <img src="../../../assets/images/xyy-sorry.png" alt="" />
          <p class="text">暂无采购计划~</p>
          <div class="row-line" style="top: 270px">
            <el-button @click="pageRefresh(1)">恢复上次采购计划</el-button>
            <el-button
              type="primary"
              plain
              @click="setRuleNew"
              style="margin-left: 20px"
              >导入采购计划单</el-button
            >
          </div>
          <!-- <p class="text" style="top: 250px">
            如需恢复上次导入的采购计划，请刷新
          </p> -->
        </div>
        <div v-else>
          <img src="../../../assets/images/xyy-sorry.png" alt="" />
          <p class="text">您上传的商品信息很完整哟～</p>
          <el-button
            type="primary"
            @click="refreshNormalGodds()"
            style="margin-top: 170px"
            >去查看全部可买商品</el-button
          >
        </div>
        </div>
    </div>
    <div
      class="footerWrapper"
     v-if="list.length||showImportVis"
    >
      <div class="footerBox" v-if="activeName!=='2'||!showAddBuyPlan">
        <div class="footerLeft">
          <el-checkbox
            v-model="allChecked"
            label="全选"
            :disabled="!hasSku"
            @change="changeAllChecked"
          />
          <!-- :indeterminate="isIndeterminate" -->
          <span @click="delGoods">删除选中商品</span>
          <el-button
            type="success"
            class="downloadPlan"
            plain
            v-if="showImportVis"
            >数据加载{{loadingProgress }}%</el-button
          >
          <el-button 
          v-else
          type="success"  
          class="downloadPlan" 
          plain          
          @click="downloadExcel"
                 ref="downLoadPlanBill"
          >
          下载采购计划单
          </el-button>
          <span style="cursor: default">共计{{ dataTotalNum }}个数据</span>
          <span style="cursor: default"
            >已选择{{ listInfo.productSelectedNum || 0 }}个数据</span
          >
        </div>
        <div class="footerRight">
          <div class="priceBox">
            <div>
              <span class="redText">{{ listInfo.shopCount || 0 }}</span
              >个商家，<span class="redText">{{
                listInfo.productSelectedNum || 0
              }}</span
              >种商品，共<span class="redText">{{
                listInfo.productTotalNum || 0
              }}</span
              >件
              <span style="margin-left: 12px"
                >合计：
                <span class="redPrice"
                  >￥{{ (listInfo.payAmount || 0).toFixed(2) }}</span
                ></span
              >
            </div>
            <div style="margin-top: 10px">
              <span>商品总价：</span>￥{{
                (listInfo.totalAmount || 0).toFixed(2)
              }}
              <span>优惠：</span>￥{{
                (listInfo.discountAmount || 0).toFixed(2)
              }}
              <span>运费：</span>￥{{
                (listInfo.freightTotalAmt || 0).toFixed(2)
              }}
            </div>
          </div>
          <div class="footerBtn">
            <div
              class="addBtn"
              :class="!hasChecked ? 'disAddBtn' : ''"
              @click="addCart"
            >
              加入购物车
            </div>
            <div
              class="settlementBtn"
              :class="!hasChecked ? 'disSettlementBtn' : ''"
              @click="submit"
            >
              去结算
            </div>
          </div>
        </div>
      </div>
      <div class="footerBox" style="width:1150px" v-else>
        <el-checkbox v-model="addAllToButPlan" :disabled="secondMatchList.length" @change="allSelectSecondMathchList" v-show="showAddBuyPlan">全选</el-checkbox>
        <el-button type="success" plain @click="addBuyPlan" v-show="showAddBuyPlan">批量加入采购计划</el-button>
      </div>
    </div>
    <PurchasePlanBill ref="purchasePlanBill" @done="purchasePlanBillDone" :maxM="MaxM" :maxRow="MaxRow">
    </PurchasePlanBill>
    <!-- 导入loading -->
    <!-- <DialogLoading
      v-if="showImportVis"
      :loadingProgress="loadingProgress"
      :cancelDialog="handleCancel"
    /> -->
    <!-- 刷新提醒 -->
    <RefreshTip
      v-if="showTipVis"
      :showTipVis="showTipVis"
      :cancelDialog="handleCancel"
      :refresh="onRefresh"
    />
    <!-- 包邮金额提示 -->
    <PostageAmountTip
      v-if="showPostageVis"
      :showPostageVis="showPostageVis"
      :unsatisfiedFreeShippingList="unsatisfiedFreeShippingList"
      :unsatisfiedStartPriceList="unsatisfiedStartPriceList"
      :cancelDialog="handleCancel"
      :confirmSubmit="onConfirmSubmit"
    />
    <!-- 设置比价规则 -->
    <SetMatchRule
      ref="setMatchRuleRef"
      :show-rule-vis="showRuleVis"
      :defalutRule="rulesOfMatch"
      :handleOkRule="onHandleOkRule"
      @ruleTemp="getRuleTemp"
      @cancelDialog="handleCancel"
      @openFirstDialog = "openFirstDialog"
    />
    <!-- 导入生成的错误文件 -->
    <ErrorFile
      v-if="showErrFile"
      :showErrFile="showErrFile"
      :errorUrl="errorUrl"
      :cancelDialog="handleCancel"
    />
    <el-dialog v-model="colDialog" :title="colData.title" width="900px">
      <div style="display: flex; align-items: center; margin-bottom: 10px">
        <el-upload
          :action="uploadUrl"
          :http-request="
            uploadFile(colData.title == '导入采购单' ? '采购计划单' : '计划单')
          "
          :show-file-list="false"
          :before-upload="beforeImportData"
        >
          <el-button
            type="primary"
            style="margin: 0 10px"
            :loading="importLoading || importLoading2"
            >{{ colData.title }}</el-button
          >
        </el-upload>
        <p>
          单次导入商品最多不超过2000条 <br />
          共计{{ colData.count }}个商品
        </p>
      </div>
      <el-form
        :inline="true"
        :model="colData.form"
        label-width="140px"
        ref="formRef"
      >
        <el-form-item
          v-for="(item, i) in colData.col"
          :key="i"
          :label="item.label"
          :prop="item.prop"
          :rules="item.rule"
        >
          <el-select
            :model-value="colData.form[item.prop]"
            @change="(val) => valueSelect(item.prop, val)"
            clearable
          >
            <el-option
              v-for="(val, n) in colData.options"
              :key="n"
              :label="val"
              :value="val"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="colDialog = false">取消</el-button>
        <el-button
          v-if="colData.title == '导入采购单'"
          type="primary"
          @click="fileSubmit(1)"
          >开始匹价</el-button
        >
        <el-button v-else type="primary" @click="fileSubmit(0)"
          >下单结算</el-button
        >
      </template>
    </el-dialog>
    <el-dialog v-model="planResultData.status" width="500px">
      <p style="fontsize: 16px; text-align: center; margin-bottom: 10px">
        <span>导入成功商品{{ planResultData.success.length }}条</span>
        <span v-if="planResultData.error.length > 0"
          >，失败{{ planResultData.error.length }}条</span
        >
      </p>
      <p
        v-if="planResultData.error.length > 0"
        style="fontsize: 16px; text-align: center"
      >
        <el-link type="success" @click="downloadErrorPro">下载失败商品</el-link>
      </p>
      <template #footer>
        <el-button @click="planResultData.status = false">取消</el-button>
        <el-button
          v-if="planResultData.success.length > 0"
          type="primary"
          @click="submitAgain"
          >继续下单</el-button
        >
      </template>
    </el-dialog>
    <el-tour v-model="showTour" @finish="tourFinish()" @close="tourClose()">
      <el-tour-step :target="dowloadModule?.$el" title="下载模板">
        <template #header>
          <div style="margin-top: 10px">
            <span
              style="
                font-weight: 600;
                font-size: 24px;
                color: #000000;
                margin-left: 10px;
              "
              >下载模板</span
            >
          </div>
        </template>
        <div
          style="
            font-size: 16px;
            color: #000000;
            width: 411px;
            margin-left: 10px;
            margin-top: 10px;
            margin-bottom: 30px;
          "
        >
          您可以下载报货计划模板，按模板中的内容填写，也可以使用自己的模板。
        </div>
      </el-tour-step>
      <el-tour-step :target="uploadModule?.$el" title="导入采购计划单">
        <template #header>
          <div style="margin-top: 10px">
            <span
              style="
                font-weight: 600;
                font-size: 24px;
                color: #000000;
                margin-left: 10px;
              "
              >导入采购计划单</span
            >
          </div>
        </template>
        <div
          style="
            font-size: 16px;
            color: #000000;
            width: 411px;
            margin-left: 10px;
            margin-top: 10px;
            margin-bottom: 30px;
          "
        >
          导入您需要的货品清单，请注意文件格式是否满足要求。原导入计划单功能已合并。
        </div>
      </el-tour-step>

      <el-tour-step :target="downLoadPlanBill?.$el" title="下载采购计划单">
        <template #header>
          <div style="margin-top: 10px">
            <span
              style="
                font-weight: 600;
                font-size: 24px;
                color: #000000;
                margin-left: 10px;
              "
              >下载采购计划单</span
            >
          </div>
        </template>
        <div
          style="
            font-size: 16px;
            color: #000000;
            width: 411px;
            margin-left: 10px;
            margin-top: 10px;
            margin-bottom: 30px;
          "
        >
          我们会根据您设置的匹价规则在您上传的采购单基础上生成您的采购计划单。
        </div>
      </el-tour-step>

      <el-tour-step :target="isCanBuyEl?.$el" title="缺少关键字段商品">
        <template #header>
          <div style="margin-top: 10px">
            <span
              style="
                font-weight: 600;
                font-size: 24px;
                color: #000000;
                margin-left: 10px;
              "
              >缺少关键字段商品</span
            >
          </div>
        </template>
        <div
          style="
            font-size: 16px;
            color: #000000;
            width: 411px;
            margin-left: 10px;
            margin-top: 10px;
            margin-bottom: 30px;
          "
        >
          导入您需要的货品清单，请注意文件格式是否满足要求。原导入计划单功能已合并。
        </div>
      </el-tour-step>

      <template #indicators="{ current, total }">
        <div style="margin-bottom: 10px; margin-left: 10px">
          <span style="font-size: 16px; color: #555555"
            >{{ current + 1 }}/{{ total }}</span
          >
        </div>
      </template>
    </el-tour>
  </div>
</template>

<script setup>
import ExcelListEmpty from "./components/excelListEmpty.vue";
import {
  matchPriceImport,
  refreshPurchasePlan,
  getMatchProgress,
  selectMatchProduct,
  exportFailedPlan,
  getSowGroundParam,
  changeCartBatch,
  importPurchasePlan,
  importPlan,
  downloadQuotation,
  matchPrice,
  clearMatchProgress,
  getTableHeaderInformation,
  downloadQuotationV2,
  inquireMatchPriceResult,
  submitMatchPrice,
  inquireRecording,
  getMaxMAndMaxRow,
  skuReplace
} from "@/http_pc/api";
import PurchasePlanBill from "./components/purchasePlanBill.vue";
import { actionTracking } from "@/config/eventTracking";
import { onMounted, ref, provide, onUnmounted, computed ,nextTick } from "vue";
import ExcelList from "./components/excelList.vue";
import DialogLoading from "./components/dataLoading.vue";
import RefreshTip from "./components/refreshTip.vue";
import PostageAmountTip from "./components/postageAmountTip.vue";
import SetMatchRule from "./components/setMatchRule.vue";
import ErrorFile from "./components/errorFile.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { exportExcel } from "../../../utils/index";
import { getFormData } from "./data";
const addAllToButPlan = ref(true);
const secondRequestId = ref('')
const showAddBuyPlan = ref(false)
const secondMatchList = ref([])
const isShowTabs = ref(false)
const activeName = ref('1')
const getMerchant = ref('')
const MaxM = ref(0)
const MaxRow = ref(0)
const dataTotalNum = ref('')
const matchedNum = ref('')
const isDisableRefresh = ref(false);
const isTopSelect = ref(false)
const isEnd = ref(false)
const requestId = ref('')
const progressDatalength = ref(0);
const showTour = ref(false);

const dowloadModule = ref();
const uploadModule = ref();
const downLoadPlanBill = ref();

const purchasePlanBill = ref(null);

const isTop = ref(false);
const isToast = ref(false)
const formRef = ref(null);
const list = ref([]);
const showRuleVis = ref(false);
const showTipVis = ref(false);
const showImportVis = ref(false);
const loading = ref(false);
const loadingProgress = ref(0);
const listInfo = ref({});
const needChangeChecked = ref(false); // change全选的时候列表的checkbox需要联动

const excelItemRef1 = ref(null);
const excelItemRef2 = ref(null);
const excelItemRef3 = ref(null);
const setMatchRuleRef = ref(null);
const hasChecked = ref(false);
const showPostageVis = ref(false);
const unsatisfiedFreeShippingList = ref([]); // 未包邮列表
const unsatisfiedStartPriceList = ref([]); // 起送价列表
const importLoading = ref(false);
const importLoading2 = ref(false);
const importLoading3 = ref(false);
const uploadUrl = "/matchPrice/importPurchasePlan";
const sowGroundParams = ref({}); // 埋点参数
const rulesOfMatch = ref({}); // 设置的比价规则
const showErrFile = ref(false);
const errorUrl = ref("");
const hasSku = ref(false);
const supplierList = ref([]);
const matchBaseParamVO = ref({
  shopCodes: [],
  canBuyStr: 'all',
  nearEffectiveFlag: -1,
  hasCouponIs: "",
  buyShopIs: "",
});
const ruleTemp = ref({
  shop: [],
  condition: "",
});
const colData = ref({
  title: "",
  form: {},
  col: [],
  file: null,
  count: 0,
  options: [],
});
const planResultData = ref({
  status: false,
  success: [],
  error: [],
});
const isCanBuyEl = ref(null);
const tourFinish = () => {
  showTour.value = false;
  matchBaseParamVO.value.canBuyStr = "";
  localStorage.setItem("auto_price_first", "1");
};
const tourClose = () => {
  matchBaseParamVO.value.canBuyStr = "";
  localStorage.setItem("auto_price_first", "1");
};

const colDialog = ref(false);
const timer = ref(null); // 匹配进度定时器
const contentHeight = ref(0);
const getActiveType = ref('')
const ruleArr = ref(0)
const matchSuccessList = ref([])
const matchFailList = ref([])
const loseEffectList = ref([])
const allChecked = ref(false)
const checkArr = computed({
  get: () => {
    const result = [];
    if (matchBaseParamVO.value.nearEffectiveFlag == 2) {
      result.push(1);
    }
    if (matchBaseParamVO.value.hasCouponIs) {
      result.push(2);
    }
    if (matchBaseParamVO.value.buyShopIs) {
      result.push(3);
    }
    return result;
  },
  set: (value) => {
    matchBaseParamVO.value.nearEffectiveFlag = value.includes(1) ? 2 : -1;
    matchBaseParamVO.value.hasCouponIs = value.includes(2) ? true : "";
    matchBaseParamVO.value.buyShopIs = value.includes(3) ? true : "";
    onHandleOkRule(5);
  },
});

const tabChange = ()=>{
 //excelItemRef.value.reStartBindHandleScroll()
}
const lookBuyLimits = ()=>{
  console.log('123')
}
const openFirstDialog = ()=>{
  showRuleVis.value = true
}
const openRuleDialog = ()=>{
  showRuleVis.value = true

}
const refreshNormalGodds = () => {
  matchBaseParamVO.value.canBuyStr = "";
  onHandleOkRule();
};
function handleCancel() {
  showRuleVis.value = false;
  showTipVis.value = false;
  showPostageVis.value = false;
  showErrFile.value = false;
  showImportVis.value = false;
  clearInterval(timer.value);
}
const secondMatchToFather = (id)=>{

  addAllToButPlan.value = true
  let matchBaseParamVOParams = getMatchBaseParamVOParams()

   launchInquireMatchPriceResult(id,6,{...matchBaseParamVOParams})
 
}
const setRule = () => {
  //isTop.value = true;
  showRuleVis.value = true;
  actionTracking("pc_page_bulkPurchase_rule", {
    text: "设置比价规则",
  });
};

const setRuleNew = () => {
  purchasePlanBill.value.open();
};

const changeAllChecked = (e) => {
  needChangeChecked.value = true;

  list.value.forEach((item) => {
    item.skus.forEach(sku=>{
      sku.selectStatus = e?1:0
    })
  })
  
  hasChecked.value = e;
  // 调重新计算接口
  onRecalculate();
};

const onRefreshList = () => {
  matchBaseParamVO.value.canBuyStr = "";
  onHandleOkRule();
};
const replaceDataFromBigSearch = (item,lineNum,excelItem)=>{
  let add = true
  supplierList.value.forEach(i=>{
    if(i.shopCode==item.shopCode){
      add = false
    }
  })
  if(add){
    supplierList.value.push({
      shopCode:item.shopCode,
      name:item.shopName
    })
  }

 
  const params = {
    'requestId':requestId.value,
    'items':[
      {
        'lineNo':lineNum,
        'sku':item
      }
    ]
  }
  skuReplace(params)

  let flag = true
  matchSuccessList.value.forEach((i,index)=>{
    if(i.lineNum=== lineNum){
      flag  = false
      i.skus[0] = item
    }
  })
 
 
  if(flag){
 
    excelItem.skus[0] = item
   delete excelItem.noMatchFlag
    delete excelItem.noMatchMsg
    matchSuccessList.value.push(excelItem)
    matchSuccessList.value.sort((a,b)=>{
       return a.lineNum - b.lineNum
      })
    
      matchFailList.value = matchFailList.value.filter((i)=>{
        return i.lineNum !== lineNum
      })
    loseEffectList.value = loseEffectList.value.filter((i)=>{
      return i.lineNum !== lineNum
    })
 
    onRecalculate();
    excelItemRef1.value.addLazyList()
  }


  list.value = [...matchSuccessList.value,...matchFailList.value,...loseEffectList.value]


  // 调重新计算接口

}
// 给孙子传一个函数,当孙子那里触发向爷爷通信就会触发sendGrandson
// 监听孙子组件checkbox的change事件
const sendGrandson = (id,e) => {
  list.value.forEach((item) => {
    if(item.skus[0]?.skuId === id){
      item.skus[0].selectStatus = e?1:0
    }
  })
  matchSuccessList.value.forEach((item)=>{
    if(item.skus[0]?.skuId === id){
      item.skus[0].selectStatus = e?1:0
    }
  })
  let flag = true
  matchFailList.value.forEach(item=>{
    if(item.skus.length&&!item.skus[0].selectStatus){
        flag  = false
      }
    if(item.skus[0]?.skuId === id){
      
      item.skus[0].selectStatus = e?1:0
    }
  })
  addAllToButPlan.value = flag
  // 获取所有选中的skuId
  let ids = excelItemRef1.value.getSelectedIds();
  const checkedCount = ids.length;
  let allSkuLength = 0;
  hasChecked.value = false;

  (list.value || []).forEach((item) => {
    // 全选按钮联动
    allSkuLength = allSkuLength + (item.skus || []).length;
    // 判断加购按钮是否可点
    (item.skus || []).forEach((sku) => {
      if (sku.selectStatus) {
        hasChecked.value = true;
      }
    });
  });
  allChecked.value = checkedCount === allSkuLength;
  // 此时allChecked改变不需初始化所有列表的checkbox状态
  needChangeChecked.value = false;
  if(activeName.value ==='1')
  // 调重新计算接口
  onRecalculate();
};
provide("sendHandle", sendGrandson);
provide("replaceDataFromBigSearch",replaceDataFromBigSearch)
provide("activeName",activeName.value)
const canBuyAndAddCart = ref(true)
const changeQtyCount = ref(0)
// 监听加购操作
const changeQty = (activityItem,productValue) => {

  if(activityItem.excelBuyNum!=productValue){

    changeQtyCount.value++
    canBuyAndAddCart.value = changeQtyCount.value>0?false:true
  }
  else{
    changeQtyCount.value--
    canBuyAndAddCart.value = changeQtyCount.value>0?false:true
  }
  list.value.forEach((i) => {
    if (i.lineNum === activityItem.lineNum) {
      i = activityItem;
    }
  });
  // 调重新计算接口
  onRecalculate();
};
provide("carrier", changeQty);

// 保存设置的比价规则
const onHandleOkRule = (type) => {
  rulesOfMatch.value = setMatchRuleRef.value.getMatchRule();
  handleCancel();
  getList(type);
};

const pageRefresh = (type) => {
  isDisableRefresh.value = true
  if(type === 1){
    isToast.value = true
  }
  getList(6);
};

const purchasePlanBillDone = (colData1, status, callback) => {
  if (importLoading3.value == true) return;
  progressDatalength.value = colData1.value.count
  importLoading3.value = true;
  const formData = new FormData();
  for (const key in colData1.value.form) {
    formData.append(key, colData1.value.form[key]);
  }
  formData.append("headRowIndex", colData1.value.headRowIndex);
  formData.append("file", colData1.value.file);
  if (status === 0) {
    matchPriceImport(formData)
      .then((res) => {
        importLoading3.value = false;
        if (res.success) {
          planResultData.value.success = res.result.successList;
          planResultData.value.error = res.result.fialList;
          planResultData.value.status = true;
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((err) => {
        purchasePlanBill.value.colDialog = false
        purchasePlanBill.value.importLoading = false
        purchasePlanBill.value.isDisable = false
        ElMessage.error("导入失败，请稍后重试");
      })
      .finally(() => {
        importLoading3.value = false;
      });
  } else {
    matchPriceImport(formData).then((res) => {
      list.value = []
      importLoading3.value = false;
      if (res.result) {
        showErrFile.value = true;
        errorUrl.value = res.result;
        return;
      }
      if (res.code == 0) {
        callback(true);
        getList();
      } else {
        purchasePlanBill.value.colDialog = false
        purchasePlanBill.value.importLoading = false
        purchasePlanBill.value.isDisable = false
        ElMessage.error(res.msg);
      }
    });
  }
};

const toShowRefresh = () => {
  showTipVis.value = true;
};
//批量加入采购计划
const getItems = ()=>{
  return matchFailList.value.filter(item=>item.skus.length&&item.skus[0].selectStatus).map(item=>{
    return {
      'lineNo':item.lineNum,
      'sku':item.skus[0]
    }
  })
}
const addBuyPlan = async ()=>{
  if(matchFailList.value.filter(item=>item.skus.length&&item.skus[0].selectStatus).length==0){
    ElMessage.error('请选择要加入采购计划的商品')
    return
  }
  let addMerchant = []

  matchFailList.value.forEach(i=>{
    if(i.skus.length&&i.skus[0].selectStatus){
      addMerchant.push({
        'name':i.skus[0].shopName,
        'shopCode':i.skus[0].shopCode
      })
    }
  })
  addMerchant.forEach(i=>{
    if(supplierList.value.some(item=>item.shopCode==i.shopCode)){
      return
    }else{
      supplierList.value.push({
        'name':i.name,
        'shopCode':i.shopCode
      })
    }
  })

  matchSuccessList.value =[...matchFailList.value.filter(item=>item.skus.length&&item.skus[0].selectStatus),...matchSuccessList.value]
  matchSuccessList.value.sort((a,b)=>a.lineNum-b.lineNum)
  await skuReplace({
    'requestId':requestId.value,
    'items':getItems()
  })
  matchFailList.value =  matchFailList.value.filter(item=>item.skus.length==0||item.skus.length&&!item.skus[0].selectStatus)
  hasChecked.value = matchSuccessList.value.some(item=>item.skus.length&&item.skus[0].selectStatus)
  hasSku.value = matchSuccessList.value.length>0
  list.value = [...matchSuccessList.value,...matchFailList.value,...loseEffectList.value]
  ElMessage.success('加入成功')
  hasSku.value = matchSuccessList.value.some(item=>item.skus[0].selectStatus)
hasChecked.value = matchSuccessList.value.some(item=>item.skus[0].selectStatus)
  onRecalculate()
}
const allSelectSecondMathchList = (val)=>{

  matchFailList.value = matchFailList.value.map(item=>{
    if(item?.skus.length){
      item.skus[0].selectStatus = val?1:0
    }
      return item
  })
}
const onRefresh = () => {
  listInfo.value = {
    productTotalNum: 0.0,
    totalAmount: 0.0,
    discountAmount: 0.0,
    freightTotalAmt: 0.0,
    payAmount: 0.0,
    productSelectedNum: 0.0,
    shopCount: 0.0,
  };
  hasChecked.value = false;
  handleCancel();
  getList();
};
const getuuid = ()=> {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random()*16|0, v = c == 'x' ? r : (r&0x3|0x8);
        return v.toString(16);
    });
}
const launchInquireMatchPriceResult =  async(id,type,...matchBaseParamVO)=>{
    
      inquireMatchPriceResult({'requestId':id,matchBaseParamVO:matchBaseParamVO[0]}) .then( async (result) => {
     
     clearInterval(timer.value);
  
     if (result.success) {
    
      if(id===requestId.value){ 
       list.value = (result.result || {}).matchSkuList || [];
       hasSku.value = false;
       supplierList.value = (result.result || {}).shopList || [];
       list.value.forEach((item) => {
         if ((item.skus || []).length) {
           hasSku.value = true;
         }
       });
    
       matchSuccessList.value = list.value.filter(item=>!item?.noMatchFlag&&item?.skus?.length || item?.noMatchFlag&&item?.noMatchFlag == 14)
       matchFailList.value = list.value.filter(item=>item?.noMatchFlag&&item?.noMatchFlag==1)
       loseEffectList.value =  list.value.filter(item=>item?.noMatchFlag&&item?.noMatchFlag!=1&&item?.noMatchFlag!=14)
      }
      else{
        matchFailList.value = ((result.result || {}).matchSkuList || []).filter(item=>item.noMatchFlag&&item.noMatchFlag!=14);
      }
     } else {
        ElMessage.error(res.errorMsg);
     }
    if(result.result.end === false){
      setTimeout(()=>{
        launchInquireMatchPriceResult(id,type,...matchBaseParamVO)
      },1300)
    }
    else{
    
      if(id===requestId.value){
        showAddBuyPlan.value = false
       // 赋值底部总计信息
       listInfo.value = {
        productTotalNum: result.result.productTotalNum || 0.0,
        totalAmount: result.result.totalAmount || 0.0,
        discountAmount: result.result.discountAmount || 0.0,
        freightTotalAmt: result.result.freightTotalAmt || 0.0,
        payAmount: result.result.payAmount || 0.0,
        productSelectedNum: result.result.productSelectedNum || 0.0,
        shopCount: result.result.shopCount || 0.0,
      };
      supplierList.value = (result.result || {}).shopList || [];
      list.value = (result.result || {}).matchSkuList || [];
      list.value.sort((a,b)=>{
       return a.lineNum - b.lineNum
      })
   
      matchSuccessList.value = list.value.filter(item=>  !item.noMatchFlag&&item.skus.length || item?.noMatchFlag&&item?.noMatchFlag == 14)
       matchFailList.value = list.value.filter(item=>item.noMatchFlag&&item.noMatchFlag==1)
       loseEffectList.value =  list.value.filter(item=>item.noMatchFlag&&item.noMatchFlag!=1&&item?.noMatchFlag!=14)
      //  if([1,2,3,4,5].includes(type)){
      //   await nextTick();
      //   excelItemRef2.value.isShowMatchAll  = false
      //  }
    }
    else{
      matchFailList.value = (result.result || {}).matchSkuList || [];
      matchFailList.value.sort((a,b)=>{
       return (a.lineNum - b.lineNum)
      })
      matchFailList.value.sort((a,b)=>{
        return b.skus.length - a.skus.length
      })
      await nextTick();
      excelItemRef2.value.isShowMatchAll  = false
      showAddBuyPlan.value = true
    }
    showImportVis.value = false;
    isDisableRefresh.value = false
    hasChecked.value = list.value.some(item=>item.skus.some(sku=>sku.selectStatus));
    allChecked.value = list.value.every(item=>item.skus?.every(sku=>sku.selectStatus))
    canBuyAndAddCart.value = matchSuccessList.value.every(item=>item.excelBuyNum==item.skus[0].qty)
    }
   })
   .catch(() => {
     clearInterval(timer.value);
     showImportVis.value = false;
   });
  if([1,2,3,4,5].includes(type)){
   nextTick(()=>{

   setTimeout(() => { 
    excelItemRef2.value.isShowMatchAll  = true
   }, 0);

  });
  
  
    return
  }
   getProgress(id);
  clearInterval(timer.value);
  timer.value = setInterval(() => {
    getProgress(id);
  }, 1000);

}
const getMatchBaseParamVOParams = ()=>{
  let matchBaseParamVOParams = Object.assign({}, matchBaseParamVO.value, {
    canBuyStr:
      matchBaseParamVO.value.canBuyStr === "all"
        ? ""
        : matchBaseParamVO.value.canBuyStr,
  });
  if(matchBaseParamVOParams.hasCouponIs === '') matchBaseParamVOParams.hasCouponIs = null
  if(matchBaseParamVOParams.buyShopIs === '') matchBaseParamVOParams.buyShopIs = null
  return matchBaseParamVOParams
}
const getList = (type) => {
  hasChecked.value = false
        listInfo.value = {
        productTotalNum:  0.0,
        totalAmount:  0.0,
        discountAmount:  0.0,
        freightTotalAmt: 0.0,
        payAmount:  0.0,
        productSelectedNum:  0.0,
        shopCount:  0.0,
      };
     
    
  //changeAllChecked(false);
  needChangeChecked.value = false;
  let matchBaseParamVOParams = getMatchBaseParamVOParams()
  // if(!requestId.value || type !== 6){
  //   requestId.value = getuuid()
  // }
 
   if([1,2,3,4,5].includes(type)){
  
      launchInquireMatchPriceResult(requestId.value,type,{...matchBaseParamVOParams})
      //clearMatchProgress({})
   }
   else{
        requestId.value = getuuid()
        submitMatchPrice({requestId:requestId.value}).then((res)=>{
        if(res.code === 0){
          list.value = []
          supplierList.value = []
          matchSuccessList.value = []
          matchFailList.value = []
          loseEffectList.value = []
        launchInquireMatchPriceResult(requestId.value,type,{...matchBaseParamVOParams})
        //clearMatchProgress({})
      }
      else if (res.code == 1){
      ElMessage.error(res.msg);
      }
      })
   } 
};

// 重新计算接口
const onRecalculate = () => {
  
  selectMatchProduct({
    matchLineVOs: matchSuccessList.value.filter(item=>item?.skus[0]?.selectStatus),
  }).then((res) => {
    if (res.status === "success") {
      const result = res.result || {};
      // 替换计算完之后的item

      list.value.forEach((excelItem) => {
        result.matchSkuList.forEach((item) => {
          if (excelItem.lineNum === item.lineNum) {
            excelItem.skus = item.skus || [];
          }
        });
      });

      // 赋值底部总计信息
      listInfo.value = {
        productTotalNum: result.productTotalNum || 0.0,
        totalAmount: result.totalAmount || 0.0,
        discountAmount: result.discountAmount || 0.0,
        freightTotalAmt: result.freightTotalAmt || 0.0,
        payAmount: result.payAmount || 0.0,
        productSelectedNum: result.productSelectedNum || 0.0,
        shopCount: result.shopCount || 0.0,
      };
      hasChecked.value = listInfo.value.productSelectedNum > 0;
      hasSku.value = listInfo.value.productSelectedNum > 0;
      // 起送包邮金额提醒
      unsatisfiedFreeShippingList.value =
        result.unsatisfiedFreeShippingList || []; // 未包邮列表
      unsatisfiedStartPriceList.value = result.unsatisfiedStartPriceList || []; // 起送价列表
    } else {
      ElMessage.error(res.errorMsg);
    }
  });
};
// 删除选中品
const delGoods = () => {
  let ids = excelItemRef1.value && excelItemRef1.value.getSelectedIds();
  if ((ids || []).length === 0) {
    ElMessage.error("请选择要删除的商品");
    return;
  }
  ElMessageBox.confirm("确认删除已选商品吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      excelItemRef1.value.delLazyGoods(ids);
      ids.forEach((id) => {
        list.value.forEach((item, index) => {
          (item.skus || []).forEach((sku, skuIndex) => {
            if (id == sku.skuId) {
              // skus的长度为1时，把整条都删掉
              if (item.skus.length === 1) {
                list.value.splice(index, 1);
              } else {
                item.skus.splice(skuIndex, 1);
              }
            }
          });
        });
      });
    matchSuccessList.value = list.value.filter(item=>  !item.noMatchFlag&&item.skus.length)
    dataTotalNum.value = list.value.length;
      // 重置全选按钮是否可点
      hasSku.value = false;
      list.value.forEach((item) => {
        if ((item.skus || []).length) {
          hasSku.value = true;
        }
      });

      // 重置底部信息
      listInfo.value = {
        productTotalNum: 0.0,
        totalAmount: 0.0,
        discountAmount: 0.0,
        freightTotalAmt: 0.0,
        payAmount: 0.0,
        productSelectedNum: 0.0,
        shopCount: 0.0,
      };
      hasChecked.value = false;
      allChecked.value = false;
    })
    .catch((err) => {
      console.log("err", err);
    });
};
const getProgress = (requestId) => {
  showImportVis.value = true;
  getMatchProgress({requestId}).then((res) => {
    console.log('res',res)
    if (res.result?.rate && res.result.rate === 100) {
      clearInterval(timer.value);
      if(isToast.value){
        isToast.value = false;
        ElMessage.success("已恢复上次采购计划并根据比价规则更新推荐采购结果");
    
      }
      //showImportVis.value = false;
      loadingProgress.value =  0;
      matchedNum.value =  0;
      dataTotalNum.value =  0;
      // getList();
    } else {
      showImportVis.value = true;
    }
    loadingProgress.value = res.result.rate || 0;
    matchedNum.value = res.result.matched || 0;
    dataTotalNum.value = res.result.total || 0;
  });
};

const beforeImportData = (uploadInfo) => {
  const fileName = uploadInfo.name;
  const fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
  if (fileType !== "xlsx" && fileType !== "xls") {
    ElMessage.warning("选择的文件类型不对");
    return false;
  }
  return true;
};
const getSelectedIds = ()=>{
  let checkedCodes = [];
		list.value.forEach((item) => {
			(item.skus || []).forEach((sku) => {
				if (sku.selectStatus) {
					checkedCodes.push(sku.skuId);
				}
			})
		})
		return checkedCodes
}
// 导入采购单
const uploadFile = (type) => {
  return (params) => {
    actionTracking("pc_page_bulkPurchase_import", {
      text: type == "采购计划单" ? "导入采购单" : "导入计划单",
    });
    const { file } = params;
    let fileFormData;
    if (file && file.name !== "") {
      fileFormData = new FormData();
      fileFormData.append("file", file);
    } else {
      ElMessage.warning("请选择上传文件!");
      return;
    }
    if (type == "采购计划单") {
      importLoading.value = true;
    } else {
      importLoading2.value = true;
    }
    colData.value.form = {};
    getTableHeaderInformation(fileFormData)
      .then((res) => {
        if (res.code == 0) {
          colData.value = {
            ...getFormData(res.result.headerInformation, type),
            file: file,
            count: res.result.rowNum,
          };
          colDialog.value = true;
          /* const fn = type == '采购计划单' ? importPurchasePlan : importPlan;
					fn(fileFormData).then((res) => {
						if (res.result) {
							showErrFile.value = true;
							errorUrl.value = res.result;
							return;
						}
						if (res.code === 0) {
							ElMessage.success(`${type == '采购计划单' ? '采购' : '计划'}单导入成功`);
							//getList();
							//获取表头
						} else {
							ElMessage.error(res.msg || '导入失败，请稍后重试');
						}
					}).catch((err) => {
						ElMessage.error('导入失败，请稍后重试');
					}).finally(() => {
						importLoading.value = false;
					}) */
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch((err) => {
        ElMessage.error("获取表头失败");
      })
      .finally(() => {
        importLoading.value = false;
        importLoading2.value = false;
      });
  };
};
const tableColDialog = (type) => {
  colData.value = {
    ...getFormData([], type),
    file: null,
    count: 0,
  };
  colDialog.value = true;
};
/**
 *
 * @param { 0 | 1} status  0:计划单    1:采购单
 */
const fileSubmit = (status) => {
  if (formRef) {
  }
  if (importLoading3.value == true) return;
  importLoading3.value = true;
  if (formRef) {
    formRef.value.validate((valid) => {
      if (valid) {
        const formData = new FormData();
        for (const key in colData.value.form) {
          formData.append(key, colData.value.form[key]);
        }
        formData.append("file", colData.value.file);
        if (status == 0) {
          importPlan(formData)
            .then((res) => {
              if (res.success) {
              
                planResultData.value.success = res.result.successList;
                planResultData.value.error = res.result.fialList;
                planResultData.value.status = true;
              
              } else {
                ElMessage.error(res.msg);
              }
            })
            .catch((err) => {
              ElMessage.error("导入失败，请稍后重试");
            })
            .finally(() => {
              importLoading3.value = false;
            });
        } else if (status == 1) {
          importPurchasePlan(formData)
            .then((res) => {
              if (res.result) {
                showErrFile.value = true;
                errorUrl.value = res.result;
                return;
              }
              if (res.code == 0) {
                colDialog.value = false;
                addAllToButPlan.value = true;
                getList();
               
              } else {
                ElMessage.error(res.msg);
              }
            })
            .catch((err) => {
              ElMessage.error("导入失败，请稍后重试");
            })
            .finally(() => {
              importLoading3.value = false;
            });
        }
      } else {
        importLoading3.value = false;
      }
    });
  }
};

// 下载模板
const download = () => {
  window.open("/fileTransfer/downloadTemplate?fileName=采购单导入模板.xlsx");
};

// 下载报价单
const downLoading = ref(false);
const downloadExcel = () => {
  if (downLoading.value) return;
  downLoading.value = true;
  let ids = getSelectedIds();
  if (list.value.length == 0 || (ids || []).length === 0) {
    ElMessage.error("当前无选中商品数据，无法下载计划单");
    downLoading.value = false;
    return;
  }
  downloadQuotationV2(matchSuccessList.value)
    .then((res) => {
      console.log('%c [ res ]-1632', 'font-size:13px; background:pink; color:#bf2c9f;', res.data)
      if (res.data.code) {
        ElMessage.error("下载失败");
        return;
      }
      console.log(res,'456')
       exportExcelV2(res.data, "计划单（药帮忙报价表）.xlsx",res);
    })
    .catch((e) => {
      console.log('123', e)
      ElMessage.error("下载失败");
    })
    .finally(() => {
      downLoading.value = false;
    });
  /* let ids = excelItemRef.value && excelItemRef.value.getSelectedIds();
		if (list.value.length == 0 || (ids || []).length === 0) {
			ElMessage.error('当前无选中商品数据，无法下载计划单');
			return 
		}
		downloadQuotation({
			matchRuleParamVO: {
				...rulesOfMatch.value
			},
			matchBaseParamVO: {
				...matchBaseParamVO.value
			}
		}).then((res) => {
			if(res.data.type === "application/json") {
				const reader = new FileReader();
				reader.onload = function(){
					const { msg } = JSON.parse(reader.result);
					//处理错误
					ElMessage.error(msg);
				};
				reader.readAsText(res.data);
			} else {
				const contentDisposition = res.headers['content-disposition'];
    		exportExcel(res.data, decodeURIComponent(contentDisposition.split('filename=')[1]));
			}
		}); */
};
const exportExcelV2= (res, ecxelName,resp)=>{
  const blob = new Blob([res]); // 接受文档流
  if ('msSaveOrOpenBlob' in navigator) {
    // IE下的导出
    // eslint-disable-next-line no-useless-concat
    window.navigator.msSaveOrOpenBlob(blob, ecxelName || '导出文件' + '.xlsx'); // 设置导出的文件名
  } else {
    // 非ie下的导出
    const a = document.createElement('a');
    const url = window.URL.createObjectURL(blob);
    // eslint-disable-next-line no-useless-concat
    const hs = resp.headers['content-disposition'];
    let filename=""
    if (hs) {
     
      filename =  hs.match(/filename="(.*)"/)[1];
    }
    console.log(filename);
    filename = filename || '导出文件' + '.xlsx'; // 设置导出的文件名
    // console.log(filename);
    a.href = url;
    a.download = decodeURIComponent(filename);
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }
}

function calcPageHeight(doc) {
  const cHeight = Math.max(
    doc.body.clientHeight,
    doc.documentElement.clientHeight
  );
  const sHeight = Math.max(
    doc.body.scrollHeight,
    doc.documentElement.scrollHeight
  );
  const height = Math.max(cHeight, sHeight);
  return height;
}

const getSowGroundParams = () => {
  getSowGroundParam({
    name: "pc_batch_purchase",
  }).then((res) => {
    sowGroundParams.value = res.result || {};
  });
};
const getMatchConversionDetails = ()=>{
  const save = ['skuId','code','commonName','spec','manufacturer','approvalNumber','shopCode','shopName','fob','discountPrice','qty','subTotal']

  const res = []
  const result = []
  list.value.filter(item=>!item.noMatchFlag).forEach(item=>{
    item.skus.forEach(i=>{
      res.push(i)
    })
  }
  )
  res.forEach(item=>{
    const cur = {}
    for(var key in item){
     if(save.indexOf(key)!==-1)
     cur[key] = item[key]
    }
    result.push(cur)
  })
  return result
}
const getDataAddCartOrbuy = (type)=>{
  return {
    'requestId': requestId.value,
    'totalAmount': listInfo.value.totalAmount,
    'payAmount': listInfo.value.payAmount,
    'conversionType': type === 1 ? 'SHOPPING_CART' : 'SETTLE',
    'matchConversionDetails':[...getMatchConversionDetails()],
  }
}
const canBuyOrAddCart = ()=>{
  return  matchSuccessList.value.some(item=>item.skus[0].autoAdjustNum==1)
  
}
const addCart = () => {
//  if(canBuyOrAddCart()){
//   ElMessage.error("请核对预计采购数量与采购数量是否一致");
//   return
//  }
  if (!hasChecked.value) {
    return;
  }
  actionTracking("pc_page_bulkPurchase_ShoppingCart", {});
  
  let skuList = [];
  list.value.forEach((item) => {
    (item.skus || []).forEach((sku) => {
      let obj = { skuId: "", qty: "" };
      if (sku.selectStatus) {
        obj.skuId = sku.skuId;
        obj.qty = sku.qty;
        skuList.push(obj);
      }
    });
  });

  changeCartBatch({
    skuList,
    ...sowGroundParams.value,
  }).then((res) => {
    if (res.status === "success") {
      // window.open('http://new-www.test.ybm100.com/merchant/center/cart/index.htm');
      inquireRecording({ ...getDataAddCartOrbuy(1)}).then((res)=>{
    if(res.success){
      ElMessage.success('加入购物车成功');
    }
  }).catch(err=>{
    ElMessage.error(err.msg)
  })
      window.open("/merchant/center/cart/index.htm");
    } else {
      ElMessage.error(res.errorMsg);
    }
  });
};

// 去结算
const submit = () => {
//   if(canBuyOrAddCart()){
//   ElMessage.error("请核对预计采购数量与采购数量是否一致");
//   return
//  }
  if (!hasChecked.value) {
    return;
  }
 


  actionTracking("pc_page_bulkPurchase_settleAccounts", {});
  if (
    unsatisfiedFreeShippingList.value.length ||
    unsatisfiedStartPriceList.value.length
  ) {
    showPostageVis.value = true;
    return;
  }
  // 确认结算
  onConfirmSubmit();
};

const onConfirmSubmit = () => {
  let skuList = [];
  // if (colData.value.title == "导入计划单") {
  //   planResultData.value.success.forEach((item) => {
  //     (item.skus || []).forEach((sku) => {
  //       let obj = { skuId: "", qty: "", orgId: "" };
  //       if (sku.selectStatus) {
  //         obj.skuId = sku.skuId;
  //         obj.qty = sku.qty;
  //         obj.orgId = sku.orgId;
  //         skuList.push(obj);
  //       }
  //     });
  //   });
  // } else {
  list.value.forEach((item) => {
    (item.skus || []).forEach((sku) => {
     
      let obj = { skuId: "", qty: "", orgId: "" };
      if (sku.selectStatus) {
        obj.skuId = sku.skuId;
        obj.qty = sku.qty;
        obj.orgId = sku.orgId;
        skuList.push(obj);
      }
    });
  });
  // }
  let notSubmitOrderOrgIds = unsatisfiedStartPriceList.value
    .map((i) => i.orgId)
    .join();
  matchPrice({
    skuList,
    notSubmitOrderOrgIds, // 不满足起送的orgid
    ...sowGroundParams.value,
  }).then((res) => {
    if (res.status === "success") {
      // window.open(`http://new-www.test.ybm100.com/merchant/center/order/matchPrice/settle.htm`);
      inquireRecording({ ...getDataAddCartOrbuy(2)})
  
      window.open("/merchant/center/order/matchPrice/settle.htm");
    } else {
      ElMessage.error(res.errorMsg);
    }
  });
};
const ruleTimer = ref(null);
const onGetMatchRule = () => {
  rulesOfMatch.value = setMatchRuleRef.value.getMatchRule();
  if (
    rulesOfMatch.value.popShopCodeList.length > 0 ||
    rulesOfMatch.value.shopCodeList.length > 0
  ) {
    clearInterval(ruleTimer.value);
    getList();
  } else {
    clearInterval(ruleTimer.value);
    ruleTimer.value = setInterval(() => {
      onGetMatchRule();
    }, 500);
  }
};
const getRuleTemp = (length,activeType,myMerchant,beforeBuyMerchant,highMerchant,chinaMerchant,otherMerchant) => {


  ruleArr.value = length
  getActiveType.value = activeType===0?'全部':activeType===1?'拼团&批购包邮':'可凑单'
  getMerchant.value = myMerchant ?'平台自营，':''
  getMerchant.value += beforeBuyMerchant||highMerchant||chinaMerchant||otherMerchant?'平台商家':''

  getMerchant.value = getMerchant.value.replace(/，$/,'')
  rulesOfMatch.value = setMatchRuleRef.value.getMatchRule();
  ruleTemp.value.condition = 0;
  ruleTemp.value.condition += rulesOfMatch.value.isCheckLimit ? 1 : 0;
  ruleTemp.value.condition += rulesOfMatch.value.isCheckMinOrder ? 1 : 0;
  ruleTemp.value.condition += rulesOfMatch.value.isCheckStock ? 1 : 0;
  ruleTemp.value.condition += rulesOfMatch.value.nearEffectDay ? 1 : 0;
  ruleTemp.value.shop = [];
  if (rulesOfMatch.value.shopCodeList.some((item) => item.selectStatus != 0)) {
    ruleTemp.value.shop.push("平台自营");
  }
  if (
    rulesOfMatch.value.popShopCodeList.some((item) => item.selectStatus != 0)
  ) {
    ruleTemp.value.shop.push("平台商家");
  }
};

const downloadErrorPro = () => {
  exportFailedPlan(planResultData.value.error).then((res) => {
    if (res) {
      exportExcel(res, "导入计划单-失败商品.xlsx");
    }
  });
};
const submitAgain = () => {
  selectMatchProduct({
    matchLineVOs: planResultData.value.success,
  }).then((res) => {
    if (res.status === "success") {
      const result = res.result || {};
      // 替换计算完之后的item
      list.value.forEach((excelItem) => {
        result.matchSkuList.forEach((item) => {
          if (excelItem.lineNum === item.lineNum) {
            excelItem.skus = item.skus || [];
          }
        });
      });

      // 赋值底部总计信息
      listInfo.value = {
        productTotalNum: result.productTotalNum || 0.0,
        totalAmount: result.totalAmount || 0.0,
        discountAmount: result.discountAmount || 0.0,
        freightTotalAmt: result.freightTotalAmt || 0.0,
        payAmount: result.payAmount || 0.0,
        productSelectedNum: result.productSelectedNum || 0.0,
        shopCount: result.shopCount || 0.0,
      };

      // 起送包邮金额提醒
      unsatisfiedFreeShippingList.value =
        result.unsatisfiedFreeShippingList || []; // 未包邮列表
      unsatisfiedStartPriceList.value = result.unsatisfiedStartPriceList || []; // 起送价列表
      if (
        unsatisfiedFreeShippingList.value.length ||
        unsatisfiedStartPriceList.value.length
      ) {
        showPostageVis.value = true;
        return;
      } else {
        let skuList = [];
        planResultData.value.success.forEach((item) => {
          (item.skus || []).forEach((sku) => {
            let obj = { skuId: "", qty: "", orgId: "" };
            if (sku.selectStatus) {
              obj.skuId = sku.skuId;
              obj.qty = sku.qty;
              obj.orgId = sku.orgId;
              skuList.push(obj);
            }
          });
        });
        let notSubmitOrderOrgIds = unsatisfiedStartPriceList.value
          .map((i) => i.orgId)
          .join();
        matchPrice({
          skuList,
          notSubmitOrderOrgIds, // 不满足起送的orgid
          ...sowGroundParams.value,
        }).then((res) => {
          if (res.status === "success") {
            // window.open(`http://new-www.test.ybm100.com/merchant/center/order/matchPrice/settle.htm`);
            window.open("/merchant/center/order/matchPrice/settle.htm");
          } else {
            ElMessage.error(res.errorMsg);
          }
        });
      }
    } else {
      ElMessage.error(res.errorMsg);
    }
  });
};
const valueSelect = (key, val) => {
  for (const k in colData.value.form) {
    if (colData.value.form[k] == val && val) {
      ElMessage.error("该数据已被选用，请勿重复选择");
      return;
    }
  }
  colData.value.form[key] = val;
};

const handleScroll = (event) => {
  // 获取滚动高度
  if(showImportVis.value){
    if (window.scrollY >= 323) {
    isTop.value = true;
  } else {
    isTop.value = false;
  }
  return
  }
  if (window.scrollY >= 230) {
    isTop.value = true;
  } else {
    isTop.value = false;
  }

   // console.log("滚动高度:", window.pageYOffset);
};

onMounted(() => { 
  getMaxMAndMaxRow().then((res)=>{
    MaxRow.value = res.result.importMaxRows
    MaxM.value = res.result.importMaxSize
  })
  let isFirst = localStorage.getItem("auto_price_first");
  if (!isFirst) {
    showTour.value = true;
    matchBaseParamVO.value.canBuyStr = "deficiency";
  }

  contentHeight.value = window.innerHeight - 291 - 128;
  // 获取埋点数据
  getSowGroundParams();
  // 获取比价规则数据-由于数据在子组件可能出现拿不到的情况，此时需要轮询获取
  /* onGetMatchRule(); */
  let height = calcPageHeight(document);
  window.parent.postMessage(
    {
      message: "getCrossHeight",
      height,
    },
    "*"
  );
  actionTracking("pc_page_bulkPurchase", {
    text: "批量采购专区",
    action: window.location.href,
  });
  document.addEventListener("scroll", handleScroll);
});

onUnmounted(() => {
  clearInterval(timer.value);
  clearInterval(ruleTimer.value);
  document.removeEventListener("scroll", handleScroll);
});
</script>
<style lang="scss" scoped>

.x-rules {
  display: flex;
  padding: 15px 10px;
  background-color: #00b377;
  align-items: center;
  color: white;
}
.wrapperBox {
  width: 100%;
  background: #f4f4f4;
  padding-bottom: 80px;
}
.headerTop {
  width: 100%;
  height: auto;
  background: #ffffff;
}
.headerBox {
  width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .hraderImg {
    width: 480px;
    height: 79px;
  }
  .progressImg {
    width: 460px;
    height: 72px;
  }
}
.content {
  position: relative;
  width: 1200px;
  height: auto;
  font-size: 14px;
  margin: 0 auto;
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
  .xx-primary {
    background: #0099e0;
    border: solid 1px #0091d4;
    cursor: pointer;
    padding: 7px 15px;
    border-radius: 3px;
    transition: all 0.2s;
  }
  .xx-primary:hover {
    background: #2db4f3;
    border: solid 1px #2ea0d4;
  }
  .xx-primary:active {
    background: #008aca;
    border: solid 1px #037cb4;
  }
  .contentBtn {
    width: 100%;
    height: 72px;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 2px;
    margin: 10px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    div {
      display: flex;
      align-items: center;
      color: #888888;
    }
    .refreshBox {
      margin-right: 20px;
      cursor: pointer;
    }
    .refreshIconBox {
      width: 14px;
      height: 14px;
      margin-right: 8px;
    }
    .contentTitle {
      margin-left: 20px;
    }
    span {
      margin-right: 30px;
      font-weight: bold;
      color: #333333;
    }
  }
}
.footerWrapper {
  position: fixed;
  bottom:0px;
  width: 100%;
  height: 80px;
  display: flex;
  align-items: center;
  background: #ffffff;
  z-index: 100;
  .footerBox {
    width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #222222;
    span {
      color: #9494a6;
    }
    .footerLeft {
      display: flex;
      flex-direction: row;
      align-items: center;
      span {
        margin-left: 30px;
        cursor: pointer;
      }
      .downloadPlan {
        margin-left: 10px;
        font-weight: 500;
        font-size: 14px;
        height:40px;
        text-align: center;
      }
    }
    .footerRight {
      display: flex;
      align-items: center;
      font-size: 14px;
      .priceBox {
        text-align: right;
      }
      .redText,
      .redPrice {
        color: #e62e2e;
      }
      .redPrice {
        font-weight: bold;
        font-size: 18px;
      }
      .footerBtn {
        display: flex;
        align-items: center;
        border-radius: 2px;
        font-weight: Bold;
        font-size: 18px;
        color: #ffffff;
        margin-left: 20px;
        div {
          width: 140px;
          height: 46px;
          text-align: center;
          line-height: 46px;
        }
      }
      .addBtn {
        background: #00b377;
        cursor: pointer;
      }
      .disAddBtn {
        background: #c6c8cb;
      }
      .settlementBtn {
        background: #f39800;
        cursor: pointer;
      }
      .disSettlementBtn {
        background: #aaaeb6;
      }
    }
  }
}
.noGoods {
  position: relative;
  text-align: center;
  height: 500px;
  padding: 90px 0;
  background: #fff;

  img {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 188px;
    height: 128px;
  }
  .row-line {
    position: absolute;
    top: 230px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
  .text {
    position: absolute;
    top: 230px;
    left: 50%;
    transform: translateX(-50%);
    color: #555555;
  }
}
.float-row-view {
  position: fixed;
  top: 0;
}

.rules-modules {
  margin: 10px 0px;
  padding: 7px 7px;
  background-color: white;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.rules-modules-left {
  background-color: #f1fcf7;
  width: 590px;
  height: 96px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.rules-modules-left-shops {
  display: flex;
  flex-direction: row;
}

.rules-modules-right {
  background-color: #f1fcf7;
  width: 590px;
  height: 96px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.rules-modules-left-left {
  height: 96px;
}

.rules-modules-right-left {
  height: 96px;
}
.float-row-view-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  background: white;
  padding: 10px 0px;
}
.float-row-view-no-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
  padding: 10px 0px;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}
.progressContainer{
  padding:14px 18px;
  background-color: #fff;
  margin:10px 0;
  height:73px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.progress-right{
  width: 51px;
height: 51px;
background: #F1FCF7;
margin-left:35px;
display: flex;
align-items: center;
justify-content: center;
color:#00B955;
font-weight:600;
font-size: 20px;
font-family: PingFangSC;
border-radius: 25px;
}
</style>
<style>

.progressContainer .el-progress-bar__inner{  
  background: linear-gradient(270deg, #00B955 1%, #77D469 100%);  
}  
.progressContainer .el-progress-bar__innerText{
	margin:-14px -14px;
  color:black;
  position: relative;
  bottom:11px;
}
.newpro img{
  height:19px;
  width:19px;
 
}
.progressContainer .el-progress-bar__outer {
  height:10px !important;
  overflow:visible !important;
}
.newpro .el-progress-bar__inner:before{
    content:"";
    width:100%;
    height:100%;
    display:block;
    background-image:repeating-linear-gradient(-45deg,hsla(0,0%,100%,.15) 25%,transparent 0,transparent 50%,hsla(0,0%,100%,.15) 0,hsla(0,0%,100%,.15) 75%,transparent 0,transparent);
    background-size:40px 40px;
    animation:mymove 2s linear infinite;
}
@keyframes mymove{
			0%   {background-position: 0;}
			25%  {background-position: 50px;}
			50%  {background-position: 100px;}
			75%  {background-position: 150px;}
			100% {background-position: 200px;}
		}
    .content-info{
      padding:5px 20px 15px ;
      margin:10px 0 3px;
      background-color: white;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
    }
    .content-info div{
      display:flex;
    }
    .el-tabs__header{
      margin:0;
      background-color:#fff;
    }
    .el-tabs__nav-scroll{
      padding-left:20px;
    }
    .el-tabs__nav-wrap:after{
      background-color:transparent;
    }
</style>